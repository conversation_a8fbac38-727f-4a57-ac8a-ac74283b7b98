package com.tydic.nbchat.user.api;

import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.login.UserLoginReqBO;
import com.tydic.nbchat.user.api.bo.regist.UserRegistReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface NbchatUserRegLoginApi {

    /**
     * 登出-移除缓存
     * @param userId
     * @return
     */
    Rsp logout(String userId);

    /**
     * 登录
     * @param loginReqBO
     * @return
     */
    Rsp<NbchatUserInfo> login(UserLoginReqBO loginReqBO);

    /**
     * 注册
     * @param registReqBO
     * @return
     */
    Rsp<NbchatUserInfo> regist(UserRegistReqBO registReqBO);

}
