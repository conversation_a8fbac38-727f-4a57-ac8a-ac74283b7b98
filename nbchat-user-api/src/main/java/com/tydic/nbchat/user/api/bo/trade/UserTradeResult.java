package com.tydic.nbchat.user.api.bo.trade;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class UserTradeResult implements Serializable {
    private UserBalanceBO balance;
    private String tenantCode;
    private String userId;
    private Integer score;
    private String type;
    private String remark;
    private String bizId;
    private String bizCode;
    private String bizName;
    private String payType;
    private String tradeId;
    private Date tradeTime;
    private Integer amount;
    private Integer price;
}
