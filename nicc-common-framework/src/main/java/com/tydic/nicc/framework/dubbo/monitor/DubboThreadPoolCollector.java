package com.tydic.nicc.framework.dubbo.monitor;

import org.apache.dubbo.common.URL;
import org.apache.dubbo.common.extension.ExtensionLoader;
import org.apache.dubbo.common.threadpool.manager.ExecutorRepository;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

public class DubboThreadPoolCollector {
    /**
     * 获取Dubbo的线程池
     *
     * @return
     */
    public static ThreadPoolExecutor getDubboThreadPoolExecutor(int port) {
        //dubbo线程池数量监控
        ExecutorRepository executorRepository = ExtensionLoader.getExtensionLoader(ExecutorRepository.class).getDefaultExtension();
        URL url = new URL(null, null, port);
        ExecutorService executor = executorRepository.getExecutor(url);
        if (null == executor) return null;
        return (ThreadPoolExecutor) executor;
    }
}
