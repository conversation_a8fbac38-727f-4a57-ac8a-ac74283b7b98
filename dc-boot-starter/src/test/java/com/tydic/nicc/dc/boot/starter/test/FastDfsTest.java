package com.tydic.nicc.dc.boot.starter.test;

import com.tydic.nicc.dc.boot.starter.fastdfs.FastdfsConfigProperties;
import com.tydic.nicc.dc.boot.starter.fastdfs.FastdfsHelper;
import com.ykrenz.fastdfs.event.UploadProgressListener;
import com.ykrenz.fastdfs.model.DownloadFileRequest;
import com.ykrenz.fastdfs.model.UploadFileRequest;
import com.ykrenz.fastdfs.model.fdfs.StorePath;
import com.ykrenz.fastdfs.model.proto.storage.DownloadCallback;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;

/**
 * @Classname FastDfsTest
 * @Description FastDfsTest
 * @Date 2022/11/8 18:07
 * @Created by kangkang
 */
@Slf4j
public class FastDfsTest {

    private FastdfsHelper createFdfsHelper(){
        FastdfsConfigProperties fastdfsConfigProperties = new FastdfsConfigProperties();
        fastdfsConfigProperties.setDefaultGroup("group0");
        fastdfsConfigProperties.setTrackerServers(Arrays.asList("**************:22122"));
        fastdfsConfigProperties.getHttp().setWebServers(Arrays.asList("**************:8888"));
        fastdfsConfigProperties.getHttp().setUrlHaveGroup(true);
        FastdfsHelper fastdfsHelper = new FastdfsHelper(fastdfsConfigProperties);
        return fastdfsHelper;
    }

    @Test
    public void testDownloadProcess(){
        FastdfsHelper fastdfsHelper = createFdfsHelper();
        fastdfsHelper.downloadFile2Local("M00/00/00/wKgKmWNsv9eABex_B_X71LVAZaE958.jar","/tmp/nicc-im.jar");
        try {
            Thread.sleep(1000000L);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testUploadProcess(){
        FastdfsHelper fastdfsHelper = createFdfsHelper();
        UploadFileRequest request = UploadFileRequest.builder().file(new File("/Users/<USER>/fsdownload/nicc-im.jar")).listener(new UploadProgressListener() {
            @Override
            public void start() {
                System.out.println("开始上传...文件总大小" + totalBytes);
            }
            @Override
            public void uploading() {
                System.out.println("上传中 上传进度为" + percent());
            }
            @Override
            public void completed() {
                System.out.println("上传完成...");
            }
            @Override
            public void failed() {
                System.out.println("上传失败...已经上传的字节数" + bytesWritten);
            }
        }).build();
        fastdfsHelper.upload(request);
    }

    @Test
    public void testUploadAndAccess(){
        FastdfsConfigProperties fastdfsConfigProperties = new FastdfsConfigProperties();
        fastdfsConfigProperties.setDefaultGroup("group0");
        fastdfsConfigProperties.setTrackerServers(Arrays.asList("**************:22122"));
        fastdfsConfigProperties.getHttp().setWebServers(Arrays.asList("**************:8888"));
        fastdfsConfigProperties.getHttp().setUrlHaveGroup(true);
        FastdfsHelper fastdfsHelper = new FastdfsHelper(fastdfsConfigProperties);
        //InputStream stream = fastdfsHelper.downloadFile("M00/00/00/wKgKmWNqL-qATCnkAAAAAAAAAAA7130.sh");
        //fastdfsHelper.deleteFile("M00/00/00/wKgKmWNqMoCAUPiJAAAY0nswAkM1289.sh");
       /* StorePath storePath = fastdfsHelper.upload(new File("/Users/<USER>/fsdownload/robot.png"));
        log.info("upload - storePath:{}",storePath);*/
        //获取访问路径
        String url = fastdfsHelper.getAccessUrl(fastdfsConfigProperties.getDefaultGroup(),"M00/00/00/wKgKmWNsXTSAeaphAASYAOV1yOs532.png");
        log.info("accessUrl - url:{}",url);
        //获取下载路径
        String downloadUrl = fastdfsHelper.getDownloadUrl("M00/00/00/wKgKmWNsXTSAeaphAASYAOV1yOs532.png","robot.png");
        log.info("downloadUrl - url:{}",downloadUrl);
        //fastdfsHelper.downloadFile2Local("M00/00/00/wKgKmWNrWQGAM_72AASYAOV1yOs980.png","/Users/<USER>/fsdownload/robot_1.png");
        fastdfsHelper.getFastDfs().shutdown();
    }


}
