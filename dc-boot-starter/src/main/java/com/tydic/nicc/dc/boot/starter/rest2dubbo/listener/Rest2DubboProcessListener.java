package com.tydic.nicc.dc.boot.starter.rest2dubbo.listener;

import com.tydic.nicc.dc.boot.starter.config.dubbo.Rest2DubboApiConfigBean;
import com.tydic.nicc.dc.boot.starter.config.dubbo.Rest2DubboServiceConfig;
import com.tydic.nicc.dc.boot.starter.rest2dubbo.DubboApiFactory;
import com.tydic.nicc.dc.boot.starter.rest2dubbo.bo.GenericInvokeBO;

import javax.servlet.http.HttpServletRequest;

/**
 * @Classname Rest2DubboProxyListener
 * @Description Rest2Dubbo代理服务监听器
 * @Date 2021/5/14 4:01 下午
 * @Created by kangkang
 */
public interface Rest2DubboProcessListener {

    /**
     * 优先级-越小越先执行
     * @return
     */
    int order();

    /**
     * 调用之前
     * @param request
     * @param apiConfigBean 配置信息
     */
    void onBefore(HttpServletRequest request, Rest2DubboApiConfigBean apiConfigBean);

    /**
     * 调用之后出发
     * @param invokeBO 泛化调用参数
     * @param serviceConfig 泛化服务信息
     * @param apiFactory dubboApiFactory
     */
    void onAfter(GenericInvokeBO invokeBO,Rest2DubboServiceConfig serviceConfig, DubboApiFactory apiFactory);

    /**
     *
     * @param code 错误码
     * @param reason 错误原因
     * @param serviceConfig 泛化服务信息
     * @param apiFactory dubboApiFactory
     */
    void onError(Integer code, String reason, Rest2DubboServiceConfig serviceConfig, DubboApiFactory apiFactory);
}
