package com.tydic.nicc.dc.boot.starter.condition;

import org.springframework.context.annotation.Conditional;

import java.lang.annotation.*;

/**
 * <AUTHOR> <br>
 * @Description: DcDubboOnCondition  <br>
 * @date 2020/2/27 3:12 下午  <br>
 * @Copyright tydic.com
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
@Conditional(DubboOnPropertyCondition.class)
public @interface DcDubboOnCondition {

    String name() default "nicc-dc-config.dubbo-provider.enable";

    String[] havingValue() default {"true"};

}
