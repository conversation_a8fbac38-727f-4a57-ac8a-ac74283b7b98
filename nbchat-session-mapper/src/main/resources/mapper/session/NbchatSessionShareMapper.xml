<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.session.mapper.NbchatSessionShareMapper">

    <resultMap type="com.tydic.nbchat.session.mapper.po.NbchatSessionSharePO" id="NbchatSessionShareMap">
        <result property="sessionShareId" column="session_share_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="shareKey" column="share_key" jdbcType="VARCHAR"/>
        <result property="expiredDate" column="expired_date" jdbcType="TIMESTAMP"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="shareUrl" column="share_url" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        session_share_id, tenant_code, user_id, session_id, share_key, expired_date, created_at, share_url, is_valid</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatSessionShareMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_session_share
        where session_share_id = #{sessionShareId} and expired_date > now()
    </select>

    <select id="queryByCondition" resultMap="NbchatSessionShareMap">
        select
        <include refid="Base_Column_List" />
        from nbchat_session_share
        where session_share_id = #{sessionShareId} and expired_date > now()
        <if test="shareKey != null and shareKey != ''">
            and share_key = #{shareKey}
        </if>
    </select>
    
    
    <select id="selectAll" resultMap="NbchatSessionShareMap" parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionSharePO">
        select
          <include refid="Base_Column_List" />
        from nbchat_session_share
         <where>
            <if test="sessionShareId != null and sessionShareId != ''">
                and session_share_id = #{sessionShareId}
            </if>
            <!--<if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>-->
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="sessionId != null and sessionId != ''">
                and session_id = #{sessionId}
            </if>
            <if test="shareKey != null and shareKey != ''">
                and share_key = #{shareKey}
            </if>
            <if test="expiredDate != null">
                and expired_date = #{expiredDate}
            </if>
            <if test="createdAt != null">
                and created_at = #{createdAt}
            </if>
            <if test="shareUrl != null and shareUrl != ''">
                and share_url = #{shareUrl}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="session_share_id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionSharePO">
        insert into nbchat_session_share
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="sessionShareId != null and sessionShareId != ''">
                session_share_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id,
            </if>
            <if test="shareKey != null and shareKey != ''">
                share_key,
            </if>
            <if test="expiredDate != null">
                expired_date,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="shareUrl != null and shareUrl != ''">
                share_url,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="sessionShareId != null and sessionShareId != ''">
                #{sessionShareId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="sessionId != null and sessionId != ''">
                #{sessionId},
            </if>
            <if test="shareKey != null and shareKey != ''">
                #{shareKey},
            </if>
            <if test="expiredDate != null">
                #{expiredDate},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="shareUrl != null and shareUrl != ''">
                #{shareUrl},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="" useGeneratedKeys="true">
        insert into nbchat_session_share(session_share_id, tenant_code, user_id, session_id, share_key, expired_date, created_at, share_url, is_valid)
        values (#{sessionShareId}, #{tenantCode}, #{userId}, #{sessionId}, #{shareKey}, #{expiredDate}, #{createdAt}, #{shareUrl}, #{isValid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_session_share
        <set>
            <if test="sessionShareId != null and sessionShareId != ''">
                session_share_id = #{sessionShareId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id = #{sessionId},
            </if>
            <if test="shareKey != null and shareKey != ''">
                share_key = #{shareKey},
            </if>
            <if test="expiredDate != null">
                expired_date = #{expiredDate},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="shareUrl != null and shareUrl != ''">
                share_url = #{shareUrl},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
        </set>
        where  session_share_id = #{sessionShareId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_session_share where session_share_id = #{sessionShareId}
    </delete>

</mapper>

