<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.session.mapper.ReportMapper">



   <select id="countSessionIndex" resultType="com.tydic.nbchat.session.mapper.po.ReportResultPO">
       SELECT
           DATE(date_time) AS sdate,
           COUNT(request_id) msgCnt ,
           COUNT(distinct user_id) activeUserCnt ,
           ROUND(COUNT(request_id) / count(distinct session_id),2) avgInteractCnt
       FROM nbchat_research_msg
       WHERE
        date_time between #{startTime} and #{endTime}
       AND user_type = 0
       AND app_id = #{appId}
       AND session_id not LIKE 'test-%'
       GROUP BY DATE(date_time)
       ORDER BY DATE(date_time);
   </select>
   

</mapper>

