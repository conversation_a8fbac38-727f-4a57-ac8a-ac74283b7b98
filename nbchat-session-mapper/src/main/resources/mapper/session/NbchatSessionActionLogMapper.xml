<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.session.mapper.NbchatSessionActionLogMapper">

    <resultMap type="com.tydic.nbchat.session.mapper.po.NbchatSessionActionLogPO" id="NbchatSessionActionLogMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="shareId" column="share_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="actionType" column="action_type" jdbcType="VARCHAR"/>
        <result property="actionTime" column="action_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id, tenant_code, session_id, share_id, user_id, action_type, action_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatSessionActionLogMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_session_action_log
        where id = #{id}
    </select>

    <select id="queryByCondi" resultMap="NbchatSessionActionLogMap">
        select
        <include refid="Base_Column_List" />
        from nbchat_session_action_log
        where share_id = #{shareId} and user_id = #{userId} limit 1
    </select>
    
    
    <select id="selectAll" resultMap="NbchatSessionActionLogMap" parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionActionLogPO">
        select
          <include refid="Base_Column_List" />
        from nbchat_session_action_log
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="sessionId != null and sessionId != ''">
                and session_id = #{sessionId}
            </if>
            <if test="shareId != null and shareId != ''">
                and share_id = #{shareId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="actionType != null and actionType != ''">
                and action_type = #{actionType}
            </if>
            <if test="actionTime != null">
                and action_time = #{actionTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionActionLogPO">
        insert into nbchat_session_action_log
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id,
            </if>
            <if test="shareId != null and shareId != ''">
                share_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="actionType != null and actionType != ''">
                action_type,
            </if>
            <if test="actionTime != null">
                action_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="sessionId != null and sessionId != ''">
                #{sessionId},
            </if>
            <if test="shareId != null and shareId != ''">
                #{shareId},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="actionType != null and actionType != ''">
                #{actionType},
            </if>
            <if test="actionTime != null">
                #{actionTime},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_session_action_log(tenant_code, session_id, share_id, user_id, action_type, action_time)
        values (#{tenantCode}, #{sessionId}, #{shareId}, #{userId}, #{actionType}, #{actionTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_session_action_log
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id = #{sessionId},
            </if>
            <if test="shareId != null and shareId != ''">
                share_id = #{shareId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="actionType != null and actionType != ''">
                action_type = #{actionType},
            </if>
            <if test="actionTime != null">
                action_time = #{actionTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_session_action_log where id = #{id}
    </delete>

</mapper>

