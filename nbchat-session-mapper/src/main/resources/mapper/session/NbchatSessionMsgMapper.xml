<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.session.mapper.NbchatSessionMsgMapper">

    <resultMap type="com.tydic.nbchat.session.mapper.po.NbchatSessionMsgPO" id="NbchatSessionMsgMap">
        <result property="requestId" column="request_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userType" column="user_type" jdbcType="VARCHAR"/>
        <result property="text" column="text" jdbcType="VARCHAR"/>
        <result property="reasoning" column="reasoning" jdbcType="VARCHAR"/>
        <result property="conversationOptions" column="conversation_options" jdbcType="VARCHAR"/>
        <result property="error" column="error" jdbcType="VARCHAR"/>
        <result property="requestOptions" column="request_options" jdbcType="VARCHAR"/>
        <result property="dateTime" column="date_time" jdbcType="TIMESTAMP"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="prompt" column="prompt" jdbcType="VARCHAR"/>
        <result property="promptRequestId" column="prompt_request_id" jdbcType="VARCHAR"/>
        <result property="robotType" column="robot_type" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="ExtMap" type="com.tydic.nbchat.session.mapper.po.NbchatSessionMsgPO" extends="NbchatSessionMsgMap">
        <result property="msgCount" column="msg_count" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        request_id
        , tenant_code, session_id, user_id, user_type, text, reasoning, conversation_options, error, request_options, date_time
            ,is_valid,prompt,prompt_request_id,robot_type
    </sql>


    <!--查询单个-->
    <select id="queryById" resultMap="NbchatSessionMsgMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_session_msg
        where request_id = #{requestId}
    </select>

    <select id="queryAppSessionMsg" resultMap="ExtMap">
        SELECT
            date_time,user_id,session_id,text,robot_type,
            COUNT(user_id) as 'msg_count'
        FROM nbchat_research_msg
        WHERE app_id = #{appId}
          and user_type = '1' and session_id not like 'test-%'
        <if test="keyword != '' and keyword != null">
            and instr(text,#{keyword}) > 0
        </if>
          <if test="startTime != '' and endTime != '' and startTime != null and endTime != null">
              and date_time between #{startTime} and #{endTime}
          </if>
        GROUP BY session_id
        order by date_time desc
    </select>

    <select id="selectByCondition"
            resultMap="NbchatSessionMsgMap"
            parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionMsgSelectCondition">
        select
        <include refid="Base_Column_List"/>
        from nbchat_session_msg
        <where>
            <if test="requestId != null and requestId != ''">
                and request_id = #{requestId}
            </if>
          <!--  <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>-->
            <if test="sessionId != null and sessionId != ''">
                and session_id = #{sessionId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="userType != null and userType != ''">
                and user_type = #{userType}
            </if>
            <if test="error != null and error != ''">
                and error = #{error}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="lastId != null and lastId != ''">
                and date_time &lt;
                (  select date_time from nbchat_session_msg where request_id = #{lastId} )
            </if>
            <if test="dateTime != null">
                and date_time &lt; #{dateTime}
            </if>
        </where>
        <if test="orderTimeDesc == false">
            order by date_time
        </if>
        <if test="orderTimeDesc == true">
            order by date_time desc
        </if>
    </select>


    <insert id="insertSelective" keyProperty="" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionMsgPO">
        insert into nbchat_session_msg
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="requestId != null and requestId != ''">
                request_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="userType != null and userType != ''">
                user_type,
            </if>
            <if test="text != null and text != ''">
                text,
            </if>
            <if test="conversationOptions != null and conversationOptions != ''">
                conversation_options,
            </if>
            <if test="error != null and error != ''">
                error,
            </if>
            <if test="requestOptions != null and requestOptions != ''">
                request_options,
            </if>
            <if test="dateTime != null">
                date_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="requestId != null and requestId != ''">
                #{requestId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="sessionId != null and sessionId != ''">
                #{sessionId},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="userType != null and userType != ''">
                #{userType},
            </if>
            <if test="text != null and text != ''">
                #{text},
            </if>
            <if test="conversationOptions != null and conversationOptions != ''">
                #{conversationOptions},
            </if>
            <if test="error != null and error != ''">
                #{error},
            </if>
            <if test="requestOptions != null and requestOptions != ''">
                #{requestOptions},
            </if>
            <if test="dateTime != null">
                #{dateTime},
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_session_msg
        <set>
            <if test="requestId != null and requestId != ''">
                request_id = #{requestId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="sessionId != null and sessionId != ''">
                session_id = #{sessionId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="userType != null and userType != ''">
                user_type = #{userType},
            </if>
            <if test="text != null and text != ''">
                text = #{text},
            </if>
            <if test="conversationOptions != null and conversationOptions != ''">
                conversation_options = #{conversationOptions},
            </if>
            <if test="error != null and error != ''">
                error = #{error},
            </if>
            <if test="requestOptions != null and requestOptions != ''">
                request_options = #{requestOptions},
            </if>
            <if test="dateTime != null">
                date_time = #{dateTime},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
        </set>
        where request_id = #{requestId} and session_id = #{sessionId}
    </update>

    <update id="updateBySessionId">
        update nbchat_session_msg
        <set>
            <if test="requestId != null and requestId != ''">
                request_id = #{requestId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="userType != null and userType != ''">
                user_type = #{userType},
            </if>
            <if test="text != null and text != ''">
                text = #{text},
            </if>
            <if test="conversationOptions != null and conversationOptions != ''">
                conversation_options = #{conversationOptions},
            </if>
            <if test="error != null and error != ''">
                error = #{error},
            </if>
            <if test="requestOptions != null and requestOptions != ''">
                request_options = #{requestOptions},
            </if>
            <if test="dateTime != null">
                date_time = #{dateTime},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
        </set>
        where session_id = #{sessionId}
    </update>


</mapper>

