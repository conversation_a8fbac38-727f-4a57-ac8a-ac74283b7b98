<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.session.mapper.NbchatSessionShareRecordMapper">

    <resultMap type="com.tydic.nbchat.session.mapper.po.NbchatSessionShareRecordPO" id="NbchatSessionShareRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="sessionShareId" column="session_share_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="dateTime" column="date_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id, session_share_id, user_id, tenant_code, date_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatSessionShareRecordMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_session_share_record
        where id = #{id}
    </select>


    <select id="queryByShareIdAndUserId" resultMap="NbchatSessionShareRecordMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_session_share_record
        where session_share_id = #{sessionShareId} and user_id = #{userId}
        limit 1
    </select>


    
    <select id="selectAll" resultMap="NbchatSessionShareRecordMap" parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionShareRecordPO">
        select
          <include refid="Base_Column_List" />
        from nbchat_session_share_record
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="sessionShareId != null and sessionShareId != ''">
                and session_share_id = #{sessionShareId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="dateTime != null">
                and date_time = #{dateTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionShareRecordPO">
        insert into nbchat_session_share_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="sessionShareId != null and sessionShareId != ''">
                session_share_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="dateTime != null">
                date_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="sessionShareId != null and sessionShareId != ''">
                #{sessionShareId},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="dateTime != null">
                #{dateTime},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_session_share_record(session_share_id, user_id, tenant_code, date_time)
        values (#{sessionShareId}, #{userId}, #{tenantCode}, #{dateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_session_share_record
        <set>
            <if test="sessionShareId != null and sessionShareId != ''">
                session_share_id = #{sessionShareId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="dateTime != null">
                date_time = #{dateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_session_share_record where id = #{id}
    </delete>

</mapper>

