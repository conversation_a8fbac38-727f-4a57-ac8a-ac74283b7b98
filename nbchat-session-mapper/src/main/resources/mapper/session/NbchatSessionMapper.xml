<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.session.mapper.NbchatSessionMapper">

    <resultMap type="com.tydic.nbchat.session.mapper.po.NbchatSessionPO" id="NbchatSessionMap">
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="sessionName" column="session_name" jdbcType="VARCHAR"/>
        <result property="categoryId" column="category_id" jdbcType="VARCHAR"/>
        <result property="createdAt" column="created_at" jdbcType="TIMESTAMP"/>
        <result property="lastTime" column="last_time" jdbcType="TIMESTAMP"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="source" column="source" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="robotType" column="robot_type" jdbcType="VARCHAR"/>
        <result property="isStar" column="is_star" jdbcType="VARCHAR"/>
        <result property="robotSession" column="robot_session" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="fromSessionId" column="from_session_id" jdbcType="VARCHAR"/>
        <result property="sessionType" column="session_type" jdbcType="VARCHAR"/>
        <result property="chatTurn" column="chat_turn" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        session_id
        , tenant_code, session_name, category_id, created_at, last_time, user_id, `source`,chat_turn,
            app_id, robot_type, is_star, robot_session, is_valid,from_session_id,session_type</sql>

    <select id="querySessionName" resultMap="NbchatSessionMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM nbchat_session
        WHERE session_id in
        <foreach collection="sessionIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectLastByFromSession" resultMap="NbchatSessionMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_session
        where user_id = #{userId}
        and from_session_id = #{sessionId}
        and is_valid = 1
        order by created_at desc limit 1
    </select>

    <!--查询单个-->
    <select id="selectById" resultMap="NbchatSessionMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_session
        where session_id = #{sessionId} and is_valid = 1
    </select>

    <select id="selectSession" resultMap="NbchatSessionMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_session
        where from_session_id = #{fromSessionId}
        and is_valid = 1 and source = 2
        and user_id = #{userId}
    </select>


    <select id="selectByCondition"
            resultMap="NbchatSessionMap"
            parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionSelectCondition">
        select
        <include refid="Base_Column_List"/>
        from nbchat_session
        <where>
            <if test="sessionId != null and sessionId != ''">
                and session_id = #{sessionId}
            </if>
            <if test="fromSessionId != null and fromSessionId != ''">
                and from_session_id = #{fromSessionId}
            </if>
            <!--<if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>-->
            <if test="sessionName != null and sessionName != ''">
                and instr(session_name,#{sessionName}) > 0
            </if>
            <if test="categoryId != null and categoryId != ''">
                and category_id = #{categoryId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="robotType != null and robotType != ''">
                and robot_type = #{robotType}
            </if>
            <if test="appId != null and appId != ''">
                and app_id = #{appId}
            </if>
            <if test="isStar != null and isStar != ''">
                and is_star = #{isStar}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="sessionType != null and sessionType != ''">
                and session_type = #{sessionType}
            </if>
        </where>
        order by created_at desc
    </select>


    <insert id="insertSelective" keyProperty="session_id" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.session.mapper.po.NbchatSessionPO">
        insert into nbchat_session
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sessionId != null and sessionId != ''">
                session_id,
            </if>
            <if test="fromSessionId != null and fromSessionId != ''">
                from_session_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="sessionName != null and sessionName != ''">
                session_name,
            </if>
            <if test="categoryId != null and categoryId != ''">
                category_id,
            </if>
            <if test="createdAt != null">
                created_at,
            </if>
            <if test="lastTime != null">
                last_time,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="source != null and source != ''">
                `source`,
            </if>
            <if test="appId != null and appId != ''">
                app_id,
            </if>
            <if test="robotType != null and robotType != ''">
                robot_type,
            </if>
            <if test="isStar != null and isStar != ''">
                is_star,
            </if>
            <if test="robotSession != null and robotSession != ''">
                robot_session,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
            <if test="sessionType != null and sessionType != ''">
                session_type,
            </if>
            <if test="chatTurn != null">
                chat_turn,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sessionId != null and sessionId != ''">
                #{sessionId},
            </if>
            <if test="fromSessionId != null and fromSessionId != ''">
                #{fromSessionId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="sessionName != null and sessionName != ''">
                #{sessionName},
            </if>
            <if test="categoryId != null and categoryId != ''">
                #{categoryId},
            </if>
            <if test="createdAt != null">
                #{createdAt},
            </if>
            <if test="lastTime != null">
                #{lastTime},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="source != null and source != ''">
                #{source},
            </if>
            <if test="appId != null and appId != ''">
                #{appId},
            </if>
            <if test="robotType != null and robotType != ''">
                #{robotType},
            </if>
            <if test="isStar != null and isStar != ''">
                #{isStar},
            </if>
            <if test="robotSession != null and robotSession != ''">
                #{robotSession},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
            <if test="sessionType != null and sessionType != ''">
                #{sessionType},
            </if>
            <if test="chatTurn != null">
                #{chatTurn},
            </if>
        </trim>
    </insert>


    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_session
        <set>
            <if test="sessionId != null and sessionId != ''">
                session_id = #{sessionId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="sessionName != null and sessionName != ''">
                session_name = #{sessionName},
            </if>
            <if test="categoryId != null and categoryId != ''">
                category_id = #{categoryId},
            </if>
            <if test="createdAt != null">
                created_at = #{createdAt},
            </if>
            <if test="lastTime != null">
                last_time = #{lastTime},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="source != null and source != ''">
                source = #{source},
            </if>
            <if test="appId != null and appId != ''">
                app_id = #{appId},
            </if>
            <if test="robotType != null and robotType != ''">
                robot_type = #{robotType},
            </if>
            <if test="isStar != null and isStar != ''">
                is_star = #{isStar},
            </if>
            <if test="robotSession != null and robotSession != ''">
                robot_session = #{robotSession},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
            <if test="chatTurn != null">
                chat_turn = #{chatTurn},
            </if>
        </set>
        where session_id = #{sessionId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from nbchat_session
        where session_id = #{sessionId}
    </delete>

</mapper>

