<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.session.mapper.SmsTemplateAreaCodeMapper">
    
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.session.mapper.po.SmsTemplateAreaCodePO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="area_code" property="areaCode" jdbcType="VARCHAR"/>
        <result column="template_name" property="templateName" jdbcType="VARCHAR"/>
        <result column="template_id" property="templateId" jdbcType="VARCHAR"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        id, area_code, template_name, template_id, created_time, updated_time
    </sql>
    
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sms_template_area_code
    </select>
    
    <select id="selectByAreaCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sms_template_area_code
        WHERE area_code = #{areaCode,jdbcType=VARCHAR}
    </select>
    
    <select id="selectByAreaCodeAndTemplateName" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sms_template_area_code
        WHERE area_code = #{areaCode,jdbcType=VARCHAR}
        AND template_name = #{templateName,jdbcType=VARCHAR}
    </select>
    
    <insert id="insert" parameterType="com.tydic.nbchat.session.mapper.po.SmsTemplateAreaCodePO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO sms_template_area_code (
            area_code, template_name, template_id
        )
        VALUES (
            #{areaCode,jdbcType=VARCHAR},
            #{templateName,jdbcType=VARCHAR},
            #{templateId,jdbcType=VARCHAR}
        )
    </insert>
    
    <update id="update" parameterType="com.tydic.nbchat.session.mapper.po.SmsTemplateAreaCodePO">
        UPDATE sms_template_area_code
        <set>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
        </set>
        WHERE area_code = #{areaCode,jdbcType=VARCHAR}
        AND template_name = #{templateName,jdbcType=VARCHAR}
    </update>
    
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM sms_template_area_code
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>
    
    <delete id="deleteByAreaCodeAndTemplateName">
        DELETE FROM sms_template_area_code
        WHERE area_code = #{areaCode,jdbcType=VARCHAR}
        AND template_name = #{templateName,jdbcType=VARCHAR}
    </delete>
</mapper>
