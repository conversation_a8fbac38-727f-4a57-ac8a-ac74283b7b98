<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.session.mapper.SmsTemplateMapper">
    
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.session.mapper.po.SmsTemplatePO">
        <id column="id" property="id" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="content" property="content" jdbcType="VARCHAR"/>
        <result column="is_default" property="isDefault" jdbcType="BOOLEAN"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        id, name, description, content, is_default, created_time, updated_time
    </sql>
    
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sms_template
    </select>
    
    <select id="selectByName" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sms_template
        WHERE name = #{name,jdbcType=VARCHAR}
    </select>
    
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sms_template
        WHERE id = #{id,jdbcType=VARCHAR}
    </select>
    
    <insert id="insert" parameterType="com.tydic.nbchat.session.mapper.po.SmsTemplatePO">
        INSERT INTO sms_template (
            id, name, description, content, is_default
        )
        VALUES (
            #{id,jdbcType=VARCHAR},
            #{name,jdbcType=VARCHAR},
            #{description,jdbcType=VARCHAR},
            #{content,jdbcType=VARCHAR},
            #{isDefault,jdbcType=BOOLEAN}
        )
    </insert>
    
    <update id="update" parameterType="com.tydic.nbchat.session.mapper.po.SmsTemplatePO">
        UPDATE sms_template
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null">
                is_default = #{isDefault,jdbcType=BOOLEAN},
            </if>
        </set>
        WHERE id = #{id,jdbcType=VARCHAR}
    </update>
    
    <delete id="deleteById" parameterType="java.lang.String">
        DELETE FROM sms_template
        WHERE id = #{id,jdbcType=VARCHAR}
    </delete>
</mapper>
