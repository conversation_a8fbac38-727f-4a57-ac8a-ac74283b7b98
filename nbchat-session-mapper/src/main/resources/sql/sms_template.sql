-- 短信模板表
CREATE TABLE IF NOT EXISTS `sms_template` (
  `id` varchar(64) NOT NULL COMMENT '模板ID',
  `name` varchar(64) NOT NULL COMMENT '模板名称',
  `description` varchar(255) DEFAULT NULL COMMENT '模板描述',
  `content` text COMMENT '模板内容',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '是否默认模板',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='短信模板表';

-- 区号模板映射表
CREATE TABLE IF NOT EXISTS `sms_template_area_code` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `area_code` varchar(10) NOT NULL COMMENT '区号',
  `template_name` varchar(64) NOT NULL COMMENT '模板名称',
  `template_id` varchar(64) NOT NULL COMMENT '模板ID',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_area_code_template_name` (`area_code`,`template_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='区号模板映射表';

-- 初始化数据
INSERT INTO `sms_template` (`id`, `name`, `description`, `is_default`) VALUES
('SMS_148860011', 'LOGIN', '用户修改密码进行验证身份获取短信验证码或邮件验证码', 1),
('SMS_463131159', 'TENANT_APPLY', '用户申请体验租户成功短信', 1),
('SMS_470875038', 'VIP_OPEN', '会员开通模板', 1),
('SMS_485820196', 'LOGIN_INTERNATIONAL', '国际版登录验证码', 0),
('SMS_485875200', 'TENANT_APPLY_INTERNATIONAL', '国际版用户申请体验租户成功短信', 0),
('SMS_485685228', 'VIP_OPEN_INTERNATIONAL', '国际版会员开通模板', 0);

-- 初始化区号模板映射
INSERT INTO `sms_template_area_code` (`area_code`, `template_name`, `template_id`) VALUES
('852', 'LOGIN', 'SMS_485820196'),
('852', 'TENANT_APPLY', 'SMS_485875200'),
('852', 'VIP_OPEN', 'SMS_485685228'),
('886', 'LOGIN', 'SMS_485820196'),
('886', 'TENANT_APPLY', 'SMS_485875200'),
('1', 'LOGIN', 'SMS_485820196');
