package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.NbchatSessionActionLogPO;

import java.util.List;

/**
 * (NbchatSessionActionLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-30 20:07:19
 */
public interface NbchatSessionActionLogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NbchatSessionActionLogPO queryById(Long id);
    NbchatSessionActionLogPO queryByCondi(NbchatSessionActionLogPO po);

    List<NbchatSessionActionLogPO> selectAll(NbchatSessionActionLogPO nbchatSessionActionLogPO);

    /**
     * 新增数据
     *
     * @param nbchatSessionActionLogPO 实例对象
     * @return 影响行数
     */
    int insert(NbchatSessionActionLogPO nbchatSessionActionLogPO);


    int insertSelective(NbchatSessionActionLogPO nbchatSessionActionLogPO);

      /**
     * 修改数据
     *
     * @param nbchatSessionActionLogPO 实例对象
     * @return 影响行数
     */
    int update(NbchatSessionActionLogPO nbchatSessionActionLogPO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

