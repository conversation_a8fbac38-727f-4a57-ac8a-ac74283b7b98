package com.tydic.nbchat.session.mapper.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatSessionActionLog)实体类
 *
 * <AUTHOR>
 * @since 2023-03-30 20:07:19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NbchatSessionActionLogPO implements Serializable {
    private static final long serialVersionUID = -80608892506986274L;
    /**
     * 自增主键
     */
    private Long id;
    /**
     * 租户
     */
    private String tenantCode;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 共享id
     */
    private String shareId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 1 查看 2 保存  
     */
    private String actionType;
    /**
     * 时间
     */
    private Date actionTime;
}

