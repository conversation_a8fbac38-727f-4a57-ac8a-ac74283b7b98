package com.tydic.nbchat.session.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (NbchatSessionMsgSelectCondition)实体类
 *
 * <AUTHOR>
 * @since 2023-03-29 19:49:38
 */
@Data
public class NbchatSessionMsgSelectCondition implements Serializable {
    private static final long serialVersionUID = 289587688205587833L;

    private String startTime;
    private String endTime;
    private String appId;
    private String keyword;

    /**
     * 请求id
     */
    private String requestId;
    /**
     * 最近一条会话id
     */
    private String lastId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 0 机器人 1 用户
     */
    private String userType;

    /**
     * 是否异常
     */
    private String error;
    /**
     * 机器人类型
     */
    private String robotType;
    private Date dateTime;
    /**
     * 0删除 1正常
     */
    private String isValid;

    private boolean orderTimeDesc;
}

