package com.tydic.nbchat.session.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatSessionShare)实体类
 *
 * <AUTHOR>
 * @since 2023-03-29 15:25:38
 */
@Data
public class NbchatSessionSharePO implements Serializable {
    private static final long serialVersionUID = -80305677970869374L;
    /**
     * 共享id
     */
    private String sessionShareId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 密码
     */
    private String shareKey;
    /**
     * 过期时间
     */
    private Date expiredDate;
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 共享链接
     */
    private String shareUrl;
    /**
     * 是否删除
     */
    private String isValid;

}

