package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.SmsTemplateAreaCodePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 区号模板映射Mapper接口
 */
@Mapper
public interface SmsTemplateAreaCodeMapper {
    
    /**
     * 查询所有区号模板映射
     * @return 区号模板映射列表
     */
    List<SmsTemplateAreaCodePO> selectAll();
    
    /**
     * 根据区号查询模板映射
     * @param areaCode 区号
     * @return 区号模板映射列表
     */
    List<SmsTemplateAreaCodePO> selectByAreaCode(@Param("areaCode") String areaCode);
    
    /**
     * 根据区号和模板名称查询模板映射
     * @param areaCode 区号
     * @param templateName 模板名称
     * @return 区号模板映射
     */
    SmsTemplateAreaCodePO selectByAreaCodeAndTemplateName(@Param("areaCode") String areaCode, 
                                                         @Param("templateName") String templateName);
    
    /**
     * 插入区号模板映射
     * @param areaCodeTemplate 区号模板映射
     * @return 影响行数
     */
    int insert(SmsTemplateAreaCodePO areaCodeTemplate);
    
    /**
     * 更新区号模板映射
     * @param areaCodeTemplate 区号模板映射
     * @return 影响行数
     */
    int update(SmsTemplateAreaCodePO areaCodeTemplate);
    
    /**
     * 删除区号模板映射
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 根据区号和模板名称删除模板映射
     * @param areaCode 区号
     * @param templateName 模板名称
     * @return 影响行数
     */
    int deleteByAreaCodeAndTemplateName(@Param("areaCode") String areaCode, 
                                       @Param("templateName") String templateName);
}
