package com.tydic.nbchat.session.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (NbchatSession)实体类
 *
 * <AUTHOR>
 * @since 2023-03-29 15:25:08
 */
@Data
public class NbchatSessionSelectCondition implements Serializable {
    private static final long serialVersionUID = -71621972286533333L;

    private String sessionType;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 来源会话id
     */
    private String fromSessionId;
    /**
     * 会话名称
     */
    private String sessionName;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 所属分类
     */
    private String categoryId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 机器人类型
     */
    private String robotType;
    /**
     * 是否收藏 1 
     */
    private String isStar;
    /**
     * 应用id
     */
    private String appId;

    private String isValid;
}

