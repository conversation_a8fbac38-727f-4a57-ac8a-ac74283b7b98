package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.SmsTemplatePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 短信模板Mapper接口
 */
@Mapper
public interface SmsTemplateMapper {
    
    /**
     * 查询所有模板
     * @return 模板列表
     */
    List<SmsTemplatePO> selectAll();
    
    /**
     * 根据模板名称查询模板
     * @param name 模板名称
     * @return 模板信息
     */
    SmsTemplatePO selectByName(@Param("name") String name);
    
    /**
     * 根据模板ID查询模板
     * @param id 模板ID
     * @return 模板信息
     */
    SmsTemplatePO selectById(@Param("id") String id);
    
    /**
     * 插入模板
     * @param template 模板信息
     * @return 影响行数
     */
    int insert(SmsTemplatePO template);
    
    /**
     * 更新模板
     * @param template 模板信息
     * @return 影响行数
     */
    int update(SmsTemplatePO template);
    
    /**
     * 删除模板
     * @param id 模板ID
     * @return 影响行数
     */
    int deleteById(@Param("id") String id);
}
