package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.NbchatSessionPO;
import com.tydic.nbchat.session.mapper.po.NbchatSessionSelectCondition;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * (NbchatSession)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-29 15:25:07
 */
public interface NbchatSessionMapper {

    List<NbchatSessionPO> querySessionName(@Param("sessionIds")List<String> sessionIds);

    /**
     * 查询最近保存的会话
     * @param userId
     * @param sessionId
     * @return
     */
    NbchatSessionPO selectLastByFromSession(@Param("userId") String userId,
                                            @Param("sessionId") String sessionId);

    /**
     * 通过ID查询单条数据
     * @param  sessionId 主键
     * @return 实例对象
     */
    NbchatSessionPO selectById(String sessionId);

    /**
     * 条件查询
     * @param condition
     * @return
     */
    List<NbchatSessionPO> selectByCondition(NbchatSessionSelectCondition condition);

    /**
     * 查询会话
     * @param nbchatSessionPO
     * @return
     */
    NbchatSessionPO selectSession(NbchatSessionPO nbchatSessionPO);

    /**
     * 插入
     * @param nbchatSessionPO
     * @return
     */
    int insertSelective(NbchatSessionPO nbchatSessionPO);

      /**
     * 修改数据
     *
     * @param nbchatSessionPO 实例对象
     * @return 影响行数
     */
    int update(NbchatSessionPO nbchatSessionPO);

}

