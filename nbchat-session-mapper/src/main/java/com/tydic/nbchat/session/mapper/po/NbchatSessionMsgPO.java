package com.tydic.nbchat.session.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatSessionMsg)实体类
 *
 * <AUTHOR>
 * @since 2023-03-29 19:49:38
 */
@Data
public class NbchatSessionMsgPO implements Serializable {
    private static final long serialVersionUID = 289587688205587833L;

    /**
     * 用户提问id
     */
    private String promptRequestId;
    /**
     * 用户提问：机器人消息上携带
     */
    private String prompt;
    /**
     * 0删除 1正常
     */
    private String isValid;
    /**
     * 请求id
     */
    private String requestId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 0 机器人 1 用户
     */
    private String userType;
    /**
     * 会话文本
     */
    private String text;

    private String reasoning;
    
    private String conversationOptions;
    /**
     * 是否异常
     */
    private String error;
    
    private String requestOptions;
    /**
     * 消息时间
     */
    private Date dateTime;

    private String robotType;
    private String appId;
    private Integer msgCount;


}

