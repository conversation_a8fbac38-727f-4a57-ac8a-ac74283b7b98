package com.tydic.nbchat.session.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatSession)实体类
 *
 * <AUTHOR>
 * @since 2023-03-29 15:25:08
 */
@Data
public class NbchatSessionPO implements Serializable {
    private static final long serialVersionUID = -71621972286533333L;
    private String sessionType;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 会话名称
     */
    private String sessionName;
    /**
     * 所属分类
     */
    private String categoryId;
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 上次活动时间
     */
    private Date lastTime;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 1:自有 2:共享
     */
    private String source;
    /**
     * 机器人id
     */
    private String appId;
    /**
     * 机器人类型
     */
    private String robotType;
    /**
     * 是否收藏 1 
     */
    private String isStar;
    /**
     * 关联机器人session
     */
    private String robotSession;
    /**
     * 0 已删除 1 正常
     */
    private String isValid;
    /**
     * 保存的分享会话id
     */
    private String fromSessionId;
    /**
     * 是否多轮对话
     */
    private Integer chatTurn;


}

