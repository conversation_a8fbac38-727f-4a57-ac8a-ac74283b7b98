package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.AiToolApp;

import java.util.List;

/**
 * ai应用配置表(AiToolApp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-11 11:30:01
 */
public interface AiToolAppMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param appId 主键
     * @return 实例对象
     */
    AiToolApp queryById(String appId);

    List<AiToolApp> selectAll(AiToolApp aiToolApp);

    /**
     * 新增数据
     *
     * @param aiToolApp 实例对象
     * @return 影响行数
     */
    int insert(AiToolApp aiToolApp);


    int insertSelective(AiToolApp aiToolApp);

      /**
     * 修改数据
     *
     * @param aiToolApp 实例对象
     * @return 影响行数
     */
    int update(AiToolApp aiToolApp);

    /**
     * 通过主键删除数据
     *
     * @param appId 主键
     * @return 影响行数
     */
    int deleteById(String appId);

}

