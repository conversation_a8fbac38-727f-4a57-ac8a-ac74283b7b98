package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.AiToolAppRes;

import java.util.List;

/**
 * (AiToolAppRes)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-16 16:36:54
 */
public interface AiToolAppResMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AiToolAppRes queryById(Integer id);

    List<AiToolAppRes> selectAll(AiToolAppRes aiToolAppRes);
    List<AiToolAppRes> selectAllSession(AiToolAppRes aiToolAppRes);

    /**
     * 新增数据
     *
     * @param aiToolAppRes 实例对象
     * @return 影响行数
     */
    int insert(AiToolAppRes aiToolAppRes);


    int insertSelective(AiToolAppRes aiToolAppRes);

      /**
     * 修改数据
     *
     * @param aiToolAppRes 实例对象
     * @return 影响行数
     */
    int update(AiToolAppRes aiToolAppRes);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}

