package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.NbchatSessionMsgPO;
import com.tydic.nbchat.session.mapper.po.NbchatSessionMsgSelectCondition;

import java.util.List;

/**
 * (NbchatSessionMsg)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-29 19:49:38
 */
public interface NbchatSessionMsgMapper {

    /**
     * 通过ID查询单条数据
     * @param  requestId 主键
     * @return 实例对象
     */
    NbchatSessionMsgPO queryById(String requestId);

    /**
     * 条件查询
     * @param condition
     * @return
     */
    List<NbchatSessionMsgPO> selectByCondition(NbchatSessionMsgSelectCondition condition);

    /**
     * 查询app会话消息
     * @param condition
     * @return
     */
    List<NbchatSessionMsgPO> queryAppSessionMsg(NbchatSessionMsgSelectCondition condition);

    /**
     * 条件插入
     * @param nbchatSessionMsgPO
     * @return
     */
    int insertSelective(NbchatSessionMsgPO nbchatSessionMsgPO);

      /**
     * 修改数据
     *
     * @param nbchatSessionMsgPO 实例对象
     * @return 影响行数
     */
    int update(NbchatSessionMsgPO nbchatSessionMsgPO);

    /**
     * 通过sessionId更新
     * @param nbchatSessionMsgPO
     * @return
     */
    int updateBySessionId(NbchatSessionMsgPO nbchatSessionMsgPO);


}

