package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.AiToolAppUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (AiToolAppUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-11 11:30:30
 */
public interface AiToolAppUserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    AiToolAppUser queryById(Long id);
    AiToolAppUser selectBySessionId(@Param("sessionId") String sessionId);

    List<AiToolAppUser> selectAll(AiToolAppUser aiToolAppUser);

    /**
     * 新增数据
     *
     * @param aiToolAppUser 实例对象
     * @return 影响行数
     */
    int insert(AiToolAppUser aiToolAppUser);


    int insertSelective(AiToolAppUser aiToolAppUser);

      /**
     * 修改数据
     *
     * @param aiToolAppUser 实例对象
     * @return 影响行数
     */
    int update(AiToolAppUser aiToolAppUser);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

