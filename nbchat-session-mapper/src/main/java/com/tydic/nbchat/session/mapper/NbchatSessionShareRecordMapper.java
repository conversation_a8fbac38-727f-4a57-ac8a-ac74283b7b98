package com.tydic.nbchat.session.mapper;

import com.tydic.nbchat.session.mapper.po.NbchatSessionShareRecordPO;

import java.util.List;

/**
 * (NbchatSessionShareRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-30 15:11:47
 */
public interface NbchatSessionShareRecordMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NbchatSessionShareRecordPO queryById(Integer id);

    NbchatSessionShareRecordPO queryByShareIdAndUserId(NbchatSessionShareRecordPO po);

    List<NbchatSessionShareRecordPO> selectAll(NbchatSessionShareRecordPO nbchatSessionShareRecordPO);

    /**
     * 新增数据
     *
     * @param nbchatSessionShareRecordPO 实例对象
     * @return 影响行数
     */
    int insert(NbchatSessionShareRecordPO nbchatSessionShareRecordPO);


    int insertSelective(NbchatSessionShareRecordPO nbchatSessionShareRecordPO);

      /**
     * 修改数据
     *
     * @param nbchatSessionShareRecordPO 实例对象
     * @return 影响行数
     */
    int update(NbchatSessionShareRecordPO nbchatSessionShareRecordPO);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}

