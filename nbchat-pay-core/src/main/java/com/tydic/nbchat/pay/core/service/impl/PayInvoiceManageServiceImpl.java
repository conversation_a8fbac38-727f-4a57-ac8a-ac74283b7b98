package com.tydic.nbchat.pay.core.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.pay.api.PayInvoiceManageService;
import com.tydic.nbchat.pay.api.PayInvoiceService;
import com.tydic.nbchat.pay.api.PayOrderApi;
import com.tydic.nbchat.pay.api.bo.PayInvoiceCountReqBo;
import com.tydic.nbchat.pay.api.bo.PayInvoiceRecordBo;
import com.tydic.nbchat.pay.api.bo.PayPersonalInvoicePriceRspBO;
import com.tydic.nbchat.pay.api.bo.PayQueryOrderReqBO;
import com.tydic.nbchat.pay.api.bo.count.PayOrderCountBO;
import com.tydic.nbchat.pay.api.bo.emus.InvoiceStatusType;
import com.tydic.nbchat.pay.api.bo.emus.InvoiceType;
import com.tydic.nbchat.pay.api.bo.invoice.PayInvoiceRecodUpdateReqBO;
import com.tydic.nbchat.pay.api.bo.invoice.PayInvoiceReqBO;
import com.tydic.nbchat.pay.api.bo.invoice.PayInvoiceUpdateReqBO;
import com.tydic.nbchat.pay.api.bo.invoice.PayUserOrderReqBO;
import com.tydic.nbchat.pay.core.busi.PayEventSender;
import com.tydic.nbchat.pay.core.handler.PayExcelSheetTitleHandler;
import com.tydic.nbchat.pay.mapper.PayInvoiceOrderMapper;
import com.tydic.nbchat.pay.mapper.PayInvoiceRecordMapper;
import com.tydic.nbchat.pay.mapper.po.*;
import com.tydic.nbchat.user.api.UserBaseInfoApi;
import com.tydic.nbchat.user.api.bo.user.UserBaseInfo;
import com.tydic.nbchat.user.api.bo.utils.PhoneNumberUtils;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.FileManagerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 发票管理服务实现类
 */
@Service
@Slf4j
public class PayInvoiceManageServiceImpl implements PayInvoiceManageService {

    private final PayInvoiceRecordMapper payInvoiceRecordMapper;
    private final PayInvoiceService payInvoiceService;
    private final PayOrderApi payOrderApi;
    private final PayInvoiceOrderMapper payInvoiceOrderMapper;
    private final PayEventSender payEventSender;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
    private UserBaseInfoApi userBaseInfoApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
    private FileManageService fileManageService;

    public PayInvoiceManageServiceImpl(PayInvoiceRecordMapper payInvoiceRecordMapper,
                                       PayInvoiceService payInvoiceService,
                                       FileManageService fileManageService,
                                       PayOrderApi payOrderApi,
                                       PayInvoiceOrderMapper payInvoiceOrderMapper,
                                       PayEventSender payEventSender) {
        this.payInvoiceRecordMapper = payInvoiceRecordMapper;
        this.payInvoiceService = payInvoiceService;
        this.fileManageService = fileManageService;
        this.payOrderApi = payOrderApi;
        this.payInvoiceOrderMapper = payInvoiceOrderMapper;
        this.payEventSender = payEventSender;
    }

    @Override
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public RspList<PayInvoiceRecordBo> recordList(PayInvoiceCountReqBo request) {
        log.info("查询发票记录列表: {}", request);

        // 初始化查询条件
        PayInvoiceReqSelectCondition queryBo = new PayInvoiceReqSelectCondition();
        BeanUtils.copyProperties(request, queryBo);
        queryBo.setCreateStartTime(request.getCreateStartTime());
        queryBo.setCreateEndTime(request.getCreateEndTime());

        // 根据手机号查询用户id
        if (PhoneNumberUtils.validPhoneNumber(request.getPhone())) {
            Rsp rsp = userBaseInfoApi.getByPhone(null, request.getPhone());
            String userId = rsp.isSuccess() ? ((UserBaseInfo) rsp.getData()).getUserId() : null;
            queryBo.setTradeUserId(userId);
        }

        // 分页查询
        Page<PayInvoiceRecord> page = PageHelper.startPage(request.getPage(), request.getLimit());
        payInvoiceRecordMapper.selectByCondition(queryBo);

        // 转换结果
        List<PayInvoiceRecordBo> recordBoList = page.getResult().stream()
                .map(this::mapToRecordBo)
                .collect(Collectors.toList());

        return BaseRspUtils.createSuccessRspList(recordBoList, page.getTotal());
    }

    /**
     * 将 PayInvoiceRecord 转换为 PayInvoiceRecordBo 并处理相关逻辑
     */
    private PayInvoiceRecordBo mapToRecordBo(PayInvoiceRecord record) {
        PayInvoiceRecordBo bo = new PayInvoiceRecordBo();
        BeanUtils.copyProperties(record, bo);

        // 设置发票类型名称
        bo.setInvoiceType(InvoiceType.getNameByCode(record.getInvoiceType()));

        // 设置用户信息和手机号
        setUserInfoAndPhone(bo);

        // 获取发票订单信息
        List<PayInvoiceOrder> invoiceOrders = fetchInvoiceOrders(bo.getInvoiceId());

        // 获取用户订单价格信息
        updateInvoicePriceInfo(bo, invoiceOrders);

        return bo;
    }

    /**
     * 获取发票订单列表
     */
    private List<PayInvoiceOrder> fetchInvoiceOrders(String invoiceId) {
        PayInvoiceOrder payInvoiceOrder = new PayInvoiceOrder();
        payInvoiceOrder.setInvoiceId(invoiceId);
        return payInvoiceOrderMapper.selectByAll(payInvoiceOrder);
    }

    /**
     * 更新发票价格相关信息
     */
    private void updateInvoicePriceInfo(PayInvoiceRecordBo bo, List<PayInvoiceOrder> invoiceOrders) {
        PayInvoiceReqBO payInvoiceReqBO = buildPayInvoiceReqBO(bo);
        Rsp rsp = payInvoiceService.userOrderPrice(payInvoiceReqBO);

        if (rsp.isSuccess()) {
            PayPersonalInvoicePriceRspBO rspData = (PayPersonalInvoicePriceRspBO) rsp.getData();
            if (rspData != null) {
                bo.setTotalAmount(rspData.getTotalAmount());
                bo.setIssuedAmount(rspData.getIssuedAmount());
                bo.setPayOrderCountBOList(rspData.getPayOrderCountBOList());

                // 构建微信订单信息
                String wechatOrderInfo = buildWechatOrderInfo(rspData.getPayOrderCountBOList(), invoiceOrders);
                bo.setWechatOrderInfo(wechatOrderInfo);
            }
        }
    }

    /**
     * 构建 PayInvoiceReqBO
     */
    private PayInvoiceReqBO buildPayInvoiceReqBO(PayInvoiceRecordBo bo) {
        PayInvoiceReqBO payInvoiceReqBO = new PayInvoiceReqBO();
        BeanUtils.copyProperties(bo, payInvoiceReqBO);
        payInvoiceReqBO.setUserId(bo.getUserId());
        payInvoiceReqBO.setInvoiceType(bo.getInvoiceType());
        payInvoiceReqBO.setInvoiceTitle(bo.getInvoiceTitle());
        payInvoiceReqBO.setIsCommonUser(false);
        return payInvoiceReqBO;
    }

    /**
     * 构建微信订单信息
     */
    private String buildWechatOrderInfo(List<PayOrderCountBO> payOrderCountBOList, List<PayInvoiceOrder> invoiceOrders) {
        if (payOrderCountBOList == null || payOrderCountBOList.isEmpty()) {
            return "";
        }
        return payOrderCountBOList.stream()
                .map(payOrderCountBO -> {
                    String wechatOrderId = payOrderCountBO.getOrderNo();
                    double payPrice = payOrderCountBO.getPayPrice().doubleValue() / 100.0;

                    return invoiceOrders.stream()
                            .filter(order -> wechatOrderId.equals(order.getOrderNo()))
                            .findFirst()
                            .map(order -> String.format("订单编号: %s; 订单金额: %.2f", wechatOrderId, payPrice))
                            .orElse(null);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.joining("\n"));
    }

    @Override
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public RspList<PayOrderCountBO> queryUserOrder(PayUserOrderReqBO request) {
        log.info("查询用户订单: {}", request);
        PayQueryOrderReqBO reqBO = new PayQueryOrderReqBO();
        BeanUtils.copyProperties(request, reqBO);
        reqBO.setUserId(request.getTargetUserId());
        reqBO.setTenantCode(request.getTargetTenantCode());
        return payOrderApi.queryMyOrder(reqBO);
    }

    @Override
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp recordUpdate(PayInvoiceUpdateReqBO request) {
        log.info("更新发票记录: {}", request);
        PayInvoiceRecord record = new PayInvoiceRecord();
        record.setInvoiceStatus(request.getInvoiceStatus());
        record.setInvoiceId(request.getInvoiceId());
        record.setUpdateTime(new Date());
        int update = payInvoiceRecordMapper.updateByPrimaryKeySelective(record);
        if (update > 0) {
            // 发送用户维度报表事件
            PayInvoiceRecord data = payInvoiceRecordMapper.selectOneByInvoiceId(request.getInvoiceId());
            payEventSender.sendUserRpEventByPayInvoice(data);
            return BaseRspUtils.createSuccessRsp("发票状态更新成功");
        }
        return BaseRspUtils.createErrorRsp("更新失败");
    }

    @Override
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp batchRecordUpdate(PayInvoiceRecodUpdateReqBO request) {
        log.info("批量更新发票状态: {}", request);
        String newInvoiceStatus = request.getInvoiceStatus();
        List<String> invoiceIdList = request.getInvoiceId();
        List<PayInvoiceRecord> recordsToUpdate = new ArrayList<>();
        for (String invoiceId : invoiceIdList) {
            PayInvoiceRecord record = new PayInvoiceRecord();
            record.setInvoiceId(invoiceId);
            record.setInvoiceStatus(newInvoiceStatus);
            record.setUpdateTime(new Date());
            recordsToUpdate.add(record);
        }
        int updateCount = payInvoiceRecordMapper.updateBatchSelective(recordsToUpdate);
        if (updateCount > 0) {
            // 发送用户维度报表事件
            List<PayInvoiceRecord> records = payInvoiceRecordMapper.selectAllByInvoiceIdIn(invoiceIdList);
            records.forEach(payEventSender::sendUserRpEventByPayInvoice);
            return BaseRspUtils.createSuccessRsp("批量更新发票状态成功");
        } else {
            return BaseRspUtils.createErrorRsp("批量更新发票状态失败");
        }
    }

    private void setUserInfoAndPhone(PayInvoiceRecordBo bo) {
        try {
            Rsp<UserBaseInfo> userBaseInfoRsp = userBaseInfoApi.getByUserId(bo.getTenantCode(), bo.getUserId());
            if (userBaseInfoRsp.isSuccess()) {
                UserBaseInfo userBaseInfo = userBaseInfoRsp.getData();
                if (userBaseInfo != null) {
                    bo.setPhone(userBaseInfo.getPhone());
                }
            }
        } catch (Exception e) {
            log.error("查询用户信息失败: {}", e.getMessage());
        }
    }

    @Override
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp exportPayDataList(PayInvoiceCountReqBo queryReqBO) {
        queryReqBO.setLimit(999999999);
        log.info("导出发票记录列表: {}", queryReqBO);
        RspList<PayInvoiceRecordBo> rspList = recordList(queryReqBO);
        if (!rspList.isSuccess() || CollectionUtils.isEmpty(rspList.getRows())) {
            return BaseRspUtils.createErrorRsp("查询发票记录列表失败");
        }
        List<PayInvoiceRecordBo> filteredRows;
        if (CollectionUtils.isEmpty(queryReqBO.getInvokerList())) {
            // 如果没有勾选任何发票ID，则导出所有查询到的记录
            filteredRows = rspList.getRows();
        } else {
            // 否则，导出勾选的发票记录
            filteredRows = rspList.getRows().stream()
                    .filter(rsp -> queryReqBO.getInvokerList().contains(rsp.getInvoiceId()))
                    .collect(Collectors.toList());
        }
        List<PayInvoiceReqExportPo> list = new ArrayList<>();
        filteredRows.forEach(rsp -> {
            PayInvoiceReqExportPo po = new PayInvoiceReqExportPo();
            BeanUtils.copyProperties(rsp, po);
            po.setIndex(list.size() + 1);
            list.add(po);
            po.setInvoiceStatus(InvoiceStatusType.InvoiceStatusType(rsp.getInvoiceStatus()));
        });
        String fileName = PayIncoiceManageConstans.NO_SCORE_VALUE + new SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒").format(new Date()) + ".xlsx";
        String tempPath = System.getProperty("java.io.tmpdir");
        File dirFile = new File(tempPath + "/" + fileName);
        // 创建表头
        List<List<String>> head = createHead();
        // 注册表头处理器
        EasyExcel.write(dirFile)
                .head(head)
                .sheet(PayIncoiceManageConstans.NO_SCORE_VALUE)
                .registerWriteHandler(new PayExcelSheetTitleHandler(PayIncoiceManageConstans.SHEET1_COLUMN_NUMBER, PayIncoiceManageConstans.TITLE_PAY_VALUE))
                .useDefaultStyle(true).relativeHeadRowIndex(1)
                .doWrite(list);
        log.info("发票-导出:{}|{}", dirFile.exists(), dirFile.getAbsolutePath());
        MultipartFile multipartFile = FileManagerHelper.parseToMultipartFile(dirFile);
        FileUploadRequest uploadRequest = new FileUploadRequest();
        uploadRequest.setTenantCode(queryReqBO.getTenantCode());
        uploadRequest.setUploadUser(queryReqBO.getUserId());
        uploadRequest.setFileName(dirFile.getName());
        try {
            uploadRequest.setFile(multipartFile.getBytes());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        uploadRequest.setUseOriginalName(true);
        RspList<FileManageSaveBO> fileManageSaveBOS = fileManageService.fileUploadRequest(uploadRequest);
        log.info("导出文件上传完毕，信息：{}", JSON.toJSONString(fileManageSaveBOS));
        if (!fileManageSaveBOS.isSuccess()) {
            log.error("文件上传失败: {}", fileManageSaveBOS.getRspDesc());
            return BaseRspUtils.createErrorRsp("文件上传失败");
        }
        return BaseRspUtils.createSuccessRsp(fileManageSaveBOS.getRows(), "上传成功");
    }

    /**
     * 创建动态表头
     */
    private static List<List<String>> createHead() {
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("序号"));
        head.add(Collections.singletonList("用户ID"));
        head.add(Collections.singletonList("用户手机号"));
        head.add(Collections.singletonList("抬头类型"));
        head.add(Collections.singletonList("发票抬头"));
        head.add(Collections.singletonList("发票税号"));
        head.add(Collections.singletonList("邮箱地址"));
        head.add(Collections.singletonList("开票金额"));
        head.add(Collections.singletonList("关联订单"));
        head.add(Collections.singletonList("总支付金额"));
        head.add(Collections.singletonList("已开票金额"));
        head.add(Collections.singletonList("备注"));
        head.add(Collections.singletonList("开票申请时间"));
        head.add(Collections.singletonList("状态更新时间"));
        head.add(Collections.singletonList("状态"));
        return head;
    }
}
