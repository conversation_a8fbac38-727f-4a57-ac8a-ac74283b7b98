package com.tydic.nbchat.pay.core.controller;

import com.tydic.nbchat.pay.api.PayGoodsSkuApi;
import com.tydic.nbchat.pay.api.bo.PayGoodsSkuReqBO;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/pay/goods/sku")
public class PayGoodsSkuController {
    private final PayGoodsSkuApi payGoodsSkuApi;

    @PostMapping("/list")
    public RspList list(@RequestBody PayGoodsSkuReqBO request) {
        return payGoodsSkuApi.list(request);
    }
}