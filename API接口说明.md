# 首页模板管理接口说明

## 接口列表

### 1. 管理员查询首页模板列表
- **接口地址**: `POST /train/tdh/index/admin/templates`
- **权限要求**: 系统管理员或租户管理员
- **请求参数**: TdhIndexTemplateQueryReqBO
- **响应**: RspList<TdhIndexTemplateQueryRspBO>
- **说明**: 管理员可以查询所有状态的模板，包括已删除的

### 2. 新增首页模板（管理员）
- **接口地址**: `POST /train/tdh/index/admin/template/add`
- **权限要求**: 系统管理员或租户管理员
- **请求参数**: TdhIndexTemplateQueryReqBO
- **响应**: Rsp
- **必填字段**: tplName（模板名称）、tplType（模板类型）

### 3. 更新首页模板（管理员）
- **接口地址**: `POST /train/tdh/index/admin/template/update`
- **权限要求**: 系统管理员或租户管理员
- **请求参数**: TdhIndexTemplateQueryReqBO
- **响应**: Rsp
- **必填字段**: tplId（模板ID）

### 4. 删除首页模板（管理员）
- **接口地址**: `POST /train/tdh/index/admin/template/delete`
- **权限要求**: 系统管理员或租户管理员
- **请求参数**: `{"tplId": "模板ID"}`
- **响应**: Rsp
- **说明**: 逻辑删除，将isValid设置为"0"

### 5. 首页模板排序（管理员）
- **接口地址**: `POST /train/tdh/index/admin/template/sort`
- **权限要求**: 系统管理员或租户管理员
- **请求参数**: TdhIndexTemplateSortReqBO
- **响应**: Rsp
- **说明**: 根据传入的模板ID列表顺序更新orderIndex字段

## 请求参数说明

### TdhIndexTemplateQueryReqBO
```json
{
  "page": 1,
  "limit": 10,
  "tplId": "模板ID",
  "tenantCode": "租户编码",
  "tplName": "模板名称",
  "tplDesc": "模板描述",
  "tplConfig": "配置",
  "tplContent": "内容",
  "tplType": "模板类型",
  "categoryCode": "分类编码",
  "tplSize": "尺寸",
  "status": "上架状态",
  "orderIndex": 排序索引,
  "isValid": "是否有效"
}
```

### TdhIndexTemplateSortReqBO
```json
{
  "tplIds": ["模板ID1", "模板ID2", "模板ID3"]
}
```

## 响应说明

### 成功响应
```json
{
  "rspCode": "0000",
  "rspDesc": "业务处理成功!",
  "data": "响应数据"
}
```

### 错误响应
```json
{
  "rspCode": "9999",
  "rspDesc": "错误信息",
  "data": null
}
```

## 权限说明
- 所有管理员接口都需要系统管理员（sysAdmin）或租户管理员（tenantAdmin）权限
- 使用@RequiresRole注解进行权限控制
- 权限验证失败会返回权限异常错误
