package com.tydic.nbchat.openapi.core.service;

import com.tydic.nbchat.openapi.api.bo.OapiTdhTrainOrderBO;
import com.tydic.nbchat.openapi.api.bo.eums.CustomizeTaskStatus;
import com.tydic.nbchat.openapi.api.bo.eums.TdhTrainStatus;
import com.tydic.nbchat.openapi.api.bo.tdh.TdhTrainResult;
import com.tydic.nbchat.openapi.core.busi.TdhCustomizeHelper;
import com.tydic.nbchat.openapi.mapper.po.OapiTdhTrainOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 泛化数字人定制
 */
@Slf4j
public class TdhMtkCustomizeTask extends AbstractTdhCustomize {

    public TdhMtkCustomizeTask(TdhCustomizeHelper tdhCustomizeHelper, OapiTdhTrainOrderBO order) {
        super(tdhCustomizeHelper,order);
    }

    @Override
    public void customize() {
        String trainTaskId = this.tdhCustomizeHelper.submitTdhCustomize(this.order);
        if (StringUtils.isNotBlank(trainTaskId)) {
            //更新任务ID
            this.order.setTrainTaskId(trainTaskId);
            OapiTdhTrainOrder update = new OapiTdhTrainOrder();
            update.setOrderId(this.order.getOrderId());
            update.setTrainTaskId(trainTaskId);
            TdhTrainResult result = tdhCustomizeHelper.pollTdhCustomize(this.order);
            if (result.isSuccess()) {
                //更新定制状态
                //update.setObjId(result.getTdhId());
                update.setObjUrl(result.getTdhImg());
                update.setDemoUrl(result.getVideoUrl());
                update.setTrainStatus(TdhTrainStatus.TRAINED.getCode());
                update.setOrderStatus(CustomizeTaskStatus.TRAINED.getCode());
                log.info("数字人定制-任务完成-更新任务状态：{}", update);
                tdhCustomizeHelper.updateCustomizeTask(update);

                //获取demo预览视频
//                this.order.setObjId(result.getTdhId());
//                String demoUrl = tdhCustomizeHelper.pollTdhCustomizeDemo(this.order);
//                log.info("数字人定制-任务完成-更新预览视频：{}", demoUrl);
//                update.setOrderStatus(CustomizeTaskStatus.TRAINED.getCode());
//                update.setDemoUrl(demoUrl);
//                tdhCustomizeHelper.updateCustomizeTask(update);
            } else {
                //更新任务状态
                update.setTrainError(result.getReason());
                update.setOrderStatus(CustomizeTaskStatus.TRAIN_ERROR.getCode());
                update.setTrainStatus(CustomizeTaskStatus.TRAIN_ERROR.getCode());
                update.setOrderDesc(result.getReason());
                tdhCustomizeHelper.updateCustomizeTask(update);
            }
        } else {
            //更新任务状态
            tdhCustomizeHelper.updateTdhTrainError(this.order.getOrderId(),null,"训练任务提交失败");
        }
    }

}
