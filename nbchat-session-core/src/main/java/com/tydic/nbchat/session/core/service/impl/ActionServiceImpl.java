package com.tydic.nbchat.session.core.service.impl;

import com.alibaba.fastjson2.JSON;
import com.tydic.nbchat.session.core.busi.ActionService;
import com.tydic.nbchat.session.mapper.NbchatSessionActionLogMapper;
import com.tydic.nbchat.session.mapper.po.NbchatSessionActionLogPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class ActionServiceImpl implements ActionService {

    @Resource
    NbchatSessionActionLogMapper actionLogMapper;

    @Override
    public void recording(NbchatSessionActionLogPO po) {
        log.info("分享：收到一条操作记录：{}", JSON.toJSONString(po));
        actionLogMapper.insertSelective(po);
    }

    @Override
    public NbchatSessionActionLogPO queryRecord(NbchatSessionActionLogPO po) {
        log.info("查询用户查看分享记录：{}",po);
        return actionLogMapper.queryByCondi(po);
    }
}
