package com.tydic.nbchat.session.core.service.impl;

import com.tydic.nbchat.session.api.AiToolAppUserApi;
import com.tydic.nbchat.session.api.bo.AiToolAppUserBO;
import com.tydic.nbchat.session.mapper.AiToolAppUserMapper;
import com.tydic.nbchat.session.mapper.po.AiToolAppUser;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class AiToolAppUserServiceImpl implements AiToolAppUserApi {

    @Resource
    AiToolAppUserMapper aiToolAppUserMapper;

    @Override
    public Rsp createSession(AiToolAppUserBO request) {
        log.info("创建app会话:{}", request);
        AiToolAppUser po = new AiToolAppUser();
        BeanUtils.copyProperties(request,po);
        if (StringUtils.isEmpty(po.getSessionId())) {
            po.setSessionId(IdWorker.nextAutoIdStr());
        }
        aiToolAppUserMapper.insertSelective(po);
        return BaseRspUtils.createSuccessRsp(po.getSessionId());
    }

    @Override
    public Rsp getSession(String sessionId) {
        return null;
    }


}
