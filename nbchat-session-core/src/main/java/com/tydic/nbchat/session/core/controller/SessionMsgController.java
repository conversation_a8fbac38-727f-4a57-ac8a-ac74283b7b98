package com.tydic.nbchat.session.core.controller;

import com.tydic.nbchat.session.api.bo.AiToolAppUserBO;
import com.tydic.nbchat.session.api.bo.NbchatSessionMsgReqBO;
import com.tydic.nbchat.session.api.SessionMsgService;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/session")
public class SessionMsgController {

    private final SessionMsgService sessionMsgService;

    public SessionMsgController(SessionMsgService sessionMsgService) {
        this.sessionMsgService = sessionMsgService;
    }
    @PostMapping("/msg/list")
    public RspList getSessionMessages(@RequestBody NbchatSessionMsgReqBO reqBO){
        return sessionMsgService.getMsgList(reqBO);
    }

    @PostMapping("/msg/delete")
    @MethodParamVerifyEnable
    public Rsp deleteMsg(@RequestBody NbchatSessionMsgReqBO reqBO){
        if (StringUtils.isEmpty(reqBO.getRequestId())) {
            return BaseRspUtils.createErrorRsp("消息id不得为空");
        }
        return sessionMsgService.deleteMsg(reqBO);
    }

    @PostMapping("/msg/clear")
    @MethodParamVerifyEnable
    public Rsp clearSession(@RequestBody NbchatSessionMsgReqBO reqBO){
        return sessionMsgService.clearMsg(reqBO);
    }


    @Deprecated
    @PostMapping("/msg/query/history")
    @MethodParamVerifyEnable
    public RspList queryHistory(@RequestBody NbchatSessionMsgReqBO reqBO){
        return sessionMsgService.queryHistory(reqBO);
    }

    @PostMapping("app/session_list")
    public RspList getSessionList(@RequestBody NbchatSessionMsgReqBO request) {
        return sessionMsgService.getAllSession(request);
    }
}
