package com.tydic.nbchat.session.core.service;

import com.tydic.nbchat.session.api.constants.AreaCodeConstants;
import com.tydic.nbchat.session.mapper.SmsTemplateAreaCodeMapper;
import com.tydic.nbchat.session.mapper.SmsTemplateMapper;
import com.tydic.nbchat.session.mapper.po.SmsTemplateAreaCodePO;
import com.tydic.nbchat.session.mapper.po.SmsTemplatePO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 基于数据库的短信模板服务
 * 从数据库加载和管理短信模板和区号映射关系
 */
@Slf4j
@Service
public class SmsTemplateDatabaseService {

    private final SmsTemplateMapper smsTemplateMapper;
    private final SmsTemplateAreaCodeMapper smsTemplateAreaCodeMapper;
    
    // 模板名称到模板ID的映射
    private final Map<String, String> templateNameToIdMap = new ConcurrentHashMap<>();
    
    // 区号到模板映射的缓存
    private final Map<String, Map<String, String>> areaCodeTemplateMap = new ConcurrentHashMap<>();

    public SmsTemplateDatabaseService(SmsTemplateMapper smsTemplateMapper, 
                                     SmsTemplateAreaCodeMapper smsTemplateAreaCodeMapper) {
        this.smsTemplateMapper = smsTemplateMapper;
        this.smsTemplateAreaCodeMapper = smsTemplateAreaCodeMapper;
    }

    @PostConstruct
    public void init() {
        refreshTemplateCache();
    }
    
    /**
     * 刷新模板缓存
     */
    public void refreshTemplateCache() {
        // 清空缓存
        templateNameToIdMap.clear();
        areaCodeTemplateMap.clear();
        
        // 加载所有模板
        List<SmsTemplatePO> templates = smsTemplateMapper.selectAll();
        templates.forEach(template -> {
            templateNameToIdMap.put(template.getName(), template.getId());
            log.info("加载模板: {} -> {}", template.getName(), template.getId());
        });
        
        // 加载所有区号模板映射
        List<SmsTemplateAreaCodePO> areaCodeTemplates = smsTemplateAreaCodeMapper.selectAll();
        
        // 按区号分组
        Map<String, List<SmsTemplateAreaCodePO>> groupedByAreaCode = areaCodeTemplates.stream()
                .collect(Collectors.groupingBy(SmsTemplateAreaCodePO::getAreaCode));
        
        // 构建区号模板映射
        groupedByAreaCode.forEach((areaCode, areaTemplates) -> {
            Map<String, String> templateMap = areaTemplates.stream()
                    .collect(Collectors.toMap(
                            SmsTemplateAreaCodePO::getTemplateName,
                            SmsTemplateAreaCodePO::getTemplateId
                    ));
            areaCodeTemplateMap.put(areaCode, templateMap);
            log.info("加载区号模板映射: {} -> {} 个模板", areaCode, templateMap.size());
        });
    }

    /**
     * 根据模板名称获取模板ID
     * @param templateName 模板名称
     * @return 模板ID
     */
    public String getTemplateIdByName(String templateName) {
        return templateNameToIdMap.getOrDefault(templateName, null);
    }

    /**
     * 根据区号和模板名称获取适合的模板ID
     * @param areaCode 区号
     * @param templateName 模板名称
     * @return 适合该区号的模板ID，如果没有特定模板则返回默认模板ID
     */
    public String getTemplateIdByAreaCode(String areaCode, String templateName) {
        if (StringUtils.isBlank(areaCode) || StringUtils.isBlank(templateName)) {
            return getTemplateIdByName(templateName);
        }
        
        // 如果是中国大陆区号，直接返回默认模板ID
        if (AreaCodeConstants.CHINA_MAINLAND.equals(areaCode)) {
            return getTemplateIdByName(templateName);
        }
        
        // 查找区号特定的模板映射
        Map<String, String> templateMap = areaCodeTemplateMap.get(areaCode);
        if (templateMap != null && templateMap.containsKey(templateName)) {
            String areaSpecificTemplateId = templateMap.get(templateName);
            log.debug("使用区号特定模板: 区号={}, 模板名称={}, 区号特定模板ID={}", 
                    areaCode, templateName, areaSpecificTemplateId);
            return areaSpecificTemplateId;
        }
        
        // 如果没有找到区号特定的模板，返回默认模板ID
        return getTemplateIdByName(templateName);
    }
    
    /**
     * 添加模板
     * @param template 模板信息
     * @return 是否添加成功
     */
    public boolean addTemplate(SmsTemplatePO template) {
        if (template == null || StringUtils.isBlank(template.getId()) || StringUtils.isBlank(template.getName())) {
            return false;
        }
        
        try {
            int result = smsTemplateMapper.insert(template);
            if (result > 0) {
                // 更新缓存
                templateNameToIdMap.put(template.getName(), template.getId());
                log.info("添加模板成功: {} -> {}", template.getName(), template.getId());
                return true;
            }
        } catch (Exception e) {
            log.error("添加模板失败: {}", template, e);
        }
        
        return false;
    }
    
    /**
     * 添加区号模板映射
     * @param areaCodeTemplate 区号模板映射
     * @return 是否添加成功
     */
    public boolean addAreaCodeTemplate(SmsTemplateAreaCodePO areaCodeTemplate) {
        if (areaCodeTemplate == null || StringUtils.isBlank(areaCodeTemplate.getAreaCode()) || 
                StringUtils.isBlank(areaCodeTemplate.getTemplateName()) || 
                StringUtils.isBlank(areaCodeTemplate.getTemplateId())) {
            return false;
        }
        
        try {
            int result = smsTemplateAreaCodeMapper.insert(areaCodeTemplate);
            if (result > 0) {
                // 更新缓存
                Map<String, String> templateMap = areaCodeTemplateMap.computeIfAbsent(
                        areaCodeTemplate.getAreaCode(), k -> new ConcurrentHashMap<>());
                templateMap.put(areaCodeTemplate.getTemplateName(), areaCodeTemplate.getTemplateId());
                log.info("添加区号模板映射成功: {} -> {} -> {}", 
                        areaCodeTemplate.getAreaCode(), 
                        areaCodeTemplate.getTemplateName(), 
                        areaCodeTemplate.getTemplateId());
                return true;
            }
        } catch (Exception e) {
            log.error("添加区号模板映射失败: {}", areaCodeTemplate, e);
        }
        
        return false;
    }
    
    /**
     * 更新区号模板映射
     * @param areaCodeTemplate 区号模板映射
     * @return 是否更新成功
     */
    public boolean updateAreaCodeTemplate(SmsTemplateAreaCodePO areaCodeTemplate) {
        if (areaCodeTemplate == null || StringUtils.isBlank(areaCodeTemplate.getAreaCode()) || 
                StringUtils.isBlank(areaCodeTemplate.getTemplateName()) || 
                StringUtils.isBlank(areaCodeTemplate.getTemplateId())) {
            return false;
        }
        
        try {
            int result = smsTemplateAreaCodeMapper.update(areaCodeTemplate);
            if (result > 0) {
                // 更新缓存
                Map<String, String> templateMap = areaCodeTemplateMap.computeIfAbsent(
                        areaCodeTemplate.getAreaCode(), k -> new ConcurrentHashMap<>());
                templateMap.put(areaCodeTemplate.getTemplateName(), areaCodeTemplate.getTemplateId());
                log.info("更新区号模板映射成功: {} -> {} -> {}", 
                        areaCodeTemplate.getAreaCode(), 
                        areaCodeTemplate.getTemplateName(), 
                        areaCodeTemplate.getTemplateId());
                return true;
            }
        } catch (Exception e) {
            log.error("更新区号模板映射失败: {}", areaCodeTemplate, e);
        }
        
        return false;
    }
    
    /**
     * 删除区号模板映射
     * @param areaCode 区号
     * @param templateName 模板名称
     * @return 是否删除成功
     */
    public boolean deleteAreaCodeTemplate(String areaCode, String templateName) {
        if (StringUtils.isBlank(areaCode) || StringUtils.isBlank(templateName)) {
            return false;
        }
        
        try {
            int result = smsTemplateAreaCodeMapper.deleteByAreaCodeAndTemplateName(areaCode, templateName);
            if (result > 0) {
                // 更新缓存
                Map<String, String> templateMap = areaCodeTemplateMap.get(areaCode);
                if (templateMap != null) {
                    templateMap.remove(templateName);
                    log.info("删除区号模板映射成功: {} -> {}", areaCode, templateName);
                }
                return true;
            }
        } catch (Exception e) {
            log.error("删除区号模板映射失败: {} -> {}", areaCode, templateName, e);
        }
        
        return false;
    }
}
