package com.tydic.nbchat.session.core.controller;

import com.tydic.nbchat.session.api.SessionService;
import com.tydic.nbchat.session.api.bo.NbchatSessionReqBO;
import com.tydic.nbchat.session.api.bo.NbchatSessionShareReqBO;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/session")
public class SessionController {

    private final SessionService sessionService;

    public SessionController(SessionService sessionService) {
        this.sessionService = sessionService;
    }

    @PostMapping("query")
    @MethodParamVerifyEnable
    public RspList getAllSessions(@RequestBody NbchatSessionReqBO reqBO){
        return sessionService.getAllSessions(reqBO);
    }

    @PostMapping("list")
    @MethodParamVerifyEnable
    public RspList getSessions(@RequestBody NbchatSessionReqBO reqBO){
        return sessionService.getSessions(reqBO);
    }

    @PostMapping("add")
    @MethodParamVerifyEnable
    public Rsp session(@RequestBody NbchatSessionReqBO reqBO){
        return sessionService.session(reqBO);
    }

    @PostMapping("share")
    @MethodParamVerifyEnable
    public Rsp shareSession(@RequestBody NbchatSessionShareReqBO reqBO){
        return sessionService.shareSession(reqBO);
    }

    @PostMapping("share/query")
    public Rsp queryShare(@RequestBody NbchatSessionShareReqBO reqBO){
        return sessionService.queryShare(reqBO);
    }

    @PostMapping("share/check")
    public Rsp checkShareKey(@RequestBody NbchatSessionShareReqBO reqBO){
        return sessionService.checkShareKey(reqBO);
    }

    @PostMapping("share/save")
    @MethodParamVerifyEnable
    public Rsp saveShare(@RequestBody NbchatSessionReqBO reqBO){
        if (StringUtils.isEmpty(reqBO.getSessionShareId())) {
            return BaseRspUtils.createSuccessRsp("分享会话id不得为空");
        }
        return sessionService.saveShare(reqBO);
    }

    @PostMapping("share/saved/id")
    public Rsp getFromShareSavedSession(@RequestBody NbchatSessionShareReqBO reqBO){
        return sessionService.getFromShareSavedSession(reqBO);
    }

}
