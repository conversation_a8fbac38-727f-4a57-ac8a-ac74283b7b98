package com.tydic.nbchat.session.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短信模板配置类
 * 用于从配置文件加载短信模板和区号映射
 */
@Data
@Configuration
@PropertySource(value = "classpath:sms-templates.yml", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "sms")
public class SmsTemplateConfig {

    private List<TemplateInfo> templates = new ArrayList<>();
    private AreaCodes areaCodes = new AreaCodes();

    /**
     * 模板信息
     */
    @Data
    public static class TemplateInfo {
        private String id;
        private String name;
        private String description;
        private boolean isDefault;
    }

    /**
     * 区号配置
     */
    @Data
    public static class AreaCodes {
        private Map<String, AreaCodeInfo> areaCodes = new HashMap<>();
    }

    /**
     * 区号信息
     */
    @Data
    public static class AreaCodeInfo {
        private boolean isDefault;
        private Map<String, String> templates = new HashMap<>();
    }
}
