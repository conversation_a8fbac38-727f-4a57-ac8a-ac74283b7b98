package com.tydic.nbchat.session.core.controller;

import com.tydic.nbchat.session.api.bo.ReportQueryBO;
import com.tydic.nbchat.session.core.service.impl.ReportService;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/session")
public class SessionReportController {

    private final ReportService reportService;

    public SessionReportController(ReportService reportService) {
        this.reportService = reportService;
    }

    @PostMapping("robot/report")
    public RspList report(@RequestBody ReportQueryBO request) {
        return reportService.countMsg(request);
    }

}
