package com.tydic.nbchat.session.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
@Component
@ConfigurationProperties(prefix = "session-config")
public class SessionConfig {
    private String shareUrlPrefix = "http://127.0.0.1";
    private Integer shareKeyLength = 4;
    private String robotType = "ChatGPT";
}
