package com.tydic.nbchat.session.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.session.api.AiToolAppResApi;
import com.tydic.nbchat.session.api.bo.AiToolAppResBO;
import com.tydic.nbchat.session.api.bo.AiToolAppUserBO;
import com.tydic.nbchat.session.mapper.AiToolAppResMapper;
import com.tydic.nbchat.session.mapper.AiToolAppUserMapper;
import com.tydic.nbchat.session.mapper.po.AiToolAppRes;
import com.tydic.nbchat.session.mapper.po.AiToolAppUser;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Data
@Service
public class AiToolAppResServiceImpl implements AiToolAppResApi {

    @Resource
    AiToolAppResMapper aiToolAppResMapper;
    @Resource
    AiToolAppUserMapper aiToolAppUserMapper;

    @Override
    public Rsp save(AiToolAppResBO request) {
        log.info("保存考核结果:{}",request);
        String sessionId = request.getSessionId();
        AiToolAppUser appUser = aiToolAppUserMapper.selectBySessionId(sessionId);
        String appId = appUser.getAppId();

        AiToolAppRes po = new AiToolAppRes();
        po.setAppId(appId);
        po.setSessionId(sessionId);
        Map<String, String> res = request.getRes();
        for (Map.Entry<String, String> entry : res.entrySet()) {
            po.setName(entry.getKey());
            po.setScore(entry.getValue());
            aiToolAppResMapper.insertSelective(po);
        }
        return BaseRspUtils.createSuccessRsp("保存成功");
    }

    @Override
    public RspList query(AiToolAppUserBO request) {
        log.info("查询考核结果:{}",request);
        AiToolAppRes cond = new AiToolAppRes();
        cond.setAppId(request.getAppId());
        Page<AiToolAppRes> page = PageHelper.startPage(request.getPage(), request.getLimit());
        aiToolAppResMapper.selectAllSession(cond);
        List<AiToolAppRes> res = page.getResult();
        if (CollectionUtils.isEmpty(res)) {
            return BaseRspUtils.createSuccessRspList(new ArrayList<>());
        }
        ArrayList<AiToolAppResBO> rspList = new ArrayList<>();
        for (AiToolAppRes record : res) {
            cond.setSessionId(record.getSessionId());
            AiToolAppUser appUser = aiToolAppUserMapper.selectBySessionId(record.getSessionId());
            List<AiToolAppRes> appRes = aiToolAppResMapper.selectAll(cond);

            AiToolAppResBO bo = new AiToolAppResBO();
            bo.setSessionId(record.getSessionId());
            bo.setName(appUser.getName());
            bo.setPhone(appUser.getPhone());
            Map<String, String> resList = appRes.stream().collect(Collectors.toMap(AiToolAppRes::getName, AiToolAppRes::getScore));
            bo.setRes(resList);
            rspList.add(bo);
        }
        return BaseRspUtils.createSuccessRspList(rspList,page.getTotal());
    }
}
