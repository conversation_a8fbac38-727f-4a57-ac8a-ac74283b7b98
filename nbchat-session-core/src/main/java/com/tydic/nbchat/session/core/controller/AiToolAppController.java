package com.tydic.nbchat.session.core.controller;

import com.tydic.nbchat.session.api.AiToolAppApi;
import com.tydic.nbchat.session.api.AiToolAppResApi;
import com.tydic.nbchat.session.api.AiToolAppUserApi;
import com.tydic.nbchat.session.api.bo.AiToolAppBO;
import com.tydic.nbchat.session.api.bo.AiToolAppResBO;
import com.tydic.nbchat.session.api.bo.AiToolAppUserBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/session")
public class AiToolAppController {

    @Resource
    AiToolAppApi aiToolAppApi;
    @Resource
    AiToolAppUserApi aiToolAppUserApi;
    @Resource
    AiToolAppResApi aiToolAppResApi;



    @PostMapping("app/save")
    public Rsp createApp(@RequestBody AiToolAppBO request){
        return aiToolAppApi.createApp(request);
    }

    @PostMapping("app/query")
    public RspList getAppRecord(@RequestBody AiToolAppBO request){
        return aiToolAppApi.getAppRecord(request);
    }

    @PostMapping("app/info")
    public Rsp getAppInfo(@RequestBody AiToolAppBO request){
        return aiToolAppApi.getAppInfo(request);
    }

    @PostMapping("app/userSession/create")
    public Rsp createSession(@RequestBody AiToolAppUserBO request){
        return aiToolAppUserApi.createSession(request);
    }

    @PostMapping("app/res/save")
    public Rsp saveRes(@RequestBody AiToolAppResBO request) {
        return aiToolAppResApi.save(request);
    }

    @PostMapping("app/res/query")
    public RspList queryRes(@RequestBody AiToolAppUserBO request) {
        return aiToolAppResApi.query(request);
    }

}
