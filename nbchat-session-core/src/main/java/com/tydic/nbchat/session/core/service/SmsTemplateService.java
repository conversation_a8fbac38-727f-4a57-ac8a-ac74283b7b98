package com.tydic.nbchat.session.core.service;

import com.tydic.nbchat.session.api.constants.AreaCodeConstants;
import com.tydic.nbchat.session.core.config.SmsTemplateConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 短信模板服务
 * 管理短信模板和区号映射关系
 */
@Slf4j
@Service
public class SmsTemplateService {

    private final SmsTemplateConfig smsTemplateConfig;
    
    // 模板名称到模板ID的映射
    private final Map<String, String> templateNameToIdMap = new ConcurrentHashMap<>();
    
    // 区号到模板映射的缓存
    private final Map<String, Map<String, String>> areaCodeTemplateMap = new ConcurrentHashMap<>();

    public SmsTemplateService(SmsTemplateConfig smsTemplateConfig) {
        this.smsTemplateConfig = smsTemplateConfig;
    }

    @PostConstruct
    public void init() {
        // 初始化模板名称到ID的映射
        smsTemplateConfig.getTemplates().forEach(template -> {
            templateNameToIdMap.put(template.getName(), template.getId());
            log.info("加载模板: {} -> {}", template.getName(), template.getId());
        });
        
        // 初始化区号模板映射
        smsTemplateConfig.getAreaCodes().getAreaCodes().forEach((areaCode, areaCodeInfo) -> {
            Map<String, String> templateMap = new HashMap<>();
            areaCodeInfo.getTemplates().forEach((templateName, templateId) -> {
                templateMap.put(templateName, templateId);
                log.info("加载区号模板映射: {} -> {} -> {}", areaCode, templateName, templateId);
            });
            areaCodeTemplateMap.put(areaCode, templateMap);
        });
    }

    /**
     * 根据模板名称获取模板ID
     * @param templateName 模板名称
     * @return 模板ID
     */
    public String getTemplateIdByName(String templateName) {
        return templateNameToIdMap.getOrDefault(templateName, null);
    }

    /**
     * 根据区号和模板名称获取适合的模板ID
     * @param areaCode 区号
     * @param templateName 模板名称
     * @return 适合该区号的模板ID，如果没有特定模板则返回默认模板ID
     */
    public String getTemplateIdByAreaCode(String areaCode, String templateName) {
        if (StringUtils.isBlank(areaCode) || StringUtils.isBlank(templateName)) {
            return getTemplateIdByName(templateName);
        }
        
        // 如果是中国大陆区号，直接返回默认模板ID
        if (AreaCodeConstants.CHINA_MAINLAND.equals(areaCode)) {
            return getTemplateIdByName(templateName);
        }
        
        // 查找区号特定的模板映射
        Map<String, String> templateMap = areaCodeTemplateMap.get(areaCode);
        if (templateMap != null && templateMap.containsKey(templateName)) {
            String areaSpecificTemplateId = templateMap.get(templateName);
            log.debug("使用区号特定模板: 区号={}, 模板名称={}, 区号特定模板ID={}", 
                    areaCode, templateName, areaSpecificTemplateId);
            return areaSpecificTemplateId;
        }
        
        // 如果没有找到区号特定的模板，返回默认模板ID
        return getTemplateIdByName(templateName);
    }
    
    /**
     * 动态添加区号模板映射
     * @param areaCode 区号
     * @param templateName 模板名称
     * @param templateId 模板ID
     */
    public void addTemplateMapping(String areaCode, String templateName, String templateId) {
        if (StringUtils.isBlank(areaCode) || StringUtils.isBlank(templateName) || StringUtils.isBlank(templateId)) {
            return;
        }
        
        Map<String, String> templateMap = areaCodeTemplateMap.computeIfAbsent(areaCode, k -> new HashMap<>());
        templateMap.put(templateName, templateId);
        log.info("动态添加区号模板映射: 区号={}, 模板名称={}, 模板ID={}", areaCode, templateName, templateId);
    }
}
