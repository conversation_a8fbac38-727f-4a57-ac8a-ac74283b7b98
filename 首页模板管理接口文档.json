{"info": {"name": "首页模板管理接口", "description": "首页模板管理相关的管理员接口", "version": "1.0.0"}, "item": [{"name": "管理员查询首页模板列表", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/templates", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "templates"]}, "body": {"mode": "raw", "raw": "{\n  \"page\": 1,\n  \"limit\": 10,\n  \"tplName\": \"\",\n  \"tplType\": \"\",\n  \"status\": \"\",\n  \"isValid\": \"\",\n  \"categoryCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"page\": 1,\n  \"limit\": 10\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/templates", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "templates"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"业务处理成功!\",\n  \"rows\": [\n    {\n      \"tplId\": \"123456789\",\n      \"tenantCode\": \"tenant001\",\n      \"tplName\": \"示例模板\",\n      \"tplDesc\": \"这是一个示例模板\",\n      \"tplType\": \"ppt\",\n      \"categoryCode\": \"cat001\",\n      \"orderIndex\": 1,\n      \"status\": \"1\",\n      \"isValid\": \"1\",\n      \"createTime\": \"2024-01-01 10:00:00\"\n    }\n  ],\n  \"count\": 1\n}"}]}, {"name": "新增首页模板", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/add", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "add"]}, "body": {"mode": "raw", "raw": "{\n  \"tplName\": \"新模板名称\",\n  \"tplDesc\": \"模板描述\",\n  \"tplType\": \"ppt\",\n  \"tplConfig\": \"{}\",\n  \"tplContent\": \"模板内容\",\n  \"categoryCode\": \"cat001\",\n  \"tplSize\": \"16:9\",\n  \"status\": \"0\",\n  \"orderIndex\": 1\n}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"tplName\": \"新模板名称\",\n  \"tplType\": \"ppt\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/add", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "add"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"新增模板成功\",\n  \"data\": \"123456789\"\n}"}]}, {"name": "更新首页模板", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/update", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "update"]}, "body": {"mode": "raw", "raw": "{\n  \"tplId\": \"123456789\",\n  \"tplName\": \"更新后的模板名称\",\n  \"tplDesc\": \"更新后的模板描述\",\n  \"tplType\": \"ppt\",\n  \"tplConfig\": \"{}\",\n  \"tplContent\": \"更新后的模板内容\",\n  \"categoryCode\": \"cat001\",\n  \"tplSize\": \"16:9\",\n  \"status\": \"1\",\n  \"orderIndex\": 2\n}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"tplId\": \"123456789\",\n  \"tplName\": \"更新后的模板名称\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/update", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "update"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"更新模板成功\",\n  \"data\": \"123456789\"\n}"}]}, {"name": "删除首页模板", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/delete", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "delete"]}, "body": {"mode": "raw", "raw": "{\n  \"tplId\": \"123456789\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"tplId\": \"123456789\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/delete", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "delete"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"删除模板成功\",\n  \"data\": \"123456789\"\n}"}]}, {"name": "首页模板排序", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/sort", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "sort"]}, "body": {"mode": "raw", "raw": "{\n  \"tplIds\": [\"123456789\", \"987654321\", \"456789123\"]\n}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "成功响应", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"tplIds\": [\"123456789\", \"987654321\", \"456789123\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/sort", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "sort"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"模板排序成功\",\n  \"data\": 3\n}"}]}, {"name": "首页模板上下架", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/status", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "status"]}, "body": {"mode": "raw", "raw": "{\n  \"tplId\": \"123456789\",\n  \"status\": \"1\"\n}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "上架成功", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"tplId\": \"123456789\",\n  \"status\": \"1\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/status", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "status"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"模板上架成功\",\n  \"data\": \"123456789\"\n}"}, {"name": "下架成功", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n  \"tplId\": \"123456789\",\n  \"status\": \"0\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/status", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "status"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"模板下架成功\",\n  \"data\": \"123456789\"\n}"}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}]}