{
  "info": {
    "_postman_id": "12345678-1234-1234-1234-123456789abc",
    "name": "首页模板管理接口（完整版）",
    "description": "首页模板管理相关的管理员接口，包括查询、新增、更新、删除、下架和排序功能",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "管理员查询首页模板列表",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test(\"Status code is 200\", function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test(\"Response has correct structure\", function () {",
              "    var jsonData = pm.response.json();",
              "    pm.expect(jsonData).to.have.property('rspCode');",
              "    pm.expect(jsonData).to.have.property('rspDesc');",
              "    pm.expect(jsonData).to.have.property('rows');",
              "    pm.expect(jsonData).to.have.property('count');",
              "});"
            ],
            "type": "text/javascript"
          }
        }
      ],
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          },
          {
            "key": "Authorization",
            "value": "Bearer {{token}}",
            "description": "管理员权限token"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"page\": 1,\n  \"limit\": 10,\n  \"tplName\": \"\",\n  \"tplType\": \"\",\n  \"status\": \"\",\n  \"isValid\": \"\",\n  \"categoryCode\": \"\"\n}",
          "options": {
            "raw": {
              "language": "json"
            }
          }
        },
        "url": {
          "raw": "{{baseUrl}}/train/tdh/index/admin/templates",
          "host": [
            "{{baseUrl}}"
          ],
          "path": [
            "train",
            "tdh",
            "index",
            "admin",
            "templates"
          ]
        },
        "description": "管理员查询首页模板列表，支持分页和条件筛选。需要系统管理员或租户管理员权限。"
      },
      "response": [
        {
          "name": "成功响应示例",
          "originalRequest": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"page\": 1,\n  \"limit\": 10\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/train/tdh/index/admin/templates",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "train",
                "tdh",
                "index",
                "admin",
                "templates"
              ]
            }
          },
          "status": "OK",
          "code": 200,
          "_postman_previewlanguage": "json",
          "header": [
            {
              "key": "Content-Type",
              "value": "application/json"
            }
          ],
          "cookie": [],
          "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"业务处理成功!\",\n  \"rows\": [\n    {\n      \"tplId\": \"123456789\",\n      \"tenantCode\": \"tenant001\",\n      \"tplName\": \"示例PPT模板\",\n      \"tplDesc\": \"这是一个示例PPT模板\",\n      \"tplType\": \"ppt\",\n      \"categoryCode\": \"cat001\",\n      \"tplSize\": \"16:9\",\n      \"orderIndex\": 1,\n      \"status\": \"1\",\n      \"isValid\": \"1\",\n      \"createTime\": \"2024-01-01T10:00:00\"\n    }\n  ],\n  \"count\": 1\n}"
        }
      ]
    },
    {
      "name": "新增首页模板",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test(\"Status code is 200\", function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test(\"Template created successfully\", function () {",
              "    var jsonData = pm.response.json();",
              "    pm.expect(jsonData.rspCode).to.eql('0000');",
              "    pm.expect(jsonData.data).to.not.be.null;",
              "    // 保存模板ID用于后续测试",
              "    if (jsonData.data) {",
              "        pm.environment.set('templateId', jsonData.data);",
              "    }",
              "});"
            ],
            "type": "text/javascript"
          }
        }
      ],
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          },
          {
            "key": "Authorization",
            "value": "Bearer {{token}}",
            "description": "管理员权限token"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"tplName\": \"新PPT模板\",\n  \"tplDesc\": \"这是一个新创建的PPT模板\",\n  \"tplType\": \"ppt\",\n  \"tplConfig\": \"{\\\"theme\\\": \\\"default\\\", \\\"layout\\\": \\\"standard\\\"}\",\n  \"tplContent\": \"模板内容JSON数据\",\n  \"categoryCode\": \"cat001\",\n  \"tplSize\": \"16:9\",\n  \"status\": \"0\",\n  \"orderIndex\": 1\n}",
          "options": {
            "raw": {
              "language": "json"
            }
          }
        },
        "url": {
          "raw": "{{baseUrl}}/train/tdh/index/admin/template/add",
          "host": [
            "{{baseUrl}}"
          ],
          "path": [
            "train",
            "tdh",
            "index",
            "admin",
            "template",
            "add"
          ]
        },
        "description": "新增首页模板，需要管理员权限。tplName和tplType为必填字段。"
      },
      "response": [
        {
          "name": "成功响应示例",
          "originalRequest": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"tplName\": \"新PPT模板\",\n  \"tplType\": \"ppt\"\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/train/tdh/index/admin/template/add",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "train",
                "tdh",
                "index",
                "admin",
                "template",
                "add"
              ]
            }
          },
          "status": "OK",
          "code": 200,
          "_postman_previewlanguage": "json",
          "header": [
            {
              "key": "Content-Type",
              "value": "application/json"
            }
          ],
          "cookie": [],
          "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"新增模板成功\",\n  \"data\": \"123456789\"\n}"
        }
      ]
    },
    {
      "name": "更新首页模板",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test(\"Status code is 200\", function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test(\"Template updated successfully\", function () {",
              "    var jsonData = pm.response.json();",
              "    pm.expect(jsonData.rspCode).to.eql('0000');",
              "});"
            ],
            "type": "text/javascript"
          }
        }
      ],
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          },
          {
            "key": "Authorization",
            "value": "Bearer {{token}}",
            "description": "管理员权限token"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"tplId\": \"{{templateId}}\",\n  \"tplName\": \"更新后的模板名称\",\n  \"tplDesc\": \"更新后的模板描述\",\n  \"tplType\": \"ppt\",\n  \"tplConfig\": \"{\\\"theme\\\": \\\"updated\\\", \\\"layout\\\": \\\"modern\\\"}\",\n  \"tplContent\": \"更新后的模板内容\",\n  \"categoryCode\": \"cat002\",\n  \"tplSize\": \"16:9\",\n  \"status\": \"1\",\n  \"orderIndex\": 2\n}",
          "options": {
            "raw": {
              "language": "json"
            }
          }
        },
        "url": {
          "raw": "{{baseUrl}}/train/tdh/index/admin/template/update",
          "host": [
            "{{baseUrl}}"
          ],
          "path": [
            "train",
            "tdh",
            "index",
            "admin",
            "template",
            "update"
          ]
        },
        "description": "更新首页模板，需要管理员权限。tplId为必填字段。"
      },
      "response": [
        {
          "name": "成功响应示例",
          "originalRequest": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"tplId\": \"123456789\",\n  \"tplName\": \"更新后的模板名称\"\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/train/tdh/index/admin/template/update",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "train",
                "tdh",
                "index",
                "admin",
                "template",
                "update"
              ]
            }
          },
          "status": "OK",
          "code": 200,
          "_postman_previewlanguage": "json",
          "header": [
            {
              "key": "Content-Type",
              "value": "application/json"
            }
          ],
          "cookie": [],
          "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"更新模板成功\",\n  \"data\": \"123456789\"\n}"
        }
      ]
    },
    {
      "name": "删除首页模板",
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "pm.test(\"Status code is 200\", function () {",
              "    pm.response.to.have.status(200);",
              "});",
              "",
              "pm.test(\"Template deleted successfully\", function () {",
              "    var jsonData = pm.response.json();",
              "    pm.expect(jsonData.rspCode).to.eql('0000');",
              "});"
            ],
            "type": "text/javascript"
          }
        }
      ],
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          },
          {
            "key": "Authorization",
            "value": "Bearer {{token}}",
            "description": "管理员权限token"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"tplId\": \"{{templateId}}\"\n}",
          "options": {
            "raw": {
              "language": "json"
            }
          }
        },
        "url": {
          "raw": "{{baseUrl}}/train/tdh/index/admin/template/delete",
          "host": [
            "{{baseUrl}}"
          ],
          "path": [
            "train",
            "tdh",
            "index",
            "admin",
            "template",
            "delete"
          ]
        },
        "description": "删除首页模板（逻辑删除），需要管理员权限。"
      },
      "response": [
        {
          "name": "成功响应示例",
          "originalRequest": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"tplId\": \"123456789\"\n}",
              "options": {
                "raw": {
                  "language": "json"
                }
              }
            },
            "url": {
              "raw": "{{baseUrl}}/train/tdh/index/admin/template/delete",
              "host": [
                "{{baseUrl}}"
              ],
              "path": [
                "train",
                "tdh",
                "index",
                "admin",
                "template",
                "delete"
              ]
            }
          },
          "status": "OK",
          "code": 200,
          "_postman_previewlanguage": "json",
          "header": [
            {
              "key": "Content-Type",
              "value": "application/json"
            }
          ],
          "cookie": [],
          "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"删除模板成功\",\n  \"data\": \"123456789\"\n}"
        }
      ]
    }