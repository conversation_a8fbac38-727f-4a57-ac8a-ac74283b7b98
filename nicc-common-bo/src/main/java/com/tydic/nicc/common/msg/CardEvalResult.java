package com.tydic.nicc.common.msg;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Classname CardEvalResult
 * @Description CardEvalResult
 * @Date 2021/11/25 6:35 下午
 * @Created by kangkang
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CardEvalResult implements Serializable {
    private String inviteType;
    private String userId;
    private String csId;
    private String evalValue;
    private String evalContent;
    private Date evalTime;
}
