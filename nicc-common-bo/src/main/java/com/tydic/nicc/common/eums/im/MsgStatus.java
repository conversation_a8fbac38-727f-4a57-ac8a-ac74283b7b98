package com.tydic.nicc.common.eums.im;

/**
 * <AUTHOR> <br>
 * @Description: 消息状态  <br>
 * @date 2021/4/27 10:40 上午  <br>
 * @Copyright tydic.com
 */
public enum MsgStatus {

    DELETED("0", "已删除"),
    RECEIVED("1", "已收到"),
    RECALLED("2", "已撤回");

    private String code;
    private String name;

    public Integer getIntCode() {
        return Integer.valueOf(code);
    }

    public Short getShortCode() {
        return Short.valueOf(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private MsgStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (MsgStatus field : MsgStatus.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }


}
