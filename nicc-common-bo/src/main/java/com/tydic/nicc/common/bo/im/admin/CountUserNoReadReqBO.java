package com.tydic.nicc.common.bo.im.admin;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Classname CountNoReadReqBO
 * @Description 统计未读消息
 * @Date 2021/5/27 2:20 下午
 * @Created by kangkang
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CountUserNoReadReqBO {
    //租户编码
    private String tenantCode;
    //chatKey
    private String chatKey;
    //消息查询人
    private String userId;
    //chatType
    private String chatType;
    //指定聊天对象
    private String chatObjId;
    //客服模式
    private boolean csModel;

}
