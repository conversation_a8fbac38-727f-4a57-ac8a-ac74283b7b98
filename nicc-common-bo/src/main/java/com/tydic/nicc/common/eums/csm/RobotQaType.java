package com.tydic.nicc.common.eums.csm;

/**
 * @Classname RobotQaType
 * @Description RobotQaType
 * @Date 2021/7/26 4:55 下午
 * @Created by kangkang
 */
public enum RobotQaType {

    QA_KEYWORD("0", "关键字匹配"),
    QA_REG("1", "正则匹配");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean matchCode(String code){
        return this.getCode().equals(code);
    }

    private RobotQaType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (RobotQaType field : RobotQaType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
