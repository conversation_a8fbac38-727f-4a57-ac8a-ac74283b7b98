package com.tydic.nicc.common.bo.user;

import com.tydic.nicc.common.eums.im.ImJoinType;

import java.io.Serializable;

/**
 * 握手请求参数
 * {
 * 	"tenantCode":"",//平台模式下，不传此参数
 * 	"channelCode":"",
 * 	"sceneCode":"",
 * 	"userId":"",//平台用户ID
 * 	"extUserId":"",//业务系统/第三方系统的用户ID
 * 	"timestamp":"",
 * 	"randomStr":"",
 * 	"signature":"",
 * 	"extraData":""//第三方自定义数据，可用于第三方自定义鉴权时透传给第三方系统
 * }
 */
public class UserJoinInfoBO implements Serializable {

    private static final long serialVersionUID = -4996921000617714915L;

    // 租户编码
    private String tenantCode;
    // 场景编码
    private String sceneCode;
    // 渠道编码
    private String channelCode;
    // 用户ID
    private String userId;
    // 业务系统/第三方系统 的用户ID
    private String extUid;
    // 签名
    private String signature;
    // 时间戳
    private String timestamp;
    // 渠道来源: 0:桌面网站 1：移动网站 2：App网站 3：微信 4：微博 5：企业微信 6：微信小程序
    private String custSource;
    // 随机串
    private String randomStr;
    // 扩展字段
    private String extraData;
    // 连接类型
    private String joinType = ImJoinType.CHAT.getCode();

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getCustSource() {
        return custSource;
    }

    public void setCustSource(String custSource) {
        this.custSource = custSource;
    }

    public String getRandomStr() {
        return randomStr;
    }

    public void setRandomStr(String randomStr) {
        this.randomStr = randomStr;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getExtraData() {
        return extraData;
    }

    public void setExtraData(String extraData) {
        this.extraData = extraData;
    }

    public String getExtUid() {
        return extUid;
    }

    public void setExtUid(String extUid) {
        this.extUid = extUid;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }

    public String getJoinType() {
        return joinType;
    }

    public void setJoinType(String joinType) {
        this.joinType = joinType;
    }

    @Override
    public String toString() {
        return "UserJoinInfoBO{" +
                "tenantCode='" + tenantCode + '\'' +
                ", sceneCode='" + sceneCode + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", userId='" + userId + '\'' +
                ", extUid='" + extUid + '\'' +
                ", signature='" + signature + '\'' +
                ", timestamp='" + timestamp + '\'' +
                ", custSource='" + custSource + '\'' +
                ", randomStr='" + randomStr + '\'' +
                ", extraData='" + extraData + '\'' +
                ", joinType='" + joinType + '\'' +
                '}';
    }
}
