package com.tydic.nicc.common.bo.im.admin;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 维护好友关系  <br>
 * @date 2021/7/14 8:09 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ImUserFriendRelSaveReqBO implements Serializable {

    @ParamNotEmpty
    private String tenantCode;
    @ParamNotEmpty
    private String userIdA;
    @ParamNotEmpty
    private String userIdB;
    //用户首次进入的渠道
    private String channelCode;
    private String chatType;
    private Integer activeState;
}
