package com.tydic.nicc.common.eums.user;

public enum UserAuthType {


    DIC_CUST("dic-auth", "自定义处理"),
    HTTP("http", "restApi模式"),
    DUBBO("dubbo", "dubbo接口模式");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private UserAuthType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (UserAuthType field : UserAuthType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

}
