package com.tydic.nicc.common.bo.csm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: 坐席强拆  <br>
 * @date 2021/6/21 10:42 上午  <br>
 * @Copyright tydic.com
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CsmForceOfflineAndTransferRspBO implements Serializable {

    //租户编码
    private String tenantCode;
    //当前客服id
    private String csId;
    //结束客服会话数
    private int closeSessionCount;

}
