package com.tydic.nicc.common.bo.im.admin;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: c2b消息历史记录-入参  <br>
 * @date 2021/4/29 4:19 下午  <br>
 * @Copyright tydic.com
 */
@Data
public class ImCommonNewMessageReqBO extends BaseInfo implements Serializable {
   /**
    * 租户编码必填
    */
   @ParamNotEmpty
   private String tenantCode;
   /**
    * fromNo 必须传查询方自己
    */
   @ParamNotEmpty
   private String fromNo;
   /**
    * toNo 可以是具体聊天人 或者租户编码
    */
   @ParamNotEmpty
   private String toNo;
   /**
    * 以lastMsgId拉取
    */
   @ParamNotEmpty
   private String msgId;

}
