package com.tydic.nbchat.train.core.helper;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import org.apache.poi.ss.usermodel.*;

public class QuestionExcelRowWriteHandler implements Row<PERSON>riteHandler {

    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        // 在行写入完成后的操作
        Row row = context.getRow();
        int rowIndex = row.getRowNum();

        // 可以根据行号设置不同的行高
        if (rowIndex == 0) {
            // 例如，设置第一行的行高
            row.setHeight((short) 900);
        } else if (rowIndex == 1) {
            row.setHeight((short) 600);
        } else {
            // 设置其他行的行高
            row.setHeight((short)500);
        }
    }

}
