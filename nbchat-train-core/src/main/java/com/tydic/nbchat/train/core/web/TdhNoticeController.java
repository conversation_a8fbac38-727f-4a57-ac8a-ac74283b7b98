package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.bo.tdh.TdhNoticeTaskBO;
import com.tydic.nbchat.train.api.tdh.TdhNoticeApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/notice")
public class TdhNoticeController {

    @Resource
    TdhNoticeApi tdhNoticeApi;

    @PostMapping("send")
    public Rsp send(@RequestBody TdhNoticeTaskBO request){
        return tdhNoticeApi.sendNotice(request);
    }

}
