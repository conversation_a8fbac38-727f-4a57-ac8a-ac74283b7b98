package com.tydic.nbchat.train.core.web.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhFontQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhSubtitleStyleQueryReqBO;
import com.tydic.nbchat.train.api.tdh.TdhFontApi;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/tdh")
public class TdhFontController {

    private final TdhFontApi tdhFontApi;

    public TdhFontController(TdhFontApi tdhFontApi) {
        this.tdhFontApi = tdhFontApi;
    }


    @PostMapping("/font/list")
    public RspList getFontList(@RequestBody TdhFontQueryReqBO request) {
        return tdhFontApi.getFontList(request);
    }

    @PostMapping("/subtitle/style/list")
    public RspList getSubtitleStyleList(@RequestBody TdhSubtitleStyleQueryReqBO request) {
        return tdhFontApi.getSubtitleStyleList(request);
    }

}
