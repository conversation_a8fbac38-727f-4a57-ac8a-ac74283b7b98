package com.tydic.nbchat.train.core.service.impl.tts;

import com.tydic.nbchat.train.api.bo.asr_tts.*;
import com.tydic.nbchat.train.api.bo.eums.FileUploadDirEnum;
import com.tydic.nbchat.train.api.tts.TdhTtsApi;
import com.tydic.nbchat.train.core.helper.NlsStrategyInvokeFactory;
import com.tydic.nbchat.train.core.util.TrainCommonUtil;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class TdhTtsServiceImpl implements TdhTtsApi {

    private final NlsStrategyInvokeFactory nlsStrategyInvokeFactory;

    public TdhTtsServiceImpl(NlsStrategyInvokeFactory nlsStrategyInvokeFactory) {
        this.nlsStrategyInvokeFactory = nlsStrategyInvokeFactory;
    }

    @Override
    public Rsp convertTextToSpeech(TtsVoiceTaskRequest request) {
        String text = TrainCommonUtil.removeXMLTags(request.getText()).trim();
        if (StringUtils.isBlank(text.trim())) {
            return BaseRspUtils.createErrorRsp("请输入文本内容!");
        }
        //设置音频文件存放目录
        request.setSectionId(FileUploadDirEnum.TTS_DIR.getCode());
        TtsVoiceTaskContext context = nlsStrategyInvokeFactory.createAudioTask(request);
        if(context.isSuccess()){
            return BaseRspUtils.createSuccessRsp(context);
        } else {
            return BaseRspUtils.createErrorRsp(context,"语音合成异常!");
        }
    }

    @Override
    public TtsVoiceTaskContext createTtsTask(TtsVoiceTaskRequest request) {
        return nlsStrategyInvokeFactory.createAudioTask(request);
    }
}
