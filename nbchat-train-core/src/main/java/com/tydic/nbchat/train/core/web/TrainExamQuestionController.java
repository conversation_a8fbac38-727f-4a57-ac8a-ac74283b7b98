package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbcahtExamQuestionApi;
import com.tydic.nbchat.train.api.bo.QuestionSaveRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/train/exam")
public class TrainExamQuestionController {

    @Resource
    NbcahtExamQuestionApi nbcahtExamQuestionApi;

    @PostMapping("add")
    public Rsp question(@RequestBody QuestionSaveRequest request) {
        return nbcahtExamQuestionApi.saveQuestion(request);
    }

    @PostMapping("/append")
    public Rsp append(@RequestBody QuestionSaveRequest request) {
        return nbcahtExamQuestionApi.appendQuestion(request);
    }

}
