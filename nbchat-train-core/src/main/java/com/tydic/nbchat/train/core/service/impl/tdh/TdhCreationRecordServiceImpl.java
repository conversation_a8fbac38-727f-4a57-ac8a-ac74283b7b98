package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.eums.ShareType;
import com.tydic.nbchat.train.api.bo.tdh.TdhCreationRecordReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhCreationRecordRspBO;
import com.tydic.nbchat.train.api.tdh.TdhCreationRecordApi;
import com.tydic.nbchat.train.core.busi.TrainEventSender;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSectionsMapper;
import com.tydic.nbchat.train.mapper.TdhCreationRecordMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourse;
import com.tydic.nbchat.train.mapper.po.NbchatTrainSections;
import com.tydic.nbchat.train.mapper.po.TdhCreationRecord;
import com.tydic.nbchat.user.api.UserBaseInfoApi;
import com.tydic.nbchat.user.api.bo.user.UserBaseInfo;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class TdhCreationRecordServiceImpl implements TdhCreationRecordApi {

    @Resource
    TdhCreationRecordMapper tdhCreationRecordMapper;
    @Resource
    NbchatTrainCourseMapper trainCourseMapper;
    @Resource
    NbchatTrainSectionsMapper trainSectionsMapper;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private UserBaseInfoApi userBaseInfoApi;
    @Resource
    private TrainEventSender trainEventSender;

    @Override
    public RspList<TdhCreationRecordRspBO> queryList(TdhCreationRecordReqBO request) {
        TdhCreationRecord cond = new TdhCreationRecord();
        BeanUtils.copyProperties(request, cond);
        if (ShareType.DEFAULT_SHARE.getCode().equals(request.getIsShare())) {
            //查询我的草稿 0
            cond.setIsShare(null);
        }
        if (ShareType.IS_SHARE.getCode().equals(request.getIsShare())) {
            //查询分享草稿 1
            cond.setUserId(null);
        }
        if (ShareType.ID_SHARE.getCode().equals(request.getIsShare())) {
            //查询单个草稿（通过ID）2
            cond.setUserId(null);
            cond.setIsShare(null);
        }
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        Page<TdhCreationRecord> info = PageHelper.startPage(request.getPage(), request.getLimit());
        tdhCreationRecordMapper.selectAll(cond);
        List<TdhCreationRecord> result = info.getResult();
        ArrayList<TdhCreationRecordRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result, rspList, TdhCreationRecordRspBO.class);
        log.debug("数字人面板-我的草稿：{}", rspList);
        rspList.forEach(list -> {
            log.debug("数字人面板-我的草稿-获取用户名称：{}", list.getUserId());
            if (StringUtils.isNotEmpty(list.getUserId())) {
                Rsp<UserBaseInfo> rsp = userBaseInfoApi.getByUserId(list.getTenantCode(), list.getUserId());
                if (rsp.isSuccess()) {
                    UserBaseInfo baseInfo = rsp.getData();
                    list.setName(baseInfo.getName());
                }
            }
        });
        return BaseRspUtils.createSuccessRspList(rspList, info.getTotal());
    }

    @Override
    public Rsp<TdhCreationRecordRspBO> queryRecord(TdhCreationRecordReqBO request) {
        TdhCreationRecord res = tdhCreationRecordMapper.queryById(request.getCreationId(), "1");
        if (ObjectUtils.isEmpty(res)) {
            return BaseRspUtils.createErrorRsp("未找到该草稿或已失效：" + request.getCreationId());
        }
        TdhCreationRecordRspBO rspBO = new TdhCreationRecordRspBO();
        BeanUtils.copyProperties(res, rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    public Rsp save(TdhCreationRecordReqBO request) {
        String courseId = request.getCourseId();
        String sectionId = request.getSectionId();
        TdhCreationRecord save = new TdhCreationRecord();
        if (StringUtils.isEmpty(request.getCreationId())) {
            log.debug("数字人面板-保存草稿：{}", request);
            BeanUtils.copyProperties(request, save);
            save.setCreationId(IdWorker.nextAutoIdStr());
            save.setIsShare(ShareType.DEFAULT_SHARE.getCode());
            tdhCreationRecordMapper.insertSelective(save);
            // 发送用户维度消息
            trainEventSender.sendUserRpEventByVideoMake(save.getTenantCode(), save.getUserId());
        } else {
            log.debug("数字人面板-更新草稿：{}", request);
            BeanUtils.copyProperties(request, save);
            save.setUpdateTime(new Date());
            save.setUserId(null);
            int i = tdhCreationRecordMapper.update(save);
        }
        if (StringUtils.isNotBlank(courseId) && StringUtils.isBlank(sectionId)) {
            //课程
            NbchatTrainCourse trainCourse = trainCourseMapper.selectByPrimaryKey(courseId);
            NbchatTrainCourse nbchatTrainCourse = new NbchatTrainCourse();
            if (trainCourse != null) {
                if (trainCourse.getVideoSource() == 1) {
                    nbchatTrainCourse.setVideoSource(0);
                }
                nbchatTrainCourse.setCourseId(courseId);
                nbchatTrainCourse.setVideoUrl("");
                trainCourseMapper.updateByPrimaryKeySelective(nbchatTrainCourse);
                log.info("数字人面板-更新课程:{}", courseId);
            }
        }
        if (StringUtils.isNotBlank(courseId) && StringUtils.isNotBlank(sectionId)) {
            //章节
            NbchatTrainSections sections = trainSectionsMapper.selectByPrimaryKey(sectionId);
            NbchatTrainSections nbchatTrainSections = new NbchatTrainSections();
            if (sections != null) {
                if (sections.getVideoSource() == 1) {
                    nbchatTrainSections.setVideoSource(0);
                }
                nbchatTrainSections.setSectionId(sectionId);
                nbchatTrainSections.setVideoUrl("");
                trainSectionsMapper.updateByPrimaryKeySelective(nbchatTrainSections);
                log.info("数字人面板-更新课程|章节:{}|{}", courseId, sectionId);
            }
        }
        return BaseRspUtils.createSuccessRsp(save);

    }
}
