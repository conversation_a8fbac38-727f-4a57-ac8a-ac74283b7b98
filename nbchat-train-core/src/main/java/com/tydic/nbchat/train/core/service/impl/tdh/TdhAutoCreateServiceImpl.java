package com.tydic.nbchat.train.core.service.impl.tdh;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.admin.api.SysRobotConfigApi;
import com.tydic.nbchat.robot.api.NbchatRobotToolsApi;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nbchat.train.api.NbchatTrainSectionApi;
import com.tydic.nbchat.train.api.bo.eums.AiCourseGeneType;
import com.tydic.nbchat.train.api.bo.eums.ElementEnum;
import com.tydic.nbchat.train.api.bo.tdh.*;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBO;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogQueryReqBO;
import com.tydic.nbchat.train.api.tdh.TdhAutoCreateApi;
import com.tydic.nbchat.train.core.busi.TrainEventSender;
import com.tydic.nbchat.train.core.config.NbchatTrainConfigProperties;
import com.tydic.nbchat.train.mapper.*;
import com.tydic.nbchat.train.mapper.po.*;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.http.RestApiHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.HttpClientHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class TdhAutoCreateServiceImpl implements TdhAutoCreateApi {

    @Resource
    TdhVideoTempMapper tdhVideoTempMapper;
    @Resource
    NbchatTrainCourseMapper nbchatTrainCourseMapper;
    @Resource
    TdhCreationRecordMapper tdhCreationRecordMapper;
    @Resource
    NbchatTrainSectionsMapper nbchatTrainSectionsMapper;
    @Resource
    TdhTemplateMapper tdhTemplateMapper;
    @Resource
    RestApiHelper restApiHelper;
    @Resource
    NbchatTrainSectionApi nbchatTrainSectionApi;
    @Resource
    NbchatTrainConfigProperties nbchatTrainConfigProperties;
    @Resource
    private TrainEventSender trainEventSender;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3 * 60 * 1000)
    private NbchatRobotToolsApi nbchatRobotToolsApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
                    group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3 * 1000)
    private SysRobotConfigApi sysRobotConfigApi;

    @Override
    public Rsp autoCreate(TdhAutoCreateBO request) {
        log.info("自动生成视频：{}", request);
        NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(request.getCourseId());
        if (ObjectUtils.isEmpty(course) || StringUtils.isEmpty(course.getCourseDesc())) {
            return BaseRspUtils.createErrorRsp("无简介文字内容，请先生成简介内容");
        }
        RspList<TrainCatalogBO> cataRsp = this.queryCatalogs(request);
        if (cataRsp.getCount() == 0) {
            return BaseRspUtils.createErrorRsp("无章节文字内容，请先生成章节内容");
        }
        boolean isPPT = false; //在ppt生成视频的需求里添加的一个步骤，使用ppt专属的模板生成课程简介
        if (StringUtils.isNotEmpty(course.getExtInfo())) {
            JSONObject obj = JSONObject.parseObject(course.getExtInfo());
            if (obj.get("createType").equals("ppt")) {
                isPPT = true;
            }
        }
        List<TrainCatalogBO> catalogs = cataRsp.getRows();
        JSONObject jsonObject = new JSONObject();
        //加入默认参数
        this.addCommonArgs(request, jsonObject);
        String tdhApi = nbchatTrainConfigProperties.getTdhApi();
        if (video_intro.equals(request.getAutoCreateType())) {
            this.cleanVideoUrl(request);
            jsonObject.put(creationName, this.createName(course.getCourseName(), "简介"));//创建名称
            //创建课程简介模板
            this.createCourseIntroTemp(request, course, jsonObject, isPPT);
            //创建课程目录模板
            this.createCatalogTemp(request, course, catalogs, jsonObject, isPPT);
            if (Objects.equals(request.getPreview(), preview_request)) {
                //页面生成预览，直接保存一条草稿，返回草稿id
                return this.saveCreationRecord(request, jsonObject);
            }
            //发送生成视频请求
            return this.postApi(tdhApi, jsonObject);
        }
        if (video_section.equals(request.getAutoCreateType())) {
            this.cleanVideoUrl(request);
            //创建章节内容模板
            int orderNum = 1;
            for (TrainCatalogBO catalog : catalogs) {
                if (request.getCatalogId().equals(catalog.getCatalogId())) {
                    jsonObject.put(creationName, this.createName(course.getCourseName(), catalog.getCatalogTitle()));//创建名称
                    //创建章节介绍页模板
                    this.createSectionIntro(request, catalog, orderNum, jsonObject);
                    //创建关键点模板
                    this.createSectionKey(request, catalog, jsonObject);
                }
                orderNum++;
            }
            if (Objects.equals(request.getPreview(), preview_request)) {
                //页面生成预览，直接保存一条草稿，返回草稿id
                return this.saveCreationRecord(request, jsonObject);
            }
            //发送生成视频请求
            return this.postApi(tdhApi, jsonObject);
        }
        return BaseRspUtils.createSuccessRsp("未预设此配置模板类型");
    }

    @Override
    public Rsp getTemplate(TdhAutoCreateBO request) {
        log.info("查询模板: {}", request);
        TdhVideoTemp cond = new TdhVideoTemp();
        BeanUtils.copyProperties(request, cond);
        cond.setTenantCode(null);
        cond.setIsValid(Integer.parseInt(EntityValidType.NORMAL.getCode()));
        TdhVideoTemp videoTemp = tdhVideoTempMapper.selectTmp(cond);
        if (!Optional.ofNullable(videoTemp).isPresent()) {
            if (request.getTmpType().equals(section_content_temp_code)) {
                cond.setKeyNum(6);
                videoTemp = tdhVideoTempMapper.selectTmp(cond);
                return BaseRspUtils.createSuccessRsp(videoTemp);
            }
            if (request.getTmpType().equals(course_cata_temp_code)) {
                cond.setCataNum(6);
                videoTemp = tdhVideoTempMapper.selectTmp(cond);
                return BaseRspUtils.createSuccessRsp(videoTemp);
            }
            return BaseRspUtils.createErrorRsp("未匹配到模板");
        }
        return BaseRspUtils.createSuccessRsp(videoTemp);
    }

    @Override
    public Rsp replaceTemp(ReplaceTempBO request) {
        log.info("替换模板:{}", request);
        //获取替换模板
        TdhTemplate cond = new TdhTemplate();
        cond.setReplaceTag(EntityValidType.NORMAL.getCode());
        cond.setTpId(request.getTpId());
        TdhTemplate tdhTemplate = tdhTemplateMapper.selectTemp(cond);
        if (ObjectUtils.isEmpty(tdhTemplate)) {
            return BaseRspUtils.createErrorRsp("该模板非可替换模板");
        }

        TdhVideoTemp tdhVideoTemp = tdhVideoTempMapper.queryById(tdhTemplate.getTmpId());
        String tempContent = tdhVideoTemp.getContent();
        //如果是固定列表-直接替换-返回
        if (CollectionUtils.isNotEmpty(request.getTextList())) {
            return BaseRspUtils.createSuccessRsp("");
        }
        String robotType = sysRobotConfigApi.getRobotValue(request.getTenantCode(), request.getUserId());
        //如果是关键点-调用ai生成-填充返回
        if (StringUtils.isNotEmpty(request.getContent())) {
            JSONObject entity = new JSONObject();
            entity.put(content_, request.getContent());
            entity.put(name_, default_content);
            HashMap<String, String> map = this.parseKeys(entity, robotType);
            JSONObject keyTempRes = this.buildTemplate(map, tempContent);
            JSONObject jsonObject = new JSONObject();
            if (StringUtils.isEmpty(request.getJsonObject())) {
                this.defaultConfig(jsonObject, "1", "1080p", "16:9");
            } else {
                jsonObject = JSONObject.parseObject(request.getJsonObject());
            }
            JSONArray parts = jsonObject.getJSONArray(parts_);
            parts.clear();
            parts.add(keyTempRes);
            return BaseRspUtils.createSuccessRsp(jsonObject);
        }
        return BaseRspUtils.createErrorRsp("参数异常");
    }

    @Override
    public Rsp autoParts(TdhPartsAutoBO request) {
        /*
        创建一个草稿记录，状态2
        调用tdh返回内容和背景图
        调用ai整合内容
        填充模板返回
         */
        log.info("自动创建片段：{}", request);
        TdhCreationRecord rec = new TdhCreationRecord();
        BeanUtils.copyProperties(request, rec);
        rec.setCreationId(IdWorker.nextAutoIdStr());
        rec.setCreationState("2");
        Rsp rsp = this.saveCreationRecord(rec);

        request.setCreationId(rec.getCreationId());
        //new Thread(() -> createParts(request)).start();

        String parts = this.createParts(request);
        return BaseRspUtils.createSuccessRsp(parts);
    }

    @Override
    public Rsp partGenerator(TdhPartGeneratorReqBO request) {
        if (CollectionUtils.isEmpty(request.getParts())) {
            return BaseRspUtils.createSuccessRsp("内容不得为空");
        }
        List<JSONObject> pdfParts = JSONObject.parseArray(JSON.toJSONString(request.getParts()), JSONObject.class);
        String parts = this.buildPartTemp(pdfParts, request.getTenantCode(), request.getUserId());
        return BaseRspUtils.createSuccessRsp(parts);
    }

    public String createParts(TdhPartsAutoBO request) {
        log.info("自动创建片段-任务开始：{}", request);
        //调用接口获取解析结果
        JSONObject body = new JSONObject();
        body.put("url", request.getPdfUrl());
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json;charset=UTF-8");
        String response = HttpClientHelper.doPost(nbchatTrainConfigProperties.getTdhAnalysisPdfUrl(), headers, body.toJSONString(), 120000);
        log.info("自动创建片段-调用tdh接口解析pdf结果:{}", response);
        RspList rspList = JSONObject.parseObject(response, RspList.class);
        if (!rspList.isSuccess()) {
            log.warn("自动创建片段-任务失败，删除草稿：{}", request.getCreationId());
            this.updateCreation(request.getCreationId(), EntityValidType.DELETE.getCode(), "", "");
            return "";
        }
        List<JSONObject> rows = rspList.getRows();
        String partTemp = this.buildPartTemp(rows, request.getTenantCode(), request.getUserId());

        //更新草稿
        this.updateCreation(request.getCreationId(), "", "0", partTemp);
        return partTemp;
    }

    public String buildPartTemp(List<JSONObject> rows, String tenantCode, String userId) {
        //选择横屏、竖屏默认模板
        TdhVideoTemp cond = new TdhVideoTemp();
        cond.setTmpType(parts_content_horizontal_temp_code);
        TdhVideoTemp horizontalTemp = tdhVideoTempMapper.selectTmp(cond);
        cond.setTmpType(parts_content_vertical_temp_code);
        TdhVideoTemp verticalTemp = tdhVideoTempMapper.selectTmp(cond);
        if (ObjectUtils.anyNull(horizontalTemp, verticalTemp)) {
            log.warn("未配置横屏、竖屏片段内容模板");
            return "";
        }
        //判断是横屏竖屏，然后用适配的模板,默认横版
        String content = horizontalTemp.getContent();
        JSONObject jsonObject = JSONObject.parseObject(content);
        JSONObject config = jsonObject.getJSONObject(config_);
        JSONArray jsonArray = jsonObject.getJSONArray(parts_);
        JSONObject part = jsonArray.getJSONObject(0);

        LinkedHashMap<String, TdhPDFAnalysisBO> urlMap = new LinkedHashMap<>();
        StringBuilder strB = new StringBuilder();
        for (JSONObject row : rows) {
            TdhPDFAnalysisBO bo = JSONObject.parseObject(row.toJSONString(), TdhPDFAnalysisBO.class);
            String text = bo.getText();
            String page = bo.getPage();
            String arg = "【P" + page + "】:\"" + text + "\"";
            strB.append(arg).append("\n");
            urlMap.put(page, bo);
        }

        String robotType = sysRobotConfigApi.getRobotValue(tenantCode, userId);
        String partsContentRes = this.getPartsContentRes(strB.toString(), robotType);
        if (StringUtils.isEmpty(partsContentRes)) {
            log.warn("自动创建片段-ai处理数据为空：{}", partsContentRes);
            return "";
        }

        HashMap<String, String> aiResMap = new HashMap<>();
        List<String> split = Arrays.stream(partsContentRes.trim().split("【P")).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        for (String row : split) {
            String num = row.substring(0, row.indexOf("】"));
            String partContent = row.substring(row.indexOf("】") + 1);
            if (StringUtils.isNotEmpty(partContent) && (partContent.charAt(0) == ':' || partContent.charAt(0) == '：')) {
                partContent = partContent.replace(":", "");
                partContent = partContent.replace("：", "");
            }
            aiResMap.put(num, partContent);
        }

        log.info("自动创建片段-待处理页数：{}", split.size());
        //按照pdf页码为准
        JSONArray array = new JSONArray();
        for (Map.Entry<String, TdhPDFAnalysisBO> entry : urlMap.entrySet()) {
            String num = entry.getKey();
            TdhPDFAnalysisBO imageInfo = entry.getValue();
            int w = Integer.parseInt(imageInfo.getW());
            int h = Integer.parseInt(imageInfo.getH());
            String url = imageInfo.getUrl();
            String partContent = aiResMap.get(num);
            String partStr = part.toJSONString();
            //判断横版竖版模板
            if (w < h) {
                //竖版
                part = this.matchTemp(verticalTemp);
                partStr = part.toJSONString();
                Integer y = Integer.parseInt(part.getJSONArray("videos").getJSONObject(0).getString("y"));
                int[] widthHeight = this.getSize(config, null, y, w, h);
                log.debug("竖版-计算图片尺寸大小结果:{}|{}", widthHeight[0], widthHeight[1]);
                partStr = partStr.replace(ElementEnum.image_w.getCode(), widthHeight[0] + "")
                        .replace(ElementEnum.image_h.getCode(), widthHeight[1] + "");
            } else {
                //横版
                Integer x = Integer.parseInt(part.getJSONArray("videos").getJSONObject(0).getString("x"));
                int[] widthHeight = this.getSize(config, x, null, w, h);
                log.debug("横版-计算图片尺寸大小结果:{}|{}", widthHeight[0], widthHeight[1]);
                partStr = partStr.replace(ElementEnum.image_w.getCode(), widthHeight[0] + "")
                        .replace(ElementEnum.image_h.getCode(), widthHeight[1] + "");
            }

            //ai没有生成这一页
            if (StringUtils.isEmpty(partContent)) {
                partStr = partStr
                        .replace(ElementEnum.parts_content.getCode(), "未生成，请补充")
                        .replace(ElementEnum.bg_url.getCode(), url);
                array.add(JSONObject.parseObject(partStr));
            } else {
                partContent = partContent.replaceAll("\"", "\\\\\"");
                partStr = partStr
                        .replace(ElementEnum.parts_content.getCode(), partContent)
                        .replace(ElementEnum.bg_url.getCode(), url);
                array.add(JSONObject.parseObject(partStr));
            }
        }
        jsonArray.clear();
        jsonArray.addAll(array);

        return jsonObject.toJSONString();
    }

    public int[] getSize(JSONObject config, Integer x, Integer y, Integer originW, Integer originH) {
        log.debug("计算图片尺寸大小:{}|{}|{}|{}|{}", config, x, y, originW, originH);
        String resolution = config.getString("resolution");
        String size = config.getString("size");
        if (resolution.equals("1080p")) {
            if (!ObjectUtils.isEmpty(y)) {
                return this.calWH(1920, 1080, originW, originH, null, y);
            }
            if (!ObjectUtils.isEmpty(x)) {
                return this.calWH(1920, 1080, originW, originH, x, null);
            }
        }
        return new int[]{1920, 1080};
    }

    public int[] calWH(Integer standW, Integer standH, Integer originW, Integer originH, Integer x, Integer y) {
        if (!ObjectUtils.isEmpty(x)) {
            int imageW = standW - 2 * x;
            int imageH = imageW * originH / originW;
            return new int[]{imageW, imageH};
        }
        if (!ObjectUtils.isEmpty(y)) {
            int imageH = standH - 2 * y;
            int imageW = imageH * originW / originH;
            return new int[]{imageW, imageH};
        }
        return new int[]{1920, 1080};
    }

    public JSONObject matchTemp(TdhVideoTemp temp) {
        JSONObject jsonObject = JSONObject.parseObject(temp.getContent());
        JSONArray jsonArray = jsonObject.getJSONArray(parts_);
        JSONObject part = jsonArray.getJSONObject(0);
        return part;
    }

    public void cleanVideoUrl(TdhAutoCreateBO request) {
        if (StringUtils.isNotEmpty(request.getCourseId())) {
            if (StringUtils.isNotEmpty(request.getSectionId())) {
                NbchatTrainSections cond = new NbchatTrainSections();
                cond.setSectionId(request.getSectionId());
                cond.setVideoUrl("");
                nbchatTrainSectionsMapper.updateByPrimaryKeySelective(cond);
                return;
            }
            NbchatTrainCourse cond = new NbchatTrainCourse();
            cond.setCourseId(request.getCourseId());
            cond.setVideoUrl("");
            nbchatTrainCourseMapper.updateByPrimaryKeySelective(cond);
        }
    }

    public Rsp saveCreationRecord(TdhAutoCreateBO request, JSONObject jsonObject) {
        TdhCreationRecord save = new TdhCreationRecord();
        BeanUtils.copyProperties(request, save);
        save.setCreationId(IdWorker.nextAutoIdStr());
        save.setCreationName(jsonObject.getString(creationName));
        save.setCreationContent(jsonObject.toJSONString());
        return this.saveCreationRecord(save);
    }

    public Rsp saveCreationRecord(TdhCreationRecord save) {
        int i = tdhCreationRecordMapper.insertSelective(save);
        if (i > 0) {
            // 发送用户维度消息
            trainEventSender.sendUserRpEventByVideoMake(save.getTenantCode(), save.getUserId());
            JSONObject rsp = new JSONObject();
            rsp.put(creationId, save.getCreationId());
            return BaseRspUtils.createSuccessRsp(rsp);
        }
        return BaseRspUtils.createErrorRsp("生成草稿失败");
    }

    public void updateCreation(String sectionId, String isValid, String creationState, String content) {
        TdhCreationRecord cond = new TdhCreationRecord();
        cond.setCreationId(sectionId);
        cond.setIsValid(isValid);
        cond.setCreationState(creationState);
        cond.setCreationContent(content);
        tdhCreationRecordMapper.update(cond);
    }

    public Rsp createCourseIntroTemp(TdhAutoCreateBO request, NbchatTrainCourse course, JSONObject jsonObject, Boolean isPPT) {
        log.info("创建课程简介模板");
        request.setTmpType(course_intro_temp_code);//查询课程介绍页模板
        if (isPPT) {
            request.setCataNum(0);//ppt类目定义为0
        }
        Rsp introTempRsp = this.getTemplate(request);
        if (!introTempRsp.isSuccess()) {
            log.info("课程简介未匹配到模板");
            return introTempRsp;
        }
        String tempConten = ((TdhVideoTemp) introTempRsp.getData()).getContent();
        //替换课程简介页模板参数
        HashMap<String, String> map = new HashMap<>();
        map.put(ElementEnum.course_name.getCode(), course.getCourseName());
        JSONObject introTempRes = this.buildTemplate(map, tempConten);
        JSONArray parts = jsonObject.getJSONArray(parts_);
        parts.add(introTempRes);
        return BaseRspUtils.createSuccessRsp("创建课程简介模板成功");
    }

    public Rsp createCatalogTemp(TdhAutoCreateBO request, NbchatTrainCourse course, List<TrainCatalogBO> catalogs, JSONObject jsonObject, Boolean isPPT) {
        log.info("创建课程目录模板");
        request.setTmpType(course_cata_temp_code);//查询课程目录页模板
        request.setCataNum(catalogs.size());
        if (isPPT) {
            request.setCataNum(0); //ppt类目定义为0
        }
        Rsp catalogTempRsp = this.getTemplate(request);
        if (!catalogTempRsp.isSuccess()) {
            log.info("课程目录未匹配到模板");
            return catalogTempRsp;
        }
        String tempContent = ((TdhVideoTemp) catalogTempRsp.getData()).getContent();
        HashMap<String, String> map = new HashMap<>();
        map.put(ElementEnum.course_desc.getCode(), course.getCourseDesc());
        for (TrainCatalogBO catalog : catalogs) {
            map.put(ElementEnum.course_catalog.concatNum((catalogs.indexOf(catalog) + 1)), catalog.getCatalogTitle());
        }
        JSONObject catalogTempRes = this.buildTemplate(map, tempContent);
        JSONArray parts = jsonObject.getJSONArray(parts_);
        parts.add(catalogTempRes);
        return BaseRspUtils.createSuccessRsp("创建课程目录模板成功");
    }

    public Rsp createSectionIntro(TdhAutoCreateBO request, TrainCatalogBO catalog, Integer sectionNum, JSONObject jsonObject) {
        log.info("自动生成章节介绍页模板");
        request.setTmpType(section_intro_temp_code);//章节介绍页模板
        Rsp sectionTempRsp = this.getTemplate(request);
        if (!sectionTempRsp.isSuccess()) {
            log.info("章节介绍页未匹配到模板");
            return sectionTempRsp;
        }
        String tempContent = ((TdhVideoTemp) sectionTempRsp.getData()).getContent();
        HashMap<String, String> map = new HashMap<>();
        map.put(ElementEnum.section_num.getCode(), sectionNum.toString());
        map.put(ElementEnum.catalog_level_1_name.getCode(), catalog.getCatalogTitle());
        JSONObject sectionTempRes = this.buildTemplate(map, tempContent);
        JSONArray parts = jsonObject.getJSONArray(parts_);
        parts.add(sectionTempRes);
        return BaseRspUtils.createSuccessRsp("创建课程目录模板成功");
    }

    public Rsp createSectionKey(TdhAutoCreateBO request, TrainCatalogBO catalog, JSONObject jsonObject) {
        log.info("自动生成章节关键点模板");
        //获取章节下面的关键点
        NbchatTrainSections section = nbchatTrainSectionsMapper.selectByPrimaryKey(catalog.getSectionId());
        JSONObject sectionObj = JSONObject.parseObject(section.getContent());
        if (ObjectUtils.isEmpty(sectionObj)) {
            return BaseRspUtils.createErrorRsp("章节内容不得为空");
        }
        String robotType = sysRobotConfigApi.getRobotValue(request.getTenantCode(), request.getUserId());
        JSONArray jsonArray = sectionObj.getJSONArray("subtitle");
        for (Object o : jsonArray) {
            JSONObject obj = (JSONObject) o;
            HashMap<String, String> map = new HashMap<>();
            //获取关键点数量
            Integer keyNums = this.parseKeys(obj, map, robotType);
            request.setTmpType(section_content_temp_code);//章节内容页模板
            request.setKeyNum(keyNums);
            Rsp keyTempRsp = this.getTemplate(request);
            if (!keyTempRsp.isSuccess()) {
                log.info("章节介绍页未匹配到模板");
                return keyTempRsp;
            }
            String tempContent = ((TdhVideoTemp) keyTempRsp.getData()).getContent();
            JSONObject keyTempRes = this.buildTemplate(map, tempContent);
            JSONArray parts = jsonObject.getJSONArray(parts_);
            parts.add(keyTempRes);
        }
        return BaseRspUtils.createSuccessRsp("创建课程目录模板成功");
    }

    public HashMap<String, String> parseKeys(JSONObject entity, String robotType) {
        HashMap<String, String> map = new HashMap<>();
        this.parseKeys(entity, map, robotType);
        return map;
    }

    /**
     * 查询所有二级目录内容关键点-描述
     *
     * @param
     * @return
     */
    public Integer parseKeys(JSONObject entity, HashMap<String, String> map, String robotType) {
        String content = entity.getString(content_);
        String name = entity.getString(name_);
        map.put(ElementEnum.catalog_level_2_content.getCode(), content);
        map.put(ElementEnum.catalog_level_2_name.getCode(), name);
        String param = name + "\n" + content;
        JSONArray keys = this.getRobotKeyRes(param, robotType);
        //根据返回的关键点数量去匹配模板
        Integer keyNum = 0;
        for (Object key : keys) {
            keyNum++;
            JSONObject entity1 = (JSONObject) key;
            map.put(ElementEnum.key.concatNum(keyNum), entity1.getString(title_));
            map.put(ElementEnum.key_content.concatNum(keyNum), entity1.getString(content_));
        }
        return keyNum;
    }

    /**
     * 调用ai获取ppt关键点结果
     *
     * @param args
     * @return
     */
    public JSONArray getRobotKeyRes(String args, String robotType) {
        RobotPromptMessageRequest msgRequest = new RobotPromptMessageRequest();
        msgRequest.setRobotType(robotType);
        msgRequest.setTrim(true);
        msgRequest.setPresetId(AiCourseGeneType.train_ppt_extract.getCode());
        msgRequest.setPresetPrompts(new ArrayList<>(Collections.singletonList(args)));
        Rsp<RobotToolsChatResponse> chatResult = nbchatRobotToolsApi.getChatResult(msgRequest);
        log.info("获取关键点-内容结果:{}", chatResult);
        if (!chatResult.isSuccess()) {
            return new JSONArray();
        }
        String content = chatResult.getData().getContent();
        if (RobotType.ZP_AI.getCode().equals(robotType)) {
            content = content.replaceAll("，", ",");
        }
        if (JSONArray.isValidArray(content)) {
            return JSONArray.parseArray(content);
        }
        log.warn("非法格式json");
        return new JSONArray();
    }

    /**
     * 调用ai整理pdf片段内容
     *
     * @param args
     * @return
     */
    public String getPartsContentRes(String args, String robotType) {
        log.info("整理pdf片段内容-原始数据:{}", args);
        RobotPromptMessageRequest msgRequest = new RobotPromptMessageRequest();
        msgRequest.setRobotType(robotType);
        msgRequest.setTrim(true);
        msgRequest.setPresetId(AiCourseGeneType.tdh_pdf_parts_content.getCode());
        msgRequest.setPresetPrompts(Collections.singletonList(args));
        Rsp<RobotToolsChatResponse> chatResult = nbchatRobotToolsApi.getChatResult(msgRequest);
        log.info("整理pdf片段内容结果:{}", JSON.toJSONString(chatResult));
        if (!chatResult.isSuccess()) {
            return "";
        }
        return chatResult.getData().getContent().trim();
    }

    public RspList<TrainCatalogBO> queryCatalogs(TdhAutoCreateBO request) {
        TrainCatalogQueryReqBO queryCatalog = new TrainCatalogQueryReqBO();
        queryCatalog.setCourseId(request.getCourseId());
        return nbchatTrainSectionApi.getSectionCatalogs(queryCatalog);
    }

    /**
     * 加入默认参数
     */
    public void addCommonArgs(TdhAutoCreateBO request, JSONObject jsonObject) {
        jsonObject.put(creationId, IdWorker.nextAutoIdStr());//creationId
        jsonObject.put(tenantCode, request.getTenantCode());//租户
        jsonObject.put(userId, request.getUserId());
        jsonObject.put(courseId, request.getCourseId());//课程id
        jsonObject.put(sectionId, request.getSectionId());//章节id
        this.defaultConfig(jsonObject, request.getQuality(), request.getResolution(), request.getSize());
    }

    public void defaultConfig(JSONObject jsonObject, String quality, String resolution, String size) {
        JSONObject config = new JSONObject();
        config.put("quality", quality);
        config.put("resolution", resolution);
        config.put("size", size);
        jsonObject.put("config", config);//配置参数
        jsonObject.put(parts_, new ArrayList<>());
    }

    public Rsp postApi(String url, JSONObject jsonObject) {
        log.info("请求模板：{}", jsonObject.toJSONString());
        String response = restApiHelper.post(url, jsonObject.toJSONString());
        log.info("调用数字人生成接口-返回结果:{}", response);
        return JSON.parseObject(response, Rsp.class);
    }

    public static JSONObject buildTemplate(Map<String, String> maps, String content) {
        log.info("开始构建模板：{}|{}", maps, content);
        for (Map.Entry<String, String> entry : maps.entrySet()) {
            String value = entry.getValue();
            String s = value.replaceAll("\\\\", "").replaceAll("\"", "\\\\\"");
            content = content.replaceAll(entry.getKey(), Matcher.quoteReplacement(s));
            //是用来确保字符串value中的文本在替换过程中不被视为正则表达式的特殊字符
        }

        content = content.replaceAll("#(\\w+)#", default_content);
        JSONObject jsonObject = JSONObject.parseObject(content);
        log.info("模板内容:{}", jsonObject.toJSONString());
        return jsonObject;
    }

}
