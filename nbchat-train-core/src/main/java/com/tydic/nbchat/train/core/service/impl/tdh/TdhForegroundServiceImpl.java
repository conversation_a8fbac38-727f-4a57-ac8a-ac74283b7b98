package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.tdh.*;
import com.tydic.nbchat.train.api.tdh.TdhForegroundApi;
import com.tydic.nbchat.train.mapper.TdhForegroundMapper;
import com.tydic.nbchat.train.mapper.po.TdhForeground;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.IntStream;

/**
 * 数字人：前景管理相关接口
 */
@Slf4j
@Service
public class TdhForegroundServiceImpl implements TdhForegroundApi {

    @Resource
    TdhForegroundMapper tdhForegroundMapper;

    @Override
    public RspList<TdhForegroundQueryRspBO> getForegroundList(TdhForegroundQueryReqBO reqBO) {
        TdhForeground cond = new TdhForeground();
        BeanUtils.copyProperties(reqBO,cond);
        cond.setTenantCode(null);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        cond.setTpState(StateEnum.STATE.AVAILABLE.getCode());
        Page<TdhForeground> info = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        tdhForegroundMapper.selectAll(cond);

        List<TdhForeground> result = info.getResult();
        ArrayList<TdhForegroundQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result,rspList, TdhForegroundQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspList,info.getTotal());
    }

    @Override
    public RspList save(TdhForegroundQueryReqBO reqBO) {
        List<TdhForegroundQueryRspBO> rspList = new ArrayList<>();
        TdhForeground cond = new TdhForeground();
        BeanUtils.copyProperties(reqBO,cond);
        reqBO.getUrlList().forEach(urlBO -> {
            cond.setObjectId(urlBO.getObjectId());
            cond.setName(urlBO.getName());
            cond.setCategory(urlBO.getCategory());
            cond.setObjectUrl(urlBO.getObjectUrl());
            cond.setObjectType(urlBO.getObjectType());
            cond.setCreateTime(new Date());
            cond.setObjectSource(StateEnum.SOURCE.SELF.getCode());
            cond.setTpState(StateEnum.STATE.AVAILABLE.getCode());
            cond.setIsValid(reqBO.getIsValid());
            cond.setTag(urlBO.getTag());
            cond.setSloop(urlBO.getSloop());
            cond.setObjectConfig(urlBO.getObjectConfig());
            cond.setThumbnail(urlBO.getThumbnail());
            cond.setPreviewUrl(urlBO.getPreviewUrl());
            cond.setDuration(urlBO.getDuration());
            cond.setOrderIndex(urlBO.getOrderIndex());
            if (StringUtils.isNotEmpty(urlBO.getObjectId())) {
                TdhForeground tdhForegroundPo=tdhForegroundMapper.queryById(urlBO.getObjectId());
                if(tdhForegroundPo!=null && StateEnum.SOURCE.SELF.getCode().equals(tdhForegroundPo.getObjectSource())){
                    cond.setObjectSource(null);
                    log.info("数字人面板-更新前景：{}",cond);
                    tdhForegroundMapper.update(cond);
                }
            }else {
                cond.setObjectId(IdWorker.nextAutoIdStr());
                tdhForegroundMapper.insertSelective(cond);
                log.info("数字人面板-保存前景：{}",cond);
            }
            TdhForegroundQueryRspBO rsp = new TdhForegroundQueryRspBO();
            BeanUtils.copyProperties(cond,rsp);
            rspList.add(rsp);
        });
        return BaseRspUtils.createSuccessRspList(rspList,rspList.size());
    }

    @Override
    public RspList saveAdmin(TdhForegroundAdminReqBO reqBO) {
        List<TdhForegroundQueryRspBO> rspList = new ArrayList<>();
        TdhForeground cond = new TdhForeground();
        BeanUtils.copyProperties(reqBO,cond);
        //权限设置默认值
        setDefaultValues(cond, reqBO);
        if (StringUtils.isNotEmpty(reqBO.getObjectId())) {
            if (StringUtils.isBlank(reqBO.getTargetTenant())) {
                cond.setTenantCode(null);
                cond.setUserId(null);
                cond.setObjectSource(null);
            }
            log.info("运营配置-片头片尾配置更新：{}", cond);
            tdhForegroundMapper.update(cond);
        }else {
            cond.setObjectId(IdWorker.nextAutoIdStr());
            cond.setCreateTime(new Date());
            cond.setObjectType(StringUtils.isNotBlank(reqBO.getObjectType())?reqBO.getObjectType():".png");
            tdhForegroundMapper.insertSelective(cond);
            log.info("运营配置-片头片尾配置保存：{}",cond);
        }
        TdhForegroundQueryRspBO rsp = new TdhForegroundQueryRspBO();
        BeanUtils.copyProperties(cond,rsp);
        rspList.add(rsp);

        return BaseRspUtils.createSuccessRspList(rspList,rspList.size());
    }

    private void setDefaultValues(TdhForeground cond, TdhForegroundAdminReqBO reqBO) {
        String targetTenant = Optional.ofNullable(reqBO.getTargetTenant())
                .filter(StringUtils::isNotBlank)
                .orElse(reqBO.getTenantCode());
            if (StateEnum.SOURCE.PUBLIC.getCode().equals(reqBO.getTargetTenant()) ||StateEnum.SOURCE.SYSTEM.getCode().equals(reqBO.getObjectSource())) {
                // 公共
                cond.setUserId(StateEnum.SOURCE.PUBLIC.getCode());
                cond.setTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
                cond.setObjectSource(StateEnum.SOURCE.SYSTEM.getCode());
                cond.setTpState(StringUtils.isNotBlank(reqBO.getTpState())?reqBO.getTpState():StateEnum.STATE.UNAVAILABLE.getCode());
            } else {
                // 租户自定义
                cond.setUserId(targetTenant);
                cond.setTenantCode(targetTenant);
                cond.setObjectSource(StateEnum.SOURCE.SELF.getCode());
                cond.setTpState(StringUtils.isNotBlank(reqBO.getTpState())?reqBO.getTpState():StateEnum.STATE.UNAVAILABLE.getCode());
            }
    }

    @Override
    public RspList<TdhForegroundQueryRspBO> getForegroundAdminList(TdhForegroundQueryReqBO reqBO) {
        log.info("运营配置-片头片尾获取：{}",reqBO);
        TdhForeground cond = new TdhForeground();
        BeanUtils.copyProperties(reqBO,cond);
        cond.setUserId(null);
        // 公共
        String targetTenant = Optional.ofNullable(reqBO.getTargetTenant())
                .filter(StringUtils::isNotBlank)
                .orElse(reqBO.getTenantCode());
        if (StateEnum.SOURCE.PUBLIC.getCode().equals(reqBO.getTargetTenant())) {
            // 公共
            cond.setUserId(StateEnum.SOURCE.PUBLIC.getCode());
            cond.setTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
            cond.setObjectSource(StateEnum.SOURCE.SYSTEM.getCode());
        } else {
            // 租户自定义
            cond.setUserId(targetTenant);
            cond.setTenantCode(targetTenant);
            cond.setObjectSource(StateEnum.SOURCE.SELF.getCode());
        }
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        //查询可配置的模板
        Page<TdhForeground> info = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        tdhForegroundMapper.selectAll(cond);
        List<TdhForeground> result = info.getResult();
        ArrayList<TdhForegroundQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result,rspList, TdhForegroundQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspList,info.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp sort(TdhForegroundSortReqBO reqBO) {
        List<TdhForeground> foregroundList = new ArrayList<>();
        IntStream.range(0,reqBO.getObjectIds().size()).forEach(i -> {
            TdhForeground foreground = new TdhForeground();
            foreground.setObjectId(reqBO.getObjectIds().get(i));
            foreground.setOrderIndex(i);
            foregroundList.add(foreground);
        });
        tdhForegroundMapper.updateBatchSelective(foregroundList);
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    @Override
    public Rsp getForeground(TdhForegroundQueryReqBO reqBO) {
        TdhForeground res = tdhForegroundMapper.queryById(reqBO.getObjectId());
        if (res != null) {
            TdhForegroundQueryRspBO rsp = new TdhForegroundQueryRspBO();
            BeanUtils.copyProperties(res, rsp);
            return BaseRspUtils.createSuccessRsp(rsp);
        }
        log.info("未找到该配置或已失效：{}", reqBO.getObjectId());
        return BaseRspUtils.createErrorRsp("未找到该配置项或已失效");
    }
}
