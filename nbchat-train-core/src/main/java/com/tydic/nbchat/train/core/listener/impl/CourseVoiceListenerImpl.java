package com.tydic.nbchat.train.core.listener.impl;

import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.train.api.CourseVoiceListener;
import com.tydic.nbchat.train.api.bo.eums.FileUploadDirEnum;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnError;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnSuccess;
import com.tydic.nbchat.train.core.config.AliTtsConfigProperties;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;

@Service
@Slf4j
public class CourseVoiceListenerImpl implements CourseVoiceListener {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 10000)
    private FileManageService fileManageService;
    @Resource
    AliTtsConfigProperties aliTtsConfigProperties;


    @Override
    public String onSuccess(CourseVoiceOnSuccess onSuccess) {
        String accessUrl = "";
        try {
            String dir = FileUploadDirEnum.TTS_TEMP.getCode();
            if (StringUtils.isNotBlank(onSuccess.getSectionId())) {
                //指定目录
                dir = onSuccess.getSectionId();
            }
            accessUrl = saveRemoteVoice(onSuccess.getAddress(), onSuccess.getFile(), dir);
        } catch (Exception e) {
            log.error("保存tts语音-异常：", e);
        }
        log.info("保存tts语音-完成:{}", accessUrl);
        return accessUrl;
    }

    @Override
    public String onError(CourseVoiceOnError onError) {
        return "";
    }


    public String saveRemoteVoice(String address, String targetDir) {
        return saveRemoteVoice(address, null, targetDir);
    }

    /**
     * 保存远端音频文件
     *
     * @param address
     * @return
     */
    public String saveRemoteVoice(String address, byte[] byteFile, String targetDir) {
        String retUrl = "";
        try {
            String fileName = IdWorker.nextAutoIdStr() + "." + aliTtsConfigProperties.getFormat();
            if (byteFile == null) {
                log.info("保存tts语音-开始: {}| {}/{}", address, targetDir, fileName);
                String filePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
                File file = new File(filePath);
                URL url = new URL(address);
                //修改超时时间
                url.openConnection().setConnectTimeout(20000);
                Files.copy(url.openStream(), Paths.get(file.toURI()));
                byteFile = Files.readAllBytes(Paths.get(filePath));
                if (file.exists()) {
                    file.delete();
                }
            }
            FileUploadRequest uploadRequest = FileUploadRequest.builder().
                    file(byteFile).fileName(fileName)
                    .uploadUser("1").tenantCode("00000000").build();
            if (StringUtils.isNotBlank(targetDir)) {
                uploadRequest.setTargetDir(targetDir);
            }
            RspList<FileManageSaveBO> rspList = fileManageService.fileUploadRequest(uploadRequest);
            if (rspList.isSuccess()) {
                retUrl = rspList.getRows().get(0).getAccessUrl();
            } else {
                log.error("保存tts语音-失败: {},{}", address, retUrl);
            }

        } catch (Exception e) {
            log.error("保存tts语音-异常: {}", address, e);
        }
        return retUrl;
    }

}
