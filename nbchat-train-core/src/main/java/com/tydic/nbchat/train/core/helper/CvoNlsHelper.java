package com.tydic.nbchat.train.core.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.train.api.bo.asr_tts.*;
import com.tydic.nbchat.train.api.bo.eums.AnchorType;
import com.tydic.nbchat.train.core.config.CosyVoiceConfigProperties;
import com.tydic.nicc.dc.boot.starter.http.RestApiHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class CvoNlsHelper implements NlsHelperApi{

    private final CosyVoiceConfigProperties configProperties;
    private final RestApiHelper restApiHelper;

    public CvoNlsHelper(CosyVoiceConfigProperties configProperties, RestApiHelper restApiHelper) {
        this.configProperties = configProperties;
        this.restApiHelper = restApiHelper;
    }


    @Override
    public String anchorConfig() {
        return AnchorType.COSYVOICE.getCode();
    }

    @Override
    public void createAudioTask(String courseId, String sectionId, boolean courseAll) {

    }

    @Override
    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, String voice, boolean async) {
        return null;
    }

    @Override
    public TtsVoiceTaskContext createAudioTask(TtsVoiceTaskRequest request) {
        log.info("语音合成[{}]-任务-开始: {}",anchorConfig(),request);
        String ttsUrl = configProperties.getTtsUrl();
        String text = request.getText();
        TtsVoiceTaskContext ttsVoiceTaskContext = new TtsVoiceTaskContext();
        ttsVoiceTaskContext.setSentences(new ArrayList<>());
        if (StringUtils.isBlank(text)) {
            log.info("语音合成-任务-语音内容不能为空！:{}",request);
            return ttsVoiceTaskContext;
        }
        JSONObject tts = new JSONObject();
        tts.put("tts_name", "cosy_voice_underline");
        tts.put("content", text);
        JSONObject obj = new JSONObject();
        obj.put("action","a2a");
        obj.put("voice_id",request.getVoice());
        tts.put("infer_params_user", obj);
        //hange_speed 调节音速， volume 调节音量，范围和火山tts是一样的
        Integer volume = request.getVolume();
        Integer speech_rate = request.getSpeechRate();
        //音量，范围0.1～3，默认为1
        float volumeRange = NlsHelperApi.convertRange(volume, 1, 0, 100, 0.1f, 2f);
        tts.put("volume", volumeRange);
        //语速，范围0.2～3，默认为1
        float speechRange = NlsHelperApi.convertRange(speech_rate, 1, -500, 500, 0.2f, 2f);
        tts.put("change_speed", speechRange);
        log.info("语音合成[{}]-任务-请求: {}",anchorConfig(),tts);
        try {
            String response = restApiHelper.post(ttsUrl, tts);
            if (JSONObject.isValid(response)) {
                JSONObject result = JSON.parseObject(response);
                Integer code = result.getInteger("code");
                if (code != 0) {
                    log.error("语音合成[{}]-任务-失败: {}|{}",anchorConfig(),tts,result);
                }
                JSONObject data = result.getJSONObject("data");
                Integer duration = data.getInteger("duration");
                String url = data.getJSONObject("save").getString("url");
                JSONArray sentences = data.getJSONArray("sentences");
                List<TtsVoiceResult> ttsVoiceResults = JSONArray.parseArray(sentences.toJSONString(), TtsVoiceResult.class);

                ttsVoiceTaskContext.setDuration(duration);
                ttsVoiceTaskContext.setAudio_address(url);
                ttsVoiceTaskContext.setSentences(ttsVoiceResults);
            } else {
                log.error("语音合成[{}]-任务-失败: {}｜{}",anchorConfig(), tts,request);
            }
        } catch (Exception e) {
            log.error("语音合成[{}]-任务-异常: {}|{}",anchorConfig(), tts,request, e);
        }
        return ttsVoiceTaskContext;
    }

    @Override
    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, boolean async) {
        return null;
    }

    @Override
    public AsrVoiceTaskContext createAsrTask(String courseId, String fileId, String filepath, boolean async) {
        return null;
    }

    @Override
    public AsrVoiceTaskContext createAsrTask(AsrVoiceTaskRequest request) {
        return null;
    }
}
