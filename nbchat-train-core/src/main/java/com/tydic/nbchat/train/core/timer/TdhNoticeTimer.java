package com.tydic.nbchat.train.core.timer;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.train.api.bo.tdh.TdhNoticeTaskBO;
import com.tydic.nbchat.train.api.tdh.TdhNoticeApi;
import com.tydic.nbchat.train.mapper.NameMapper;
import com.tydic.nbchat.train.mapper.TdhCreationTaskMapper;
import com.tydic.nbchat.train.mapper.TdhNoticeTaskMapper;
import com.tydic.nbchat.train.mapper.po.TdhCreationTask;
import com.tydic.nbchat.train.mapper.po.TdhNoticeTask;
import com.tydic.nbchat.user.api.NoticeApi;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nbchat.user.api.bo.eums.SmsTemplateEnum;
import com.tydic.nbchat.user.api.bo.eums.WxTemplateEnum;
import com.tydic.nbchat.user.api.bo.notice.NoticeContext;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@ConditionalOnProperty(
        value = "nbchat-train.config.notice-timer-enable",
        havingValue = "true")
@EnableScheduling
@Component
public class TdhNoticeTimer {

    @Resource
    TdhNoticeTaskMapper tdhNoticeTaskMapper;
    @Resource
    TdhCreationTaskMapper tdhCreationTaskMapper;
    @Resource
    TdhNoticeApi tdhNoticeApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private NoticeApi noticeApi;

    private final RedisHelper redisHelper;

    public static final String TDH_NOTICE_LOCK_KEY = "TDH_NOTICE_LOCK_KEY";
    public static final String TDH_CUSTOM_NOTICE_LOCK_KEY = "TDH_CUSTOM_NOTICE_LOCK_KEY";
    private static final String EVENT_TYPE_1 = "1"; //视频类型
    private static final String EVENT_TYPE_2 = "2";
    private static final String EVENT_TYPE_3 = "3"; //数字人定制完成
    private static final String EVENT_TYPE_4 = "4"; //声音定制完成
    @Autowired
    private NameMapper nameMapper;

    public TdhNoticeTimer(RedisHelper redisHelper) {
        this.redisHelper = redisHelper;
    }


    @Scheduled(fixedRate = 60 * 1000)
    public void run() {
        //加锁
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().
                lockKey(TDH_NOTICE_LOCK_KEY).
                requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity);
        try {
            log.info("监听视频制作完成状态任务-开始执行...");
            if (locked) {
                List<TdhNoticeTask> tasks = tdhNoticeTaskMapper.selectFin();
                for (TdhNoticeTask task : tasks) {
                    TdhCreationTask creationTask = tdhCreationTaskMapper.queryById(task.getTaskId());
                    if (ObjectUtils.isEmpty(creationTask)) {
                        log.info("监听到任务不存在 id：{}", task.getTaskId());
                        continue;
                    }
                    if (creationTask.getTaskState().equals("1")) {
                        if (StringUtils.isNotEmpty(creationTask.getTenantCode())) {
                            Boolean flag = redisHelper.sHasKey(RedisConstants.NBCHAT_USER_NOTICE_BLANK_LIST_KEY, creationTask.getTenantCode());
                            if (flag) {
                                log.info("租户已在灰度名单中:{}", creationTask.getTenantCode());
                                return;
                            }
                        }

                        TdhNoticeTaskBO request = TdhNoticeTaskBO.builder().userId(creationTask.getUserId())
                                .taskId(creationTask.getTaskId()).smsTempCode(SmsTemplateEnum.VIDEO_DKZN_FINISH.getTemplateID())
                                .build();
                        //发送短信通知
                        Rsp send = this.sendSMS(request);
                        if (send.isSuccess()) {
                            task.setNoteResult(JSON.toJSONString(send));
                            task.setNoteState("1");
                            task.setUpdateTime(new Date());
                            tdhNoticeTaskMapper.update(task);
                        }
                        //发送公众号通知
                        this.sendNotice(creationTask);
                    }
                }
            }
        } catch (Exception e) {
            log.error("监听视频制作完成状态任务,执行异常:{}", redisLockEntity, e);
        } finally {
            if (locked) {
                redisHelper.unlockLua(redisLockEntity);
            }
        }
    }

    @Scheduled(fixedRate = 60 * 1000)
    public void runCustom() {
        //加锁
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().
                lockKey(TDH_CUSTOM_NOTICE_LOCK_KEY).
                requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity);
        try {
            log.info("监听定制完成状态任务-开始执行...");
            if (locked) {
                List<TdhNoticeTask> tasks = tdhNoticeTaskMapper.selectCustFin();
                for (TdhNoticeTask task : tasks) {
                    Boolean flag = redisHelper.sHasKey(RedisConstants.NBCHAT_USER_NOTICE_BLANK_LIST_KEY, task.getTenantCode());
                    if (flag) {
                        log.info("租户已在灰度名单中:{}", task.getTenantCode());
                        return;
                    }
                    TdhNoticeTaskBO request = TdhNoticeTaskBO.builder().userId(task.getUserId()).taskId(null).build();
                    if (EVENT_TYPE_3.equals(task.getEventType())) {
                        request.setSmsTempCode(SmsTemplateEnum.HUMAN_CUSTOMIZATION_COMPLETED.getTemplateID());
                    } else if (EVENT_TYPE_4.equals(task.getEventType())) {
                        request.setSmsTempCode(SmsTemplateEnum.VOICE_CUSTOMIZATION_COMPLETED.getTemplateID());
                    }
                    //发送短信通知
                    Rsp send = this.sendSMS(request);
                    if (send.isSuccess()) {
                        task.setNoteState("1");
                    } else {
                        task.setNoteState("2");
                    }
                    task.setUpdateTime(new Date());
                    task.setNoteResult(JSON.toJSONString(send));
                    tdhNoticeTaskMapper.update(task);

                    //发送公众号通知
                    String name = nameMapper.queryCustomizeRecord(task.getTaskId());
                    TdhCreationTask creationTask = new TdhCreationTask();
                    creationTask.setCreationName(name);
                    creationTask.setEndTime(task.getCreateTime());
                    creationTask.setUserId(task.getUserId());
                    creationTask.setEventType(task.getEventType());
                    creationTask.setPlayUrl("");
                    this.sendNotice(creationTask);
                }
            }
        } catch (Exception e) {
            log.error("监听定制完成状态任务,执行异常:{}", redisLockEntity, e);
        } finally {
            if (locked) {
                redisHelper.unlockLua(redisLockEntity);
            }
        }
    }

    public Rsp sendSMS(TdhNoticeTaskBO request) {
        try {
            return tdhNoticeApi.sendNotice(request);
        } catch (Exception e) {
            log.error("发送短信异常：", e);
            return BaseRspUtils.createErrorRsp("发送短信异常");
        }
    }

    public void sendNotice(TdhCreationTask creationTask) {
        try {
            List<String> params = new ArrayList<>();
            String name = creationTask.getCreationName();
            if (name.length() > 14) {
                name = name.substring(0, 10) + "...";
            }
            if (ObjectUtils.isEmpty(creationTask.getTaskId())) { //定制通知
                if (EVENT_TYPE_3.equals(creationTask.getEventType())) {
                    params.add("《" + name + "》数字人形象定制");
                }
                if (EVENT_TYPE_4.equals(creationTask.getEventType())) {
                    params.add("《" + name + "》声音音色复刻");
                }
                params.add("已复刻完成");
                params.add(DateTimeUtil.getTimeShortString(creationTask.getEndTime(), DateTimeUtil.TIME_FORMAT_NORMAL));
            } else { //视频生成完成通知
                params.add("《" + name + "》视频生成");
                params.add("已复刻完成");
                params.add(DateTimeUtil.getTimeShortString(creationTask.getEndTime(), DateTimeUtil.TIME_FORMAT_NORMAL));
            }
            NoticeContext context = new NoticeContext();
            context.setUserId(creationTask.getUserId());
            context.setTemplateType(WxTemplateEnum.VIDEO.getCode());
            context.setParams(params);
            context.setUrl(creationTask.getPlayUrl());
            noticeApi.officialNotice(context);
        } catch (Exception e) {
            log.error("发送公众号通知异常：", e);
        }
    }
}
