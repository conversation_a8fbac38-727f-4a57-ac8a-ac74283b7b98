package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatUserTrainRecordApi;
import com.tydic.nbchat.train.api.bo.course.TranCourseQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TranCourseSaveReqBO;
import com.tydic.nbchat.train.api.bo.course.TranUserCourseQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TranUserCourseSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/user/course")
public class TrainCourseRecordController {
    private final NbchatUserTrainRecordApi nbchatUserTrainRecordApi;

    public TrainCourseRecordController(NbchatUserTrainRecordApi nbchatUserTrainRecordApi) {
        this.nbchatUserTrainRecordApi = nbchatUserTrainRecordApi;
    }

    /**
     * 查询列表
     *
     * @param request
     * @return
     */
    @PostMapping("/list")
    public RspList getUserTrainCourses(@RequestBody TranUserCourseQueryReqBO request) {
        return nbchatUserTrainRecordApi.getUserTrainCourses(request);
    }

    /**
     * 保存对象
     *
     * @param request
     * @return
     */
    @PostMapping("/save")
    public Rsp saveUserTrainRecord(@RequestBody TranUserCourseSaveReqBO request) {
        return nbchatUserTrainRecordApi.saveUserTrainRecord(request);
    }

    /**
     * 收藏课程
     *
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    @PostMapping("/star")
    public Rsp collectCourse(@RequestBody TranUserCourseSaveReqBO request) {
        return nbchatUserTrainRecordApi.collectCourse(request);
    }

}
