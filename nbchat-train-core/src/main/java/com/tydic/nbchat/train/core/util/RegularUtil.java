package com.tydic.nbchat.train.core.util;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegularUtil {
    /**
     * 截取文件名
     * @param urlString
     * @return
     */
    public static String extractFileNameWithoutExtension(String urlString) {
        // 正则表达式，匹配文件名并忽略后缀
        Pattern pattern = Pattern.compile("/([^/]+)\\.[^\\.]+$");
        Matcher matcher = pattern.matcher(urlString);

        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return null;
        }
    }
    /**
     * 从文本内容中提取第一个有效的JSON字符串
     * 支持嵌套JSON对象和JSON数组
     *
     * @param content 可能包含JSON的文本内容
     * @return 提取的JSON字符串，未找到则返回null
     */
    public static String extractJsonString(String content) {
        if (content == null || content.trim().isEmpty()) {
            return null;
        }

        // 查找开始的 { 或 [
        int startObjIndex = content.indexOf('{');
        int startArrIndex = content.indexOf('[');

        // 确定开始位置和对应的结束字符
        int startIndex;
        char startChar, endChar;

        if (startObjIndex >= 0 && (startArrIndex < 0 || startObjIndex < startArrIndex)) {
            startIndex = startObjIndex;
            startChar = '{';
            endChar = '}';
        } else if (startArrIndex >= 0) {
            startIndex = startArrIndex;
            startChar = '[';
            endChar = ']';
        } else {
            return null; // 没有找到JSON结构
        }

        // 查找匹配的闭合括号，处理嵌套结构
        int depth = 0;
        boolean inQuotes = false;
        boolean escape = false;

        for (int i = startIndex; i < content.length(); i++) {
            char c = content.charAt(i);

            if (escape) {
                escape = false;
            } else if (c == '\\') {
                escape = true;
            } else if (c == '"') {
                inQuotes = !inQuotes;
            } else if (!inQuotes) {
                if (c == startChar) {
                    depth++;
                } else if (c == endChar) {
                    depth--;
                    if (depth == 0) {
                        return content.substring(startIndex, i + 1);
                    }
                }
            }
        }

        return null; // 未找到完整的JSON结构
    }

    public static List<String> arryStrParseToList(String input) {
        return input != null ? Arrays.asList(input.replace("[", "").replace("]", "").replace("\"", "").trim().split(",")) : List.of();
    }
}
