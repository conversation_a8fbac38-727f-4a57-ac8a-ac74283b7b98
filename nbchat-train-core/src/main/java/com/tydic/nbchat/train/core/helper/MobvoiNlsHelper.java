package com.tydic.nbchat.train.core.helper;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.train.api.CourseVoiceAsrListener;
import com.tydic.nbchat.train.api.CourseVoiceListener;
import com.tydic.nbchat.train.api.bo.asr_tts.AsrVoiceTaskContext;
import com.tydic.nbchat.train.api.bo.asr_tts.AsrVoiceTaskRequest;
import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceTaskContext;
import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceTaskRequest;
import com.tydic.nbchat.train.api.bo.eums.AnchorType;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnSuccess;
import com.tydic.nbchat.train.core.config.MobvoiConfigProperties;
import com.tydic.nbchat.train.core.util.HttpClientUtil;
import com.tydic.nbchat.train.core.util.SrtToJsonConverter;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;

@Slf4j
@Component
public class MobvoiNlsHelper implements NlsHelperApi{

    private static final String TTS_URL = "https://open.mobvoi.com/api/tts/v1";
    private final MobvoiConfigProperties configProperties;
    private final CourseVoiceListener courseVoiceListener;
    private final CourseVoiceAsrListener courseVoiceAsrListener;

    public MobvoiNlsHelper(MobvoiConfigProperties configProperties,
                           CourseVoiceListener courseVoiceListener,
                           CourseVoiceAsrListener courseVoiceAsrListener) {
        this.configProperties = configProperties;
        this.courseVoiceListener = courseVoiceListener;
        this.courseVoiceAsrListener = courseVoiceAsrListener;
    }

    @Override
    public String anchorConfig() {
        return AnchorType.MOBVOI.getCode();
    }

    @Override
    public void createAudioTask(String courseId, String sectionId, boolean courseAll) {

    }

    @Override
    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, String voice, boolean async) {
        TtsVoiceTaskRequest request = new TtsVoiceTaskRequest();
        request.setCourseId(courseId);
        request.setSectionId(sectionId);
        request.setText(text);
        request.setVoice(voice);
        request.setAsync(async);
        return createAudioTask(request);
    }

    @Override
    public TtsVoiceTaskContext createAudioTask(TtsVoiceTaskRequest request) {
        String taskId = NiccCommonUtil.createMsgId();
        JSONObject tts = new JSONObject();
        long timestamp = System.currentTimeMillis() / 1000;
        String signature = configProperties.getAppKey() + configProperties.getSecretKey() + timestamp;
        tts.put("signature", NiccCommonUtil.stringToMD5(signature));
        tts.put("timestamp", timestamp);
        tts.put("appkey", configProperties.getAppKey());
        tts.put("speaker", request.getVoice());
        tts.put("ignore_limit", true);
        tts.put("gen_srt", true);
        tts.put("audio_type", "wav");
        tts.put("text", request.getText());
        Integer volume = request.getVolume();
        Integer speech_rate = request.getSpeechRate();
        Integer pitch_rate = request.getPitchRate();
        TtsVoiceTaskContext context = new TtsVoiceTaskContext();
        context.setTask_id(taskId);
        context.setSectionId("1");
        context.setCourseId("1");
        /**
         * 合成音量
         * 默认值：1.0
         * 可选值：0.1-1.0
         */
        float volumeRange = NlsHelperApi.convertRange(volume, 1, 0, 100, 0.1f, 1.0f);
        tts.put("volume", volumeRange);
        /**
         * 发音人合成的语速，支持小数点后两位
         * 默认值：1.0
         * 可选值：0.5-2.0
         */
        float speechRange = NlsHelperApi.convertRange(speech_rate, 1, -500, 500, 0.5f, 2f);
        tts.put("speed", speechRange);
        /**
         * 语调参数，参数小于0则语调变低，反之则高
         * 默认值：0
         * 可选值：-10<pitch<10
         */
        float pitchRange = NlsHelperApi.convertRange(pitch_rate, 0, -500, 500, -10f, 10f);
        tts.put("pitch", pitchRange);
        CloseableHttpResponse audioResponse = null;
        CloseableHttpResponse srtResponse = null;
        try {
            log.info("语音合成-任务开始[{}]: {}", this.anchorConfig(), tts.toJSONString());
            audioResponse = HttpClientUtil.doPostJsonStreaming(TTS_URL, tts.toJSONString());
            Header firstHeader = audioResponse.getFirstHeader("Content-Type");
            if (audioResponse.getEntity().isStreaming() &&
                    !firstHeader.getValue().contains("application/json")) {
                // 下载audio文件
                InputStream input = audioResponse.getEntity().getContent();
                byte[] bytes = IOUtils.toByteArray(input);
                //FileUtils.writeByteArrayToFile(new File("/Users/<USER>/Downloads/sample.wav"), bytes);
                //回调写入数据
                final CourseVoiceOnSuccess onSuccess = CourseVoiceOnSuccess.builder().
                        courseId(context.getCourseId()).
                        sectionId(context.getSectionId()).
                        taskId(taskId).file(bytes).build();

                // 下载srt字幕文件
                Header srtHeader = audioResponse.getFirstHeader("srt_address");
                String srtAddress = srtHeader.getValue();
                //System.out.println("srt_address: " + srtAddress);
                srtResponse = HttpClientUtil.getResult(srtAddress, null);
                if (srtResponse != null) {
                    //InputStream srtInput = srtResponse.getEntity().getContent();
                    //byte[] srtBytes = IOUtils.toByteArray(srtInput);
                    //FileUtils.writeByteArrayToFile(new File("/Users/<USER>/Downloads/sample.srt"), srtBytes);
                    String srtContent = EntityUtils.toString(srtResponse.getEntity(), "utf-8");
                    context.setSentences(SrtToJsonConverter.parseSrt(srtContent));
                }
                final TtsVoiceTaskContext finalContext = context;
                Optional.ofNullable(courseVoiceListener).ifPresent(listener ->
                        finalContext.setAudio_address(listener.onSuccess(onSuccess)));
                long end = System.currentTimeMillis() / 1000;
                log.info("语音合成-任务完成[{}]: {}, {} s", this.anchorConfig(), finalContext.getAudio_address(),
                        (end - timestamp));
                return finalContext;
            } else {
                String content = EntityUtils.toString(audioResponse.getEntity(), "utf-8");
                log.error("语音合成-任务失败[{}]: {}, {}",this.anchorConfig(), request, content);
            }
        } catch (Exception e) {
            log.error("语音合成-任务异常[{}]: {}",this.anchorConfig(), request, e);
        } finally {
            try {
                if (audioResponse != null) {
                    audioResponse.close();
                }
                if (srtResponse != null) {
                    srtResponse.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return context;
    }

    @Override
    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, boolean async) {
        TtsVoiceTaskRequest request = new TtsVoiceTaskRequest();
        request.setCourseId(courseId);
        request.setSectionId(sectionId);
        request.setText(text);
        request.setVoice(configProperties.getVoice());
        request.setAsync(async);
        return createAudioTask(request);
    }

    @Override
    public AsrVoiceTaskContext createAsrTask(String courseId, String fileId, String filepath, boolean async) {
        return null;
    }

    @Override
    public AsrVoiceTaskContext createAsrTask(AsrVoiceTaskRequest request) {
        return null;
    }

    public static void main(String[] args) {
        String AppKey = "xx";
        String AppSecret = "xx";
        //测试
        JSONObject tts = new JSONObject();
        long timestamp = System.currentTimeMillis() / 1000;
        String signature = AppKey + AppSecret + timestamp;
        tts.put("signature", NiccCommonUtil.stringToMD5(signature));
        tts.put("timestamp", timestamp);
        tts.put("appkey", AppKey);
        tts.put("speaker","mercury_fatima_24k");
        tts.put("ignore_limit", true);
        tts.put("gen_srt", true);
        tts.put("audio_type", "wav");
        tts.put("text", "لا يهم كم أنت بطيئ طالما أنك لن تتوقف. لا يوجد طريق مختصر إلى مكان يستحق الذهاب إليه. دائما خذ في عين الإعتبيار أن قرار النجاح هو أهم من أي شيئ آخر.");
        /**
         * 发音人合成的语速，支持小数点后两位
         * 默认值：1.0
         * 可选值：0.5-2.0
         */
        tts.put("speed", "1.0");
        /**
         * 合成音量
         * 默认值：1.0
         * 可选值：0.1-1.0
         */
        tts.put("volume", 1.0);
        /**
         * 语调参数，参数小于0则语调变低，反之则高
         * 默认值：0
         * 可选值：-10<pitch<10
         */
        tts.put("pitch", 1.0);
        System.out.println(tts.toJSONString());
        CloseableHttpResponse audioResponse = null;
        CloseableHttpResponse srtResponse = null;
        try {
            audioResponse = HttpClientUtil.doPostJsonStreaming(TTS_URL, tts.toJSONString());
            Header firstHeader = audioResponse.getFirstHeader("Content-Type");
            if (audioResponse.getEntity().isStreaming() &&
                    !firstHeader.getValue().contains("application/json")) {
                // 下载audio文件
                InputStream input = audioResponse.getEntity().getContent();
                byte[] bytes = IOUtils.toByteArray(input);
                FileUtils.writeByteArrayToFile(new File("/Users/<USER>/Downloads/sample.wav"), bytes);
            } else {
                System.out.println(EntityUtils.toString(audioResponse.getEntity(), "utf-8"));
            }

            // 下载srt字幕文件
            Header srtHeader = audioResponse.getFirstHeader("srt_address");
            String srtAddress = srtHeader.getValue();
            System.out.println("srt_address: " + srtAddress);
            srtResponse = HttpClientUtil.getResult(srtAddress, null);
            InputStream input = srtResponse.getEntity().getContent();
            byte[] bytes = IOUtils.toByteArray(input);
            FileUtils.writeByteArrayToFile(new File("/Users/<USER>/Downloads/sample.srt"), bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (audioResponse != null) {
                    audioResponse.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (srtResponse != null) {
                    srtResponse.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
