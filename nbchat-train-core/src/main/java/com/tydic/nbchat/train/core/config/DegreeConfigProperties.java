package com.tydic.nbchat.train.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-train.config.degree-config")
public class DegreeConfigProperties {
    private Map<String, String> projectCode; // tenantCode - projectType
}
