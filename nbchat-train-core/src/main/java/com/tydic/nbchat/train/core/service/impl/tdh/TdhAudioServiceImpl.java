package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.tdh.TdhAudioBO;
import com.tydic.nbchat.train.api.tdh.TdhAudioApi;
import com.tydic.nbchat.train.mapper.TdhAudioMapper;
import com.tydic.nbchat.train.mapper.po.TdhAudio;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class TdhAudioServiceImpl implements TdhAudioApi {

    @Resource
    TdhAudioMapper tdhAudioMapper;

    @Override
    public Rsp save(TdhAudioBO request) {
        log.info("保存背景音乐:{}",request);
        TdhAudio po = new TdhAudio();
        BeanUtils.copyProperties(request,po);
        if (StringUtils.isEmpty(request.getObjectId())) {
            po.setObjectId(IdWorker.nextAutoIdStr());
            po.setCreateTime(new Date());
            po.setObjectSource(StateEnum.SOURCE.SELF.getCode());
            tdhAudioMapper.insertSelective(po);
        } else {
            tdhAudioMapper.update(po);
        }
        return BaseRspUtils.createSuccessRsp(null);
    }

    @Override
    public RspList query(TdhAudioBO request) {
        log.info("查询背景音乐列表:{}",request);
        TdhAudio cond = new TdhAudio();
        BeanUtils.copyProperties(request,cond);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        cond.setTenantCode(null);
        Page<TdhAudio> tdhAudios = PageHelper.startPage(request.getPage(), request.getLimit());
        tdhAudioMapper.selectAll(cond);
        ArrayList<TdhAudioBO> bos = new ArrayList<>();
        NiccCommonUtil.copyList(tdhAudios.getResult(),bos,TdhAudioBO.class);
        return BaseRspUtils.createSuccessRspList(bos,tdhAudios.getTotal());
    }

    @Override
    public Rsp delete(TdhAudioBO request) {
        if (StringUtils.isBlank(request.getObjectId())) {
            return BaseRspUtils.createErrorRsp("objectId不能为空");
        }
        TdhAudio po = new TdhAudio();
        po.setObjectId(request.getObjectId());
        po.setIsValid(EntityValidType.DELETE.getCode());
        int i = tdhAudioMapper.update(po);
        if (i > 0) {
            return BaseRspUtils.createSuccessRsp(request.getObjectId());
        }
        return BaseRspUtils.createErrorRsp("删除失败");
    }
}
