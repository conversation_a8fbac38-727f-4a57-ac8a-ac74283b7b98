package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskDegreeBO;
import com.tydic.nbchat.train.api.trainTask.CreateDegreeApi;
import com.tydic.nbchat.train.api.trainTask.QueryDegreeApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/task/degree")
public class TrainTaskDegreeController {

    private final QueryDegreeApi queryDegreeApi;
    private final CreateDegreeApi createDegreeApi;

    public TrainTaskDegreeController(QueryD<PERSON>ree<PERSON><PERSON> queryDegreeApi, <PERSON>reateDegree<PERSON>pi createDegreeApi) {
        this.queryDegreeApi = queryDegreeApi;
        this.createDegreeApi = createDegreeApi;
    }

    /**
     * 创建证书发放
     * @param request
     * @return
     */
    @PostMapping("create")
    public Rsp create(@RequestBody NbchatTrainTaskDegreeBO request) {
        return createDegreeApi.create(request);
    }

    @PostMapping("make")
    public Rsp createDegree(@RequestBody NbchatTrainTaskDegreeBO request) throws Exception {
        return createDegreeApi.makeDegree(request.getId());
    }

    /**
     * 查询证书发放列表
     * @param request
     * @return
     */
    @PostMapping("list")
    public RspList list(@RequestBody NbchatTrainTaskDegreeBO request) {
        return queryDegreeApi.list(request);
    }

    /**
     * 统计证书发放统计
     * @param request
     * @return
     */
    @PostMapping("analysis")
    public RspList analysis(@RequestBody NbchatTrainTaskDegreeBO request) {
        return queryDegreeApi.analysis(request);
    }

    /**
     * 新的统计证书发放统计
     * @param request
     * @return
     */
    @PostMapping("newAnalysis")
    public RspList newAnalysis(@RequestBody NbchatTrainTaskDegreeBO request) {
        return queryDegreeApi.newAnalysis(request);
    }
}
