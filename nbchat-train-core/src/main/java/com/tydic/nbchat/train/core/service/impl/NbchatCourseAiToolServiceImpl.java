package com.tydic.nbchat.train.core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.tydic.nbchat.admin.api.SysRobotConfigApi;
import com.tydic.nbchat.robot.api.NbchatRobotToolsApi;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nbchat.train.api.NbchatCourseAiToolApi;
import com.tydic.nbchat.train.api.bo.eums.AiCourseGeneType;
import com.tydic.nbchat.train.api.bo.generate.TranCourseGenerateRequest;
import com.tydic.nbchat.train.core.util.TrainCommonUtil;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseTextMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourseText;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class NbchatCourseAiToolServiceImpl implements NbchatCourseAiToolApi {
    @Resource
    private NbchatTrainCourseTextMapper nbchatTrainCourseTextMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3 * 1000)
    private SysRobotConfigApi sysRobotConfigApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 120000)
    private NbchatRobotToolsApi nbchatRobotToolsApi;


    @Override
    public Rsp generateCourseDesc(String courseId) {
        return getChatResult(courseId, AiCourseGeneType.train_course_desc.getCode(),"");
    }

    @Override
    public Rsp generateCourseCatalog(String courseId) {
        return getChatResult(courseId,AiCourseGeneType.train_course_catalog.getCode(),"");
    }

    @Override
    public Rsp generateCourseSections(String courseId,String catalog) {
        return getChatResult(courseId,AiCourseGeneType.train_course_section_all.getCode(),catalog);
    }

    @Override
    public Rsp generate(TranCourseGenerateRequest request) {
        return getChatResult(request);
    }

    private Rsp<RobotToolsChatResponse> getChatResult(String courseId,String presetId,String catalog){
        TranCourseGenerateRequest request = new TranCourseGenerateRequest();
        request.setCourseId(courseId);
        request.setPresetId(presetId);
        request.setCatalog(catalog);
        return getChatResult(request);
    }

    private Rsp<RobotToolsChatResponse> getChatResult(TranCourseGenerateRequest request){
        String presetId = request.getPresetId();
        String catalog = request.getCatalog();
        String courseId = request.getCourseId();
        if(StringUtils.isBlank(request.getCount())){
            request.setCount("5");
        }
        NbchatTrainCourseText courseText = nbchatTrainCourseTextMapper.queryById(courseId);
        if(courseText == null || StringUtils.isBlank(courseText.getText())){
            return BaseRspUtils.createErrorRsp("生成失败:请先提交课程资料!");
        }
        String text = courseText.getText();

        //查询用户的机器人配置
        String robotValue = sysRobotConfigApi.getRobotValue(request.getTenantCode(),request.getUserId());

        List<String> prompts = Lists.newArrayList(text);
        if(AiCourseGeneType.train_course_exam_s_choice.getCode().equals(presetId) ||
                AiCourseGeneType.train_course_scene_dialogue.getCode().equals(presetId)){
            //加入数量参数
            prompts.add(request.getCount());
        }
        if (AiCourseGeneType.prac_course_scene_dialogue.getCode().equals(presetId)) {
            //加入数量参数
            prompts.add(request.getCount());
            //加入课程描述
            prompts.add(request.getDialogueDesc());
            if (!StringUtils.isAnyEmpty(request.getUserRole(), request.getRobotRole())) {
                prompts.add(request.getUserRole());
                prompts.add(request.getRobotRole());
            }

        }
        if(AiCourseGeneType.train_course_section.getCode().equals(presetId) ||
                AiCourseGeneType.train_course_section_all.getCode().equals(presetId) ){
            if(StringUtils.isBlank(catalog)){
                return BaseRspUtils.createErrorRsp("生成失败:章节标题不得为空!");
            }
            //根据课程目录生成
            if(JSONObject.isValidArray(catalog)){
                prompts.add(JSONObject.parseArray(catalog).toString());
            } else if (JSONObject.isValid(catalog)) {
                prompts.add(JSONObject.parseObject(catalog).toString());
            } else {
                prompts.add(catalog);
            }
        }
        RobotPromptMessageRequest messageRequest = new RobotPromptMessageRequest();
        messageRequest.setUserId(courseId);
        messageRequest.setPresetPrompts(prompts);
        messageRequest.setPresetId(presetId);
        messageRequest.setTrim(true);
        messageRequest.setRobotType(robotValue);
        log.info("请求机器人模块:{}|{}|{}",request.getUserId(),presetId,robotValue);
        Rsp<RobotToolsChatResponse> rsp = nbchatRobotToolsApi.getChatResult(messageRequest);
        String jsonStr = "";
        if(rsp.isSuccess()){
            jsonStr = rsp.getData().getContent();
            if(!JSONObject.isValid(jsonStr)){
                //提取json
                jsonStr = TrainCommonUtil.extractJson(jsonStr);
                rsp.getData().setContent(jsonStr);
            }
            if(AiCourseGeneType.train_course_exam_s_choice.getCode().equals(presetId)){
                //处理考试题
                String examContent = jsonStr;
                if(JSONObject.isValidArray(examContent)){
                    JSONArray array = JSONObject.parseArray(examContent);
                    for (int i = 0; i < array.size(); i++) {
                        String answers = array.getJSONObject(i).getString("answers");
                        answers = replaceAnswers(answers);
                        array.getJSONObject(i).put("answers",JSONObject.parseArray(answers));

                        String items = array.getJSONObject(i).getString("items");
                        items = replaceItems(items);
                        array.getJSONObject(i).put("items",JSONObject.parseArray(items));
                    }
                    rsp.getData().setContent(array.toString());
                }
            }
        }
        return rsp;
    }

    private String replaceItems(String items){
        return items.replaceAll("\"A：","\"").
                replaceAll("\"B：","\"").
                replaceAll("\"C：","\"").
                replaceAll("\"D：","\"").
                replaceAll("\"E：","\"");
    }
    private String replaceAnswers(String answers){
        return answers.replaceAll("A","0").
                replaceAll("B","1").
                replaceAll("C","2").
                replaceAll("D","3").
                replaceAll("E","4");

    }
}
