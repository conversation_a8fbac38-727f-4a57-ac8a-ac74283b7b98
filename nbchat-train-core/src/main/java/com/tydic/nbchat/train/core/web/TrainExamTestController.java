package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatExamTestApi;
import com.tydic.nbchat.train.api.bo.eums.TrainCommonState;
import com.tydic.nbchat.train.api.bo.exam.ExamTestCreateReqBO;
import com.tydic.nbchat.train.api.bo.exam.ExamTestResultReqBO;
import com.tydic.nbchat.train.api.bo.exam.ExamTestResultRspBO;
import com.tydic.nbchat.train.api.bo.exam.ExamTestSubmitReqBO;
import com.tydic.nbchat.train.core.service.impl.event.EventPublishFactory;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRecord;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/exam")
public class TrainExamTestController {
    private final NbchatExamTestApi nbchatExamTestApi;

    public TrainExamTestController(NbchatExamTestApi nbchatExamTestApi) {
        this.nbchatExamTestApi = nbchatExamTestApi;
    }

    /**
     * 查询试卷 - 现在先从题库直接抽取
     *
     * @param request
     * @return
     */
    @PostMapping("/user/test/paper")
    public Rsp getExamTestPaper(@RequestBody ExamTestCreateReqBO request) {
        return nbchatExamTestApi.createExamTest(request);
    }

    /**
     * 提交考试
     *
     * @param request
     * @return
     */
    @PostMapping("/user/test/submit")
    public Rsp submitExamTestPaper(@RequestBody ExamTestSubmitReqBO request) {
        Rsp rsp = nbchatExamTestApi.submitExamTestPaper(request);
        if (rsp.isSuccess()) {
            NbchatTrainRecord data = (NbchatTrainRecord) rsp.getData();
            if (TrainCommonState.PASS.getCode().equals(data.getTestPassState())) {
                //发布培训任务完成检测事件
                EventPublishFactory.publishUserCompleteTaskEvent(request.getTenantCode(), request.getUserId());
            }
        }
        ExamTestResultReqBO req = new ExamTestResultReqBO();
        req.setCourseId(request.getCourseId());
        req.setUserId(request.getUserId());
        return getExamTestResult(req);
    }


    @PostMapping("/user/test/result")
    public Rsp<ExamTestResultRspBO> getExamTestResult(@RequestBody ExamTestResultReqBO request) {
        return nbchatExamTestApi.getExamTestResult(request);
    }

}
