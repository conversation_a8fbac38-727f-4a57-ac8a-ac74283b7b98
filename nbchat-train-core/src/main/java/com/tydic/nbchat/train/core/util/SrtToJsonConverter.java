package com.tydic.nbchat.train.core.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceResult;

import java.io.*;
import java.util.*;
import java.util.regex.*;

public class SrtToJsonConverter {

    // 将时间戳转换为毫秒
    private static int timeToMillis(String time) {
        String[] parts = time.split(":|,");
        int h = Integer.parseInt(parts[0]);
        int m = Integer.parseInt(parts[1]);
        int s = Integer.parseInt(parts[2]);
        int ms = Integer.parseInt(parts[3]);
        return h * 3600_000 + m * 60_000 + s * 1000 + ms;
    }

    public static List<TtsVoiceResult> parseSrt(String srtContent) {
        List<TtsVoiceResult> list = new ArrayList<>();
        Pattern pattern = Pattern.compile("(\\d+)\\s+([\\d:,]+)\\s-->\\s([\\d:,]+)\\s+([\\s\\S]*?)(?=\\n\\d+|\\z)", Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(srtContent);

        while (matcher.find()) {
            String start = matcher.group(2).trim();
            String end = matcher.group(3).trim();
            String text = matcher.group(4).replace("\n", "").replace("\r", "").trim();
            list.add(new TtsVoiceResult(timeToMillis(start), timeToMillis(end), text, text,""));
        }
        return list;
    }

    public static String toJson(List<TtsVoiceResult> subtitles) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.enable(SerializationFeature.INDENT_OUTPUT);
        return mapper.writeValueAsString(subtitles);
    }

    public static String toTtsResu(List<TtsVoiceResult> subtitles) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        mapper.enable(SerializationFeature.INDENT_OUTPUT);
        return mapper.writeValueAsString(subtitles);
    }

    // 示例用法
    public static void main(String[] args) throws IOException {
        String srt = new String(java.nio.file.Files.readAllBytes(java.nio.file.Paths.get("/Users/<USER>/Downloads/sample.srt")), "UTF-8");
        List<TtsVoiceResult> subtitles = parseSrt(srt);
        String json = toJson(subtitles);
        System.out.println(json);
    }
}