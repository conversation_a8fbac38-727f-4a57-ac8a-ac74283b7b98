package com.tydic.nbchat.train.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.train.api.NbchatTrainCatalogApi;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBO;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBatchSaveBO;
import com.tydic.nbchat.train.mapper.NbchatTrainCatalogMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSectionsMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog;
import com.tydic.nbchat.train.mapper.po.NbchatTrainSections;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class NbchatTrainCatalogServiceImpl implements NbchatTrainCatalogApi {

    @Resource
    NbchatTrainCatalogMapper nbchatTrainCatalogMapper;
    @Resource
    NbchatTrainCourseMapper nbchatTrainCourseMapper;
    @Resource
    NbchatTrainSectionsMapper nbchatTrainSectionsMapper;

    @Override
    public Rsp save(TrainCatalogBO request) {
        log.info("更新/保存 类目：{}",request);
        NbchatTrainCatalog record = new NbchatTrainCatalog();
        BeanUtils.copyProperties(request,record);
        if (StringUtils.isEmpty(request.getCatalogId())) {
            record.setCatalogId(IdWorker.nextAutoIdStr());
            record.setCreateTime(new Date());
            nbchatTrainCatalogMapper.insertSelective(record);
        } else {
            nbchatTrainCatalogMapper.updateByPrimaryKeySelective(record);
        }
        //更新课程创建进度
        nbchatTrainCourseMapper.updateStepState(request.getCourseId(), StateEnum.STEP.CATEGORY.getCode());
        return BaseRspUtils.createSuccessRsp(record.getCatalogId());
    }

    @Transactional
    @Override
    public Rsp saveBatch(TrainCatalogBatchSaveBO request) {
        log.info("类目批量保存/更新：{}",request);
        List<TrainCatalogBatchSaveBO.Node> catalogs = request.getCatalogs();
        if (CollectionUtils.isEmpty(catalogs)) {
            return BaseRspUtils.createErrorRsp("保存类目、章节不得为空");
        }
        if (StringUtils.isEmpty(request.getCourseId())) {
            return BaseRspUtils.createErrorRsp("课程ID不得为空");
        }
        //删除章节
        List<String> sectionIds = catalogs.stream().map(TrainCatalogBatchSaveBO.Node::getSectionId).collect(Collectors.toList());
        List<NbchatTrainSections> sectionsList = nbchatTrainSectionsMapper.selectBySectionIds(request.getCourseId(), null);
        if (CollectionUtils.isNotEmpty(sectionsList)) {
            List<String> originSectionIds = sectionsList.stream().map(NbchatTrainSections::getSectionId).collect(Collectors.toList());
            List<String> deleteIds = originSectionIds.stream().filter(v -> !sectionIds.contains(v)).collect(Collectors.toList());
            log.info("删除的章节id：{}",deleteIds);
            if (CollectionUtils.isNotEmpty(deleteIds)) {
                nbchatTrainSectionsMapper.delete(deleteIds);
                for (String deleteId : deleteIds) {
                    nbchatTrainCatalogMapper.delete(deleteId);
                }
            }
        }

        //删除类目
        List<NbchatTrainCatalog> trainCatalogs = nbchatTrainCatalogMapper.selectByCourseId(request.getCourseId(), null, null);

        List<String> catalogIds = catalogs.stream().map(TrainCatalogBatchSaveBO.Node::getCatalogId).collect(Collectors.toList());
        List<String> subCollectIds = catalogs.stream().flatMap(v -> v.getCatalog2().stream().map(TrainCatalogBatchSaveBO.Node::getCatalogId)).collect(Collectors.toList());
        catalogIds.addAll(subCollectIds);
        if (CollectionUtils.isNotEmpty(trainCatalogs) && CollectionUtils.isNotEmpty(catalogIds)) {
            List<String> originCatalogIds = trainCatalogs.stream().map(NbchatTrainCatalog::getCatalogId).collect(Collectors.toList());
            List<String> deleteIds = originCatalogIds.stream().filter(v -> !catalogIds.contains(v)).collect(Collectors.toList());
            log.info("删除的目录id：{}",deleteIds);
            for (String deleteId : deleteIds) {
                nbchatTrainCatalogMapper.deleteById(deleteId);
            }
        }

        int index = 1;
        int sectionIndex = 1;
        for (TrainCatalogBatchSaveBO.Node node : catalogs) {
            //处理章节
            NbchatTrainSections section = new NbchatTrainSections();
            section.setTenantCode(request.getTenantCode());
            section.setVideoUrl(node.getVideoUrl());
            section.setContent(node.getSectionContent());
            section.setCourseId(request.getCourseId());
            section.setSectionsIndex((short) sectionIndex ++);
            section.setCreateTime(new Date());
            if (StringUtils.isNotEmpty(node.getSectionId())) {
                section.setSectionId(node.getSectionId());
                nbchatTrainSectionsMapper.updateByPrimaryKeySelective(section);
            } else {
                section.setSectionId(IdWorker.nextAutoIdStr());
                nbchatTrainSectionsMapper.insertSelective(section);
            }
            String sections = JSON.toJSONString(Arrays.asList(section.getSectionId()));
            //处理目录
            NbchatTrainCatalog record = new NbchatTrainCatalog();
            record.setCourseId(request.getCourseId());
            record.setSectionIds(sections);
            record.setCatalogTitle(node.getCatalogName());
            record.setCatalogLevel((short) 1);
            record.setCatalogIndex((short) index ++);
            record.setParentId("-1");
            record.setCreateTime(new Date());
            //处理父目录
            this.processCatalog(node,record);
            String parentCatalogId = record.getCatalogId();
            List<TrainCatalogBatchSaveBO.Node> catalog2 = node.getCatalog2();
            for (TrainCatalogBatchSaveBO.Node node2 : catalog2) {
                record.setSectionIds(null);
                record.setCatalogTitle(node2.getCatalogName());
                record.setCatalogLevel((short) 2);
                record.setCatalogIndex((short) index ++);
                record.setParentId(parentCatalogId);
                //处理子目录
                this.processCatalog(node2,record);
            }
        }
        //更新课程课时
        nbchatTrainCourseMapper.updateClassHour(request.getCourseId());
        //更新课程创建进度
        nbchatTrainCourseMapper.updateStepState(request.getCourseId(), StateEnum.STEP.CONTENT.getCode());
        //自动生成语音
        new Thread(() -> {
            //nlsStrategyInvokeFactory.getApi().createAudioTask(request.getCourseId(), "",false);
            //nlsStrategyInvokeFactory.getApi().createAudioTask(request.getCourseId(), "",true);
        }).start();

        return BaseRspUtils.createSuccessRsp(null);
    }

    public void processCatalog(TrainCatalogBatchSaveBO.Node node,NbchatTrainCatalog record) {
        if (StringUtils.isNotEmpty(node.getCatalogId())) {
            record.setCatalogId(node.getCatalogId());
            record.setIsValid(EntityValidType.NORMAL.getCode());
            nbchatTrainCatalogMapper.updateByPrimaryKeySelective(record);
        } else {
            record.setCatalogId(IdWorker.nextAutoIdStr());
            nbchatTrainCatalogMapper.insertSelective(record);
        }
    }

    public Rsp delete(String courseId) {
        TrainCatalogBO request = new TrainCatalogBO();
        request.setCourseId(courseId);
        return this.delete(request);
    }
    @Override
    public Rsp delete(TrainCatalogBO request) {
        log.info("删除 类目：{}",request);
        if (StringUtils.isEmpty(request.getCourseId())) {
            return BaseRspUtils.createErrorRsp("课程ID不得为空");
        }
        int update = nbchatTrainCatalogMapper.update(request.getCourseId());
        return BaseRspUtils.createSuccessRsp(update);
    }

    @Override
    public RspList query(TrainCatalogBO request) {
        log.info("查询 目录：{}",request);
        if (StringUtils.isEmpty(request.getCourseId())) {
            return BaseRspUtils.createErrorRspList("课程id不得为空");
        }
        List<NbchatTrainCatalog> catalogs = nbchatTrainCatalogMapper.selectByCourseId(request.getCourseId(), null, null);
        return BaseRspUtils.createSuccessRspList(catalogs);
    }


}
