package com.tydic.nbchat.train.core.service.impl;

import com.tydic.nbchat.train.api.NbchatVideoQaRecordApi;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQaRecordBO;
import com.tydic.nbchat.train.mapper.NbchatVideoQaRecordMapper;
import com.tydic.nbchat.train.mapper.po.NbchatVideoQaRecord;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class NbchatVideoQaRecordApiImpl implements NbchatVideoQaRecordApi{

    @Resource
    NbchatVideoQaRecordMapper nbchatVideoQaRecordMapper;

    @Override
    public Rsp save(NbchatVideoQaRecordBO request) {
        log.info("保存视频考试记录:{}",request);
        NbchatVideoQaRecord rec = new NbchatVideoQaRecord();
        BeanUtils.copyProperties(request,rec);
        int i = nbchatVideoQaRecordMapper.insertSelective(rec);
        if (i > 0) {
            return BaseRspUtils.createSuccessRsp(null);
        }
        return BaseRspUtils.createErrorRsp("保存记录失败");
    }

    @Override
    public Rsp query(NbchatVideoQaRecordBO request) {
        log.info("查询视频考试记录:{}",request);

        return null;
    }
}
