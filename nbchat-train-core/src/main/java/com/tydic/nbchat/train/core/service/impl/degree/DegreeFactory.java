package com.tydic.nbchat.train.core.service.impl.degree;

import com.tydic.nbchat.train.api.bo.eums.ProjectType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class DegreeFactory {

    private final List<DegreeApi> degreeApis;

    public DegreeFactory(List<DegreeApi> degreeApis) {
        this.degreeApis = degreeApis;
    }

    public DegreeApi getDegreeApi(String projectType) {
        for (DegreeApi degreeApi : degreeApis) {
            if (degreeApi.projectType().equals(projectType)) {
                return degreeApi;
            }
        }
        throw new RuntimeException("找不到DegreeApi实现接口: projectType=" + projectType);
    }

    public String createDegree(String degreeId, ProjectType type) {
        DegreeApi degreeApi = getDegreeApi(type.getCode());
        String degreePath = null;
        try {
            degreePath = degreeApi.createDegree(degreeId);
        } catch (Exception e) {
            log.error("制作证书失败", e);
        }
        return degreePath;
    }


}
