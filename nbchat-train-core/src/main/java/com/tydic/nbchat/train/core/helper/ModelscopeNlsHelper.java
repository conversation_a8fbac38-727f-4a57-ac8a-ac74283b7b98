package com.tydic.nbchat.train.core.helper;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.train.api.CourseVoiceAsrListener;
import com.tydic.nbchat.train.api.CourseVoiceListener;
import com.tydic.nbchat.train.api.NbchatTrainSectionApi;
import com.tydic.nbchat.train.api.bo.asr_tts.*;
import com.tydic.nbchat.train.api.bo.asr_tts.modelscope.AsrResponse;
import com.tydic.nbchat.train.api.bo.asr_tts.modelscope.TtsResponse;
import com.tydic.nbchat.train.api.bo.eums.AnchorType;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceAsrOnSuccess;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnError;
import com.tydic.nbchat.train.core.config.ModelscopeConfigProperties;
import com.tydic.nbchat.train.core.util.TrainCommonUtil;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSectionsMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourse;
import com.tydic.nbchat.train.mapper.po.NbchatTrainSections;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.HttpClientHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.URL;
import java.util.*;

@Slf4j
@Component
public class ModelscopeNlsHelper implements NlsHelperApi {
    private final ModelscopeConfigProperties configProperties;
    private final CourseVoiceListener courseVoiceListener;
    private final CourseVoiceAsrListener courseVoiceAsrListener;
    private final NbchatTrainSectionApi nbchatTrainSectionApi;
    @Resource
    NbchatTrainCourseMapper nbchatTrainCourseMapper;
    @Resource
    NbchatTrainSectionsMapper nbchatTrainSectionsMapper;

    public ModelscopeNlsHelper(ModelscopeConfigProperties configProperties,
                               CourseVoiceListener courseVoiceListener,
                               CourseVoiceAsrListener courseVoiceAsrListener,
                               NbchatTrainSectionApi nbchatTrainSectionApi) {
        this.configProperties = configProperties;
        this.courseVoiceListener = courseVoiceListener;
        this.courseVoiceAsrListener = courseVoiceAsrListener;
        this.nbchatTrainSectionApi = nbchatTrainSectionApi;
    }


    @Override
    public String anchorConfig() {
        return AnchorType.MODELSCOPE.getCode();
    }

    private TtsResponse buildTtsRequest(String courseId,String text,String voice) {
        JSONObject taskObject = new JSONObject();
        taskObject.put("tts_name", voice);
        taskObject.put("content", text);
        taskObject.put("if_sentences", "true");
        log.info("语音合成[{}]-任务-开始:{},{}", courseId, configProperties.getPrivateTtsApi(), taskObject);
        String result = HttpClientHelper.doPost(configProperties.getPrivateTtsApi(),
                new HashMap<>(), taskObject, 240000);
        log.info("语音合成[{}]-任务-完成:{}", courseId, result);
        if (JSONObject.isValid(result)) {
            return JSONObject.parseObject(result, TtsResponse.class);
        }
        return new TtsResponse();
    }

    @Override
    public void createAudioTask(String courseId, String sectionId, boolean courseAll) {
        if (!courseAll && StringUtils.isNotEmpty(courseId)) {
            NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(courseId);
            createAudioTask(courseId, null, course.getCourseDesc(), true);
        }
        if (StringUtils.isNotEmpty(sectionId)) {
            Rsp<String> rsp = nbchatTrainSectionApi.querySection(sectionId);
            createAudioTask(null, sectionId, rsp.getData(), true);
        }
        if (courseAll && StringUtils.isNotEmpty(courseId)) {
            List<NbchatTrainSections> nbchatTrainSections = nbchatTrainSectionsMapper.selectBySectionIds(courseId, null);
            for (NbchatTrainSections section : nbchatTrainSections) {
                Rsp<String> rsp = nbchatTrainSectionApi.querySection(section.getSectionId());
                createAudioTask(null, section.getSectionId(), rsp.getData(), true);
            }
        }
    }

    @Override
    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, String voice, boolean async) {
        TtsVoiceTaskRequest request = new TtsVoiceTaskRequest();
        request.setCourseId(courseId);
        request.setSectionId(sectionId);
        request.setText(text);
        request.setVoice(voice);
        request.setAsync(async);
        return createAudioTask(request);
    }

    public TtsVoiceTaskContext createAudioTask(TtsVoiceTaskRequest request) {
        String text = request.getText();
        String courseId = request.getCourseId();
        String sectionId = request.getSectionId();
        String voice = request.getVoice();
        String taskId = IdWorker.nextAutoIdStr();
        TtsVoiceTaskContext context = new TtsVoiceTaskContext();
        String err = "";
        try {
            if (StringUtils.isBlank(text)) {
                log.info("语音合成[{}]-任务-语音内容不能为空！:{}", courseId, voice);
                return context;
            }
            text = TrainCommonUtil.removeXMLTags(text).trim();
            TtsResponse response = buildTtsRequest(courseId, text, voice);
            if (response.success()) {
                context.setTask_id(taskId);
                context.setCourseId(courseId);
                context.setSentences(response.getData().getSentences());
                context.setSectionId(sectionId);
                if (StringUtils.isNoneBlank(response.getData().getUrl())) {
                    context.setAudio_address(response.getData().getUrl());
                    return context;
                } else {
                    log.error("语音合成[{}]-任务-下载地址为空！:{}", courseId, response);
                }
            }
            err = "语音合成失败:" + response.getCode();
        } catch (Exception e) {
            log.error("语音合成[{}]-异常:", courseId, e);
            err = e.getMessage();
        }
        final CourseVoiceOnError onError = CourseVoiceOnError.builder().
                sectionId(sectionId).
                taskId(taskId).error(err).build();
        Optional.ofNullable(courseVoiceListener).ifPresent(listener -> listener.onError(onError));
        return context;
    }

    public TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, boolean async) {
        return createAudioTask(courseId, sectionId, text, configProperties.getVoice(), async);
    }

    @Override
    public AsrVoiceTaskContext createAsrTask(String courseId, String fileId, String filepath, boolean async) {
        Long requestTime = System.currentTimeMillis();
        String taskId = IdWorker.nextAutoIdStr();
        String asrApi = configProperties.getPrivateAsrApi();
        AsrVoiceTaskContext context = new AsrVoiceTaskContext();
        String err = "";
        try {
            if (StringUtils.isNotBlank(configProperties.getPrivateAsrApi())) {
                return createPrivateAsrTask(filepath);
            }
            JSONObject taskObject = new JSONObject();
            taskObject.put("audio_in", filepath);
            taskObject.put("enable_sentences", true);
            log.info("录音文件识别[{}]-任务-开始:{}", courseId, taskObject);
            String result = HttpClientHelper.doPost(asrApi, new HashMap<>(), taskObject, 60000);
            log.info("录音文件识别[{}]-任务-完成:{}", courseId, result);
            if (JSONObject.isValid(result)) {
                AsrResponse response = JSONObject.parseObject(result, AsrResponse.class);
                if (response.success()) {
                    context.setTaskId(taskId);
                    context.setRequestId(taskId);
                    context.setCourseId(courseId);
                    context.setRequestTime(requestTime);
                    context.setStatusCode(200);
                    context.setSentences(response.getData().getSentences());
                    CourseVoiceAsrOnSuccess onSuccess = CourseVoiceAsrOnSuccess.builder().
                            courseId(courseId).
                            fileId(fileId).
                            taskContext(context).
                            taskId(taskId).build();
                    Optional.ofNullable(courseVoiceAsrListener).ifPresent(listener -> listener.onSuccess(onSuccess));
                    return context;
                }
            }
            err = result;
        } catch (Exception e) {
            log.error("录音文件识别[{}]-异常:", courseId, e);
            err = e.getMessage();
        }
        final CourseVoiceOnError onError = CourseVoiceOnError.builder().courseId(courseId).fileId(fileId).error(err).build();
        Optional.ofNullable(courseVoiceAsrListener).ifPresent(listener -> listener.onError(onError));
        return context;
    }



    private AsrVoiceTaskContext createPrivateAsrTask(String inputUrl) throws Exception {
        /**
         * {
         *     "msg": "success",
         *     "code": 200,
         *     "data": {
         *         "msg": null,
         *         "asrResult": "喂喂喂喂喂喂喂喂。",
         *         "code": "000000",
         *         "uuid": "6d567b6b-1ef5-42dd-94a6-cc83431fb307"
         *     }
         * }
         */
        String taskId = IdWorker.nextAutoIdStr();
        String url = configProperties.getPrivateAsrApi();
        String sign = configProperties.getPrivateAsrSign();
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("sign",sign);
        //读取url文件
        InputStream inputStream = new URL(inputUrl).openStream();
        HttpEntity entity = HttpClientHelper.buildMultipartEntity("files",inputStream,
                ContentType.APPLICATION_OCTET_STREAM,taskId + ".wav",paramMap);
        String res = HttpClientHelper.doPostFile(url,new HashMap<>(), entity);
        if (JSONObject.isValid(res)) {
            AsrResponse response = JSONObject.parseObject(res, AsrResponse.class);
            if (response.success()) {
                AsrVoiceTaskContext context = new AsrVoiceTaskContext();
                context.setTaskId(taskId);
                context.setRequestId(taskId);
                context.setStatusCode(200);
                List<AsrVoiceResult> sentences = new ArrayList<>();
                AsrVoiceResult result = new AsrVoiceResult();
                result.setText(response.getData().getText());
                result.setBeginTime(0);
                result.setEndTime(100);
                sentences.add(result);
                context.setSentences(sentences);
                return context;
            }
        } else {
            log.error("私有语音识别异常: {},{}",sign,res);
        }
        return new AsrVoiceTaskContext();
    }


    @Override
    public AsrVoiceTaskContext createAsrTask(AsrVoiceTaskRequest request) {
        return createAsrTask(request.getCourseId(),
                request.getFileId(),
                request.getFilePath(), request.isAsync());
    }
}
