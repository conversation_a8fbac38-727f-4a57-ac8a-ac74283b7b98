package com.tydic.nbchat.train.core.web.tdh;

import com.tydic.nbchat.train.api.TdhMaterialPackageApi;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialLibraryQueryBo;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialLibraryRspBo;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 图库素材
 */
@Slf4j
@RestController
@RequestMapping("/train/tdh_material")
public class TdhMaterialLibraryController {
    private final TdhMaterialPackageApi tdhMaterialPackageApi;

    public TdhMaterialLibraryController(TdhMaterialPackageApi tdhMaterialPackageApi) {
        this.tdhMaterialPackageApi = tdhMaterialPackageApi;
    }

    @PostMapping("/library/list")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public RspList<TdhMaterialLibraryRspBo> getMaterialLibraryList(@RequestBody TdhMaterialLibraryQueryBo queryBo) {
        return tdhMaterialPackageApi.getMaterialLibraryId(queryBo);
    }
    /**
     * 根据素材包pkgId批量新增图片素材
     */
    @PostMapping("/library/save")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp<TdhMaterialLibraryRspBo> saveMaterialLibrary(@RequestBody TdhMaterialLibraryQueryBo queryBo) {
        return tdhMaterialPackageApi.saveMaterialLibrary(queryBo);
    }
    /**
     * 用户获取图库素材包和图片素材列表
     */
    @PostMapping("/library/match")
    public RspList<TdhMaterialLibraryRspBo> getMaterialLibraryId(@RequestBody TdhMaterialLibraryQueryBo queryBo) {
        return tdhMaterialPackageApi.getMaterialPackageAndLibrary(queryBo);
    }
}
