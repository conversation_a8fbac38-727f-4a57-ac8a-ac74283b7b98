package com.tydic.nbchat.train.core.service.impl.order;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.admin.api.SysTenantManagementApi;
import com.tydic.nbchat.admin.api.bo.SysTenantManagementReqBO;
import com.tydic.nbchat.admin.api.bo.eum.OrderStatusEnum;
import com.tydic.nbchat.train.api.CustomizeOrderApi;
import com.tydic.nbchat.train.api.bo.eums.CustomizeStatusEnum;
import com.tydic.nbchat.train.api.bo.eums.VipFlagType;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordModifyStatusReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordQueryReqBO;
import com.tydic.nbchat.train.api.tdh.TdhCustomizeRecordOperationsApi;
import com.tydic.nbchat.train.core.busi.TdhAnchorBusiService;
import com.tydic.nbchat.train.core.busi.TdhHumanBusiService;
import com.tydic.nbchat.train.core.busi.TrainEventSender;
import com.tydic.nbchat.train.mapper.NameMapper;
import com.tydic.nbchat.train.mapper.TdhCustomizeRecordMapper;
import com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord;
import com.tydic.nbchat.train.mapper.po.TdhVirtualAnchor;
import com.tydic.nbchat.train.mapper.po.TdhVirtualHuman;
import com.tydic.nbchat.user.api.NoticeApi;
import com.tydic.nbchat.user.api.UserVipApi;
import com.tydic.nbchat.user.api.UserVipRightsApi;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.CustomizeTypeEnum;
import com.tydic.nbchat.user.api.bo.eums.RightsTypeEnum;
import com.tydic.nbchat.user.api.bo.eums.UserVipType;
import com.tydic.nbchat.user.api.bo.notice.NoticeBO;
import com.tydic.nbchat.user.api.bo.vip.NbchatUserVipRightsBO;
import com.tydic.nbchat.user.api.bo.vip.UserVipBO;
import com.tydic.nbchat.user.api.bo.vip.UserVipQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class CustomizeOrderServiceImpl implements CustomizeOrderApi {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}", group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private UserVipRightsApi userVipRightsApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}", group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NoticeApi noticeApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}", group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private UserVipApi userVipApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}", group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private SysTenantManagementApi sysTenantManagementApi;

    @Resource
    private NameMapper nameMapper;
    @Resource
    private TrainEventSender trainEventSender;
    @Resource
    private TdhCustomizeRecordMapper tdhCustomizeRecordMapper;

    private final TdhHumanBusiService tdhHumanBusiService;
    private final TdhAnchorBusiService tdhAnchorBusiService;
    private final TdhCustomizeRecordOperationsApi tdhCustomizeRecordOperationsApi;

    public CustomizeOrderServiceImpl(TdhCustomizeRecordOperationsApi tdhCustomizeRecordOperationsApi, TdhHumanBusiService tdhHumanBusiService, TdhAnchorBusiService tdhAnchorBusiService) {
        this.tdhCustomizeRecordOperationsApi = tdhCustomizeRecordOperationsApi;
        this.tdhHumanBusiService = tdhHumanBusiService;
        this.tdhAnchorBusiService = tdhAnchorBusiService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp createOrder(TdhCustomizeRecordQueryReqBO request) {
        log.info("创建订单:{}", request);

        Integer salePrice = this.getPayPrice(request);

        this.buildCustomRecord(request, salePrice);

        this.saveCustomizeRecord(request);

        return BaseRspUtils.createSuccessRsp(request.getOrderNo());
    }


    public Integer getPayPrice(TdhCustomizeRecordQueryReqBO request) {
        if (ObjectUtils.isNotEmpty(request.getOrderPrice())) {
            return request.getOrderPrice();
        }
        if (!UserAttributeConstants.DEFAULT_APPID.equals(request.getTenantCode())) {
            SysTenantManagementReqBO reqBO = SysTenantManagementReqBO.builder()
                    .targetTenantCode(request.getTenantCode()).build();
            Rsp rsp = sysTenantManagementApi.queryCustomConfig(reqBO);
            if (rsp.isSuccess()) {
                JSONObject data = (JSONObject) rsp.getData();
                Integer price = data.getJSONObject("custom").getJSONObject(request.getCustomizeType()).getInteger("price");
                if (ObjectUtils.isNotEmpty(price)) {
                    return price;
                }
            }
        }
        return nameMapper.querySalePrice(request.getSkuId());
    }

    public void buildCustomRecord(TdhCustomizeRecordQueryReqBO request, Integer salePrice) {
        request.setOrderPrice(salePrice);
        request.setTdhName(StringUtils.left(request.getTdhName(), 10));
        request.setVoiceName(StringUtils.left(request.getVoiceName(), 10));
        request.setOrderNo(IdWorker.nextAutoIdStr());
        if (CustomizeTypeEnum._2D.equalsCode(request.getCustomizeType())) {
            TdhVirtualHuman human = tdhHumanBusiService.addHuman(request);
            request.setTdhId(human.getTdhId());
        } else if (CustomizeTypeEnum._AUDIO.equalsCode(request.getCustomizeType())) {
            TdhVirtualAnchor anchor = tdhAnchorBusiService.addAnchor(request);
            request.setVolcId(anchor.getVoice());
        } else if (CustomizeTypeEnum._25DMTK.equalsCode(request.getCustomizeType())) {
            TdhVirtualHuman human = tdhHumanBusiService.add25d_mtk_human(request);
            request.setTdhId(human.getTdhId());
        } else if (CustomizeTypeEnum._2DGIF.equalsCode(request.getCustomizeType())) {
            TdhVirtualHuman human = tdhHumanBusiService.add2d_gif_human(request);
            request.setTdhId(human.getTdhId());
        }
    }

    public void saveCustomizeRecord(TdhCustomizeRecordQueryReqBO request) {
        TdhCustomizeRecord po = new TdhCustomizeRecord();
        BeanUtils.copyProperties(request, po);
        po.setOrderStatus(OrderStatusEnum.UNPAID.getCode());
        po.setCustomizeStatus(CustomizeStatusEnum.ORDER_CREATE.getCode());
        po.setUpdateUser(request.getUserId());
        tdhCustomizeRecordMapper.insertSelective(po);
        // 发送用户维度报表数据
        trainEventSender.sendUserRpEventByTdhCust(po, null);
    }

    @Override
    public Rsp updateRights(NbchatUserVipRightsBO request) {
        log.info("更新用户VIP数字人权益:{}", request);
        if (StringUtils.isEmpty(request.getRightsType())) {
            return BaseRspUtils.createErrorRsp("权益类型不能为空");
        }
        Rsp rsp = userVipRightsApi.deductRights(request);
        if (!rsp.isSuccess()) {
            log.error("更新用户VIP权益失败:{}", request);
            return BaseRspUtils.createErrorRsp("订单处理失败，请联系客服");
        }
        request.setStartTime(new Date());
        request.setEndTime(DateTimeUtil.DateAddYear(1));
        this.handle(request);
        return BaseRspUtils.createSuccessRsp("更新用户VIP权益成功");
    }

    @Override
    public Rsp freeCustomize(NbchatUserVipRightsBO request) {
        log.info("免费定制:{}", request);
        if (!request.getIsFreeExperience()) { //专业会员免费定制
            UserVipQueryReqBO req = new UserVipQueryReqBO();
            req.setTenantCode(request.getTenantCode());
            req.set_userId(request.getUserId());
            Rsp<UserVipBO> userVip = userVipApi.getUserVip(req);
            if (!userVip.isSuccess() || !UserVipType.PROFESSIONAL.getCode().equals(userVip.getData().getVipType())) {
                return BaseRspUtils.createErrorRsp("用户不是专业会员，不享有该权益");
            }
            request.setStartTime(new Date());
            request.setEndTime(userVip.getData().getVipEnd());
            request.setVipFlag(VipFlagType.PROFESSIONAL_VIP.getCode()); // 专业会员
            JSONObject extInfo = new JSONObject();
            extInfo.put("vipFlag", VipFlagType.PROFESSIONAL_VIP.getCode());
            request.setExtInfo(extInfo.toString());
        }
        this.handle(request);
        return BaseRspUtils.createSuccessRsp("免费定制成功");
    }

    private void handle(NbchatUserVipRightsBO request) {
        TdhCustomizeRecord po = new TdhCustomizeRecord();
        po.setOrderNo(request.getOrderNo());
        po.setOrderStatus(OrderStatusEnum.PAY_SUCCESS.getCode());
        po.setCustomizeStatus(CustomizeStatusEnum.CUSTOMIZE_COMPLETED.getCode());
        po.setStartTime(request.getStartTime());
        po.setEndTime(request.getEndTime());
        po.setExtInfo(request.getExtInfo());
        tdhCustomizeRecordMapper.updateOrderStatus(po);

        TdhCustomizeRecordModifyStatusReqBO bo = new TdhCustomizeRecordModifyStatusReqBO();
        bo.setOrderNo(request.getOrderNo());
        bo.setVipFlag(request.getVipFlag());
        bo.setCustomizeStatus(CustomizeStatusEnum.CUSTOMIZE_COMPLETED.getCode());
        bo.setCustomizeType(request.getRightsType());
        if (RightsTypeEnum.TDH.getCode().equals(request.getRightsType())) {
            bo.setCustomizeType(CustomizeTypeEnum._2D.getCode());
        } else if (RightsTypeEnum.AUDIO.getCode().equals(request.getRightsType())) {
            bo.setCustomizeType(CustomizeTypeEnum._AUDIO.getCode());
        }
        tdhCustomizeRecordOperationsApi.modifyStatus(bo);

        NoticeBO notice = new NoticeBO();
        notice.setUserId(request.getUserId());
        notice.setTenantCode(request.getTenantCode());
        notice.setType(NoticeBO.Type.TDH);
        notice.setAction(NoticeBO.Action.CUSTOM);
        notice.setOrderId(request.getOrderNo());
        try {
            noticeApi.notice(notice);
        } catch (Exception e) {
            log.error("更新用户VIP数字人权益-发送通知失败:{}", notice, e);
        }
        // 发送用户维度报表消息
        trainEventSender.sendUserRpEventByTdhCust(po, null);
    }
}
