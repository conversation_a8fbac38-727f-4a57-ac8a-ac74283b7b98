package com.tydic.nbchat.train.core.service.impl.outer;

import com.tydic.nbchat.train.api.OuterCreationResourceApi;
import com.tydic.nbchat.train.api.bo.eums.OuterResourceBizCode;
import com.tydic.nbchat.train.api.outer.OuterResourceBO;
import com.tydic.nbchat.train.api.outer.OuterResourceQueryReqBO;
import com.tydic.nbchat.train.api.outer.OuterResourceSaveReqBO;
import com.tydic.nbchat.train.api.outer.TdhCreationTaskBO;
import com.tydic.nbchat.train.core.busi.TdhCreationBusiService;
import com.tydic.nbchat.train.mapper.PptExamOuterSourceMapper;
import com.tydic.nbchat.train.mapper.po.PptExamOuterSourceWithBLOBs;
import com.tydic.nbchat.train.mapper.po.TdhCreationRecord;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class OuterCreationResourceServiceImpl implements OuterCreationResourceApi {

    @Resource
    private PptExamOuterSourceMapper pptExamOuterSourceMapper;

    private final TdhCreationBusiService tdhCreationBusiService;

    public OuterCreationResourceServiceImpl(TdhCreationBusiService tdhCreationBusiService) {
        this.tdhCreationBusiService = tdhCreationBusiService;
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<String> saveResource(OuterResourceSaveReqBO request) {
        if (StringUtils.isBlank(request.getRequestId())) {
            //新增
            String requestId = IdWorker.nextAutoIdStr();
            request.setRequestId(requestId);
            log.info("创作资源同步-保存:{}", request);
            PptExamOuterSourceWithBLOBs record = new PptExamOuterSourceWithBLOBs();
            record.setRequestId(request.getRequestId());
            record.setTenantCode(request.getTenantCode());
            record.setUserId(request.getUserId());
            record.setSourceUrl(request.getSourceUrl());
            record.setSourceContent(request.getSourceContent());
            record.setCreateTime(new Date());
            pptExamOuterSourceMapper.insertSelective(record);
            return BaseRspUtils.createSuccessRsp(requestId);
        } else {
            log.info("创作资源同步-更新:{}", request);
            //更新
            PptExamOuterSourceWithBLOBs record = new PptExamOuterSourceWithBLOBs();
            record.setRequestId(request.getRequestId());
            record.setResultUrl(request.getResultUrl());
            record.setResultContent(request.getResultContent());
            record.setUpdateTime(new Date());
            record.setBizId(request.getBizId());
            record.setBizCode(request.getBizCode());
            record.setStatus("2");
            int i = pptExamOuterSourceMapper.updateByPrimaryKeySelective(record);
            if (i > 0) {
                return BaseRspUtils.createSuccessRsp(request.getRequestId());
            }
            return BaseRspUtils.createErrorRsp("资源更新失败");
        }
    }

    @Override
    public Rsp<OuterResourceBO> getResource(OuterResourceQueryReqBO request) {
        PptExamOuterSourceWithBLOBs source =
                pptExamOuterSourceMapper.selectByPrimaryKey(request.getRequestId());
        OuterResourceBO outerResourceBO = new OuterResourceBO();
        if (source != null) {
            BeanUtils.copyProperties(source, outerResourceBO);
            String creationId = source.getBizId();
            if (OuterResourceBizCode.tdh_creation.getCode().equals(source.getBizCode())) {
                TdhCreationTaskBO taskBO = tdhCreationBusiService.getTdhTask(source.getBizId());
                outerResourceBO.setTdhTaskResult(taskBO);
                if (StringUtils.isNotBlank(taskBO.getPlayUrl())) {
                    outerResourceBO.setResultContent(taskBO.getCreationName());
                    outerResourceBO.setResultUrl(taskBO.getPlayUrl());
                }
                if (StringUtils.isNotBlank(taskBO.getTaskId())) {
                    creationId = taskBO.getCreationId();
                }
                TdhCreationRecord record = tdhCreationBusiService.getTdhRecord(creationId);
                if (record != null) {
                    outerResourceBO.setLastTime(record.getUpdateTime());
                    outerResourceBO.setThumbUrl(record.getPreviewUrl());
                }
            }
            return BaseRspUtils.createSuccessRsp(outerResourceBO);
        }
        return BaseRspUtils.createErrorRsp("任务不存在");
    }
}
