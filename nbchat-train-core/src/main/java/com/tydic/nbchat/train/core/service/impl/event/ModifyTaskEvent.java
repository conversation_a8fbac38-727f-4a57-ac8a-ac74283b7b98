package com.tydic.nbchat.train.core.service.impl.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

@Getter
@Setter
public class ModifyTaskEvent<T> extends ApplicationEvent {

    private String tenantCode;
    private String taskId;

    public ModifyTaskEvent(T source, String tenantCode, String taskId) {
        super(source);
        this.taskId = taskId;
        this.tenantCode = tenantCode;
    }
}
