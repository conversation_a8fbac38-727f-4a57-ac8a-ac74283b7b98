package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.bo.tdh.TdhPipQueryReqBO;
import com.tydic.nbchat.train.api.tdh.TdhPipApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/tdh/pip")
@AllArgsConstructor
public class TrainTdhPipController {
    private final TdhPipApi tdhPipApi;

    /**
     * 新增画中画记录
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @PostMapping("/add")
    public RspList AddPip(@RequestBody TdhPipQueryReqBO reqBO){
        RspList rsp = tdhPipApi.AddPip(reqBO);
        return rsp;
    }

    /**
     * 修改画中画
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @PostMapping("/update")
    public RspList update(@RequestBody TdhPipQueryReqBO reqBO){
        RspList rsp = tdhPipApi.update(reqBO);
        return rsp;
    }

    /**
     * 查询最近20条上传记录
     * @param @param reqBO
     * @return @return {@link RspList }
     */
    @PostMapping("/queryRecentHistory")
    public RspList queryRecentHistory(@RequestBody TdhPipQueryReqBO reqBO){
        RspList rspList = tdhPipApi.queryRecentHistory(reqBO);
        return rspList;
    }

    /**
     * 查询画中画
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @PostMapping("/query")
    public Rsp query(@RequestBody TdhPipQueryReqBO reqBO){
        Rsp rsp = tdhPipApi.query(reqBO);
        return rsp;
    }

    /**
     * 删除画中画
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @PostMapping("/delete")
    public  Rsp delete(@RequestBody TdhPipQueryReqBO reqBO){
        Rsp rsp = tdhPipApi.delete(reqBO);
        return rsp;
    }
}
