package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.TrainExamQaApi;
import com.tydic.nbchat.train.api.bo.train.NbchatExamQaBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/train/exam")
public class TrainExamQaController {

    @Resource
    TrainExamQaApi trainExamQaApi;


    @PostMapping("/create/qa/paper")
    public Rsp createNewPaper(@RequestBody NbchatExamQaBO request){
        return trainExamQaApi.createNewPaper(request);
    }

    @PostMapping("/get/qa/record")
    public Rsp getExamQaList(@RequestBody NbchatExamQaBO request){
        return trainExamQaApi.getExamQaRecord(request);
    }


    @PostMapping("/save/qa/record")
    public Rsp saveRecord(@RequestBody NbchatExamQaBO request) {
        return trainExamQaApi.saveRecord(request);
    }
}
