package com.tydic.nbchat.train.core.service.impl.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhNoticeTaskBO;
import com.tydic.nbchat.train.api.tdh.TdhNoticeApi;
import com.tydic.nbchat.train.mapper.TdhCreationTaskMapper;
import com.tydic.nbchat.train.mapper.po.TdhCreationTask;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.UserService;
import com.tydic.nbchat.user.api.bo.AuthUserReqBO;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class TdhNoticeServiceImpl implements TdhNoticeApi {

    @Resource
    TdhCreationTaskMapper tdhCreationTaskMapper;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private NbchatUserApi nbchatUserApi;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    UserService userService;

    @Override
    public Rsp sendNotice(TdhNoticeTaskBO request) {
        log.info("要发送通知的任务：{}",request);
        Rsp<NbchatUserInfo> userInfo = nbchatUserApi.getUserInfo(request.getUserId());
        if (!userInfo.isSuccess()) {
            return BaseRspUtils.createErrorRsp("获取用户信息失败");
        }

        String creationName = "";
        if (null != request.getTaskId()) {
            TdhCreationTask creationTask = tdhCreationTaskMapper.queryById(request.getTaskId());
            creationName = creationTask.getCreationName();
        }
        String phone = userInfo.getData().getPhone();
        AuthUserReqBO reqBO = AuthUserReqBO.builder().smsTempCode(request.getSmsTempCode())
                .keyword(creationName).phone(phone).build();

        return userService.sendSMSCommon(reqBO);
    }
}
