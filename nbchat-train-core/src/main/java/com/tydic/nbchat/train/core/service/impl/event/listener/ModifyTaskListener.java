package com.tydic.nbchat.train.core.service.impl.event.listener;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskBO;
import com.tydic.nbchat.train.core.service.impl.event.DetectUserDegreeService;
import com.tydic.nbchat.train.core.service.impl.event.ModifyTaskEvent;
import com.tydic.nbchat.train.core.service.impl.trainTask.QueryUserService;
import com.tydic.nbchat.train.mapper.NbchatTrainTaskMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ModifyTaskListener implements ApplicationListener<ModifyTaskEvent> {

    @Resource
    NbchatTrainTaskMapper nbchatTrainTaskMapper;

    private final DetectUserDegreeService detectUserDegreeService;
    private final QueryUserService queryUserService;


    public ModifyTaskListener(DetectUserDegreeService detectUserDegreeService, QueryUserService queryUserService) {
        this.detectUserDegreeService = detectUserDegreeService;
        this.queryUserService = queryUserService;
    }

    @Async
    @Override
    public void onApplicationEvent(ModifyTaskEvent event) {
        log.info("监听到【任务修改事件】：{}", JSON.toJSONString(event));
        Integer taskId = Integer.valueOf(event.getTaskId());
        //查询任务，根据部门、岗位、是否包含下级获取用户列表，
        NbchatTrainTask trainTask = nbchatTrainTaskMapper.queryTask(taskId);
        List<String> userIds = queryUserService.getDeptPostUsers(taskId, trainTask.getSupportSubDept(),
                trainTask.getPostId(), trainTask.getTenantCode());
        if (CollectionUtils.isEmpty(userIds)) {
            userIds = queryUserService.getUsers(taskId);
        }
        long start = System.currentTimeMillis();
        for (String userId : userIds) {
            NbchatTrainTaskBO request = new NbchatTrainTaskBO();
            request.setTenantCode(event.getTenantCode());
            request.setUserId(userId);
            request.setId(Integer.valueOf(event.getTaskId()));
            detectUserDegreeService.handle(request);
        }
        log.info("监听到【任务修改事件】-处理耗时：{}/ms", System.currentTimeMillis() - start);
    }
}
