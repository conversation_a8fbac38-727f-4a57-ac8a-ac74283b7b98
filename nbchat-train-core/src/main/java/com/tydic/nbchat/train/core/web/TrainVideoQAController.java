package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainVideoQaApi;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQaBO;
import com.tydic.nbchat.train.api.bo.video.VideoExamSubmitReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/video/qa")
public class TrainVideoQAController {

    NbchatTrainVideoQaApi nbchatTrainVideoQaApi;

    TrainVideoQAController(NbchatTrainVideoQaApi nbchatTrainVideoQaApi){
        this.nbchatTrainVideoQaApi = nbchatTrainVideoQaApi;
    }

    @PostMapping("add")
    public Rsp add(@RequestBody NbchatVideoQaBO request){
        return nbchatTrainVideoQaApi.add(request);
    }

    @PostMapping("query")
    public RspList query(@RequestBody NbchatVideoQaBO request){
        return nbchatTrainVideoQaApi.query(request);
    }


    @PostMapping("submit")
    public Rsp submit(@RequestBody VideoExamSubmitReqBO request){
        return nbchatTrainVideoQaApi.submit(request);
    }

    @PostMapping("valid")
    public Rsp testValid(@RequestBody VideoExamSubmitReqBO request){
        log.info("valid success:{}", request);
        return null;
    }

}
