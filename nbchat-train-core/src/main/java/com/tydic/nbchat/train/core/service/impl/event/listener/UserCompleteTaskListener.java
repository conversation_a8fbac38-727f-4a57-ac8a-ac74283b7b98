package com.tydic.nbchat.train.core.service.impl.event.listener;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskBO;

import com.tydic.nbchat.train.core.service.impl.event.DetectUserDegreeService;
import com.tydic.nbchat.train.core.service.impl.event.UserCompleteTaskEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class UserCompleteTaskListener implements ApplicationListener<UserCompleteTaskEvent> {

    private final DetectUserDegreeService detectUserDegreeService;

    public UserCompleteTaskListener(DetectUserDegreeService detectUserDegreeService) {
        this.detectUserDegreeService = detectUserDegreeService;
    }

    @Async
    @Override
    public void onApplicationEvent(UserCompleteTaskEvent event) {
        log.info("监听到【证书触点事件】-：{}", JSON.toJSONString(event));
        NbchatTrainTaskBO request = new NbchatTrainTaskBO();
        request.setTenantCode(event.getTenantCode());
        request.setUserId(event.getUserId());
        request.setStartStatus("1"); //上架任务状态
        long start = System.currentTimeMillis();
        detectUserDegreeService.handle(request);
        log.info("【证书触点事件】-处理耗时：{}/ms", System.currentTimeMillis() - start);
    }
}
