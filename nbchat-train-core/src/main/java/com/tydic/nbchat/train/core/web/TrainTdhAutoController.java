package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.bo.tdh.*;
import com.tydic.nbchat.train.api.tdh.TdhAutoCreateApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/tdh")
public class TrainTdhAutoController {

    private final TdhAuto<PERSON>reate<PERSON>pi tdhAutoCreateApi;


    public TrainTdhAutoController(TdhAuto<PERSON>reate<PERSON>pi tdhAutoCreateApi) {
        this.tdhAutoCreateApi = tdhAutoCreateApi;
    }

    @Deprecated
    @PostMapping("video/produce/auto")
    public Rsp autoCreate(@RequestBody TdhAutoCreateBO request) {
        if (ObjectUtils.isEmpty(request.getAutoCreateType())) {
            return BaseRspUtils.createErrorRsp("模板类型不得为空");
        }
        if (tdhAutoCreateApi.video_intro.equals(request.getAutoCreateType()) && StringUtils.isEmpty(request.getCourseId())) {
            return BaseRspUtils.createErrorRsp("课程ID不得为空");
        }
        if (tdhAutoCreateApi.video_section.equals(request.getAutoCreateType()) && StringUtils.isEmpty(request.getCatalogId())) {
            return BaseRspUtils.createErrorRsp("目录ID不得为空");
        }
        return tdhAutoCreateApi.autoCreate(request);
    }


    @PostMapping("/template/replace")
    public Rsp replaceTemp(@RequestBody ReplaceTempBO request){
        if (StringUtils.isEmpty(request.getTpId())) {
            return BaseRspUtils.createErrorRsp("模板id不得为空");
        }
        return tdhAutoCreateApi.replaceTemp(request);
    }

    @PostMapping("parts/auto")
    public Rsp autoParts(@RequestBody TdhPartsAutoBO request){
        return tdhAutoCreateApi.autoParts(request);
    }

    @PostMapping("parts/generator")
    public Rsp partGenerator(@RequestBody TdhPartGeneratorReqBO request){
        return tdhAutoCreateApi.partGenerator(request);
    }

}
