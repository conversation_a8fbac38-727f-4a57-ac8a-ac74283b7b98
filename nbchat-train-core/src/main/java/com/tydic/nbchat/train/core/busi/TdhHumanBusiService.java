package com.tydic.nbchat.train.core.busi;

import com.tydic.nbchat.train.api.bo.eums.CustomizeStatusEnum;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanReqBO;
import com.tydic.nbchat.train.mapper.TdhVirtualHumanMapper;
import com.tydic.nbchat.train.mapper.po.TdhVirtualHuman;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class TdhHumanBusiService {

    @Resource
    private TdhVirtualHumanMapper virtualHumanMapper;

    public TdhVirtualHuman addHuman(TdhCustomizeRecordQueryReqBO request) {
        TdhVirtualHuman human = new TdhVirtualHuman();
        BeanUtils.copyProperties(request, human);
        human.setTdhId(IdWorker.nextAutoIdStr());
        human.setTdhType("2d_mtk");
        human.setTdhImgThu(request.getTdhImg());
        human.setCreateTime(new Date());
        human.setTdhSource("1");
        human.setCustomizeStatus(CustomizeStatusEnum.ORDER_CREATE.getCode());
        virtualHumanMapper.insertSelective(human);
        return human;
    }

    public TdhVirtualHuman add25d_mtk_human(TdhCustomizeRecordQueryReqBO request) {
        TdhVirtualHuman human = new TdhVirtualHuman();
        BeanUtils.copyProperties(request, human);
        human.setTdhId(IdWorker.nextAutoIdStr());
        human.setTdhType("2.5d_mtk");
        human.setTdhImgThu(request.getTdhImg());
        human.setCreateTime(new Date());
        human.setTdhSource("1");
        human.setTdhImg("");
        human.setCustomizeStatus(CustomizeStatusEnum.ORDER_CREATE.getCode());
        virtualHumanMapper.insertSelective(human);
        return human;
    }

    public TdhVirtualHuman add2d_gif_human(TdhCustomizeRecordQueryReqBO request) {
        TdhVirtualHuman human = new TdhVirtualHuman();
        BeanUtils.copyProperties(request, human);
        human.setTdhId(IdWorker.nextAutoIdStr());
        human.setTdhType("2d_gif");
        human.setTdhImgThu(request.getTdhImg());
        human.setCreateTime(new Date());
        human.setTdhSource("1");
        human.setCustomizeStatus(CustomizeStatusEnum.ORDER_CREATE.getCode());
        virtualHumanMapper.insertSelective(human);
        return human;
    }

    public void add(TdhVirtualHumanReqBO request) {
        TdhVirtualHuman po = new TdhVirtualHuman();
        BeanUtils.copyProperties(request, po);
        if (StringUtils.isEmpty(request.getTdhId())) {
            virtualHumanMapper.insertSelective(po);
        } else {
            virtualHumanMapper.update(po);
        }
    }

    public void updateHuman(TdhVirtualHumanReqBO request) {
        if (StringUtils.isEmpty(request.getOrderNo())) {
            log.info("更新数字人信息失败，orderNo为空");
            return;
        }
        TdhVirtualHuman human = new TdhVirtualHuman();
        BeanUtils.copyProperties(request, human);
        virtualHumanMapper.updateByOrderNo(human);
    }

}
