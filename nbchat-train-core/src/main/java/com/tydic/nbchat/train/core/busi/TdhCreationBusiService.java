package com.tydic.nbchat.train.core.busi;

import com.tydic.nbchat.train.api.outer.TdhCreationTaskBO;
import com.tydic.nbchat.train.mapper.TdhCreationRecordMapper;
import com.tydic.nbchat.train.mapper.TdhCreationTaskMapper;
import com.tydic.nbchat.train.mapper.po.TdhCreationRecord;
import com.tydic.nbchat.train.mapper.po.TdhCreationTask;
import com.tydic.nicc.common.eums.EntityValidType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class TdhCreationBusiService {

    @Resource
    private TdhCreationTaskMapper tdhCreationTaskMapper;
    @Resource
    private TdhCreationRecordMapper tdhCreationRecordMapper;

    public TdhCreationTaskBO getTdhTask(String taskId) {
        TdhCreationTask task = tdhCreationTaskMapper.selectSimpleByPrimaryKey(taskId);
        TdhCreationTaskBO taskBO = new TdhCreationTaskBO();
        if (task != null) {
            BeanUtils.copyProperties(task, taskBO);
        }
        return taskBO;
    }

    public TdhCreationRecord getTdhRecord(String creationId) {
        return tdhCreationRecordMapper.queryById(creationId, EntityValidType.NORMAL.getCode());
    }
}
