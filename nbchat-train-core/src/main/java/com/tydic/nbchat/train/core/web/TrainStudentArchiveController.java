package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainStudentsArchiveReportApi;
import com.tydic.nbchat.train.api.bo.course.TranStudentArchiveQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description:类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/user/archive")
public class TrainStudentArchiveController {
    @Resource
    NbchatTrainStudentsArchiveReportApi nbchatTrainStudentsArchiveReportApi;

    @PostMapping("/query")
    public Rsp getStudentArchive(@RequestBody TranStudentArchiveQueryReqBO request) {
        return nbchatTrainStudentsArchiveReportApi.getStudentArchive(request);
    }


    @PostMapping("/list")
    public RspList getStudentArchiveList(@RequestBody TranStudentArchiveQueryReqBO request){
        return nbchatTrainStudentsArchiveReportApi.getStudentArchiveList(request);
    }
}
