package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryRspBO;
import com.tydic.nbchat.train.api.tdh.TdhIndexTemplateApi;
import com.tydic.nbchat.train.mapper.TdhIndexTemplateMapper;
import com.tydic.nbchat.train.mapper.po.TdhIndexTemplate;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class TdhIndexTemplateServiceImpl implements TdhIndexTemplateApi {
    private final TdhIndexTemplateMapper tdhIndexTemplateMapper;

    @Override
    public RspList<TdhIndexTemplateQueryRspBO> list(TdhIndexTemplateQueryReqBO request) {
        List<TdhIndexTemplateQueryRspBO> rspBOList = new ArrayList<>();
        TdhIndexTemplate tdhIndexTemplate = new TdhIndexTemplate();
        BeanUtils.copyProperties(request, tdhIndexTemplate);
        tdhIndexTemplate.setTenantCode(null);
        tdhIndexTemplate.setIsValid(EntityValidType.NORMAL.getCode());
        Page<TdhIndexTemplate> page = PageHelper.startPage(request.getPage(), request.getLimit());
        tdhIndexTemplateMapper.selectList(tdhIndexTemplate);
        if (CollectionUtils.isEmpty(page.getResult())) {
           return BaseRspUtils.createSuccessRspList(rspBOList,0L);
        }
        NiccCommonUtil.copyList(page.getResult(), rspBOList, TdhIndexTemplateQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspBOList, page.getTotal());
    }

    @Override
    public Rsp info(TdhIndexTemplateQueryReqBO reqBO) {
        TdhIndexTemplate tdhIndexTemplate = tdhIndexTemplateMapper.selectByPrimaryKey(reqBO.getTplId());
        if (tdhIndexTemplate != null) {
            TdhIndexTemplateQueryRspBO rspBO = new TdhIndexTemplateQueryRspBO();
            BeanUtils.copyProperties(tdhIndexTemplate, rspBO);
            return BaseRspUtils.createSuccessRsp(rspBO,"查询成功");
        }
        return BaseRspUtils.createErrorRsp("查询失败-没有找到对应的模板");
    }
}
