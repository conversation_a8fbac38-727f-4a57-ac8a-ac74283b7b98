package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainSectionApi;
import com.tydic.nbchat.train.api.TrainCourseVideosApi;
import com.tydic.nbchat.train.api.bo.course.TrainVideosQueryReqBO;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @datetime：2024/9/2 14:51
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/train/course/section")
public class TrainCourseVideosController {

    @Resource
    private  TrainCourseVideosApi trainCourseVideosApi;


    @PostMapping("/videos/list")
    public RspList getTrainCourseVideos(@RequestBody TrainVideosQueryReqBO request) {
        return trainCourseVideosApi.getTrainCourseVideos(request);
    }
}
