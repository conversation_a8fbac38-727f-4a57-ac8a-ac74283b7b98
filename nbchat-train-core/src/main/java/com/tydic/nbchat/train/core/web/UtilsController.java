package com.tydic.nbchat.train.core.web;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.tydic.nbchat.train.api.bo.train.SceneDialogueContent;
import com.tydic.nbchat.train.mapper.NbchatTrainCatalogMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainRecordMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSceneDialogueMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainUserRecordMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRecord;
import com.tydic.nbchat.train.mapper.po.NbchatTrainSceneDialogue;
import com.tydic.nbchat.train.mapper.po.NbchatTrainUserRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.awt.*;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/utils")
public class UtilsController {

    @Resource
    NbchatTrainRecordMapper nbchatTrainRecordMapper;
    @Resource
    NbchatTrainCatalogMapper nbchatTrainCatalogMapper;
    @Resource
    NbchatTrainUserRecordMapper nbchatTrainUserRecordMapper;
    @Resource
    NbchatTrainSceneDialogueMapper nbchatTrainSceneDialogueMapper;

    @GetMapping("/fonts")
    public String fonts() {
        GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();
        String[] fontNames = ge.getAvailableFontFamilyNames();
        // 打印所有可用的字体名称
        StringBuilder sb = new StringBuilder();
        for (String fontName : fontNames) {
            sb.append(fontName).append("<br>");
        }
        return sb.toString();
    }

    @PostMapping("studied")
    public void doStudied() {
        List<NbchatTrainCatalog> catalogs = nbchatTrainCatalogMapper.selectByCondition();
        for (NbchatTrainCatalog catalog : catalogs) {
            catalog.setSectionIds(catalog.getSectionIds().replaceAll("\"|\\[|\\]", ""));
        }
        Map<String, String> map = catalogs.stream().collect(Collectors.toMap(NbchatTrainCatalog::getSectionIds, NbchatTrainCatalog::getCatalogId));
        List<NbchatTrainRecord> records = nbchatTrainRecordMapper.selectTrainRecord(null);
        for (NbchatTrainRecord record : records) {
            List<String> sections = JSON.parseArray(record.getTrainSections(), String.class);
            for (String section : sections) {
                String s = map.get(section);
                if (StringUtils.isNotEmpty(s)) {
                    NbchatTrainUserRecord insert = new NbchatTrainUserRecord();
                    insert.setCreateTime(new Date());
                    insert.setUserId(record.getUserId());
                    insert.setCatalogId(s);
                    nbchatTrainUserRecordMapper.insert(insert);
                }
            }
        }
    }

    @PostMapping("importDialogue")
    public String importDialogue(@RequestBody JSONObject obj) {
        String courseId = (String) obj.get("courseId");
        String content = (String) obj.get("content");
        String tenantCode = (String) obj.get("tenantCode");
        List<SceneDialogueContent> dialogueContents =
                JSONObject.parseArray(content, SceneDialogueContent.class);
        for (SceneDialogueContent dialogueContent : dialogueContents) {
            NbchatTrainSceneDialogue dialogue = new NbchatTrainSceneDialogue();
            dialogue.setRole("user");
            dialogue.setContent(dialogueContent.getUser());
            dialogue.setDateTime(new Date());
            dialogue.setCourseId(courseId);
            dialogue.setTenantCode(tenantCode);
            nbchatTrainSceneDialogueMapper.insertSelective(dialogue);

            NbchatTrainSceneDialogue dialogue1 = new NbchatTrainSceneDialogue();
            dialogue1.setRole("assistant");
            dialogue1.setContent(dialogueContent.getAssistant());
            dialogue1.setDateTime(new Date());
            dialogue1.setCourseId(courseId);
            dialogue1.setTenantCode(tenantCode);
            nbchatTrainSceneDialogueMapper.insertSelective(dialogue1);
        }
        return "导入对话流完成";
    }
}
