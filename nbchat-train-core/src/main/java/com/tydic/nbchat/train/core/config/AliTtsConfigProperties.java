package com.tydic.nbchat.train.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-train.config.ali-config.tts")
public class AliTtsConfigProperties {
    private String appKey = "test";
    private String ttsApi = "https://nls-gateway.cn-shanghai.aliyuncs.com/rest/v1/tts/async";
    private String voice = "siyue";
    private String format = "wav";
    private Integer sampleRate = 16000;
    //回调地址
    private String notifyUrl = "";
}
