package com.tydic.nbchat.train.core.service.impl.trainTask;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.task.NbchatTaskRecordBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskDegreeBO;
import com.tydic.nbchat.train.api.trainTask.QueryDegreeApi;
import com.tydic.nbchat.train.core.service.impl.degree.zhongjiao.ZhongjiaoDegreeService;
import com.tydic.nbchat.train.mapper.*;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskDegree;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.tydic.nbchat.train.api.trainTask.TrainTaskApi.TRAIN_TASK_FINISH;


@Slf4j
@Service
public class QueryDegreeServiceImpl implements QueryDegreeApi {

    @Resource
    NbchatTrainTaskDegreeMapper nbchatTrainTaskDegreeMapper;
    @Resource
    TrainTaskServiceImpl trainTaskService;
    @Resource
    NameMapper nameMapper;


    @Override
    public RspList list(NbchatTrainTaskDegreeBO request) {
        log.info("查询证书请求参数：{}", request);
        NbchatTrainTaskDegree po = new NbchatTrainTaskDegree();
        BeanUtils.copyProperties(request, po);
        po.setUserId(null);
        if (StringUtils.isNotEmpty(request.getChannelCode()) && "userClient".equals(request.getChannelCode())) {
            po.setUserId(request.getUserId());
        }
        Page<NbchatTrainTaskDegree> page = PageHelper.startPage(request.getPage(), request.getLimit());
        nbchatTrainTaskDegreeMapper.selectAll(po);
        List<NbchatTrainTaskDegree> result = page.getResult();
        List<NbchatTrainTaskDegreeBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(result, list, NbchatTrainTaskDegreeBO.class);
        for (NbchatTrainTaskDegreeBO degreeBO : list) {
            if (StringUtils.isNotEmpty(degreeBO.getDeptId())) {
                String deptName = nameMapper.queryOrganizeName(degreeBO.getDeptId());
                degreeBO.setDeptName(deptName);
            }
            if (StringUtils.isNotEmpty(degreeBO.getPostId())) {
                String postName = nameMapper.queryPostName(degreeBO.getPostId());
                degreeBO.setPostName(postName);
            }
            String desc = nameMapper.queryTaskFiled(degreeBO.getTaskId());
            degreeBO.setDegreeDesc(desc);
        }
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    @Override
    public RspList analysis(NbchatTrainTaskDegreeBO request) {
        log.info("证书统计请求参数：{}", request);
        Page<NbchatTrainTaskDegree> page = PageHelper.startPage(request.getPage(), request.getLimit());
        nbchatTrainTaskDegreeMapper.analysis(request.getTenantCode(), request.getDeptId(), request.getDegreeName());
        List<NbchatTrainTaskDegreeBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), list, NbchatTrainTaskDegreeBO.class);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    /**
     * 新的证书统计方法
     *
     * @param request
     * @return
     */
    @Override
    public RspList newAnalysis(NbchatTrainTaskDegreeBO request) {
        log.info("新证书统计请求参数：{}", request);
        long time = new Date().getTime();
        Page<NbchatTrainTaskDegree> page = PageHelper.startPage(request.getPage(), request.getLimit());
        NbchatTrainTaskDegree cond = new NbchatTrainTaskDegree();
        BeanUtils.copyProperties(request, cond);
        nbchatTrainTaskDegreeMapper.newAnalysis(cond);
        log.info("查询证书统计耗时：{}", new Date().getTime() - time);
        List<NbchatTrainTaskDegreeBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), list, NbchatTrainTaskDegreeBO.class);
        long times = new Date().getTime();
        for (NbchatTrainTaskDegreeBO degreeBO : list) {
            //目标机构
            if (StringUtils.isNotEmpty(degreeBO.getDeptId())) {
                String TargetName = nameMapper.queryOrganizeName(degreeBO.getTargetId());
                String deptName = nameMapper.queryOrganizeName(degreeBO.getDeptId());
                degreeBO.setTargetName(TargetName);
                degreeBO.setDeptName(deptName);
            }
            long time1 = new Date().getTime();
            if (StringUtils.isNotEmpty(degreeBO.getTaskId())) {
                //查询部门下的用户
                HashSet<String> userIds = new HashSet<>();
                int taskId = Integer.parseInt(degreeBO.getTaskId());
                this.getSetUsers(userIds, degreeBO.getSupportSubDept(), degreeBO.getPostId(), request.getTenantCode(), degreeBO.getDeptId());
                degreeBO.setTargetUserCount(userIds.size());
                int finishUserCount = 0;
                for (String userId : userIds) {
                    NbchatTaskRecordBO taskBO = new NbchatTaskRecordBO();
                    trainTaskService.handleStatus(taskId, userId, taskBO);
                    if (TRAIN_TASK_FINISH.equals(taskBO.getStatus())) {
                        finishUserCount++;
                    }
                }
                degreeBO.setFinishUserCount(finishUserCount);
                if (degreeBO.getTargetUserCount() != 0) {
                    String rate = String.format("%.2f%%", ((float) finishUserCount / degreeBO.getTargetUserCount()) * 100);
                    degreeBO.setFinishRate(rate);
                }
            }
            log.info("查询单个机构下面的用户：{}", new Date().getTime() - time1);
            //查询岗位名称
            if (StringUtils.isNotEmpty(degreeBO.getPostId())) {
                String postNameStr = Arrays.stream(degreeBO.getPostId().split(",")).map(nameMapper::queryPostName)
                        .collect(Collectors.joining(","));
                degreeBO.setPostName(postNameStr);
            }
        }
        log.info("查询全部的用户耗时：{}", new Date().getTime() - times);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    public String getSetUsers(Set<String> userIds, String supportSubDept, String postId, String tenantCode, String deptId) {
        Set<String> users = nameMapper.queryDeptPostUser(deptId, supportSubDept, postId, tenantCode);
        userIds.addAll(users);
        return "";
    }


}
