package com.tydic.nbchat.train.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.tydic.nbchat.train.api.TrainExamQaApi;
import com.tydic.nbchat.train.api.bo.eums.QuestionType;
import com.tydic.nbchat.train.api.bo.exam.NbchatExamQuestionBO;
import com.tydic.nbchat.train.api.bo.exam.NbchatQaRecordBO;
import com.tydic.nbchat.train.api.bo.train.NbchatExamQaBO;
import com.tydic.nbchat.train.mapper.NbchatExamQaRecordMapper;
import com.tydic.nbchat.train.mapper.NbchatExamQuestionMapper;
import com.tydic.nbchat.train.mapper.po.NbchatExamNewTestPaper;
import com.tydic.nbchat.train.mapper.po.NbchatExamQaRecord;
import com.tydic.nbchat.train.mapper.po.NbchatExamQuestion;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TrainExamQaServiceImpl implements TrainExamQaApi {

    @Resource
    NbchatExamQuestionMapper nbchatExamQuestionMapper;
    @Resource
    NbchatExamQaRecordMapper nbchatExamQaRecordMapper;


    @Override
    public Rsp createNewPaper(NbchatExamQaBO request) {
        log.info("创建qa试题:{}", request);

        NbchatExamNewTestPaper po = new NbchatExamNewTestPaper();
        po.setQuestions(999);
        po.setTenantCode(request.getTenantCode());
        po.setCourseId(request.getCourseId());
        po.setQuestionType(QuestionType.QA_MIN.getCode());
        List<NbchatExamQuestion> questions = nbchatExamQuestionMapper.checkNewTestPaper(po);
        if (CollectionUtils.isEmpty(questions)) {
            return BaseRspUtils.createErrorRsp("没有可用的试题");
        }
        List<String> qId = questions.stream().map(NbchatExamQuestion::getQuestionId).collect(Collectors.toList());

        NbchatExamQaRecord record = new NbchatExamQaRecord();
        record.setId(IdWorker.nextAutoIdStr());
        record.setTenantCode(request.getTenantCode());
        record.setUserId(request.getUserId());
        record.setCourseId(request.getCourseId());
        record.setQuestionPaper(JSON.toJSONString(qId));
        record.setCreateTime(new Date());
        nbchatExamQaRecordMapper.insertSelective(record);

        List<NbchatExamQuestion> questionList = nbchatExamQuestionMapper.queryQuestions(qId);
        ArrayList<NbchatExamQuestionBO> res = new ArrayList<>();
        NiccCommonUtil.copyList(questionList,res,NbchatExamQuestionBO.class);
        NbchatQaRecordBO rsp = new NbchatQaRecordBO();
        rsp.setId(record.getId());
        rsp.setQuestions(res);
        return BaseRspUtils.createSuccessRsp(rsp);
    }

    @Override
    public Rsp getExamQaRecord(NbchatExamQaBO request) {
        log.info("获取qa考试记录:{}", request);

        NbchatQaRecordBO rsp = new NbchatQaRecordBO();
        NbchatExamQaRecord po = new NbchatExamQaRecord();
        po.setTenantCode(request.getTenantCode());
        po.setUserId(request.getUserId());
        po.setCourseId(request.getCourseId());
        NbchatExamQaRecord record = nbchatExamQaRecordMapper.selectRecord(po);
        if (ObjectUtils.isEmpty(record)) {
            return BaseRspUtils.createSuccessRsp(null);
        }
        rsp.setId(record.getId());
        rsp.setAnswers(record.getAnswerContent());
        String questionPaper = record.getQuestionPaper();
        if (JSON.isValidArray(questionPaper)) {
            List<String> qId = JSON.parseArray(questionPaper, String.class);
            List<NbchatExamQuestion> questionList = nbchatExamQuestionMapper.queryQuestions(qId);
            List<NbchatExamQuestionBO>  questionBOList = new ArrayList<>();
            NiccCommonUtil.copyList(questionList,questionBOList,NbchatExamQuestionBO.class);
            rsp.setQuestions(questionBOList);
        }
        return BaseRspUtils.createSuccessRsp(rsp);
    }

    @Override
    public Rsp saveRecord(NbchatExamQaBO request) {
        log.info("保存qa记录:{}",request);

        NbchatExamQaRecord po = new NbchatExamQaRecord();
        po.setId(request.getId());
        po.setAnswerContent(request.getAnswerContent());
        nbchatExamQaRecordMapper.update(po);
        return BaseRspUtils.createSuccessRsp(null);
    }
}
