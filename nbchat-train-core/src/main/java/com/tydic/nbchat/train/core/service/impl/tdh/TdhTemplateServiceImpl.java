package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysUserStarApi;
import com.tydic.nbchat.admin.api.bo.eum.StarType;
import com.tydic.nbchat.admin.api.star.UserStarRequest;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.eums.TemplateSourceType;
import com.tydic.nbchat.train.api.bo.eums.VipFlagType;
import com.tydic.nbchat.train.api.bo.tdh.TdhHotTemplateQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhTemplateQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhTemplateQueryRspBO;
import com.tydic.nbchat.train.api.tdh.TdhTemplateApi;
import com.tydic.nbchat.train.mapper.TdhTemplateMapper;
import com.tydic.nbchat.train.mapper.po.TdhTemplate;
import com.tydic.nbchat.train.mapper.po.TdhTemplateSelectCondition;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 数字人：模板管理相关接口
 */
@Slf4j
@Service
public class TdhTemplateServiceImpl implements TdhTemplateApi {

    @Resource
    TdhTemplateMapper tdhTemplateMapper;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private SysUserStarApi sysUserStarApi;

    @Override
    public RspList<TdhTemplateQueryRspBO> getTemplateList(TdhTemplateQueryReqBO reqBO) {
        TdhTemplateSelectCondition cond = new TdhTemplateSelectCondition();
        BeanUtils.copyProperties(reqBO, cond);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        cond.setTpState(StateEnum.STATE.AVAILABLE.getCode());
        Page<TdhTemplate> info = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        tdhTemplateMapper.selectByCondition(cond);
        List<TdhTemplate> result = info.getResult();
        ArrayList<TdhTemplateQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result, rspList, TdhTemplateQueryRspBO.class);
        rspList.forEach(rsp -> rsp.setIsStar(isStar(reqBO,rsp.getTpId())));
        return BaseRspUtils.createSuccessRspList(rspList, info.getTotal());
    }

    @Override
    public RspList<TdhTemplateQueryRspBO> getTemplateAdminList(TdhTemplateQueryReqBO reqBO) {
        TdhTemplateSelectCondition cond = new TdhTemplateSelectCondition();
        BeanUtils.copyProperties(reqBO, cond);
        cond.setTenantCode(reqBO.getTargetTenant());
        cond.setUserId(null);
        if (StringUtils.isNotBlank(reqBO.getTargetTenant())) {
            cond.setUserId(reqBO.getTargetTenant());
        }
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        //查询可配置的模板
        cond.setTpSource("0");
        Page<TdhTemplate> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        tdhTemplateMapper.selectByCondition(cond);
        List<TdhTemplate> result = page.getResult();
        ArrayList<TdhTemplateQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result, rspList, TdhTemplateQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspList, page.getTotal());
    }

    @Override
    public Rsp<TdhTemplateQueryRspBO> save(TdhTemplateQueryReqBO reqBO) {
        TdhTemplate rec = new TdhTemplate();
        BeanUtils.copyProperties(reqBO, rec);
        setDefaultValues(rec, reqBO);
        if (StringUtils.isNotEmpty(reqBO.getTpId())) {
            if (StringUtils.isBlank(reqBO.getTargetTenant())) {
                rec.setTenantCode(null);
                rec.setUserId(null);
                rec.setTpSource(null);
            }
            rec.setUpdateTime(new Date());
            int update = tdhTemplateMapper.update(rec);
            return BaseRspUtils.createSuccessRsp(update);
        } else {
            setDefaultValues(rec, reqBO);
            rec.setTpId(IdWorker.nextAutoIdStr());
            rec.setCreateTime(new Date());
            int result = tdhTemplateMapper.insertSelective(rec);
            if (result > 0) {
                TdhTemplateQueryRspBO rsp = new TdhTemplateQueryRspBO();
                BeanUtils.copyProperties(rec, rsp);
                return BaseRspUtils.createSuccessRsp(rsp);
            }
        }
        return BaseRspUtils.createErrorRsp("保存失败");
    }

    private void setDefaultValues(TdhTemplate rec, TdhTemplateQueryReqBO reqBO) {
        rec.setVipFlag(Optional.ofNullable(rec.getVipFlag())
                .filter(StringUtils::isNotBlank)
                .orElse(VipFlagType.COMMON_USER.getCode()));

        String targetTenant = Optional.ofNullable(reqBO.getTargetTenant())
                .filter(StringUtils::isNotBlank)
                .orElse(reqBO.getTenantCode());

        if (TemplateSourceType.CUSTOM.getCode().equals(reqBO.getTpSource())) {
            // 个人自定义
            rec.setUserId(reqBO.getUserId());
            rec.setTenantCode(targetTenant);
            rec.setTpState(StateEnum.STATE.AVAILABLE.getCode());
            rec.setTpSource(TemplateSourceType.CUSTOM.getCode());
        } else {
            if (StateEnum.SOURCE.PUBLIC.getCode().equals(reqBO.getTargetTenant())) {
                // 公共
                rec.setUserId(StateEnum.SOURCE.PUBLIC.getCode());
                rec.setTenantCode(UserAttributeConstants.DEFAULT_TENANT_CODE);
                rec.setTpSource(StateEnum.SOURCE.SYSTEM.getCode());
                rec.setTpState(StringUtils.isNotBlank(reqBO.getTpState())?reqBO.getTpState():StateEnum.STATE.UNAVAILABLE.getCode());
            } else {
                // 租户自定义
                rec.setUserId(targetTenant);
                rec.setTenantCode(targetTenant);
                rec.setTpSource(StateEnum.SOURCE.SYSTEM.getCode());
                rec.setTpState(StringUtils.isNotBlank(reqBO.getTpState())?reqBO.getTpState():StateEnum.STATE.UNAVAILABLE.getCode());
            }
        }
    }

    @Override
    public Rsp getTemplate(TdhTemplateQueryReqBO request) {
        TdhTemplate res = tdhTemplateMapper.queryById(request.getTpId());
        if (res != null) {
            TdhTemplateQueryRspBO rsp = new TdhTemplateQueryRspBO();
            BeanUtils.copyProperties(res, rsp);
            return BaseRspUtils.createSuccessRsp(rsp);
        }
        log.info("未找到该模板或已失效：{}", request.getTpId());
        return BaseRspUtils.createErrorRsp("未找到该模板或已失效");
    }

    @Override
    public RspList getHotTemplateList(TdhTemplateQueryReqBO request) {
        TdhTemplateSelectCondition cond = new TdhTemplateSelectCondition();
        BeanUtils.copyProperties(request, cond);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        cond.setTpState(StateEnum.STATE.AVAILABLE.getCode());
        cond.setIsHot(EntityValidType.NORMAL.getCode());
        cond.setTenantCode(StateEnum.SOURCE.PUBLIC.getCode());
        cond.setTpSource(StateEnum.SOURCE.SYSTEM.getCode());
        Page<TdhTemplate> info = PageHelper.startPage(request.getPage(), request.getLimit());
        tdhTemplateMapper.selectByCondition(cond);
        List<TdhTemplate> result = info.getResult();
        ArrayList<TdhHotTemplateQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result, rspList, TdhHotTemplateQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspList, info.getTotal());
    }

    @Override
    public RspList getStarTemplateList(TdhTemplateQueryReqBO request) {
        TdhTemplateSelectCondition cond = new TdhTemplateSelectCondition();
        BeanUtils.copyProperties(request, cond);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        cond.setTpState(StateEnum.STATE.AVAILABLE.getCode());
        Page<TdhTemplate> info = PageHelper.startPage(request.getPage(), request.getLimit());
        tdhTemplateMapper.selectStarTemplateByCondition(cond);
        List<TdhTemplate> result = info.getResult();
        ArrayList<TdhTemplateQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result, rspList, TdhTemplateQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspList, info.getTotal());
    }

    public String isStar(TdhTemplateQueryReqBO reqBO,String busiId){
        UserStarRequest request = new UserStarRequest();
        request.setBusiId(busiId);
        request.setUserId(reqBO.getUserId());
        request.setTenantCode(reqBO.getTenantCode());
        Rsp rsp = null;
        try {
            rsp = sysUserStarApi.getStar(request);
        } catch (Exception e) {
            log.warn("数字人面板-获取用户模板-查询收藏状态异常：{}", e.getMessage());
        }
        return rsp != null && rsp.isSuccess() ? StarType.FAVORITED.getCode(): StarType.UNFAVORITED.getCode();
    }
}
