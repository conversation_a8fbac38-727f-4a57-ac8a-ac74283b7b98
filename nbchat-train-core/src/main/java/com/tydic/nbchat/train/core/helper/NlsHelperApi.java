package com.tydic.nbchat.train.core.helper;

import com.tydic.nbchat.train.api.bo.asr_tts.AsrVoiceTaskContext;
import com.tydic.nbchat.train.api.bo.asr_tts.AsrVoiceTaskRequest;
import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceTaskContext;
import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceTaskRequest;

public interface NlsHelperApi {

    String anchorConfig();

    /**
     * 创建语音任务
     * @param courseId
     * @param sectionId
     * @param courseAll
     */
    @Deprecated
    void createAudioTask(String courseId, String sectionId, boolean courseAll);

    /**
     * 语音合成任务
     * @param courseId
     * @param sectionId
     * @param text
     * @param voice
     * @param async
     * @return
     */
    TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text, String voice, boolean async);

    /**
     * 语音合成任务
     * @param request
     * @return
     */
    TtsVoiceTaskContext createAudioTask(TtsVoiceTaskRequest request);

    /**
     * 语音合成任务
     * @param courseId
     * @param sectionId
     * @param text
     * @param async
     * @return
     */
    TtsVoiceTaskContext createAudioTask(String courseId, String sectionId, String text,boolean async);

    /**
     * 语音识别任务
     * @param courseId
     * @param fileId
     * @param filepath
     * @param async
     * @return
     */
    AsrVoiceTaskContext createAsrTask(String courseId, String fileId, String filepath, boolean async);

    /**
     * 语音识别任务
     * @param request
     * @return
     */
    AsrVoiceTaskContext createAsrTask(AsrVoiceTaskRequest request);



    /**
     * 转换为火山语音参数
     * @param inputVal
     * @param defaultVal
     * @param oMin
     * @param oMax
     * @param newMin
     * @param newMax
     * @return
     */
    static float convertRange(Integer inputVal, float defaultVal, float oMin, float oMax,
                              float newMin, float newMax) {
        if (inputVal == null || inputVal > oMax || inputVal < oMin) {
            return defaultVal;
        }
        float originalRange = oMax - oMin;
        float newRange = newMax - newMin;
        float res = ((inputVal - oMin) * newRange / originalRange) + newMin;
        return Math.max(res, newMin);
    }
}
