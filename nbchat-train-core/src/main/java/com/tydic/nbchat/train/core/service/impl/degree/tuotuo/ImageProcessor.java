package com.tydic.nbchat.train.core.service.impl.degree.tuotuo;

import org.springframework.core.io.ClassPathResource;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

class ImageProcessor {

    private static final String FONT_NAME = "Alibaba PuHuiTi Regular";
    private static final int FONT_SIZE_NORMAL = 36;
    private static final Color FONT_COLOR_NORMAL = Color.BLACK;

    private Graphics2D graphics;
    private BufferedImage image;

    public ImageProcessor() throws IOException {
        this.initGraphics();
    }

    private void initGraphics() throws IOException {
        image = ImageIO.read(new ClassPathResource("images/degree_template_tt.jpg").getInputStream());
        graphics = (Graphics2D) image.getGraphics();
        graphics.setFont(new Font(FONT_NAME, Font.BOLD, FONT_SIZE_NORMAL));
        graphics.setColor(FONT_COLOR_NORMAL);
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);//抗锯齿
    }

    public ImageProcessor userName(String userName) {
        //常规信息
        this.drawText(userName, 300, 710);
        return this;
    }

    public ImageProcessor projectName(String projectName) {
        //常规信息
        this.drawText(projectName,170, 870);
        return this;
    }

    //颁发时间
    public ImageProcessor issueTime(String issueTime) {
        //常规信息
        graphics.setFont(new Font(FONT_NAME, Font.PLAIN, 36));
        this.drawText(issueTime, 650, 1450);
        return this;
    }

    public String build() throws IOException {
        return this.saveImage();
    }

    private void drawText(String text, int x, int y) {
        graphics.drawString(text, x, y);
    }

    private String saveImage() throws IOException {
        String path = "/tmp/" + System.currentTimeMillis() + "_degree.png";
        ImageIO.write(image, "png", new File(path));
        return path;
    }

}
