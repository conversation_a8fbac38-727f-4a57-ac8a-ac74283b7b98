package com.tydic.nbchat.train.core.util;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.tydic.nbchat.train.mapper.po.PptTheme;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * PptThemeMatcherProcessor
 * <p>
 * 查找最合适的主题，按优先级顺序：
 * 1) 场景 + 风格 + 配色
 * 2) 场景 + 风格
 * 3) 场景
 * <p>
 * 同一优先级分组内，再按 star(3→2→1→0) 进行筛选并随机返回。
 */
@Slf4j
public class PptThemeMatcherProcessor {

    /**
     * 查找最合适的主题
     *
     * @param themes       所有可选的 PPT 主题
     * @param targetStyles 目标风格列表
     * @param targetColors 目标配色列表
     * @return 匹配到的 PPT 主题（Optional）
     */
    public static Optional<PptTheme> findBestMatch(List<PptTheme> themes,
                                                   List<String> targetStyles,
                                                   List<String> targetColors) {

        // 收集不同优先级下的主题
        List<MatchScore> fullMatches = new ArrayList<>();
        List<MatchScore> styleMatches = new ArrayList<>();
        List<MatchScore> sceneMatches = new ArrayList<>();

        // 根据样式和配色的匹配情况，将主题分到不同分组
        for (PptTheme theme : themes) {
            List<String> styles = RegularUtil.arryStrParseToList(theme.getStyle());
            List<String> colors = RegularUtil.arryStrParseToList(theme.getColor());

            int styleMatchCount = countMatches(styles, targetStyles);
            int colorMatchCount = countMatches(colors, targetColors);

            // 1) 风格和配色都有匹配（至少一个风格命中 & 至少一个配色命中）
            if (styleMatchCount > 0 && colorMatchCount > 0) {
                fullMatches.add(new MatchScore(theme));
            }
            // 2) 只有风格匹配
            else if (styleMatchCount > 0) {
                styleMatches.add(new MatchScore(theme));
            }
            // 3) 仅场景匹配
            else {
                sceneMatches.add(new MatchScore(theme));
            }
        }

        // 按优先级顺序，依次在对应分组内找最优匹配
        if (!fullMatches.isEmpty()) {
            return pickBestByStar(fullMatches);
        } else if (!styleMatches.isEmpty()) {
            return pickBestByStar(styleMatches);
        } else {
            return pickBestByStar(sceneMatches);
        }
    }

    /**
     * 计算匹配的元素数量，例如匹配到几个风格或配色
     *
     * @param themeAttributes  主题自身的属性列表（风格/配色）
     * @param targetAttributes 目标需求的属性列表（风格/配色）
     * @return 匹配到的数量
     */
    private static int countMatches(List<String> themeAttributes, List<String> targetAttributes) {
        int matchCount = 0;
        if (CollectionUtils.isEmpty(themeAttributes) || CollectionUtils.isEmpty(targetAttributes)) {
            return matchCount;
        }
        for (String targetAttribute : targetAttributes) {
            if (themeAttributes.contains(targetAttribute)) {
                matchCount++;
            }
        }
        return matchCount;
    }

    /**
     * 在一个分组内部，根据 star 从高到低(3 → 2 → 1 → 0)进行筛选，并在找到第一个非空分组时，
     * 随机返回其中一个主题。
     *
     * @param matches 候选主题列表
     * @return 最优匹配（Optional）
     */
    private static Optional<PptTheme> pickBestByStar(List<MatchScore> matches) {
        if (matches.isEmpty()) {
            return Optional.empty();
        }

        // 先按照 star 分组
        Map<Integer, List<MatchScore>> starMap =
                matches.stream().collect(Collectors.groupingBy(MatchScore::getStar));

        // star 从高到低（3→2→1→0）依次去查找
        for (int starLevel = 3; starLevel >= 0; starLevel--) {
            List<MatchScore> starList = starMap.get(starLevel);
            if (starList != null && !starList.isEmpty()) {
                // 从该星级分组随机返回一个
                return Optional.of(
                        starList.get(ThreadLocalRandom.current().nextInt(starList.size()))
                                .getTheme()
                );
            }
        }
        // 如果所有 starLevel(3~0) 都没有找到可用条目，则在传入的 matches 里随机挑一个
        return Optional.of(
                matches.get(ThreadLocalRandom.current().nextInt(matches.size()))
                        .getTheme()
        );
    }

    /**
     * 辅助类：将主题与其 star 封装在一起
     */
    static class MatchScore {
        private final PptTheme theme;
        private final int star;

        public MatchScore(PptTheme theme) {
            this.theme = theme;
            // 假设 PptTheme 中 star 为 String，可能为 null 或者 "0" ~ "3" 等
            this.star = parseStar(theme.getStar());
        }

        public PptTheme getTheme() {
            return theme;
        }

        public int getStar() {
            return star;
        }

        /**
         * 安全解析 star，若无法解析则默认返回 0
         */
        private static int parseStar(String starStr) {
            if (starStr == null) {
                return 0;
            }
            try {
                return Integer.parseInt(starStr.trim());
            } catch (NumberFormatException e) {
                // 如果 starStr 格式异常，也返回默认值 0
                return 0;
            }
        }
    }
}