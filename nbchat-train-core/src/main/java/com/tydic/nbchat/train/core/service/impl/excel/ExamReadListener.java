package com.tydic.nbchat.train.core.service.impl.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.train.api.NbcahtExamQuestionApi;
import com.tydic.nbchat.train.api.bo.QuestionSaveRequest;
import com.tydic.nbchat.train.api.bo.eums.QuestionType;
import com.tydic.nbchat.train.api.bo.question.Question;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ExamReadListener implements ReadListener {

    private final NbcahtExamQuestionApi nbcahtExamQuestionApi;

    private final String courseId;
    private final String tenantCode;

    public ExamReadListener(NbcahtExamQuestionApi nbcahtExamQuestionApi, String courseId, String tenantCode) {
        this.nbcahtExamQuestionApi = nbcahtExamQuestionApi;

        this.courseId = courseId;
        this.tenantCode = tenantCode;
    }

    @Override
    public void invokeHead(Map headMap, AnalysisContext context) {
        ReadListener.super.invokeHead(headMap, context);
        log.info("解析到一条头数据:{}|{}", headMap.size(), JSONObject.toJSONString(headMap));
    }


    @Override
    public void invoke(Object o, AnalysisContext analysisContext) {
        LinkedHashMap<Integer, String> map = (LinkedHashMap) o;
        log.info("解析到一条数据:{}", JSONObject.toJSONString(map));
        //校验表格格式
        this.checkExcelFormat(map);

        QuestionSaveRequest question1 = QuestionSaveRequest.builder()
                .courseId(courseId).tenantCode(tenantCode)
                .questionType(QuestionType.getCodeByName(map.get(0))).build();
        Question question2 = Question.builder()
                .question(map.get(1)).difficulty(map.get(2)).explan(map.get(3)).build();
        //选项
        List<String> items = map.values().stream().skip(6).filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        //答案
        String[] split = map.get(5).toUpperCase().split(",");
        List<Integer> answers = new ArrayList<>();
        for (String s : split) {
            answers.add((int) s.charAt(0) - (int) 'A');
        }
        nbcahtExamQuestionApi.consumerQuestion(question1, question2, items, answers);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("解析结束");
    }

    public void checkExcelFormat(LinkedHashMap<Integer, String> map) {
        if (ObjectUtils.isEmpty(map) || map.size() < 7) {
            log.warn("题目数据不完整:{}", map);
            throw new RuntimeException("题目数据不完整");
        }
        if (StringUtils.isEmpty(map.get(5))) {
            log.warn("题目正确答案为空:{}", map);
            throw new RuntimeException("题目正确答案为空");
        }
    }

}
