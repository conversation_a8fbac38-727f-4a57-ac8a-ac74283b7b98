package com.tydic.nbchat.train.core.web.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhIndexTemplateQueryRspBO;
import com.tydic.nbchat.train.api.tdh.TdhIndexTemplateApi;
import com.tydic.nbchat.train.mapper.po.TdhIndexTemplate;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/train/tdh/index")
public class TdhIndexTemplateController {

    private final TdhIndexTemplateApi tdhIndexTemplateApi;

    @PostMapping("/templates")
    public RspList<TdhIndexTemplateQueryRspBO> list(@RequestBody TdhIndexTemplateQueryReqBO request) {
        return tdhIndexTemplateApi.list(request);
    }

    @PostMapping("/template/info")
    public Rsp info(@RequestBody TdhIndexTemplateQueryReqBO reqBO) {
        return tdhIndexTemplateApi.info(reqBO);
    }
}
