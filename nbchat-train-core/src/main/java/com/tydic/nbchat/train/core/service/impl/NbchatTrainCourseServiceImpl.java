package com.tydic.nbchat.train.core.service.impl;

import com.tydic.nbchat.robot.api.NbchatResearchMajorApi;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMajorAnalysisStatus;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMajorQueryRequest;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMajorSaveRequest;
import com.tydic.nbchat.robot.api.bo.research.NbchatResearchMajorRspBO;
import com.tydic.nbchat.train.api.NbchatTrainCourseApi;
import com.tydic.nbchat.train.api.NbchatTrainSectionApi;
import com.tydic.nbchat.train.api.bo.constants.CourseScoreConstants;
import com.tydic.nbchat.train.api.bo.constants.ExamRuleConstants;
import com.tydic.nbchat.train.api.bo.course.*;
import com.tydic.nbchat.train.api.bo.eums.ExamTestType;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.eums.TaskStateType;
import com.tydic.nbchat.train.api.bo.train.section.SectionsSaveRequest;
import com.tydic.nbchat.train.api.bo.train.section.TrainSectionsBO;
import com.tydic.nbchat.train.mapper.*;
import com.tydic.nbchat.train.mapper.po.*;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class NbchatTrainCourseServiceImpl implements NbchatTrainCourseApi {

    @Resource
    private NbchatTrainCourseMapper nbchatTrainCourseMapper;
    @Resource
    private NbchatTrainRecordMapper nbchatTrainRecordMapper;
    @Resource
    private NbchatTrainCourseCategoryMapper nbchatTrainCourseCategoryMapper;
    @Resource
    private NbchatTrainCourseTextMapper nbchatTrainCourseTextMapper;
    @Resource
    private NbchatExamTestPaperMapper nbchatExamTestPaperMapper;
    @Resource
    private TdhCreationTaskMapper taskMapper;
    @Resource
    private NbchatTrainCatalogMapper trainCatalogMapper;
    @Resource
    private TdhCreationRecordMapper tdhCreationRecordMapper;
    @Resource
    private NbchatTrainSectionApi nbchatTrainSectionApi;
    @Resource
    private NameMapper nameMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NbchatResearchMajorApi nbchatResearchMajorApi;


    @Override
    public Rsp deleteCourse(TranCourseQueryReqBO queryReqBO) {
        log.info("课程管理-删除对象:{}", queryReqBO);
        NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(queryReqBO.getCourseId());
        if (course == null || !EntityValidType.NORMAL.getCode().equals(course.getIsValid())) {
            return BaseRspUtils.createErrorRsp("删除失败：该课程不存在!");
        }
        NbchatTrainCourse delete = new NbchatTrainCourse();
        delete.setCourseState(StateEnum.COURSE.OFF.getCode());
        delete.setIsValid(EntityValidType.DELETE.getCode());
        delete.setUpdateTime(new Date());
        delete.setCourseId(queryReqBO.getCourseId());
        nbchatTrainCourseMapper.updateByPrimaryKeySelective(delete);
        return BaseRspUtils.createSuccessRsp(queryReqBO.getCourseId());
    }

    @Override
    public Rsp<TranCourseBO> getCourse(TranCourseQueryReqBO queryReqBO) {
        log.info("课程管理-查询对象:{}", queryReqBO);
        NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(queryReqBO.getCourseId());
        if (course == null || !EntityValidType.NORMAL.getCode().equals(course.getIsValid())) {
            return BaseRspUtils.createErrorRsp("查询失败：该课程不存在!");
        }
        TranCourseBO courseBO = new TranCourseBO();
        BeanUtils.copyProperties(course, courseBO);
        int count = nbchatTrainRecordMapper.countTrained(courseBO.getCourseId());
        courseBO.setTrainCount(count);

        courseBO.setCourseState(StateEnum.COURSE.getNameByCode(courseBO.getCourseState()));

        NbchatTrainCourseCategory category = nbchatTrainCourseCategoryMapper.queryById(course.getCategory());
        if (ObjectUtils.isNotEmpty(category)) {
            courseBO.setCategoryName(category.getCateName());
        }
        NbchatTrainCourseCategory category2 = nbchatTrainCourseCategoryMapper.queryById(course.getCategory2());
        if (ObjectUtils.isNotEmpty(category2)) {
            courseBO.setCategory2Name(category2.getCateName());
        }
        FileResearchMajorQueryRequest request = new FileResearchMajorQueryRequest();
        request.setMajorId(course.getMajorId());
        Rsp<FileResearchMajorAnalysisStatus> majorStatus = nbchatResearchMajorApi.getMajorAnalysisStatus(request);
        if (!(majorStatus.isSuccess() && majorStatus.getData().getDocTotalCount() > 0)) {
            courseBO.setMajorId("");
        }
        return BaseRspUtils.createSuccessRsp(courseBO);
    }

    @Override
    public RspList<TranCourseBO> getCourses(TranCourseQueryReqBO queryReqBO) {
        List<TranCourseBO> list = Lists.newArrayList();
        NbchatTrainCourseSelectCondition condition = new NbchatTrainCourseSelectCondition();
        BeanUtils.copyProperties(queryReqBO, condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        if (StringUtils.isEmpty(queryReqBO.getCourseState()) && !queryReqBO.isSearchAll()) {
            condition.setCourseState(StateEnum.COURSE.ON.getCode());
        }
        List<NbchatTrainCourse> nbchatTrainCourses = nbchatTrainCourseMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(nbchatTrainCourses, list, TranCourseBO.class);
        for (TranCourseBO tranCourseBO : list) {
            int count = nbchatTrainRecordMapper.countTrained(tranCourseBO.getCourseId());
            tranCourseBO.setTrainCount(count);
        }
        Stream<TranCourseBO> stream = list.stream();
        if (queryReqBO.isOrder()) {
            stream = list.stream().sorted(Comparator.comparing(TranCourseBO::getTrainCount).reversed());
        }
        List<TranCourseBO> page = stream.skip((long) (queryReqBO.getPage() - 1) * queryReqBO.getLimit()).
                limit(queryReqBO.getLimit()).collect(Collectors.toList());
        for (TranCourseBO tranCourseBO : page) {
            this.setNames(tranCourseBO, queryReqBO);
            int i = nbchatTrainRecordMapper.countVideo(tranCourseBO.getCourseId());
            tranCourseBO.setVideoCount(i);
        }
        return BaseRspUtils.createSuccessRspList(page, list.size());
    }

    public void setNames(TranCourseBO tranCourseBO,TranCourseQueryReqBO queryReqBO){
        NbchatTrainCourseCategory category = nbchatTrainCourseCategoryMapper.queryById(tranCourseBO.getCategory());
        if (ObjectUtils.isNotEmpty(category)) {
            tranCourseBO.setCategoryName(category.getCateName());
        }
        NbchatTrainCourseCategory category2 = nbchatTrainCourseCategoryMapper.queryById(tranCourseBO.getCategory2());
        if (ObjectUtils.isNotEmpty(category2)) {
            tranCourseBO.setCategory2Name(category2.getCateName());
        }
        String name = StateEnum.COURSE.getNameByCode(tranCourseBO.getCourseState());
        if (StringUtils.isNotEmpty(name)) {
            tranCourseBO.setCourseState(name);
        }
        String sceneName = StateEnum.COURSE.getNameByCode(tranCourseBO.getSceneState());
        if (StringUtils.isNotEmpty(sceneName)) {
            tranCourseBO.setSceneState(sceneName);
        }
        String userId = tranCourseBO.getUserId();
        if (StringUtils.isNotEmpty(userId)) {
            String userName = nameMapper.queryUserName(userId, queryReqBO.getTenantCode());
            tranCourseBO.setCreateUserName(userName);
        }
    }

    @Override
    @MethodParamVerifyEnable
    @Transactional
    public Rsp saveCourse(TranCourseSaveReqBO saveReqBO) {
        log.debug("课程管理-保存课程:{}", saveReqBO);
        String courseId = saveReqBO.getCourseId();
        if (StringUtils.isBlank(courseId)) {
            courseId = IdWorker.nextAutoIdStr();
            //保存
            if (StringUtils.isAnyBlank(saveReqBO.getCourseName(), saveReqBO.getUserId())) {
                return BaseRspUtils.createErrorRsp("课程新增失败：参数异常!");
            }
            NbchatTrainCourse insert = new NbchatTrainCourse();
            BeanUtils.copyProperties(saveReqBO, insert);
            //创建专属机器人
            String majorId = this.createMajor(insert);
            //添加课程
            insert.setMajorId(majorId);
            insert.setCreateTime(new Date());
            insert.setIsValid(EntityValidType.NORMAL.getCode());
            insert.setUpdateTime(new Date());
            insert.setCourseId(courseId);
            insert.setStepState(StateEnum.STEP.BASE_INFO.getCode());
            nbchatTrainCourseMapper.insertSelective(insert);
            //新增考试配置
            this.addExamConfig(insert);
        } else {
            NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(saveReqBO.getCourseId());
            if (course == null || !EntityValidType.NORMAL.getCode().equals(course.getIsValid())) {
                return BaseRspUtils.createErrorRsp("更新失败：该课程不存在!");
            }
            //更新
            NbchatTrainCourse update = new NbchatTrainCourse();
            BeanUtils.copyProperties(saveReqBO, update);
            update.setUpdateTime(new Date());
            update.setStepState(null);
            update.setVideoSource(saveReqBO.getVideoSource());
            log.info("更新课程:{}", update);
            nbchatTrainCourseMapper.updateByPrimaryKeySelective(update);
            if (StringUtils.isNotEmpty(saveReqBO.getStepState())) {
                nbchatTrainCourseMapper.updateStepState(saveReqBO.getCourseId(), saveReqBO.getStepState());
            }
        }
        return BaseRspUtils.createSuccessRsp(courseId);
    }

    @Override
    public Rsp newCourse(TranCourseSaveReqBO saveReqBO) {
        log.info("新增考试、对战类型课程:{}", saveReqBO);
        NbchatTrainCourse po = new NbchatTrainCourse();
        BeanUtils.copyProperties(saveReqBO, po);
        //添加课程
        po.setCreateTime(new Date());
        po.setIsValid(EntityValidType.NORMAL.getCode());
        po.setUpdateTime(new Date());
        po.setCourseId(IdWorker.nextAutoIdStr());
        po.setStepState(StateEnum.STEP.BASE_INFO.getCode());
        po.setCourseState(StateEnum.COURSE.PRE_EDIT.getCode());
        nbchatTrainCourseMapper.insertSelective(po);

        //新增考试配置
        if (saveReqBO.getCourseType().equals("3")) {
            this.addExamConfig(po);
        }
        return BaseRspUtils.createSuccessRsp(po.getCourseId());
    }


    @Override
    public RspList getTopHubCourse(TrainCourseHotHubBO reqBO) {
        log.info("查询热点课程:{}", reqBO);
        List<TranCourseBO> list = Lists.newArrayList();
        NbchatTrainCourseSelectCondition condition = new NbchatTrainCourseSelectCondition();
        BeanUtils.copyProperties(reqBO, condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        condition.setCourseState(StateEnum.COURSE.ON.getCode());
        List<NbchatTrainCourse> nbchatTrainCourses = nbchatTrainCourseMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(nbchatTrainCourses, list, TranCourseBO.class);
        for (TranCourseBO tranCourseBO : list) {
            int count = nbchatTrainRecordMapper.countTrained(tranCourseBO.getCourseId());
            tranCourseBO.setTrainCount(count);
        }
        List<TranCourseBO> collect = list.stream().sorted(Comparator.comparing(TranCourseBO::getTrainCount).reversed()).limit(reqBO.getLimit()).collect(Collectors.toList());
        return BaseRspUtils.createSuccessRspList(collect);
    }

    @Override
    public Rsp getCourseContent(NbchatTrainCourseTextBO reqBO) {
        log.info("查询课程内容：{}", reqBO);
        NbchatTrainCourseTextBO rspBO = new NbchatTrainCourseTextBO();
        NbchatTrainCourseText courseText = nbchatTrainCourseTextMapper.queryById(reqBO.getCourseId());
        if (ObjectUtils.isEmpty(courseText)) {
            return BaseRspUtils.createSuccessRsp("未找到课程资料内容");
        }
        BeanUtils.copyProperties(courseText, rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    @Transactional
    public Rsp saveCourseContent(NbchatTrainCourseTextBO reqBO) {
        log.debug("保存课程内容：{}", reqBO);
        NbchatTrainCourseText record = new NbchatTrainCourseText();
        BeanUtils.copyProperties(reqBO, record);
        record.setUpdateTime(new Date());

        NbchatTrainCourseText courseText = nbchatTrainCourseTextMapper.queryById(reqBO.getCourseId());
        if (ObjectUtils.isEmpty(courseText)) {
            nbchatTrainCourseTextMapper.insertSelective(record);
        } else {
            nbchatTrainCourseTextMapper.update(record);
        }
        //更新创建课程进度
        nbchatTrainCourseMapper.updateStepState(reqBO.getCourseId(), StateEnum.STEP.ANALYSIS.getCode());
        return BaseRspUtils.createSuccessRsp(null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp saveCourseSections(TrainCourseSectionsQueryReqBO request) {
        log.debug("保存课程章节：{}", request);
        List<SectionsSaveRequest> list = request.getSectionsSaveRequest();
        list.forEach(sectionsSaveRequest -> {
            String videoUrl = sectionsSaveRequest.getVideoUrl();
            SectionsSaveRequest reqBO = new SectionsSaveRequest();
            BeanUtils.copyProperties(sectionsSaveRequest, reqBO);
            if (StringUtils.isNotBlank(videoUrl)) {
                //解绑章节与草稿的关系
                TdhCreationRecord tdhCreationRecord = new TdhCreationRecord();
                tdhCreationRecord.setSectionId(sectionsSaveRequest.getSectionId());
                unBundle(tdhCreationRecord, false);
                //解绑章节与任务的关系
                TdhCreationTask tdhCreationTask = new TdhCreationTask();
                tdhCreationTask.setSectionId(sectionsSaveRequest.getSectionId());
                unBundleTask(tdhCreationTask, false);
            }
            Rsp rsp = nbchatTrainSectionApi.saveSection(reqBO);
            if (!rsp.isSuccess()) {
                log.info("保存课程章节失败：{}", rsp);
            }
            log.info("保存课程章节结果：{}", rsp);
        });
        //保存课程内容
        TranCourseSaveReqBO courseSaveReqBO = request.getCourseSaveReqBO();
        BeanUtils.copyProperties(request.getCourseSaveReqBO(), courseSaveReqBO);
        Rsp rsp = saveCourse(courseSaveReqBO);
        if (!rsp.isSuccess()) {
            log.info("保存课程内容失败：{}", rsp);
            return rsp;
        }
        log.info("保存课程内容结果：{}", rsp);
        String courseVideoUrl = request.getCourseSaveReqBO().getVideoUrl();
        if (StringUtils.isNotBlank(courseVideoUrl)) {
            //解绑课程与草稿的关系
            TdhCreationRecord tdhCreationRecord = new TdhCreationRecord();
            tdhCreationRecord.setCourseId(request.getCourseSaveReqBO().getCourseId());
            unBundle(tdhCreationRecord, true);
            //解绑章节与任务的关系
            TdhCreationTask tdhCreationTask = new TdhCreationTask();
            tdhCreationTask.setCourseId(request.getCourseSaveReqBO().getCourseId());
            unBundleTask(tdhCreationTask, true);
        }
        return BaseRspUtils.createSuccessRsp("保存成功");
    }

    @Override
    public Rsp queryCourseSections(TrainCourseSectionsQueryReqBO request) {
        log.info("查询课程章节：{}", request);
        String courseId = request.getCourseSaveReqBO().getCourseId();
        TrainCourseSectionsQueryRspBO rspBO = new TrainCourseSectionsQueryRspBO();
        RspList<TrainSectionsBO> rspList = nbchatTrainSectionApi.queryCourse(courseId);
        List<TrainSectionsBO> trainSectionsBOList = new ArrayList<>();
        if (!rspList.isSuccess()) {
            log.info("查询课程章节失败：{}", rspList);
        } else {
            trainSectionsBOList = rspList.getRows();
            log.info("查询课程章节结果：{}条", trainSectionsBOList.size());
            trainSectionsBOList.forEach(trainSectionsBO -> {
                if (trainSectionsBO.getVideoSource() == 0) {
                    //查询章节视频的任务状态
                    trainSectionsBO = getSectionTaskState(trainSectionsBO);
                }
                if (trainSectionsBO.getVideoSource() == 1) {
                    trainSectionsBO.setTaskId("");
                    trainSectionsBO.setCreationId("");
                    trainSectionsBO.setTaskState(TaskStateType.MISSION_COMPLETED.getCode());
                }
                //查询章节的名称
                NbchatTrainCatalog catalog = trainCatalogMapper.selectBySectionId(trainSectionsBO.getSectionId());
                log.info("查询章节的名称：{}", catalog);
                trainSectionsBO.setCatalogId(catalog.getCatalogId());
                trainSectionsBO.setCatalogTitle(catalog.getCatalogTitle());
            });
            rspBO.setTrainSectionsBOList(trainSectionsBOList);
        }
        //查询课程内容
        TranCourseQueryReqBO tranCourseQueryReqBO = new TranCourseQueryReqBO();
        BeanUtils.copyProperties(request.getCourseSaveReqBO(), tranCourseQueryReqBO);
        Rsp<TranCourseBO> rsp = getCourse(tranCourseQueryReqBO);
        TranCourseBO tranCourseBO = new TranCourseBO();
        if (!rsp.isSuccess()) {
            rspBO.setTranCourseBO(tranCourseBO);
            log.info("查询课程内容失败：{}", rsp);
        } else {
            tranCourseBO = rsp.getData();
            if (tranCourseBO.getVideoSource() == 0) {
                //查询课程视频的任务状态
                tranCourseBO = getCourseTaskState(tranCourseBO);
            }
            if (tranCourseBO.getVideoSource() == 1) {
                tranCourseBO.setTaskId("");
                tranCourseBO.setCreationId("");
                tranCourseBO.setTaskState(TaskStateType.MISSION_COMPLETED.getCode());
            }
            rspBO.setTranCourseBO(tranCourseBO);
        }
        log.info("查询课程章节结果：{}", rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO, "查询成功");
    }

    private void addExamConfig(NbchatTrainCourse course) {
        try {
            NbchatExamTestPaper testPaper = new NbchatExamTestPaper();
            testPaper.setCourseId(course.getCourseId());
            testPaper.setTenantCode(course.getTenantCode());
            testPaper.setCreateTime(new Date());
            testPaper.setCreateUser(course.getUserId());
            testPaper.setUpdateTime(new Date());
            testPaper.setPassingScore(ExamRuleConstants.DEFAULT_PASSING_SCORE);
            testPaper.setTestNum(ExamRuleConstants.DEFAULT_EXAM_NUM);
            testPaper.setTestType(ExamTestType.RANDOM.getCode());
            testPaper.setTrainState(CourseScoreConstants.ZERO);
            nbchatExamTestPaperMapper.insertSelective(testPaper);
        } catch (Exception e) {
            log.error("添加考试配置失败", e);
        }
    }

    private String createMajor(NbchatTrainCourse courseInfo) {
        try {
            FileResearchMajorSaveRequest request = new FileResearchMajorSaveRequest();
            request.setMajorName(courseInfo.getCourseName() + "机器人");
            request.setMajorDesc(courseInfo.getCourseName());
            request.setTenantCode(courseInfo.getTenantCode());
            request.setUserId(courseInfo.getUserId());
            request.setMajorSource(Integer.valueOf(StateEnum.SOURCE.OTHER_1.getCode()));
            request.setCollectionName("kb_base_course");
            request.setEmbedModel("bce-embedding-base_v1");
            request.setMajorConfig("{\"topK\": 3, \"score\": 0.3, \"rerank\": false, \"searchType\": \"both\", \"rerankModel\": \"bce-reranker-base_v1\"}");
            Rsp rsp = nbchatResearchMajorApi.saveMajor(request);
            if (rsp.isSuccess()) {
                NbchatResearchMajorRspBO data = (NbchatResearchMajorRspBO) rsp.getData();
                return data.getMajorId();
            }
            log.info("创建专属机器人结果：{}", rsp);
        } catch (Exception e) {
            log.error("创建专属机器人失败", e);
        }
        return null;
    }

    private TrainSectionsBO getSectionTaskState(TrainSectionsBO trainSectionsBO) {
        TdhCreationTask tdhCreationTask = new TdhCreationTask();
        tdhCreationTask.setCourseId(trainSectionsBO.getCourseId());
        tdhCreationTask.setSectionId(trainSectionsBO.getSectionId());
        tdhCreationTask.setTenantCode(trainSectionsBO.getTenantCode());
        if (StringUtils.isNotBlank(trainSectionsBO.getVideoUrl())) {
            tdhCreationTask.setVideoUrl(trainSectionsBO.getVideoUrl());
        }

        TdhCreationTask task = getTaskId(tdhCreationTask, false);
        if (task != null) {
            trainSectionsBO.setTaskId(task.getTaskId());
            trainSectionsBO.setTaskState(task.getTaskState());
            trainSectionsBO.setCreationId("");
            List<TdhCreationRecord> tdhCreationRecord = querySectionCreationRecord(trainSectionsBO);
            if (TaskStateType.TASK_EXCEPTION.getCode().equals(task.getTaskState())) {
                String taskState = tdhCreationRecord.size() == 0 ? TaskStateType.TASK_EXCEPTION.getCode() : TaskStateType.EDIT.getCode();
                trainSectionsBO.setTaskState(taskState);
                TdhCreationRecord creationRecord = getCreationId(tdhCreationRecord, false);
                if (TaskStateType.EDIT.getCode().equals(taskState) && creationRecord != null) {
                    trainSectionsBO.setTaskId("");
                    trainSectionsBO.setVideoUrl("");
                    String creationId = creationRecord.getCreationId();
                    trainSectionsBO.setCreationId(creationId);
                }
            }
            TdhCreationRecord creationRecord = getCreationId(tdhCreationRecord, false);
            if (!CollectionUtils.isEmpty(tdhCreationRecord) && creationRecord != null) {
                Date updateTime = creationRecord.getUpdateTime();
                Date startTime = task.getStartTime();
                int comparison = updateTime.compareTo(startTime);
                if (comparison > 0) {
                    //草稿的更新时间大于视频任务的开始时间 代表有视频的时候去点击了编辑课程
                    trainSectionsBO.setTaskState(TaskStateType.EDIT.getCode());
                    trainSectionsBO.setTaskId("");
                    trainSectionsBO.setVideoUrl("");
                    trainSectionsBO.setCreationId(creationRecord.getCreationId());
                }
            }
        } else {
            trainSectionsBO.setTaskId("");
            List<TdhCreationRecord> tdhCreationRecord = querySectionCreationRecord(trainSectionsBO);
            String taskState = tdhCreationRecord.isEmpty() ? TaskStateType.TO_BE_EDITED.getCode() : TaskStateType.EDIT.getCode();
            trainSectionsBO.setTaskState(taskState);
            trainSectionsBO.setCreationId("");
            TdhCreationRecord creationRecord = getCreationId(tdhCreationRecord, false);
            if (TaskStateType.EDIT.getCode().equals(taskState) && creationRecord != null) {
                String creationId = creationRecord.getCreationId();
                trainSectionsBO.setVideoUrl("");
                trainSectionsBO.setCreationId(creationId);
            }
        }
        return trainSectionsBO;
    }

    private TranCourseBO getCourseTaskState(TranCourseBO tranCourseBO) {
        TdhCreationTask tdhCreationTask = new TdhCreationTask();
        tdhCreationTask.setCourseId(tranCourseBO.getCourseId());
        tdhCreationTask.setTenantCode(tranCourseBO.getTenantCode());
        if (StringUtils.isNotBlank(tranCourseBO.getVideoUrl())) {
            tdhCreationTask.setVideoUrl(tranCourseBO.getVideoUrl());
        }

        TdhCreationTask task = getTaskId(tdhCreationTask, true);
        if (task != null) {
            tranCourseBO.setTaskId(task.getTaskId());
            tranCourseBO.setTaskState(task.getTaskState());
            tranCourseBO.setCreationId("");
            List<TdhCreationRecord> tdhCreationRecord = queryCourseCreationRecord(tranCourseBO, false);
            if (TaskStateType.TASK_EXCEPTION.getCode().equals(task.getTaskState())) {

                String taskState = tdhCreationRecord.size() == 0 ? TaskStateType.TASK_EXCEPTION.getCode() : TaskStateType.EDIT.getCode();
                tranCourseBO.setTaskState(taskState);
                TdhCreationRecord creationRecord = getCreationId(tdhCreationRecord, true);
                if (TaskStateType.EDIT.getCode().equals(taskState) && creationRecord != null) {
                    tranCourseBO.setTaskId("");
                    tranCourseBO.setVideoUrl("");
                    String creationId = creationRecord.getCreationId();
                    tranCourseBO.setCreationId(creationId);
                }
            }
            TdhCreationRecord creationRecord = getCreationId(tdhCreationRecord, true);
            if (!CollectionUtils.isEmpty(tdhCreationRecord) && creationRecord != null) {
                Date updateTime = creationRecord.getUpdateTime();
                Date startTime = task.getStartTime();
                int comparison = updateTime.compareTo(startTime);
                if (comparison > 0) {
                    //草稿的更新时间大于视频任务的开始时间 代表有视频的时候去点击了编辑课程
                    tranCourseBO.setTaskState(TaskStateType.EDIT.getCode());
                    tranCourseBO.setTaskId("");
                    tranCourseBO.setVideoUrl("");
                    tranCourseBO.setCreationId(creationRecord.getCreationId());
                }
            }
        } else {
            tranCourseBO.setTaskId("");
            List<TdhCreationRecord> tdhCreationRecord = queryCourseCreationRecord(tranCourseBO, true);
            String taskState = tdhCreationRecord.size() == 0 ? TaskStateType.TO_BE_EDITED.getCode() : TaskStateType.EDIT.getCode();
            tranCourseBO.setTaskState(taskState);
            tranCourseBO.setCreationId("");
            TdhCreationRecord creationRecord = getCreationId(tdhCreationRecord, true);
            if (TaskStateType.EDIT.getCode().equals(taskState) && creationRecord != null) {
                String creationId = creationRecord.getCreationId();
                tranCourseBO.setVideoUrl("");
                tranCourseBO.setCreationId(creationId);
            }
        }
        return tranCourseBO;
    }

    private TdhCreationTask getTaskId(TdhCreationTask task, Boolean course) {
        log.info("查询TaskId：{}", task);
        String courseId = task.getCourseId();
        String sectionId = task.getSectionId();
        String videoUrl = task.getVideoUrl();
        if (StringUtils.isNotBlank(sectionId) && StringUtils.isNotBlank(task.getVideoUrl())) {
            task.setCourseId(null);
            task.setSectionId(null);
        }
        if (course && StringUtils.isNotBlank(videoUrl)) {
            task.setCourseId(null);
        }
        task.setIsValid(EntityValidType.NORMAL.getCode());
        List<TdhCreationTask> list = taskMapper.selectAll(task);
        log.info("查询TaskId结果：{}", list);
        if (CollectionUtils.isEmpty(list) && StringUtils.isNotBlank(task.getVideoUrl())) {
            task.setVideoUrl("");
            task.setCourseId(courseId);
            task.setSectionId(sectionId);
            list = taskMapper.selectAll(task);
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        list = list.stream()
                .filter(li -> li.getStartTime() != null)
                .collect(Collectors.toList());
        if (course && StringUtils.isBlank(videoUrl)) {
            list = list.stream()
                    .filter(li -> li.getSectionId() == null || li.getSectionId().equals(""))
                    .collect(Collectors.toList());
        }
        Date now = new Date();
        TdhCreationTask tdhCreationTask = list.stream().min((task1, task2) -> {
            if (task1.getStartTime() == null || task2.getStartTime() == null) {
                return 0;
            }
            long diff1 = Math.abs(now.getTime() - task1.getStartTime().getTime());
            long diff2 = Math.abs(now.getTime() - task2.getStartTime().getTime());
            return Long.compare(diff1, diff2);
        }).orElse(null);
        log.info("查询TaskId结果：{}", tdhCreationTask);
        return tdhCreationTask;
    }


    private List<TdhCreationRecord> querySectionCreationRecord(TrainSectionsBO trainSectionsBO) {
        TdhCreationRecord record = new TdhCreationRecord();
        record.setCourseId(trainSectionsBO.getCourseId());
        record.setSectionId(trainSectionsBO.getSectionId());
        record.setTenantCode(trainSectionsBO.getTenantCode());
        record.setIsValid(EntityValidType.NORMAL.getCode());
        List<TdhCreationRecord> tdhCreationRecord = tdhCreationRecordMapper.selectAll(record);
        return tdhCreationRecord;
    }

    private TdhCreationRecord getCreationId(List<TdhCreationRecord> list, Boolean course) {
        if (course) {
            list = list.stream()
                    .filter(li -> li.getSectionId() == null || li.getSectionId().equals(""))
                    .collect(Collectors.toList());
        }
        Date now = new Date();
        TdhCreationRecord tdhCreationRecord = list.stream().min((task1, task2) -> {
            if (task1.getUpdateTime() == null || task2.getUpdateTime() == null) {
                return 0;
            }
            long diff1 = Math.abs(now.getTime() - task1.getUpdateTime().getTime());
            long diff2 = Math.abs(now.getTime() - task2.getUpdateTime().getTime());
            return Long.compare(diff1, diff2);
        }).orElse(null);
        return tdhCreationRecord;
    }

    private List<TdhCreationRecord> queryCourseCreationRecord(TranCourseBO tranCourseBO, boolean course) {
        TdhCreationRecord record = new TdhCreationRecord();
        record.setCourseId(tranCourseBO.getCourseId());
        record.setTenantCode(tranCourseBO.getTenantCode());
        record.setIsValid(EntityValidType.NORMAL.getCode());
        List<TdhCreationRecord> list = tdhCreationRecordMapper.selectAll(record);
        if (course) {
            list = list.stream()
                    .filter(li -> li.getSectionId() == null || li.getSectionId().equals(""))
                    .collect(Collectors.toList());
        }
        return list;
    }

    private void unBundle(TdhCreationRecord tdhCreationRecord, Boolean course) {
        List<TdhCreationRecord> creationRecordList = tdhCreationRecordMapper.selectAll(tdhCreationRecord);
        if (!CollectionUtils.isEmpty(creationRecordList)) {
            creationRecordList.forEach(record -> {
                TdhCreationRecord creationRecord = new TdhCreationRecord();
                creationRecord.setCreationId(record.getCreationId());
                if (course) {
                    if (StringUtils.isBlank(record.getSectionId())) {
                        creationRecord.setCourseId("");
                        tdhCreationRecordMapper.update(creationRecord);
                    }
                } else {
                    creationRecord.setSectionId("");
                    creationRecord.setCourseId("");
                    tdhCreationRecordMapper.update(creationRecord);
                }
            });
        }
    }

    private void unBundleTask(TdhCreationTask task, Boolean course) {
        List<TdhCreationTask> list = taskMapper.selectAll(task);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(record -> {
                TdhCreationTask creationTask = new TdhCreationTask();
                creationTask.setTaskId(record.getTaskId());
                if (course) {
                    if (StringUtils.isBlank(record.getSectionId())) {
                        creationTask.setCourseId("");
                        taskMapper.update(creationTask);
                    }
                } else {
                    creationTask.setSectionId("");
                    creationTask.setCourseId("");
                    taskMapper.update(creationTask);
                }
            });
        }
    }
}
