package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.bo.ppt.*;
import com.tydic.nbchat.train.api.ppt.PPTApi;
import com.tydic.nbchat.train.api.ppt.PPTThemeApi;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * PPT模版管理
 */
@Slf4j
@RestController
@RequestMapping("/train/ppt")
public class PPTController {

    private final PPTApi pptApi;
    private final PPTThemeApi pptThemeApi;

    public PPTController(PPTApi pptApi,
                         PPTThemeApi pptThemeApi) {
        this.pptApi = pptApi;
        this.pptThemeApi = pptThemeApi;
    }

    /**
     * 创作记录保存
     *
     * @param request
     * @return
     */
    @PostMapping("/save")
    public Rsp save(@RequestBody PPTCreationRecordBO request) {
        return pptApi.save(request);
    }

    /**
     * 删除主题模版
     *
     * @param request
     * @return
     */
    @PostMapping("/theme/delete")
    public Rsp delete(@RequestBody PPTCreationRecordBO request) {
        return pptApi.delete(request);
    }

    /**
     * 复制主题模版
     *
     * @param request
     * @return
     */
    @PostMapping("/theme/copy")
    public Rsp copyTheme(@RequestBody PPTCreationRecordBO request) {
        return pptApi.copyTheme(request);
    }


    @PostMapping("query")
    public Rsp query(@RequestBody PPTCreationRecordBO request) {
        return pptApi.query(request);
    }

    @PostMapping("layout/query")
    public Rsp layoutQuery(@RequestBody PPTLayoutBO request) {
        return pptApi.layoutQuery(request);
    }

    @PostMapping("layout/info")
    public Rsp layoutQueryInfo(@RequestBody PPTLayoutBO request) {
        request.setTenantCode(null);
        request.setUserId(null);
        return pptApi.layoutQuery(request);
    }

    @PostMapping("theme/query")
    public RspList themeQuery(@RequestBody PPTThemeBO request) {
        return pptApi.themeQuery(request);
    }

    @PostMapping("theme/star/list")
    public RspList themeStarList(@RequestBody PPTThemeBO request) {
        return pptApi.themeStarList(request);
    }

    @PostMapping("theme/info")
    public Rsp themeInfo(@RequestBody PPTThemeBO request) {
        return pptApi.themeInfo(request);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin}, logical = Logical.OR)
    @PostMapping("/theme/admin_list")
    public RspList themeAdminList(@RequestBody PPTThemeBO request) {
        return pptApi.themeAdminList(request);
    }

    @PostMapping("theme/save")
    public Rsp themeSave(@RequestBody PPTThemeReqBO request) {
        return pptApi.themeSave(request);
    }

    @PostMapping("image/conversion")
    public RspList imageConversion(@RequestBody ImageConversionReqBO reqBO) {
        return pptApi.imageConversion(reqBO);
    }

    /**
     * ppt创作记录查询
     *
     * @param request
     * @return
     */
    @PostMapping("/query/create/history")
    public RspList queryCreateHistory(@RequestBody PPTCreationRecordBO request) {
        return pptApi.queryCreateHistory(request);
    }

    /**
     * ppt创作记录复制
     *
     * @param request
     * @return
     */
    @PostMapping("/copy/history")
    public Rsp copyHistory(@RequestBody PPTCreationRecordBO request) {
        return pptApi.copyHistory(request);
    }

    /**
     * 模版的上下架
     *
     * @param request
     * @return
     */
    @PostMapping("/release/template")
    public Rsp releaseTemplate(@RequestBody PPTThemeReqBO request) {
        return pptApi.releaseTemplate(request);
    }

    /**
     * 模版排序（主题模版、视频模版、口播模版）
     *
     * @param request
     * @return
     */
    @PostMapping("/sort/update")
    public Rsp sort(@RequestBody PPTSortReqBO request) {
        return pptApi.sort(request);
    }

    /**
     * 通过场景-风格-配色匹配主题模版
     *
     * @param request
     * @return
     */
    @PostMapping("/theme/match")
    public Rsp matchTheme(@RequestBody PPTThemeMatchReqBO request) {
        return pptApi.matchTheme(request);
    }

    /**
     * AI匹配主题模版
     *
     * @param request
     * @return
     */
    @PostMapping("/theme/ai_match")
    public Rsp aiMatchTheme(@RequestBody PPTThemeMatchReqBO request) {
        return pptThemeApi.aiMatchTheme(request);
    }

}
