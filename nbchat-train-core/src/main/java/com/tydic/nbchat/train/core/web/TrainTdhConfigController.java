package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.tdh.TdhConfigGetRsp;
import com.tydic.nbchat.train.core.config.NbchatTrainConfigProperties;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/train/tdh")
public class TrainTdhConfigController {
    private final NbchatTrainConfigProperties nbchatTrainConfigProperties;

    public TrainTdhConfigController(NbchatTrainConfigProperties nbchatTrainConfigProperties) {
        this.nbchatTrainConfigProperties = nbchatTrainConfigProperties;
    }

    @PostMapping("/config")
    public Rsp getConfig(@RequestBody BaseInfo request){
        TdhConfigGetRsp configGetRsp = TdhConfigGetRsp.builder().
                anchorConfig(nbchatTrainConfigProperties.getAnchorConfig()).build();
       return BaseRspUtils.createSuccessRsp(configGetRsp);
    }

}
