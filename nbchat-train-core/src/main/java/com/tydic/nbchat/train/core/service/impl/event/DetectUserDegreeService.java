package com.tydic.nbchat.train.core.service.impl.event;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.train.api.bo.task.NbchatTaskRecordBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskDegreeBO;
import com.tydic.nbchat.train.api.trainTask.CreateDegreeApi;
import com.tydic.nbchat.train.api.trainTask.TrainTaskApi;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class DetectUserDegreeService {

    private final TrainTaskApi trainTaskApi;
    private final CreateDegreeApi createDegreeApi;

    public DetectUserDegreeService(TrainTaskApi trainTaskApi, CreateDegreeApi createDegreeApi) {
        this.trainTaskApi = trainTaskApi;
        this.createDegreeApi = createDegreeApi;
    }

    public void handle(NbchatTrainTaskBO request) {
        log.info("检测用户证书：{}", JSON.toJSONString(request));
        request.setIsDegree(EntityValidType.NORMAL.getCode());
        RspList rspList = trainTaskApi.queryTask(request);
        if (!rspList.isSuccess() || rspList.getRows().isEmpty()) {
            log.info("检测用户证书-未查询到此用户的任务列表：{}|{}", request.getTenantCode(), request.getUserId());
            return;
        }
        List<NbchatTaskRecordBO> rows = rspList.getRows();
        for (NbchatTaskRecordBO row : rows) {
            if (TrainTaskApi.TRAIN_TASK_FINISH.equals(row.getStatus())) {
                log.info("检测用户证书-用户[{}]的任务[{}|{}]已完成", request.getUserId(), row.getTaskId(), row.getTaskName());
                NbchatTrainTaskDegreeBO bo = new NbchatTrainTaskDegreeBO();
                bo.setTaskId(String.valueOf(row.getTaskId()));
                bo.setTenantCode(request.getTenantCode());
                bo.setUserId(request.getUserId());
                Rsp rsp = createDegreeApi.create(bo);
                log.info("检测用户证书-发放证书结果：{}", rsp);
            }
        }
    }


}
