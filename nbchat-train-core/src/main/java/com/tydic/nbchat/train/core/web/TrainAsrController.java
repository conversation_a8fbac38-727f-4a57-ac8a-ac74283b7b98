package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.bo.asr_tts.AsrVoiceTaskContext;
import com.tydic.nbchat.train.api.bo.asr_tts.AsrVoiceTaskRequest;
import com.tydic.nbchat.train.core.helper.NlsStrategyInvokeFactory;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/train/course/asr")
public class TrainAsrController {
    private final NlsStrategyInvokeFactory nlsStrategyInvokeFactory;

    public TrainAsrController(NlsStrategyInvokeFactory nlsStrategyInvokeFactory) {
        this.nlsStrategyInvokeFactory = nlsStrategyInvokeFactory;
    }

    @PostMapping("/task/create")
    public Rsp<AsrVoiceTaskContext> createAsrTask(@RequestBody AsrVoiceTaskRequest request){
        AsrVoiceTaskContext context = nlsStrategyInvokeFactory.createAsrTask(request);
        if(context.isSuccess()){
            return BaseRspUtils.createSuccessRsp(context);
        }
        return BaseRspUtils.createErrorRsp(context,"语音识别异常!");
    }


}
