package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryReqBO;
import com.tydic.nbchat.train.core.service.impl.trainTask.UserTaskService;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/task")
public class TrainUserTaskController {

    private final UserTaskService userTaskService;

    public TrainUserTaskController(UserTaskService userTaskService) {
        this.userTaskService = userTaskService;
    }

    @PostMapping("user/progress")
    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    public RspList userProgress(@RequestBody SysDeptUserQueryReqBO request) {
        return userTaskService.userProgress(request);
    }


}
