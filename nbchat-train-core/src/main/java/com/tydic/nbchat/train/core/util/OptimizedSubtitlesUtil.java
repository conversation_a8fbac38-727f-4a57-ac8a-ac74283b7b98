package com.tydic.nbchat.train.core.util;

import com.tydic.nbchat.train.api.bo.asr_tts.TtsVoiceResult;
import com.tydic.nbchat.train.api.bo.asr_tts.TtsWordResult;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class OptimizedSubtitlesUtil {

    private static final Pattern CHINESE_CHAR_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]");
    private static final Pattern JAPANESE_CHAR_PATTERN = Pattern.compile("[\\u3040-\\u30FF\\u31F0-\\u31FF\\uFF65-\\uFF9F]");

    /**
     * 是否英文字幕
     * @param subtitles
     * @return
     */
    public static boolean isEnglishSubtitles(List<TtsVoiceResult> subtitles) {
        // 拼接字幕所有文本
        StringBuilder textBuilder = new StringBuilder();
        for (TtsVoiceResult tts : subtitles) {
            textBuilder.append(TrainCommonUtil.removeXMLTags(tts.getText()));
        }
        String text = textBuilder.toString();
        if (text.isEmpty()) {
            return false;
        }
        text = text.trim();
        if (text.isEmpty()) {
            return false;
        }
        // 英文字符数量
        int englishCount = 0;
        // 中文字符数量
        int chineseCount = 0;
        for (char c : text.toCharArray()) {
            if (c >= '\u4e00' && c <= '\u9fa5') {
                chineseCount++;
            } else {
                englishCount++;
            }
        }
        return englishCount / (double) (englishCount + chineseCount) > 0.9;
    }

    public static boolean isChineseDominant(String text) {
        String content =  TrainCommonUtil.removeXMLTags(text);
        if (StringUtils.isEmpty(content)) {
            return false;
        }
        int chineseCount = 0;
        int japaneseCount = 0;
        int totalCount = 0;

        for (char c : content.toCharArray()) {
            if (Character.isWhitespace(c)) {
                continue; // 忽略空白字符
            }
            totalCount++;
            if (isChineseCharacter(c)) {
                chineseCount++;
            } else if (isJapaneseCharacter(c)) {
                japaneseCount++;
            }
        }
        // 如果中文字符比例大于50%，且中文字符多于日文字符，则认为是中文为主
        return totalCount > 0 && ((double) chineseCount / totalCount) > 0.7 && chineseCount > japaneseCount;
    }



    /**
     * 判断字符是否为中文字符
     *
     * @param c 输入字符
     * @return 如果是中文字符，返回 true；否则返回 false
     */
    private static boolean isChineseCharacter(char c) {
        Matcher matcher = CHINESE_CHAR_PATTERN.matcher(String.valueOf(c));
        return matcher.find();
    }


    /**
     * 判断字符是否为日文字符
     *
     * @param c 输入字符
     * @return 如果是日文字符，返回 true；否则返回 false
     */
    private static boolean isJapaneseCharacter(char c) {
        Matcher matcher = JAPANESE_CHAR_PATTERN.matcher(String.valueOf(c));
        return matcher.find();
    }


    public static String removeLastPunctuation(String text) {
        if (text == null) {
            return "";
        }
        if (text.length() > 1) {
            if (text.endsWith(",") || text.endsWith("，") || text.endsWith(".") || text.endsWith("!") || text.endsWith("！")
                    || text.endsWith("、") || text.endsWith("；") || text.endsWith(";") || text.endsWith("。")
                    || text.endsWith("：")) {
                return text.substring(0, text.length() - 1);
            }
        }
        return text;
    }

    public static String trim_enter_blank(String text) {
        if (text != null) {
            text = text.trim();
            text = text.replaceAll("\t|\n|\r\n", "");
        }
        return text;
    }

    /**
     * 对字幕进行优化：原始python代码
     *
     * @param list
     * @return
     */
    public static List<TtsVoiceResult> optimizeSubtitles(List<TtsVoiceResult> list) {
        boolean is_optimized = true;
        List<TtsVoiceResult> optimizedList = new ArrayList<>();
        for (TtsVoiceResult tts : list) {
            int start_time = tts.getBegin_Time();
            int end_time = tts.getEnd_time();
            String text = tts.getText();
            text = trim_enter_blank(text);
            if (text.isEmpty()) {
                continue;
            }
            if (text.length() > 22) {
                float durationPerChar = (end_time - start_time) / (float) text.length();
                //四舍五入
                String[] sentences = text.split("(?<=([。！？；]))");
                for (int i = 0; i < sentences.length; i++) {
                    String sentence = sentences[i];
                    end_time = Math.round(start_time + sentence.length() * durationPerChar);
                    if (!sentence.isEmpty()) {
                        TtsVoiceResult obj = buildTtsObject(start_time, end_time, sentence);
                        optimizedList.add(obj);
                        if (i < sentences.length - 1) {
                            start_time = end_time;
                        } else {
                            start_time = tts.getEnd_time();
                        }
                        if (sentence.length() > 22) {
                            is_optimized = false;
                        }
                    }
                }
            } else {
                optimizedList.add(tts);
            }
        }
        if (!is_optimized) {
            //System.out.println(JSONObject.toJSONString(optimizedList));
            return optimizeSubtitlesSub(optimizedList, 22);
        }
        return optimizedList;
    }

    public static List<String> splitText(String text, int maxLine) {
        //根据长度对字符串平均分割
        List<String> list = new ArrayList<>();
        int len = text.length();
        int start = 0;
        int end = maxLine;
        while (start < len) {
            if (end > len) {
                end = len;
            }
            list.add(text.substring(start, end));
            start = end;
            end += maxLine;
        }
        return list;
    }


    public static List<TtsVoiceResult> splitByCharLen(TtsVoiceResult tts) {
        int start_time = tts.getBegin_Time();
        int end_time = tts.getEnd_time();
        float durationPerChar = (end_time - start_time) / (float) tts.getText().length();
        //按照长度分
        List<String> list = splitText(tts.getText(), 25);
        List<TtsVoiceResult> resultList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            String text = list.get(i);
            end_time = Math.round(start_time + text.length() * durationPerChar);
            TtsVoiceResult obj = buildTtsObject(start_time, end_time, text);
            if (i < list.size() - 1) {
                start_time = end_time;
            } else {
                start_time = tts.getEnd_time();
            }
            resultList.add(obj);
        }
        return resultList;
    }

    /**
     * @return
     */
    public static List<TtsVoiceResult> optimizeSubtitlesSub(List<TtsVoiceResult> tts, int lineLimit) {
        List<TtsVoiceResult> optimizedList = new ArrayList<>();
        for (TtsVoiceResult ttsVoiceResult : tts) {
            String text = ttsVoiceResult.getText();
            int start_time = ttsVoiceResult.getBegin_Time();
            int end_time = ttsVoiceResult.getEnd_time();
            if (text.isEmpty()) {
                continue;
            }
            if (text.length() > lineLimit) {
                float durationPerChar = (end_time - start_time) / (float) text.length();
                String[] sentences = text.split("(?<=([，、]))");
                List<String> newSentences = Arrays.asList(sentences);
                for (int i = 0; i < newSentences.size(); i++) {
                    String sentence = newSentences.get(i);
                    end_time = Math.round(start_time + sentence.length() * durationPerChar);
                    if (!sentence.isEmpty()) {
                        TtsVoiceResult obj = buildTtsObject(start_time, end_time, sentence);
                        if (i < newSentences.size() - 1) {
                            start_time = end_time;
                        } else {
                            start_time = ttsVoiceResult.getEnd_time();
                        }
                        if (obj.getText().length() > 22) {
                            //继续拆分，按字符长度直接截取
                            optimizedList.addAll(splitByCharLen(obj));
                        } else {
                            optimizedList.add(obj);
                        }
                    }
                }
            } else {
                TtsVoiceResult obj = new TtsVoiceResult();
                obj.setBegin_Time(ttsVoiceResult.getBegin_Time());
                obj.setEnd_time(ttsVoiceResult.getEnd_time());
                obj.setText(removeLastPunctuation(text));
                optimizedList.add(obj);
            }
        }
        return optimizedList;
    }

    private static TtsVoiceResult buildTtsObject(int start_time, int end_time, String text) {
        TtsVoiceResult ttsVoiceResult = new TtsVoiceResult();
        ttsVoiceResult.setBegin_Time(start_time);
        ttsVoiceResult.setEnd_time(end_time);
        ttsVoiceResult.setText(text);
        return ttsVoiceResult;
    }

    /**
     * # 计算一句话，每个字的起始时间和结束时间
     * def calculate_word_time(sentence: str, start: int,end: int) -> list:
     *     """
     *     计算一句话，每个字的起始时间和结束时间
     *     返回一个字典列表 [{"s": "","start": 0,"end": 200}]
     *     :param sentence:
     *     :param start:
     *     :param end:
     *     :return:
     *     """
     *     # 按字符平均计算每个字符的时间，保留3位小数
     *     word_time = (end - start) / len(sentence)
     *     words = []
     *     if sentence and len(sentence) > 0:
     *         for idx, word in enumerate(sentence):
     *             words.append({
     *                 "s": word,
     *                 "start": round(start + word_time * idx, 3),
     *                 "end": round(start + word_time * (idx + 1), 3)
     *             })
     *     return words
     */
    public static List<TtsWordResult> calculateWordTime(String sentence, int start, int end) {
        List<TtsWordResult> words = new ArrayList<>();
        if (sentence != null && !sentence.isEmpty()) {
            float word_time = (end - start) / (float) sentence.length();
            for (int i = 0; i < sentence.length(); i++) {
                TtsWordResult result = new TtsWordResult();
                result.setS(sentence.substring(i, i + 1));
                result.setStart(Math.round(start + word_time * i));
                result.setEnd(Math.round(start + word_time * (i + 1)));
                words.add(result);
            }
        }
        return words;
    }

    /**
     * 对英文字幕进行优化
     * @param list
     * @return
     */
    public static List<TtsVoiceResult> optimizeEnglishSubtitles(List<TtsVoiceResult> list) {
        List<TtsVoiceResult> optimizedSubtitles = new ArrayList<>();
        int lineLimit = 50;
        for (TtsVoiceResult subtitle : list) {
            String text = subtitle.getText();
            int startTime = subtitle.getBegin_Time();
            int endTime = subtitle.getEnd_time();
            if (text.isEmpty()) {
                continue;
            }
            if (text.length() > lineLimit) {
                double durationPerChar = (double) (endTime - startTime) / text.length();
                String[] sentences = text.split("\\. ");
                List<String> newSentences = new ArrayList<>();
                for (String sentence : sentences) {
                    if (sentence.length() > lineLimit) {
                        String[] subSentences = sentence.split(", ");
                        for (String subSentence : subSentences) {
                            if (subSentence.length() > lineLimit) {
                                String[] subSubSentences = subSentence.split("; ");
                                for (String subSubSentence : subSubSentences) {
                                    if (subSubSentence.length() > lineLimit) {
                                        String[] words = subSubSentence.split(" ");
                                        StringBuilder tempSentence = new StringBuilder();
                                        for (String word : words) {
                                            if (tempSentence.length() + word.length() + 1 > lineLimit) {
                                                newSentences.add(tempSentence.toString().trim());
                                                tempSentence = new StringBuilder(word);
                                            } else {
                                                tempSentence.append(" ").append(word);
                                            }
                                        }
                                        if (tempSentence.length() > 0) {
                                            newSentences.add(tempSentence.toString().trim());
                                        }
                                    } else {
                                        newSentences.add(subSubSentence.trim());
                                    }
                                }
                            } else {
                                newSentences.add(subSentence.trim());
                            }
                        }
                    } else {
                        newSentences.add(sentence.trim());
                    }
                }
                for (String newSentence : newSentences) {
                    if (newSentence.trim().isEmpty()) {
                        continue;
                    }
                    int newEndTime = startTime + (int) (newSentence.length() * durationPerChar);
                    optimizedSubtitles.add(new TtsVoiceResult(startTime, newEndTime, newSentence,""));
                    startTime = newEndTime;
                }
            } else {
                optimizedSubtitles.add(new TtsVoiceResult(startTime, endTime, text,""));
            }
        }
        return optimizedSubtitles;
    }

    public static void main(String[] args) {
        String text1 = "以下の说明では、时代背景と社会构造、天下统一を目指した三人の杰出した人物、戦国大名のタイプ分析、そして戦国时代の歴史的影响という侧面から展开します。";
        String text2 = "これはテストです。少しだけ中文があります。";

        System.out.println("Text1 is Chinese dominant: " + isChineseDominant(text1)); // true
        System.out.println("Text2 is Chinese dominant: " + isChineseDominant(text2)); // false
    }
}