package com.tydic.nbchat.train.core.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-train.config")
public class NbchatTrainConfigProperties {
    private boolean pageJoinEnable = true;
    private int pageJoinWordLimit = 500;
    private String courseGenerateRobot = "chatgpt";
    private String anchorConfig = "ali";
    private String anchorAsrConfig = "";
    private String anchorTtsConfig = "";
    private String tdhApi = "/tdh/task/submit"; //创建视频内网地址
    private String tdhEmbeddingApi = ""; //文本向量化接口
    private String tdhAnalysisPdfUrl = "/tdh/tools/doc_extract"; //解析pdf背景、内容请求地址
    private ModelscopeConfigProperties modelscopeConfig;

    private String degreeTaskUrlPrefix = "https://chat-test.tydiczt.com/chat/#/certificateExamDetails?taskId={TASKID}&userId={USERID}";

    public String getAsrType(){
        if (StringUtils.isBlank(anchorAsrConfig)){
            return anchorConfig;
        }
        return anchorAsrConfig;
    }

    public String getTtsType(){
        if (StringUtils.isBlank(anchorTtsConfig)){
            return anchorConfig;
        }
        return anchorTtsConfig;
    }
}
