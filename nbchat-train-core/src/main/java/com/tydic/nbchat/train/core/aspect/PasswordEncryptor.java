package com.tydic.nbchat.train.core.aspect;

import com.tydic.nicc.dc.boot.starter.util.JasyptENC;
import org.jasypt.util.text.BasicTextEncryptor;

public class PasswordEncryptor {
    public static void main(String[] args) {
        BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        textEncryptor.setPasswordCharArray("Ug8gYp75aV".toCharArray());
        String myEncryptedPassword = textEncryptor.encrypt("JNEGOtydic2024dba");
        System.out.println(myEncryptedPassword);

        String encryptedPassword = "Hk/i96m8TKbQhg+0jS6UwxAbzVqrbrXVcSlNUTnVDNw=";
        String encryptionKey = "encryptionkey";
        String decryptedPassword = JasyptENC.decrypt(encryptionKey, encryptedPassword);
        System.out.println("解密后的密码: " + decryptedPassword);
    }
}