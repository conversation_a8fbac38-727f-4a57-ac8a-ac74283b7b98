package com.tydic.nbchat.train.core.web;


import com.tydic.nbchat.train.api.trainTask.TrainTaskApi;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskConfigBO;
import com.tydic.nbchat.train.core.service.impl.event.EventPublishFactory;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/task")
public class TrainTaskController {

    private final TrainTaskApi trainTaskApi;


    public TrainTaskController(TrainTaskApi trainTaskApi) {
        this.trainTaskApi = trainTaskApi;
    }


    @PostMapping("save")
    public Rsp save(@RequestBody NbchatTrainTaskConfigBO request) {
        Rsp save = trainTaskApi.save(request);
        Integer taskId = request.getTaskConfig().getId();
        String startStatus = request.getTaskConfig().getStartStatus();
        if ("1".equals(startStatus) && ObjectUtils.isNotEmpty(taskId)) {
            EventPublishFactory.publishModifyTaskEvent(request.getTenantCode(), String.valueOf(taskId));
        }
        return save;
    }

    @PostMapping("list")
    public RspList list(@RequestBody NbchatTrainTaskBO request) {
        return trainTaskApi.list(request);
    }

    @PostMapping("info")
    public Rsp info(@RequestBody NbchatTrainTaskBO request) {
        return trainTaskApi.info(request);
    }

    @PostMapping("user/list")
    public RspList queryTask(@RequestBody NbchatTrainTaskBO request) {
        return trainTaskApi.queryTask(request);
    }

    @PostMapping("type/course")
    public RspList typeCourse(@RequestBody NbchatTrainTaskBO request) {
        return trainTaskApi.queryTaskCourse(request);
    }

    /**
     * 学习任务分析
     * @param request
     * @return
     */
    @PostMapping("analysis")
    public RspList analysis(@RequestBody NbchatTrainTaskBO request) {
        return trainTaskApi.analysis(request);
    }

    @PostMapping("course/list")
    public RspList queryTaskCourseList(@RequestBody NbchatTrainTaskBO request) {
        return trainTaskApi.queryTaskCourseList(request);
    }

    @PostMapping("/course/list/noauth")
    public RspList queryTaskCourseListNoAuth(@RequestBody NbchatTrainTaskBO request) {
        return trainTaskApi.queryTaskCourseList(request);
    }

}
