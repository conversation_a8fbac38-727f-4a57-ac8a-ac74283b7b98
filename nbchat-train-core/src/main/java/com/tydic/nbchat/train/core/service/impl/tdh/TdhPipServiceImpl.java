package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.constants.TdhPipConstants;
import com.tydic.nbchat.train.api.bo.tdh.TdhPipQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhPipQueryRspBO;
import com.tydic.nbchat.train.api.tdh.TdhPipApi;
import com.tydic.nbchat.train.core.util.RegularUtil;
import com.tydic.nbchat.train.mapper.TdhPipMapper;
import com.tydic.nbchat.train.mapper.po.TdhPip;
import com.tydic.nbchat.train.mapper.po.TdhPipCondition;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class TdhPipServiceImpl implements TdhPipApi {
    private final TdhPipMapper tdhPipMapper;
    /**
     * 新增画中画记录
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RspList AddPip(TdhPipQueryReqBO reqBO) {
        List<TdhPipQueryRspBO> rspList = new ArrayList<>();
        TdhPip tdhPip = new TdhPip();
        BeanUtils.copyProperties(reqBO,tdhPip);
        reqBO.getUrlList().forEach(pipUrlBO -> {
            String id = IdWorker.nextAutoIdStr();
            tdhPip.setName(pipUrlBO.getName());
            tdhPip.setPipUrl(pipUrlBO.getPipUrl());
            tdhPip.setCategory(pipUrlBO.getCategory());
            tdhPip.setPipDesc(pipUrlBO.getPipDesc());
            tdhPip.setPipType(pipUrlBO.getPipType());
            tdhPip.setFirstFrame(pipUrlBO.getFirstFrame());
            tdhPip.setIsValid(TdhPipConstants.DEFAULT_VALID);
            tdhPip.setCreateTime(new Date());
            tdhPip.setPipId(id);
            tdhPip.setPipSource(TdhPipConstants.DEFAULT_PIP_SOURCE);
            int save = tdhPipMapper.insertSelective(tdhPip);
            log.info("新增画中画记录：{}",save);
            if (save !=1){
                log.info("新增画中画失败");
            }
            log.info("新增画中画成功");
            TdhPipQueryRspBO tdhPipQueryRspBO = new TdhPipQueryRspBO();
            BeanUtils.copyProperties(tdhPip,tdhPipQueryRspBO);
            rspList.add(tdhPipQueryRspBO);
        });

        return BaseRspUtils.createSuccessRspList(rspList,rspList.size());
    }

    /**
     * 修改画中画
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public RspList update(TdhPipQueryReqBO reqBO) {
        List<TdhPipQueryRspBO> rspBOList = new ArrayList<>();
        TdhPip tdhPip = new TdhPip();
        BeanUtils.copyProperties(reqBO,tdhPip);
        tdhPip.setIsValid(null);
        tdhPip.setCreateTime(null);
        reqBO.getUrlList().forEach(pipUrlBO ->{
            tdhPip.setPipId(pipUrlBO.getPipId());
            tdhPip.setName(pipUrlBO.getName());
            tdhPip.setPipUrl(pipUrlBO.getPipUrl());
            tdhPip.setCategory(pipUrlBO.getCategory());
            tdhPip.setPipDesc(pipUrlBO.getPipDesc());
            tdhPip.setPipType(pipUrlBO.getPipType());
            tdhPip.setFirstFrame(pipUrlBO.getFirstFrame());
            int update = tdhPipMapper.update(tdhPip);
            if (update !=1){
                log.info("修改画中画失败");
            }
            log.info("修改画中画成功");
            TdhPipQueryRspBO tdhPipQueryRspBO = new TdhPipQueryRspBO();
            BeanUtils.copyProperties(tdhPip,tdhPipQueryRspBO);
            rspBOList.add(tdhPipQueryRspBO);
        });

        return BaseRspUtils.createSuccessRspList(rspBOList,rspBOList.size());
    }

    /**
     * 查询最近20条上传记录
     * @param @param reqBO
     * @return @return {@link RspList }
     */
    @Override
    public RspList queryRecentHistory(TdhPipQueryReqBO reqBO) {
        List<TdhPipQueryRspBO> rspBOList = new ArrayList<>();
        TdhPipCondition condition = new TdhPipCondition();
        BeanUtils.copyProperties(reqBO, condition);

        // 根据 fileType 设置 pipTypes
        if ("image".equals(reqBO.getFileType())) {
            condition.setPipTypes(Arrays.asList("png", "jpg", "gif"));
        } else if ("video".equals(reqBO.getFileType())) {
            condition.setPipTypes(Arrays.asList("mp4", "avi", "mov"));
        }

        Page page = PageHelper.startPage(TdhPipConstants.DEFAULT_PAGE_NUM, TdhPipConstants.DEFAULT_PAGE_SIZE);
        log.info("查询最近20条上传记录-condition：{}", condition);
        List<TdhPip> pipList = tdhPipMapper.selectPipRecentHistoryByCondition(condition);
        if (CollectionUtils.isEmpty(pipList)) {
            log.info("查询最近20条上传记录-结果为空");
            return BaseRspUtils.createSuccessRspList(rspBOList);
        }
        log.info("查询最近20条上传记录-结果：{}", pipList);
        NiccCommonUtil.copyList(page.getResult(), rspBOList, TdhPipQueryRspBO.class);
        log.info("查询最近20条上传记录-结果：{}|{}", page.getTotal(), rspBOList);
        return BaseRspUtils.createSuccessRspList(rspBOList, page.getTotal());
    }
    /**
     * 查询画中画
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp query(TdhPipQueryReqBO reqBO) {
        TdhPip tdhPip = tdhPipMapper.queryById(reqBO.getPipId());
        if (StringUtils.isEmpty(tdhPip)){
            log.info("查询画中画失败");
            return BaseRspUtils.createErrorRsp("查询失败");
        }
        log.info("查询画中画成功:{}",tdhPip);
        return BaseRspUtils.createSuccessRsp(tdhPip,"查询成功");
    }

    /**
     * 删除画中画
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp delete(TdhPipQueryReqBO reqBO) {
        TdhPip tdhPip = new TdhPip();
        tdhPip.setIsValid(TdhPipConstants.DEFAULT_INVALID);
        tdhPip.setPipId(reqBO.getPipId());
        log.info("删除画中画：tdhPip-{}",tdhPip);
        int delete = tdhPipMapper.update(tdhPip);
        if (delete !=1){
            log.info("删除画中画失败");
            return BaseRspUtils.createErrorRsp("删除失败");
        }
        log.info("删除画中画成功");
        return BaseRspUtils.createSuccessRsp("删除成功");
    }
}
