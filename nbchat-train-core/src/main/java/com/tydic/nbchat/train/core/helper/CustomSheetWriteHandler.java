package com.tydic.nbchat.train.core.helper;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.ss.usermodel.*;

import org.apache.poi.ss.util.CellRangeAddress;

public class CustomSheetWriteHandler implements SheetWriteHandler {

    private final int maxColumn;

    public CustomSheetWriteHandler(int maxColumn) {
        this.maxColumn = maxColumn;
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        Sheet sheet = writeSheetHolder.getSheet();
        Workbook workbook = writeWorkbookHolder.getWorkbook();

        // 空出前三行
        sheet.createRow(0);
        // 根据最大列数合并单元格
        CellRangeAddress cellRangeAddress = new CellRangeAddress(0, 0, 0, maxColumn - 1);
        sheet.addMergedRegion(cellRangeAddress);
        sheet.setColumnWidth(0, 11*256);
        sheet.setColumnWidth(1, 45*256);
        sheet.setColumnWidth(2, 11*256);
        sheet.setColumnWidth(3, 45*256);
        int defaultWidth = 31 * 256; // 默认列宽为 15 个字符宽
        for (int i = 4; i < maxColumn; i++) {
            sheet.setColumnWidth(i, defaultWidth);
        }

        // 创建一个新行
        Row row = sheet.createRow(0);
        row.setHeight((short) 900);
        // 创建一个新单元格
        Cell cell = row.createCell(0);
        // 设置单元格的值
        cell.setCellValue("课件帮-试题导出");

        // 创建Apache POI字体对象
        Font font = workbook.createFont();
        font.setFontName("宋体"); // 设置字体为宋体
        font.setFontHeightInPoints((short)20); // 设置字体大小为20号
        font.setBold(true); // 设置字体加粗

        // 创建Apache POI单元格样式对象
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setFont(font); // 应用字体
        cellStyle.setAlignment(HorizontalAlignment.CENTER); // 设置水平居中
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 设置垂直居中
        cellStyle.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex()); // 设置背景颜色为黄色
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND); // 设置填充模式为实心填充

        // 应用样式到单元格
        cell.setCellStyle(cellStyle);

    }
}