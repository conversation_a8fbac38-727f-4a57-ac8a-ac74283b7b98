package com.tydic.nbchat.train.core.service.impl.trainTask;

import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.train.api.bo.eums.ProjectType;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskDegreeBO;
import com.tydic.nbchat.train.api.trainTask.CreateDegreeApi;
import com.tydic.nbchat.train.core.config.DegreeConfigProperties;
import com.tydic.nbchat.train.core.service.impl.degree.DegreeFactory;
import com.tydic.nbchat.train.mapper.NameMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainTaskDegreeMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainTaskMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTask;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskDegree;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.util.Date;
import java.util.List;


@Slf4j
@Service
public class CreateDegreeServiceImpl implements CreateDegreeApi {

    @Resource
    NbchatTrainTaskDegreeMapper nbchatTrainTaskDegreeMapper;
    @Resource
    NbchatTrainTaskMapper nbchatTrainTaskMapper;
    @Resource
    NameMapper nameMapper;


    private final DegreeFactory degreeFactory;
    private final DegreeConfigProperties degreeConfigProperties;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 50000)
    private FileManageService fileManageService;

    public CreateDegreeServiceImpl(DegreeFactory degreeFactory, DegreeConfigProperties degreeConfigProperties) {
        this.degreeFactory = degreeFactory;
        this.degreeConfigProperties = degreeConfigProperties;
    }


    @Override
    public Rsp makeDegree(String degreeId) throws Exception {
        degreeFactory.createDegree(degreeId, ProjectType.Zhongjiao);
        return null;
    }

    @Override
    public Rsp create(NbchatTrainTaskDegreeBO request) {
        log.info("创建/更新证书请求参数：{}", request);
        if (StringUtils.isEmpty(request.getId())) {
            if (StringUtils.isEmpty(request.getTaskId())) {
                return BaseRspUtils.createErrorRsp("任务id不能为空");
            }
            NbchatTrainTask trainTask = nbchatTrainTaskMapper.queryTask(Integer.valueOf(request.getTaskId()));
            if (ObjectUtils.isEmpty(trainTask)) {
                return BaseRspUtils.createErrorRsp("任务不存在");
            }
            NbchatTrainTaskDegree degreeRecord = nbchatTrainTaskDegreeMapper.queryDegree(String.valueOf(request.getTaskId()), request.getUserId());
            if (ObjectUtils.isNotEmpty(degreeRecord) && StringUtils.isNotEmpty(degreeRecord.getDegreeUrl())) {
                log.info("已获取证书1：userId={}|degreeId={}",request.getUserId(),degreeRecord.getId());
                return BaseRspUtils.createSuccessRsp(degreeRecord);
            }
            if (trainTask.getEndTime().before(new Date())) {
                log.warn("任务已过期，无法发放证书：taskId={},userId={}", request.getTaskId(),request.getUserId());
                return BaseRspUtils.createErrorRsp("任务已过期，无法取证");
            }
            NbchatTrainTaskDegree po = new NbchatTrainTaskDegree();
            BeanUtils.copyProperties(request, po);
            List<NbchatTrainTaskDegree> degrees = nbchatTrainTaskDegreeMapper.selectAll(po);
            if (CollectionUtils.isNotEmpty(degrees) ) {
                if (StringUtils.isEmpty(degrees.get(0).getDegreeUrl())) {
                    return this.make(degrees.get(0));
                } else {
                    log.info("已获取证书2：{}|{}",request.getUserId(),degrees.get(0));
                    return BaseRspUtils.createSuccessRsp(degrees.get(0));
                }
            }
            //保存证书记录
            NbchatTrainTaskDegree degree = this.saveRecord(request, trainTask);
            return this.make(degree);
        } else {
            if (StringUtils.isEmpty(request.getId())) {
                return BaseRspUtils.createErrorRsp("证书id不能为空");
            }
            NbchatTrainTaskDegree po = new NbchatTrainTaskDegree();
            po.setId(request.getId());
            po.setStatus(request.getStatus());
            nbchatTrainTaskDegreeMapper.update(po);
            return BaseRspUtils.createSuccessRsp("更新证书状态成功");
        }
    }

    //制作证书
    public Rsp make(NbchatTrainTaskDegree degree){
        try {
            ProjectType type = this.getProjectType(degree.getTenantCode());
            //创建证书接口
            String degreePath = degreeFactory.createDegree(degree.getId(), type);
            if (StringUtils.isEmpty(degreePath)) {
                return BaseRspUtils.createErrorRsp("创建证书失败");
            }
            //上传证书
            RspList rspList = this.uploadDegree(degreePath,degree.getTenantCode(),degree.getUserId());
            if (rspList.isSuccess()) {
                FileManageSaveBO res = (FileManageSaveBO) rspList.getRows().get(0);
                degree.setDegreeUrl(res.getAccessUrl());
                nbchatTrainTaskDegreeMapper.update(degree);
                log.info("上传证书成功，证书地址：{}", res.getAccessUrl());
                return BaseRspUtils.createSuccessRsp(degree);
            } else {
                return BaseRspUtils.createErrorRsp("上传证书失败");
            }
        } catch (Exception e) {
            log.error("创建证书失败", e);
            return BaseRspUtils.createErrorRsp("创建证书失败");
        }
    }

    public ProjectType getProjectType(String tenantCode) {
        ProjectType type = ProjectType.Zhongjiao;
        String code = degreeConfigProperties.getProjectCode().get(tenantCode);
        if (StringUtils.isNotEmpty(code)) {
            type = ProjectType.getByCode(code);
        }
        return type;
    }

    public RspList uploadDegree(String degreePath,String tenantCode,String userId) throws Exception {
        log.info("上传证书请求参数：证书地址：{}", degreePath);
        String fileName = degreePath.substring(degreePath.lastIndexOf("/") + 1);
        byte[] byteArray = IOUtils.toByteArray(new FileInputStream(degreePath));
        FileUploadRequest uploadRequest = FileUploadRequest.builder().
                file(byteArray).fileName(fileName).autoDir(false).targetDir("tdh/degree")
                .uploadUser(userId).tenantCode(tenantCode).build();
        return fileManageService.fileUploadRequest(uploadRequest);
    }

    public NbchatTrainTaskDegree saveRecord(NbchatTrainTaskDegreeBO request, NbchatTrainTask trainTask) {
        NbchatTrainTaskDegree po = new NbchatTrainTaskDegree();
        BeanUtils.copyProperties(request, po);

        po.setId(IdWorker.nextAutoIdStr());
        String phone = nameMapper.queryUserPhone(request.getUserId(), request.getTenantCode());
        po.setPhone(phone);
        String userName = nameMapper.queryUserName(request.getUserId(), request.getTenantCode());
        po.setUserName(userName);
        String deptId = nameMapper.queryDeptId(request.getUserId(), request.getTenantCode());
        po.setDeptId(deptId);
        po.setPostId(trainTask.getPostId());
        po.setDegreeName(trainTask.getDegreeName());
        po.setValidityPeriod(trainTask.getDegreeValidity());
        po.setIssueDate(new Date());
        nbchatTrainTaskDegreeMapper.insertSelective(po);
        return po;
    }




}
