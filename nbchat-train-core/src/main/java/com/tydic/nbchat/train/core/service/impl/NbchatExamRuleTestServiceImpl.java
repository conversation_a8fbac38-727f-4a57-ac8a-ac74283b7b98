package com.tydic.nbchat.train.core.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.train.api.NbcahtExamQuestionApi;
import com.tydic.nbchat.train.api.NbchatExamRuleTestApi;
import com.tydic.nbchat.train.api.bo.QuestionSaveRequest;
import com.tydic.nbchat.train.api.bo.constants.CourseScoreConstants;
import com.tydic.nbchat.train.api.bo.constants.ExamRuleConstants;
import com.tydic.nbchat.train.api.bo.constants.QuestionConstants;
import com.tydic.nbchat.train.api.bo.eums.ExamTestType;
import com.tydic.nbchat.train.api.bo.eums.QuestionType;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.exam.ExamQuestion;
import com.tydic.nbchat.train.api.bo.exam.ExamQuestionItem;
import com.tydic.nbchat.train.api.bo.examRule.ExamRuleBO;
import com.tydic.nbchat.train.api.bo.examRule.ExamRuleReqBO;
import com.tydic.nbchat.train.api.bo.examRule.ExamRuleRspBO;
import com.tydic.nbchat.train.api.bo.question.QuestionSaveRspBO;
import com.tydic.nbchat.train.core.helper.CustomSheetWriteHandler;
import com.tydic.nbchat.train.core.helper.QuestionExcelRowWriteHandler;
import com.tydic.nbchat.train.core.service.impl.excel.ExamReadListener;
import com.tydic.nbchat.train.mapper.*;
import com.tydic.nbchat.train.mapper.po.*;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.FileManagerHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class NbchatExamRuleTestServiceImpl implements NbchatExamRuleTestApi {
    @Resource
    private NbchatExamTestPaperMapper nbchatExamTestPaperMapper;
    @Resource
    private NbchatTrainCourseMapper nbchatTrainCourseMapper;
    @Resource
    private NbchatTrainCourseCategoryMapper nbchatTrainCourseCategoryMapper;
    @Resource
    private NbcahtExamQuestionApi nbcahtExamQuestionApi;
    @Resource
    private NbchatExamQuestionMapper nbchatExamQuestionMapper;
    @Resource
    private NbchatExamQuestionItemsMapper nbchatExamQuestionItemsMapper;


    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private FileManageService fileManageService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp save(ExamRuleBO request) {
        log.info("保存考试配置:{}", request);
        //判断课程是否存在
        NbchatTrainCourse course = nbchatTrainCourseMapper.selectByPrimaryKey(request.getCourseId());
        if (course == null || !EntityValidType.NORMAL.getCode().equals(course.getIsValid())) {
            log.info("保存考试配置:查询失败：该课程不存在!{}", request.getCourseId());
            return BaseRspUtils.createErrorRsp("该课程不存在!");
        }
        log.info("保存考试配置:课程查询成功!{}", course);
        //判断题数
        int testNum = request.getTestNum();
        if (testNum >= ExamRuleConstants.MIN_EXAM_NUM && testNum <= ExamRuleConstants.MAX_EXAM_NUM && testNum % ExamRuleConstants.EXAM_NUM_MAGNIFICATION == 0) {
            // testNum 是 5 的倍数，且值位于 10 到 50 之间
            log.info("保存考试配置:考试次数满足条件!{}", request.getTestNum());
        } else {
            // testNum 不是 5 的倍数，或者值不位于 10 到 50 之间
            log.info("保存考试配置:考试次数不满足条件!{},已设置为默认", request.getTestNum());
            request.setTestNum(ExamRuleConstants.DEFAULT_EXAM_NUM);
        }
        //判断题类型是否和试题数相等
        if (request.getMultiple() + request.getSingle() + request.getTrueOrFalse() != request.getTestNum()) {
            log.info("保存考试配置:试题类型的数量设置不正确-单选|多选|判断|试卷数量:{}|{}|{}|{}", request.getSingle(), request.getMultiple(), request.getTestNum(), request.getTestNum());
            return BaseRspUtils.createErrorRsp("试题数量不符合");
        }
        //判断出题顺序
        if (StringUtils.isNotBlank(request.getTestType()) || (ExamTestType.ORDER.getCode() != request.getTestType() && ExamTestType.RANDOM.getCode() != request.getTestType())) {
            request.setTestType(ExamTestType.RANDOM.getCode());
        }
        //判断合格分数
        if (StringUtils.isBlank(request.getPassingScore())) {
            request.setPassingScore(ExamRuleConstants.DEFAULT_PASSING_SCORE);
        }
        //判断是否需要学完课程
        if (StringUtils.isBlank(request.getTrainState())) {
            request.setTrainState(CourseScoreConstants.ZERO);
        }
        log.info("保存考试配置:出题顺序为：{}", request.getTestType());
        //判断课程是否已经配置过
        NbchatExamTestPaper nbchatExamTestPaper = nbchatExamTestPaperMapper.queryByCourseId(request.getCourseId(), request.getTenantCode());
        if (nbchatExamTestPaper == null) {
            log.info("保存考试配置:该课程{}——{}未配置过考试配置!进行保存操作！", request.getCourseId(), course.getCourseName());
            //插入
            nbchatExamTestPaper = new NbchatExamTestPaper();
            BeanUtils.copyProperties(request, nbchatExamTestPaper);
            nbchatExamTestPaperMapper.insert(nbchatExamTestPaper);
            log.info("保存考试配置-成功:{}", nbchatExamTestPaper);
        } else {
            log.info("保存考试配置:该课程{}——{}已配置过考试配置!进行更新操作！", request.getCourseId(), course.getCourseName());
            //更新
            nbchatExamTestPaper = new NbchatExamTestPaper();
            BeanUtils.copyProperties(request, nbchatExamTestPaper);
            nbchatExamTestPaper.setUpdateTime(new Date());
            nbchatExamTestPaperMapper.update(nbchatExamTestPaper);
            log.info("更新考试配置-成功:{}", nbchatExamTestPaper);
        }
        //更新配置状态
        NbchatTrainCourse nbchatTrainCourse = new NbchatTrainCourse();
        nbchatTrainCourse.setCourseId(request.getCourseId());
        nbchatTrainCourse.setUpdateTime(new Date());
        nbchatTrainCourse.setTestPaperState(StateEnum.COURSE.EDIT.getCode());
        nbchatTrainCourseMapper.updateByPrimaryKeySelective(nbchatTrainCourse);
        if (StringUtils.isNotBlank(request.getContent())) {
            log.info("保存考试配置:试题内容不为空:{}", request.getContent());
            //保存试题
            QuestionSaveRequest questionSaveRequest = new QuestionSaveRequest();
            BeanUtils.copyProperties(request, questionSaveRequest);
            Rsp rsp = saveQuestion(questionSaveRequest, request.getSingle(), request.getMultiple(), request.getTrueOrFalse());
            return rsp;
        }
        return BaseRspUtils.createSuccessRsp("保存成功");
    }

    /**
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp query(ExamRuleReqBO request) {
        log.info("查询考试配置:{}", request);
        NbchatTrainCourse nbchatTrainCourse = nbchatTrainCourseMapper.selectByPrimaryKey(request.getCourseId());
        if (nbchatTrainCourse == null || !EntityValidType.NORMAL.getCode().equals(nbchatTrainCourse.getIsValid())) {
            log.error("查询考试配置:查询失败：该课程不存在!{}", request.getCourseId());
            return BaseRspUtils.createErrorRsp("该课程不存在!");
        }
        log.info("查询考试配置:查询课程成功!{}", nbchatTrainCourse);
        ExamRuleRspBO examRuleRspBO = new ExamRuleRspBO();
        examRuleRspBO.setCourseName(nbchatTrainCourse.getCourseName());
        examRuleRspBO.setExamState(nbchatTrainCourse.getTestPaperState());
        examRuleRspBO.setTenantCode(nbchatTrainCourse.getTenantCode());
        examRuleRspBO.setCourseId(nbchatTrainCourse.getCourseId());
        examRuleRspBO.setCourseState(nbchatTrainCourse.getCourseState());
        examRuleRspBO.setCourseType(nbchatTrainCourse.getCourseType());
        //查询分类名称
        NbchatTrainCourseCategory nbchatTrainCourseCategory = nbchatTrainCourseCategoryMapper.queryById(nbchatTrainCourse.getCategory2());
        if (nbchatTrainCourseCategory == null) {
            log.error("查询考试配置:查询失败：该课程分类不存在!{}", nbchatTrainCourse.getCategory2());
        } else {
            examRuleRspBO.setCatalogName(nbchatTrainCourseCategory.getCateName());
            log.info("查询考试配置:查询课程分类成功!{}", nbchatTrainCourseCategory);
        }
        NbchatExamTestPaper nbchatExamTestPapers = nbchatExamTestPaperMapper.queryByCourseId(request.getCourseId(), request.getTenantCode());
        if (nbchatExamTestPapers == null) {
            log.warn("查询考试配置:查询失败：该课程的考试配置不存在!{}", request.getCourseId());
            return BaseRspUtils.createSuccessRsp(examRuleRspBO, "该课程的考试配置不存在!");
        }
        log.info("查询考试配置:查询课程的考试配置成功!{}", nbchatExamTestPapers);
        BeanUtils.copyProperties(nbchatExamTestPapers, examRuleRspBO);
        //判断是否查询试题
        if (CourseScoreConstants.ZERO.equals(request.getQueryQuestion())) {
            log.info("查询考试配置-成功:{}", examRuleRspBO);
            return BaseRspUtils.createSuccessRsp(examRuleRspBO, "查询成功");
        }
        List<ExamQuestion> examQuestionList = queryQuestion(request.getCourseId());
        if (CollectionUtils.isNotEmpty(examQuestionList)) {
            examRuleRspBO.setQuestions(examQuestionList);
        }
        log.info("查询考试配置-成功:{}", examRuleRspBO);
        return BaseRspUtils.createSuccessRsp(examRuleRspBO, "查询成功");
    }

    /**
     * 题库配置查询(分页)
     *
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    @Override
    public RspList getCourseExamList(ExamRuleReqBO request) {
        log.info("查询考试配置(分页): {}", request);
        List<ExamRuleRspBO> result = new ArrayList<>();
        NbchatTrainCourseSelectCondition condition = new NbchatTrainCourseSelectCondition();
        BeanUtils.copyProperties(request, condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        Page<NbchatTrainCourse> page = PageHelper.startPage(request.getPage(), request.getLimit());
        nbchatTrainCourseMapper.selectCourseByCondition(condition);
        page.getResult().forEach(nbchatTrainCourse -> {
            request.setCourseId(nbchatTrainCourse.getCourseId());
            request.setQueryQuestion(CourseScoreConstants.ZERO);
            Rsp rsp = query(request);
            if (rsp.isSuccess()) {
                ExamRuleRspBO examRuleRspBO = (ExamRuleRspBO) rsp.getData();
                result.add(examRuleRspBO);
            }
        });
        log.info("查询考试配置(分页): {}", page.getTotal());
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    /**
     * 上架
     *
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp putOnShelves(ExamRuleReqBO request) {
        if (StringUtils.isBlank(request.getTestPaperState())) {
            log.warn("查询考试配置:查询失败：试题配置状态不能为空!{}", request.getTestPaperState());
            return BaseRspUtils.createErrorRsp("试题配置状态不能为空！");
        }
        if (StateEnum.COURSE.ON.getCode().equals(request.getTestPaperState())) {
            List<NbchatExamQuestion> nbchatExamQuestionList = nbchatExamQuestionMapper.selectByCourseId(request.getCourseId());
            if (nbchatExamQuestionList == null || nbchatExamQuestionList.size() == 0) {
                log.warn("查询考试配置:查询失败：试题不存在!{}", request.getCourseId());
                return BaseRspUtils.createErrorRsp("请先 生成试题！");
            }
            int examQuestionsNum = nbchatExamQuestionList.size();
            NbchatExamTestPaper nbchatExamTestPaper = nbchatExamTestPaperMapper.queryByCourseId(request.getCourseId(), request.getTenantCode());
            if (nbchatExamTestPaper == null) {
                log.warn("查询考试配置:查询失败：该课程的考试配置不存在!{}", request.getCourseId());
                return BaseRspUtils.createErrorRsp("该课程的考试配置不存在！");
            }
            int num = nbchatExamTestPaper.getTestNum();
            if (examQuestionsNum < num) {
                log.warn("查询考试配置:查询失败：配置题数大于题库题数!{}", request.getCourseId());
                return BaseRspUtils.createErrorRsp("配置题数大于题库题数，请重新配置！");
            }
        }
        //更新配置状态
        NbchatTrainCourse nbchatTrainCourse = new NbchatTrainCourse();
        nbchatTrainCourse.setCourseId(request.getCourseId());
        nbchatTrainCourse.setUpdateTime(new Date());
        nbchatTrainCourse.setTestPaperState(request.getTestPaperState());
        nbchatTrainCourseMapper.updateByPrimaryKeySelective(nbchatTrainCourse);
        return BaseRspUtils.createSuccessRsp("操作成功！");
    }

    /**
     * 题库导出
     *
     * @param request
     * @return
     */
    @Override
    public Rsp export(ExamRuleReqBO request) {
        log.info("试题导出:{}", request);
        List<ExamQuestion> questions = queryQuestion(request.getIds(), null);
        Rsp rsp = export(questions, request);
        return rsp;
    }

    @Override
    @Transactional
    public Rsp importFile(InputStream inputStream, String courseId, String tenantCode) {
        ExamReadListener listener = new ExamReadListener(nbcahtExamQuestionApi, courseId, tenantCode);
        EasyExcel.read(inputStream).headRowNumber(2).registerReadListener(listener).sheet().doRead();
        return BaseRspUtils.createSuccessRsp("导入成功");
    }

    /**
     * 试题导出
     *
     * @param request
     * @return
     */
    @Override
    public Rsp exportQuestion(ExamRuleReqBO request) {
        String content = request.getContent();
        JSONArray jsonArray = JSON.parseArray(content);
        List<ExamQuestion> questions = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            ExamQuestion examQuestion = new ExamQuestion();
            examQuestion.setQuestionName(jsonObject.getString("question"));
            examQuestion.setQuestionType(jsonObject.getString("type"));
            examQuestion.setDifficulty(jsonObject.getString("difficulty"));
            examQuestion.setExplan(jsonObject.getString("explan"));
            examQuestion.setKnowledges(jsonObject.getString("knowledges"));
            JSONArray itemsArray = jsonObject.getJSONArray("items");
            List<ExamQuestionItem> items = new ArrayList<>();
            for (int j = 0; j < itemsArray.size(); j++) {
                ExamQuestionItem item = new ExamQuestionItem();
                item.setItemValue(itemsArray.getString(j));
                item.setItemIndex((short) j);
                items.add(item);
            }
            examQuestion.setItems(items);
            //判断是否为选择题
            if (QuestionType.isChoice(jsonObject.getString("type")) || QuestionType.isJudgment(jsonObject.getString("type"))) {
                JSONArray answersJsonArray = jsonObject.getJSONArray("answers");
                List<String> answers = new ArrayList<>();
                for (int j = 0; j < answersJsonArray.size(); j++) {
                    answers.add(answersJsonArray.getString(j));
                }
                examQuestion.setAnswers(answers);
            }else {
                List<String> answers = items.stream()
                        .map(ExamQuestionItem::getItemValue)
                        .collect(Collectors.toList());
                examQuestion.setAnswers(answers);
            }
            questions.add(examQuestion);
        }
        Rsp rsp = export(questions, request);
        return rsp;
    }

    private Rsp export(List<ExamQuestion> questions, ExamRuleReqBO request) {
        QuestionSaveRspBO rspBO = new QuestionSaveRspBO();
        try {
            String fileName = QuestionConstants.EXCEL_NAME + new SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒").format(new Date()) + ".xlsx";
            String tempPath = System.getProperty("java.io.tmpdir");
            File dirFile = new File(tempPath + "/" + fileName);
            //转换数据格式
            List<NbchatQuestion> data = convertToExportFormat(questions);
            // 根据最大选项数量动态创建表头
            int maxOptions = data.stream().mapToInt(e -> e.getOptions().size()).max().orElse(0);
            int totalColumns = QuestionConstants.FIXED_COLUMN + maxOptions;
            List<List<String>> head = createDynamicHead(maxOptions);

            //设置excel格式
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = setExcelStyle();
            EasyExcel.write(dirFile)
                    .head(head)
                    .sheet(QuestionConstants.EXCEL_NAME)
                    .registerWriteHandler(new CustomSheetWriteHandler(totalColumns))
                    .registerWriteHandler(horizontalCellStyleStrategy) // 注册样式策略
                    .registerWriteHandler(new QuestionExcelRowWriteHandler())
                    //设置默认样式及写入头信息开始的行数
                    .useDefaultStyle(true).relativeHeadRowIndex(1)
                    .doWrite(data.stream().map(item -> {
                        List<Object> row = new ArrayList<>();
                        row.add(item.getQuestionType());
                        row.add(item.getQuestionName());
                        row.add(item.getDifficulty());
                        row.add(item.getExplan());
                        row.add(item.getKnowledges());
                        row.add(item.getAnswer());
                        row.addAll(item.getOptions());
                        return row;
                    }).collect(Collectors.toList()));
            log.info("试题导出:{}|{}", dirFile.exists(), dirFile.getAbsolutePath());
            MultipartFile multipartFile = FileManagerHelper.parseToMultipartFile(dirFile);
            FileUploadRequest uploadRequest = new FileUploadRequest();
            uploadRequest.setTenantCode(request.getTenantCode());
            uploadRequest.setUploadUser(request.getUserId());
            uploadRequest.setFileName(dirFile.getName());
            uploadRequest.setFile(multipartFile.getBytes());
            uploadRequest.setUseOriginalName(true);
            RspList<FileManageSaveBO> fileManageSaveBOS = fileManageService.fileUploadRequest(uploadRequest);
            log.info("文件上传完毕，信息：{}", JSON.toJSONString(fileManageSaveBOS));
            if (!fileManageSaveBOS.isSuccess()) {
                return BaseRspUtils.createErrorRsp("上传文件失败");
            }
            rspBO.setFileManageSaveBO(fileManageSaveBOS.getRows());
            log.info("试题导出-导出成功");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return BaseRspUtils.createSuccessRsp(rspBO, "导出成功");
    }


    public List<ExamQuestion> queryQuestion(List<String> qIds, Object obj) {
        if (CollectionUtils.isNotEmpty(qIds)) {
            List<NbchatExamQuestion> questions = nbchatExamQuestionMapper.queryQuestions(qIds);
            return this.queryQuestion(questions);
        }
        return new ArrayList<>();
    }

    public List<ExamQuestion> queryQuestion(List<NbchatExamQuestion> questions) {
        List<ExamQuestion> examQuestions = new ArrayList<>();
        questions.forEach(nbchatExamQuestion -> {
            ExamQuestion examQuestion = new ExamQuestion();
            examQuestion.setQuestionId(nbchatExamQuestion.getQuestionId());
            examQuestion.setQuestionName(nbchatExamQuestion.getQuestionName());
            examQuestion.setQuestionType(nbchatExamQuestion.getQuestionType());
            examQuestion.setExplan(nbchatExamQuestion.getExplan());
            examQuestion.setKnowledges(nbchatExamQuestion.getKnowledges());
            examQuestion.setDifficulty(nbchatExamQuestion.getDifficulty());
            NbchatExamQuestionItems condition = new NbchatExamQuestionItems();
            condition.setQuestionId(nbchatExamQuestion.getQuestionId());
            //查询试题选项
            List<NbchatExamQuestionItems> itemRec = nbchatExamQuestionItemsMapper.selectByCondition(condition);
            if (itemRec == null || itemRec.size() == 0) {
                log.error("查询考试配置:查询失败：试题选项不存在!{}", nbchatExamQuestion.getQuestionId());
            }
            List<ExamQuestionItem> items = new ArrayList<>();
            NiccCommonUtil.copyList(itemRec, items, ExamQuestionItem.class);
            examQuestion.setItems(items);
            //判断是否为选择题
            if (QuestionType.isChoice(nbchatExamQuestion.getQuestionType()) || QuestionType.isJudgment(nbchatExamQuestion.getQuestionType())) {
                List<String> answers = items.stream()
                        .filter(item -> ExamRuleConstants.ANSWERS_VALUE.equals(item.getIsRight()))
                        .map(ExamQuestionItem::getItemIndex)
                        .map(String::valueOf)
                        .collect(Collectors.toList());
                examQuestion.setAnswers(answers);
            }else {
                List<String> answers = items.stream()
                        .map(ExamQuestionItem::getItemValue)
                        .collect(Collectors.toList());
                examQuestion.setAnswers(answers);
            }
            examQuestions.add(examQuestion);
        });
        return examQuestions;
    }

    /**
     * 查询试题
     *
     * @param @param courseId 课程ID
     * @return @return {@link List }<{@link ExamQuestion }>
     */
    public List<ExamQuestion> queryQuestion(String courseId) {
        log.info("查询考试配置:开始查询试题:{}", courseId);
        List<NbchatExamQuestion> nbchatExamQuestionList = nbchatExamQuestionMapper.selectByCourseId(courseId);
        if (nbchatExamQuestionList == null || nbchatExamQuestionList.size() == 0) {
            log.warn("查询考试配置:查询失败：试题不存在!{}", courseId);
            return null;
        }
        log.info("查询考试配置:查询试题成功!共有：{}条", nbchatExamQuestionList.size());
        List<ExamQuestion> examQuestions = this.queryQuestion(nbchatExamQuestionList);
        log.info("查询考试配置:查询成功!共有：{}条", examQuestions.size());
        return examQuestions;
    }

    /**
     * 保存试题
     *
     * @param @param questionSaveRequest 问题保存请求
     * @return @return {@link Rsp }
     */
    @Transactional(rollbackFor = Exception.class)
    public Rsp saveQuestion(QuestionSaveRequest questionSaveRequest, int singleNum, int multipleNum, int trueOrFalseNum) {
        log.info("保存考试配置:开始保存试题");
        //删除原有试题
        int del = nbchatExamTestPaperMapper.deleteQuestion(questionSaveRequest.getCourseId(), questionSaveRequest.getTenantCode());
        log.info("删除原有试题-成功:{}条", del);
        int delItem = nbchatExamTestPaperMapper.deleteQuestionItem(questionSaveRequest.getCourseId(), questionSaveRequest.getTenantCode());
        log.info("删除原有试题选项-成功:{}条", delItem);
        //保存试题
        if (!JSONValidator.from(questionSaveRequest.getContent()).validate()) {
            log.info("保存考试配置:试题结构异常:{}", questionSaveRequest.getContent());
            return BaseRspUtils.createErrorRsp("试题结构异常");
        }
        int single = saveQuestions(questionSaveRequest, QuestionType.CHOICE_S.getCode());
        int multiple = saveQuestions(questionSaveRequest, QuestionType.CHOICE_M.getCode());
        int trueOrFalse = saveQuestions(questionSaveRequest, QuestionType.TRUE_FALSE.getCode());
        //判断试题类型的数量是否对应
        if (singleNum > single || multipleNum > multiple || trueOrFalseNum > trueOrFalse) {
            return BaseRspUtils.createErrorRsp("试题类型数量不符合");
        }
        return BaseRspUtils.createSuccessRsp("保存成功");
    }

    private int saveQuestions(QuestionSaveRequest questionSaveRequest, String questionType) {
        List<String> list = JSON.parseObject(questionSaveRequest.getContent(), new TypeReference<List<String>>() {
        });
        List<String> questionsList = list.stream()
                .filter(json -> questionType.equals(JSON.parseObject(json).getString("type")))
                .collect(Collectors.toList());
        log.info("保存试题-开始:{}", questionsList.size());
        if (CollectionUtils.isNotEmpty(questionsList)) {
            QuestionSaveRequest request = new QuestionSaveRequest();
            request.setContent(questionsList.toString());
            request.setCourseId(questionSaveRequest.getCourseId());
            request.setTenantCode(questionSaveRequest.getTenantCode());
            request.setQuestionType(questionType);
            request.setUserId(questionSaveRequest.getUserId());
            request.setPartId(questionSaveRequest.getPartId());
            Rsp rsp = nbcahtExamQuestionApi.saveQuestion(request);
            if (rsp.isSuccess()) {
                log.info("保存试题-成功:{}|{}", questionType, rsp);
            } else {
                log.error("保存试题-失败:{}|{}", questionType, rsp);
            }
        }
        return questionsList.size();
    }

    /**
     * 转换数据格式
     *
     * @param examQuestions
     * @return
     */
    public List<NbchatQuestion> convertToExportFormat(List<ExamQuestion> examQuestions) {
        // 使用 stream 查找最大的 items 集合大小
        OptionalInt maxItemsSizeOptional = examQuestions.stream()
                .mapToInt(examQuestion -> examQuestion.getItems().size())
                .max(); // 找出最大值
        // 转换为 int，如果没有找到最大值，则使用默认值，比如 0
        int maxItemsSize = maxItemsSizeOptional.orElse(0);
        List<NbchatQuestion> exports = new ArrayList<>();
        for (ExamQuestion question : examQuestions) {
            NbchatQuestion export = new NbchatQuestion();
            export.setQuestionType(QuestionType.getNameByCode(question.getQuestionType()));
            export.setQuestionName(question.getQuestionName());
            export.setDifficulty(question.getDifficulty());
            export.setExplan(question.getExplan());
            export.setKnowledges(question.getKnowledges());
            // 设置选项
            List<String> options = new ArrayList<>();
            for (ExamQuestionItem item : question.getItems()) {
                options.add(item.getItemValue());
            }
            if (question.getItems().size() < maxItemsSize) {
                for (int i = 0; i < maxItemsSize - question.getItems().size(); i++) {
                    options.add("");
                }
            }
            export.setOptions(options);
            // 设置正确答案
            StringBuilder answer = new StringBuilder();
            if (QuestionType.isChoice(question.getQuestionType()) || QuestionType.isJudgment(question.getQuestionType())){
                List<Short> answers = question.getAnswers().stream().map(Short::parseShort).collect(Collectors.toList());
                for (short ans : answers) {
                    if (answer.length() > 0) answer.append(",");
                    answer.append((char) ('A' + ans));
                }
            }else {
                for (String ans : question.getAnswers()) {
                    if (answer.length() > 0) answer.append("；");
                    answer.append(ans);
                }
            }
            export.setAnswer(answer.toString());
            exports.add(export);
        }
        return exports;
    }

    /**
     * 创建动态表头
     *
     * @param maxOptions
     * @return
     */
    private List<List<String>> createDynamicHead(int maxOptions) {
        List<List<String>> head = new ArrayList<>();
        head.add(Collections.singletonList("试题类型"));
        head.add(Collections.singletonList("题目"));
        head.add(Collections.singletonList("难度"));
        head.add(Collections.singletonList("试题解析"));
        head.add(Collections.singletonList("知识要点"));
        head.add(Collections.singletonList("正确答案"));
        for (int i = 0; i < maxOptions; i++) {
            head.add(Collections.singletonList("选项" + (char) ('A' + i)));
        }
        return head;
    }

    /**
     * 设置excel格式
     *
     * @return
     */
    private HorizontalCellStyleStrategy setExcelStyle() {
        //设置内容格式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints(QuestionConstants.CONTENT_FONT_HEIGHT);
        contentWriteFont.setFontName(QuestionConstants.FONT_NAME);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 设置水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);

        //设置表头格式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName(QuestionConstants.FONT_NAME);
        headWriteFont.setFontHeightInPoints(QuestionConstants.HEAD_FONT_HEIGHT);
        headWriteFont.setBold(false);
        headWriteCellStyle.setWriteFont(headWriteFont);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        return horizontalCellStyleStrategy;
    }

}
