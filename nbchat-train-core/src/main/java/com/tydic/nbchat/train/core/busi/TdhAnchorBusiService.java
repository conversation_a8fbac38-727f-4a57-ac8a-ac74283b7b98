package com.tydic.nbchat.train.core.busi;

import com.tydic.nbchat.train.api.bo.eums.AnchorType;
import com.tydic.nbchat.train.api.bo.eums.CustomizeStatusEnum;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualAnchorQueryReqBO;
import com.tydic.nbchat.train.mapper.TdhVirtualAnchorMapper;
import com.tydic.nbchat.train.mapper.po.TdhVirtualAnchor;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class TdhAnchorBusiService {

    @Resource
    private TdhVirtualAnchorMapper virtualAnchorMapper;

    public TdhVirtualAnchor addAnchor(TdhCustomizeRecordQueryReqBO request) {
        TdhVirtualAnchor anchor = new TdhVirtualAnchor();
        anchor.setAnchorId(IdWorker.nextAutoIdStr());
        anchor.setTenantCode(request.getTenantCode());
        anchor.setUserId(request.getUserId());
        anchor.setName(request.getVoiceName());
        anchor.setAnchorConfig(AnchorType.COSYVOICE.getCode());
        anchor.setOrderNo(request.getOrderNo());
        anchor.setCustomizeStatus(CustomizeStatusEnum.ORDER_CREATE.getCode());
        if (StringUtils.isEmpty(request.getVolcId())) {
            anchor.setVoice("cvo_" + DateTimeUtil.getTimeShortString(new Date(), DateTimeUtil.TIME_FORMAT_SHORT));
        } else {
            anchor.setVoice(request.getVolcId());
        }
        anchor.setDemoUrl(request.getVoiceDemo());
        anchor.setCreateTime(new Date());
        anchor.setAnchorSource("1");
        anchor.setCategory("定制");
        anchor.setType("通用场景");
        anchor.setLanguage("中文");
        virtualAnchorMapper.insertSelective(anchor);
        return anchor;
    }


    public void add(TdhVirtualAnchorQueryReqBO request) {
        TdhVirtualAnchor po = new TdhVirtualAnchor();
        BeanUtils.copyProperties(request, po);
        if (StringUtils.isEmpty(request.getAnchorId())) {
            virtualAnchorMapper.insertSelective(po);
        } else {
            virtualAnchorMapper.update(po);
        }
    }

    public void updateAnchor(TdhVirtualAnchorQueryReqBO request) {
        if (StringUtils.isEmpty(request.getOrderNo())) {
            log.info("更新数字人信息失败，orderNo为空");
            return;
        }
        TdhVirtualAnchor po = new TdhVirtualAnchor();
        BeanUtils.copyProperties(request, po);
        virtualAnchorMapper.updateByOrderNo(po);
    }

}
