package com.tydic.nbchat.train.core.service.impl.ppt;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.tydic.nbchat.admin.api.SysTreeCategoryApi;
import com.tydic.nbchat.admin.api.bo.category.SysTreeCategoryReqBO;
import com.tydic.nbchat.admin.api.bo.category.SysTreeCategoryRsqBO;
import com.tydic.nbchat.robot.api.NbchatRobotToolsApi;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nbchat.train.api.bo.eums.ComposeType;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.ppt.PPTThemeBO;
import com.tydic.nbchat.train.api.bo.ppt.PPTThemeMatchReqBO;
import com.tydic.nbchat.train.api.bo.ppt.PPTThemeMatchRspBO;
import com.tydic.nbchat.train.api.ppt.PPTThemeApi;
import com.tydic.nbchat.train.core.util.PptThemeMatcherProcessor;
import com.tydic.nbchat.train.core.util.RegularUtil;
import com.tydic.nbchat.train.mapper.PptThemeMapper;
import com.tydic.nbchat.train.mapper.TdhMaterialPackageMapper;
import com.tydic.nbchat.train.mapper.po.PptTheme;
import com.tydic.nbchat.train.mapper.po.TdhMaterialPackage;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PPTThemeServiceImpl implements PPTThemeApi {

    // JSON响应中的键值常量
    private static final String SCENE_KEY = "scene";      // 场景键
    private static final String STYLE_KEY = "style";      // 风格键
    private static final String COLOR_KEY = "color";      // 配色键
    private static final String CATEGORY_KEY = "m";       // 素材类别键
    private static final String SUB_CATEGORY_KEY = "n";   // 素材子类别键
    private static final String MATERIAL_NAME_KEY = "a";  // 素材包名称键
    private static final String MATERIAL_KEYWORDS_KEY = "k";  // 素材包关键词键
    private static final String MATERIAL_KEY = "s";      // 素材包键
    private static final String DEFAULT_IMAGE_PROMPT_ID = "ppt_picture_filter";
    private static final String DEFAULT_IMAGE_PKG_NAME = "概念图形";
    private static final String MATERIAL_CATEGORY_BUSI_TYPE = "material_pack";
    private static final int MATERIAL_CATEGORY_NUM = 9999999;

    @Resource
    private PptThemeMapper pptThemeMapper;

    @Resource
    private TdhMaterialPackageMapper tdhMaterialPackageMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private SysTreeCategoryApi sysTreeCategoryApi;


    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private NbchatRobotToolsApi nbchatRobotToolsApi;

    /**
     * AI匹配主题和素材包
     *
     * @param request 匹配请求参数
     * @return 匹配结果响应
     */
    @Override
    public Rsp aiMatchTheme(PPTThemeMatchReqBO request) {
        PPTThemeMatchRspBO rspBO = new PPTThemeMatchRspBO();
        rspBO.setPresetId(request.getPresetId());
        rspBO.setRobotType(request.getRobotType());

        // 处理主题匹配
        rspBO.setContent(processThemeMatching(request,rspBO));

        // 处理图像包匹配
        request.setPresetId(request.getImagePromptId() == null ? DEFAULT_IMAGE_PROMPT_ID : request.getImagePromptId());
        List<String> presetPrompts = request.getPresetPrompts();
        presetPrompts.add(getCompleteMaterialPacks());
        request.setPresetPrompts(presetPrompts);
        rspBO.setPkgId(processImagePackageMatching(request));

        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    /**
     * 处理主题匹配逻辑
     *
     * @param request 匹配请求参数
     * @return 匹配的主题ID
     */
    private String processThemeMatching(PPTThemeMatchReqBO request,PPTThemeMatchRspBO rspBO) {
        JSONObject jsonObject = getChatResult(request);
        if (jsonObject != null) {
            try {
                rspBO.setAiRecommend(jsonObject.toString());
                String scene = jsonObject.getString(SCENE_KEY);
                List<String> style = jsonObject.getJSONArray(STYLE_KEY).toJavaList(String.class);
                List<String> color = jsonObject.getJSONArray(COLOR_KEY).toJavaList(String.class);

                request.setScene(scene);
                request.setStyle(style);
                request.setColor(color);

                return matchTheme(request);
            } catch (Exception e) {
                log.error("AI匹配主题模板-解析主题JSON响应时出错: {}", e.getMessage(), e);
            }
        }

        // 默认随机返回一个主题
        log.info("AI匹配主题模板-无法获取AI匹配主题，将随机选择一个主题");
        return pptThemeMapper.findRandomThemeId();
    }

    /**
     * 处理素材包匹配逻辑
     *
     * @param request 匹配请求参数
     * @return 匹配的素材包ID
     */
    private String processImagePackageMatching(PPTThemeMatchReqBO request) {
        JSONObject imageJsonObject = getChatResult(request);
        if (imageJsonObject != null) {
            try {
                String materialPackageName = imageJsonObject.getString(MATERIAL_NAME_KEY);

                TdhMaterialPackage record = new TdhMaterialPackage();
                record.setPkgName(materialPackageName);
                record.setStatus(EntityValidType.NORMAL.getCode());
                record.setIsValid(EntityValidType.NORMAL.getCode());

                List<TdhMaterialPackage> packageList = tdhMaterialPackageMapper.selectByCondition(record);
                if (!packageList.isEmpty()) {
                    return packageList.get(0).getPkgId();
                }
            } catch (Exception e) {
                log.error("AI匹配素材包-解析素材包JSON响应时出错: {}", e.getMessage(), e);
            }
        }

        // 默认随机返回一个素材包
        log.info("AI匹配素材包-无法获取AI匹配素材包，将采用固定素材包：{}", DEFAULT_IMAGE_PKG_NAME);
        String pkgId = tdhMaterialPackageMapper.findByPkgName(DEFAULT_IMAGE_PKG_NAME);
        log.info("AI匹配素材包-固定素材包ID: {}", pkgId);
        return pkgId==null?tdhMaterialPackageMapper.findRandomMaterialPackageId():pkgId;
    }

    /**
     * 根据场景、风格和配色匹配主题
     *
     * @param request 包含场景、风格和配色的请求
     * @return 匹配的主题ID
     */
    private String matchTheme(PPTThemeMatchReqBO request) {
        PptTheme pptTheme = new PptTheme();
        pptTheme.setTenantCode(request.getTenantCode());
        pptTheme.setUserId(request.getUserId());
        pptTheme.setScene(request.getScene());
        pptTheme.setIsValid(EntityValidType.NORMAL.getCode());
        pptTheme.setThemeState(EntityValidType.NORMAL.getCode());
        pptTheme.setThemeSource(StateEnum.SOURCE.SYSTEM.getCode());
        pptTheme.setComposeType(ComposeType.THEME.getCode());

        List<PptTheme> pptThemeList = pptThemeMapper.selectAll(pptTheme);
        if (CollectionUtils.isEmpty(pptThemeList)) {
            log.warn("AI匹配主题模板-用户{}在{}场景下没有找到可用主题", request.getUserId(), request.getScene());
            return pptThemeMapper.findRandomThemeId();
        }

        Optional<PptTheme> optionalPptTheme = PptThemeMatcherProcessor.findBestMatch(
                pptThemeList, request.getStyle(), request.getColor());

        if (!optionalPptTheme.isPresent()) {
            log.warn("AI匹配主题模板-用户{}在{}场景下没有找到匹配的主题", request.getUserId(), request.getScene());
            return pptThemeMapper.findRandomThemeId();
        }

        PPTThemeBO rspBO = new PPTThemeBO();
        BeanUtils.copyProperties(optionalPptTheme.get(), rspBO);
        return rspBO.getThemeId();
    }

    /**
     * 获取AI聊天结果
     *
     * @param request 请求参数
     * @return 解析后的JSON对象，如果获取失败则返回null
     */
    private JSONObject getChatResult(PPTThemeMatchReqBO request) {
        if (request == null || StringUtils.isEmpty(request.getPresetId())) {
            log.warn("AI匹配-请求无效预设ID为空");
            return null;
        }

        try {
            RobotPromptMessageRequest promptMessageRequest = new RobotPromptMessageRequest();
            BeanUtils.copyProperties(request, promptMessageRequest);

            Rsp rsp = nbchatRobotToolsApi.getChatResult(promptMessageRequest);
            if (!rsp.isSuccess() || rsp.getData() == null) {
                log.warn("AI匹配-失败，预设ID: {}", request.getPresetId());
                return null;
            }

            RobotToolsChatResponse data = (RobotToolsChatResponse) rsp.getData();
            String content = RegularUtil.extractJsonString(data.getContent());

            if (StringUtils.isBlank(content)) {
                log.warn("AI匹配-模型返回内容中未找到合法的JSON，预设ID: {}", request.getPresetId());
                return null;
            }

            log.info("AI匹配-模型返回内容中找到合法的JSON，预设ID: {}, 内容: {}",
                    request.getPresetId(), content);
            return JSON.parseObject(content);
        } catch (Exception e) {
            log.error("AI匹配-处理AI聊天结果时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 数据格式转换，获取完整的素材包数据
     *
     * @return
     */
    private String getCompleteMaterialPacks() {
        log.info("开始构建素材包完整数据");

        // 获取所有有效的素材包
        TdhMaterialPackage record = new TdhMaterialPackage();
        record.setStatus(EntityValidType.NORMAL.getCode());
        record.setIsValid(EntityValidType.NORMAL.getCode());
        List<TdhMaterialPackage> packageList = tdhMaterialPackageMapper.selectByCondition(record);
        log.info("AI匹配素材包-查询到素材包数量: {}", packageList.size());

        // 获取所有分类
        SysTreeCategoryReqBO sysTreeCategoryReqBO = new SysTreeCategoryReqBO();
        sysTreeCategoryReqBO.setBusiType(MATERIAL_CATEGORY_BUSI_TYPE);
        sysTreeCategoryReqBO.setLimit(MATERIAL_CATEGORY_NUM);
        RspList rspList = sysTreeCategoryApi.getCategoryList(sysTreeCategoryReqBO);
        if (!rspList.isSuccess() || CollectionUtils.isEmpty(rspList.getRows())) {
            log.error("AI匹配素材包-查询素材包分类失败: {}", rspList);
            return null;
        }
        List<SysTreeCategoryRsqBO> treeCategoryRsqBOList =  rspList.getRows();
        log.info("AI匹配素材包-查询到素材包分类数量: {}", treeCategoryRsqBOList != null ? treeCategoryRsqBOList.size() : 0);
        Map<String, String> categoryMap = treeCategoryRsqBOList.stream()
                .collect(Collectors.toMap(SysTreeCategoryRsqBO::getCateId, SysTreeCategoryRsqBO::getCateName));

        com.alibaba.fastjson.JSONArray resultArray = new com.alibaba.fastjson.JSONArray();

        // 按主分类分组
        Map<String, List<TdhMaterialPackage>> categoryPackageMap = packageList.stream()
                .filter(pkg -> StringUtils.isNotBlank(pkg.getCategory1()))
                .collect(Collectors.groupingBy(TdhMaterialPackage::getCategory1));

        // 处理每个主分类
        categoryPackageMap.forEach((mainCategory, packages) -> {
            com.alibaba.fastjson.JSONObject mainCategoryObj = new com.alibaba.fastjson.JSONObject();
            mainCategoryObj.put(CATEGORY_KEY, categoryMap.get(mainCategory));

            // 按子分类分组
            Map<String, List<TdhMaterialPackage>> subCategoryPackageMap = packages.stream()
                    .filter(pkg -> StringUtils.isNotBlank(pkg.getCategory2()))
                    .collect(Collectors.groupingBy(TdhMaterialPackage::getCategory2));

            com.alibaba.fastjson.JSONArray subCategoriesArray = new com.alibaba.fastjson.JSONArray();

            // 处理每个子分类下的素材包
            subCategoryPackageMap.forEach((subCategory, subPackages) -> {
                for (TdhMaterialPackage pkg : subPackages) {
                    com.alibaba.fastjson.JSONObject subCategoryObj = new com.alibaba.fastjson.JSONObject();
                    subCategoryObj.put(SUB_CATEGORY_KEY, categoryMap.get(subCategory));
                    subCategoryObj.put(MATERIAL_NAME_KEY, pkg.getPkgName());

                    // 处理关键词（保持原样，不分割）
                    com.alibaba.fastjson.JSONArray keywordsArray = new com.alibaba.fastjson.JSONArray();
                    if (StringUtils.isNotBlank(pkg.getKeywords())) {
                        // 直接添加原始关键词字符串
                        keywordsArray.add(pkg.getKeywords());
                    }
                    subCategoryObj.put(MATERIAL_KEYWORDS_KEY, keywordsArray);

                    subCategoriesArray.add(subCategoryObj);
                }
            });

            mainCategoryObj.put(MATERIAL_KEY, subCategoriesArray);
            resultArray.add(mainCategoryObj);
        });

        String result = resultArray.toJSONString();
        log.info("AI匹配素材包-完整素材包数据为: {}", result);
        return result;
    }
}