package com.tydic.nbchat.train.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.train.api.NbchatExamTestApi;
import com.tydic.nbchat.train.api.NbchatUserTrainRecordApi;
import com.tydic.nbchat.train.api.bo.course.TranUserCourseSaveReqBO;
import com.tydic.nbchat.train.api.bo.eums.QuestionType;
import com.tydic.nbchat.train.api.bo.eums.TrainCommonState;
import com.tydic.nbchat.train.api.bo.exam.*;
import com.tydic.nbchat.train.core.service.impl.event.EventPublishFactory;
import com.tydic.nbchat.train.mapper.*;
import com.tydic.nbchat.train.mapper.po.*;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NbchatExamTestServiceImpl implements NbchatExamTestApi {

    @Resource
    NbchatExamTestRecordMapper nbchatExamTestRecordMapper;
    @Resource
    NbchatExamQuestionMapper nbchatExamQuestionMapper;
    @Resource
    NbchatExamQuestionItemsMapper nbchatExamQuestionItemsMapper;
    @Resource
    NbchatTrainRecordMapper nbchatTrainRecordMapper;
    @Resource
    NbchatExamTestPaperMapper nbchatExamTestPaperMapper;

    private final NbchatUserTrainRecordApi nbchatUserTrainRecordApi;

    public NbchatExamTestServiceImpl(NbchatUserTrainRecordApi nbchatUserTrainRecordApi) {
        this.nbchatUserTrainRecordApi = nbchatUserTrainRecordApi;
    }

    @Override
    public Rsp createExamTest(ExamTestCreateReqBO reqBO) {
        log.info("创建试卷-开始：{}", reqBO);
        NbchatExamTestPaper examPaper = nbchatExamTestPaperMapper.queryByCourseId(reqBO.getCourseId(), reqBO.getTenantCode());
        if (ObjectUtils.isEmpty(examPaper)) {
            log.warn("未配置试卷：{}", reqBO);
            //examPaper = new NbchatExamTestPaper();
            //examPaper.setTestType("1");
            return BaseRspUtils.createSuccessRsp("未配置试卷");
        }
        if (examPaper.getTestNum() != examPaper.getSingle() + examPaper.getMultiple() + examPaper.getTrueOrFalse()) {
            return BaseRspUtils.createSuccessRsp("试卷配置考试题数量与各类型总数不符");
        }

        NbchatExamTestRecordWithBLOBs query = new NbchatExamTestRecordWithBLOBs();
        BeanUtils.copyProperties(reqBO, query);

        //创建学习记录
        this.saveTrainRecord(reqBO);

        NbchatExamTestRecordWithBLOBs testPapering = nbchatExamTestRecordMapper.queryValidTestPaper(query);
        if (testPapering != null) {
            return buildTestPaper(testPapering);
        }
        NbchatExamNewTestPaper condition = new NbchatExamNewTestPaper();
        BeanUtils.copyProperties(reqBO, condition);
        condition.setCreateType(examPaper.getTestType());

        List<NbchatExamQuestion> allQuestions = new ArrayList<>();
        if (examPaper.getSingle() > 0) {
            List<NbchatExamQuestion> questions = this.getQuestionByType(condition, QuestionType.CHOICE_S.getCode(), examPaper.getSingle());
            if (CollectionUtils.isNotEmpty(questions) && questions.size() == examPaper.getSingle()) {
                allQuestions.addAll(questions);
            } else {
                return BaseRspUtils.createSuccessRsp("该课程下单选题目数量不足");
            }
        }
        if (examPaper.getMultiple() > 0) {
            List<NbchatExamQuestion> questions = this.getQuestionByType(condition, QuestionType.CHOICE_M.getCode(), examPaper.getMultiple());
            if (CollectionUtils.isNotEmpty(questions) && questions.size() == examPaper.getMultiple()) {
                allQuestions.addAll(questions);
            } else {
                return BaseRspUtils.createSuccessRsp("该课程下多选题数量不足");
            }
        }
        if (examPaper.getTrueOrFalse() > 0) {
            List<NbchatExamQuestion> questions = this.getQuestionByType(condition, QuestionType.TRUE_FALSE.getCode(), examPaper.getTrueOrFalse());
            if (CollectionUtils.isNotEmpty(questions) && questions.size() == examPaper.getTrueOrFalse()) {
                allQuestions.addAll(questions);
            } else {
                return BaseRspUtils.createSuccessRsp("该课程下判断题目数量不足");
            }
        }
        //按规则处理题目数量
        /*allQuestions = this.processQuesNum(allQuestions);
        if (allQuestions.size() < 5) {
            return BaseRspUtils.createSuccessRsp("题目数量不足");
        }*/
        NbchatExamTestRecordWithBLOBs record = new NbchatExamTestRecordWithBLOBs();
        record.setTestId(IdWorker.nextAutoIdStr());
        record.setCourseId(reqBO.getCourseId());
        record.setUserId(reqBO.getUserId());
        record.setTenantCode(reqBO.getTenantCode());
        record.setTestPaperId(IdWorker.nextAutoIdStr());
        record.setStartTime(new Date());
        Map<String, List<String>> content = allQuestions.stream().collect(Collectors.groupingBy(NbchatExamQuestion::getQuestionType, Collectors.mapping(NbchatExamQuestion::getQuestionId, Collectors.toList())));
        record.setTestPaperContent(JSON.toJSONString(content));
        nbchatExamTestRecordMapper.insertSelective(record);
        return this.buildTestPaper(record);
    }

    public List<NbchatExamQuestion> getQuestionByType(NbchatExamNewTestPaper condition, String questionType, Integer questionNum) {
        condition.setQuestionType(questionType);
        condition.setQuestions(questionNum);
        return nbchatExamQuestionMapper.newTestPaper(condition);
    }

    public void saveTrainRecord(ExamTestCreateReqBO reqBO) {
        TranUserCourseSaveReqBO queryReqBO = new TranUserCourseSaveReqBO();
        queryReqBO.setTenantCode(reqBO.getTenantCode());
        queryReqBO.setCourseId(reqBO.getCourseId());
        queryReqBO.setUserId(reqBO.getUserId());
        nbchatUserTrainRecordApi.saveUserTrainRecord(queryReqBO);
    }


    public Rsp<ExamTestCreateRspBO> buildTestPaper(NbchatExamTestRecordWithBLOBs record) {
        long start = System.currentTimeMillis();
        ExamTestCreateRspBO rspBO = new ExamTestCreateRspBO();
        rspBO.setTenantCode(record.getTenantCode());
        rspBO.setUserId(record.getUserId());
        rspBO.setCourseId(record.getCourseId());
        rspBO.setStartTime(record.getStartTime());
        rspBO.setTestId(record.getTestId());
        String content = record.getTestPaperContent();
        List<ExamTestPaper> questions = new ArrayList<>();
        Map<String, List<String>> map = JSON.parseObject(content, Map.class);
        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
            ExamTestPaper paper = new ExamTestPaper();
            paper.setQuestionType(entry.getKey());
            List<ExamQuestion> examQuestions = new ArrayList<>();
            for (String questionId : entry.getValue()) {
                NbchatExamQuestion questionRecord = nbchatExamQuestionMapper.selectByPrimaryKey(questionId);
                ExamQuestion examQuestion = new ExamQuestion();
                examQuestion.setQuestionId(questionId);
                examQuestion.setQuestionName(questionRecord.getQuestionName());
                examQuestion.setExplan(questionRecord.getExplan());
                examQuestion.setDifficulty(questionRecord.getDifficulty());
                if (QuestionType.isChoice(questionRecord.getQuestionType()) || QuestionType.isJudgment(questionRecord.getQuestionType())) {
                    NbchatExamQuestionItems condition = new NbchatExamQuestionItems();
                    condition.setQuestionId(questionId);
                    List<NbchatExamQuestionItems> itemRec = nbchatExamQuestionItemsMapper.selectByCondition(condition);
                    List<ExamQuestionItem> items = new ArrayList<>();
                    NiccCommonUtil.copyList(itemRec, items, ExamQuestionItem.class);
                    examQuestion.setItems(items);
                }
                examQuestions.add(examQuestion);
            }
            paper.setQuestions(examQuestions);
            questions.add(paper);
        }
        questions = questions.stream().sorted(Comparator.comparing(ExamTestPaper::getQuestionType)).collect(Collectors.toList());
        rspBO.setTestPapers(questions);
        log.info("创建试卷结束，构建试卷耗时：{}/ms", System.currentTimeMillis() - start);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp submitExamTestPaper(ExamTestSubmitReqBO reqBO) {
        log.info("提交试卷-开始：{}", reqBO);
        int correctCount = 0;
        List<ExamTestSubmitAnswer> answers = reqBO.getAnswers();
        Map<String, List<ExamTestSubmitAnswer>> typeMap = answers.stream().collect(Collectors.groupingBy(ExamTestSubmitAnswer::getType));
        for (Map.Entry<String, List<ExamTestSubmitAnswer>> entry : typeMap.entrySet()) {
            if (entry.getKey().equals(QuestionType.CHOICE_S.getCode()) || entry.getKey().equals(QuestionType.TRUE_FALSE.getCode())) {
                List<NbchatExamQuestionItems> choice_s_correct = queryCorrectQuestion(reqBO.getTestId(), entry.getValue());
                correctCount += choice_s_correct.size();
                log.info("单选/判断答对：{} 道", choice_s_correct.size());
            } else if (entry.getKey().equals(QuestionType.CHOICE_M.getCode())) {
                List<ExamTestSubmitAnswer> m_answers = entry.getValue();
                for (ExamTestSubmitAnswer mAnswer : m_answers) {
                    NbchatExamQuestionItems cond = new NbchatExamQuestionItems();
                    cond.setQuestionId(mAnswer.getQuestionId());
                    cond.setIsRight("1");
                    List<NbchatExamQuestionItems> qCorrectItems = nbchatExamQuestionItemsMapper.selectByCondition(cond);
                    List<String> collect = qCorrectItems.stream().map(NbchatExamQuestionItems::getItemId).collect(Collectors.toList());
                    List<String> userItems = mAnswer.getItemIds();
                    if (collect.containsAll(userItems) && userItems.containsAll(collect)) {
                        correctCount++;
                    }
                }
            } else {
                log.info("未识别的题目类型");
            }
        }

        NbchatExamTestRecordWithBLOBs testRecord = new NbchatExamTestRecordWithBLOBs();
        testRecord.setTestId(reqBO.getTestId());
        testRecord.setAnswerContent(JSON.toJSONString(reqBO.getAnswers()));
        testRecord.setEndTime(new Date());
        testRecord.setScore((short) 0);
        if (correctCount > 0) {
            if (reqBO.getAnswers().size() == 50) {
                testRecord.setScore((short) (2 * correctCount));
            }
            if (reqBO.getAnswers().size() == 25) {
                testRecord.setScore((short) (4 * correctCount));
            }
            if (reqBO.getAnswers().size() == 20) {
                testRecord.setScore((short) (5 * correctCount));
            }
            if (reqBO.getAnswers().size() == 10) {
                testRecord.setScore((short) (10 * correctCount));
            }
            if (reqBO.getAnswers().size() == 5) {
                testRecord.setScore((short) (20 * correctCount));
            }
        }
        nbchatExamTestRecordMapper.updateByPrimaryKeySelective(testRecord);
        NbchatTrainRecord trainRecord = nbchatTrainRecordMapper.selectByUserAndCourseId(reqBO.getUserId(), reqBO.getCourseId());
        if (trainRecord != null) {
            NbchatExamTestPaper nbchatExamTestPaper = nbchatExamTestPaperMapper.queryByCourseId(reqBO.getCourseId(), reqBO.getTenantCode());
            String score = nbchatExamTestPaper.getPassingScore();
            if (testRecord.getScore() >= Integer.parseInt(score) ) {
                trainRecord.setTestPassState(TrainCommonState.PASS.getCode());
            }
            trainRecord.setTestState(EntityValidType.NORMAL.getCode());
            nbchatTrainRecordMapper.updateByPrimaryKeySelective(trainRecord);
        }
        return BaseRspUtils.createSuccessRsp(trainRecord);
    }

    //计算单选答案
    public List<NbchatExamQuestionItems> queryCorrectQuestion(String testId, List<ExamTestSubmitAnswer> answers) {
        if (CollectionUtils.isEmpty(answers)) {
            NbchatExamTestRecordWithBLOBs record = nbchatExamTestRecordMapper.selectByPrimaryKey(testId);
            String content = record.getAnswerContent();
            answers = JSON.parseArray(content, ExamTestSubmitAnswer.class);
        }
        List<String> items = answers.stream().flatMap(v -> v.getItemIds().stream()).collect(Collectors.toList());
        return nbchatExamQuestionItemsMapper.queryCorrectItems(items);
    }

    @Override
    public Rsp<ExamTestResultRspBO> getExamTestResult(ExamTestResultReqBO reqBO) {
        log.info("查询考试结果-开始：{}", reqBO);
        NbchatExamTestRecordWithBLOBs condition = new NbchatExamTestRecordWithBLOBs();
        condition.setCourseId(reqBO.getCourseId());
        condition.setUserId(reqBO.getUserId());
        condition.setQueryType(reqBO.getQueryType());
        NbchatExamTestRecordWithBLOBs record = nbchatExamTestRecordMapper.queryByCondition(condition);
        if (ObjectUtils.isEmpty(record)) {
            ExamTestResultRspBO rspBO = new ExamTestResultRspBO();
            rspBO.setScore(0D);
            return BaseRspUtils.createSuccessRsp(rspBO, "未查询到考试完成信息");
        }
        //List<NbchatExamQuestionItems> correctList = queryCorrectQuestion(record.getTestId(),null);
        //List<String> correctQuestIds = correctList.stream().map(NbchatExamQuestionItems::getQuestionId).collect(Collectors.toList());
        String answerContent = record.getAnswerContent();
        List<ExamTestSubmitAnswer> answers = JSON.parseArray(answerContent, ExamTestSubmitAnswer.class);
        Map<String, List<String>> answersMap = answers.stream().collect(Collectors.toMap(ExamTestSubmitAnswer::getQuestionId, ExamTestSubmitAnswer::getItemIds));

        String testContent = record.getTestPaperContent();
        Map<String, List<String>> testContentMap = JSON.parseObject(testContent, Map.class);
        List<String> questionIds = testContentMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        //List<String> incorrectList = questionIds.stream().filter(v -> !correctQuestIds.contains(v)).collect(Collectors.toList());
        List<ExamQuestion> questions = new ArrayList<>();
        for (String questionId : questionIds) {
            ExamQuestion question = new ExamQuestion();
            NbchatExamQuestion questionRecord = nbchatExamQuestionMapper.selectByPrimaryKey(questionId);
            NbchatExamQuestionItems queryItem = new NbchatExamQuestionItems();
            queryItem.setQuestionId(questionId);
            queryItem.setIsRight("1");
            List<NbchatExamQuestionItems> correctItems = nbchatExamQuestionItemsMapper.selectByCondition(queryItem);
            List<ExamQuestionItem> items = new ArrayList<>();
            NiccCommonUtil.copyList(correctItems, items, ExamQuestionItem.class);

            List<String> userAnswerItems = answersMap.get(questionId);
            NbchatExamQuestionItems queryUserItem = new NbchatExamQuestionItems();
            queryUserItem.setItemIds(userAnswerItems);
            List<NbchatExamQuestionItems> userAnswerItemsInfo = nbchatExamQuestionItemsMapper.selectByCondition(queryUserItem);
            List<ExamQuestionItem> userItems = new ArrayList<>();
            NiccCommonUtil.copyList(userAnswerItemsInfo, userItems, ExamQuestionItem.class);
            question.setQuestionId(questionId);
            question.setQuestionName(questionRecord.getQuestionName());
            question.setItems(items);
            question.setUserItems(userItems);
            question.setQuestionType(questionRecord.getQuestionType());
            question.setDifficulty(questionRecord.getDifficulty());
            question.setExplan(questionRecord.getExplan());
            questions.add(question);
        }
        ExamTestResultRspBO rspBO = new ExamTestResultRspBO();
        rspBO.setScore(Double.valueOf(record.getScore()));
        rspBO.setCourseId(reqBO.getCourseId());
        rspBO.setUserId(reqBO.getUserId());
        rspBO.setQuestions(questions);
        NbchatExamTestPaper examPaper = nbchatExamTestPaperMapper.queryByCourseId(reqBO.getCourseId(), reqBO.getTenantCode());
        if (ObjectUtils.isNotEmpty(examPaper)) {
            rspBO.setPassScore(Double.valueOf(examPaper.getPassingScore()));
        }
        return BaseRspUtils.createSuccessRsp(rspBO);
    }
}
