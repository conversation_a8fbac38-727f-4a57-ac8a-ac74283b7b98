package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.AutoBuildApi;
import com.tydic.nbchat.train.api.bo.autoBuild.AutoBuildBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/course/auto")
public class TrainAutoBuildController {

    @Resource
    AutoBuildApi autoBuildApi;


    @PostMapping("/build")
    public Rsp process(@RequestBody AutoBuildBO request){
        if (request.isAsync()) {
            new Thread(() -> {
                autoBuildApi.build(request);
            }).start();
            return BaseRspUtils.createSuccessRsp("课程异步生成中");
        }
        autoBuildApi.build(request);
        return BaseRspUtils.createSuccessRsp("课程同步生成结束");
    }

}
