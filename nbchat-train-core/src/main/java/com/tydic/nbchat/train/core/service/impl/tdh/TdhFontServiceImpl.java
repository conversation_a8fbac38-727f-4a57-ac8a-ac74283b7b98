package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.tdh.*;
import com.tydic.nbchat.train.api.tdh.TdhFontApi;
import com.tydic.nbchat.train.mapper.TdhFontMapper;
import com.tydic.nbchat.train.mapper.TdhSubtitleStyleMapper;
import com.tydic.nbchat.train.mapper.po.TdhFont;
import com.tydic.nbchat.train.mapper.po.TdhFontQueryCondition;
import com.tydic.nbchat.train.mapper.po.TdhSubtitleStyle;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Service
public class TdhFontServiceImpl implements TdhFontApi {

    @Resource
    private TdhFontMapper tdhFontMapper;
    @Resource
    private TdhSubtitleStyleMapper tdhSubtitleStyleMapper;

    @Override
    public RspList<TdhFontQueryRspBO> getFontList(TdhFontQueryReqBO queryReqBO) {
        List<TdhFontBO> fontBOS = new ArrayList<>();
        TdhFontQueryCondition condition = new TdhFontQueryCondition();
        BeanUtils.copyProperties(queryReqBO, condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        List<TdhFont> fontList = tdhFontMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(fontList,fontBOS,TdhFontBO.class);
        //根据fimaly分组
        List<TdhFontQueryRspBO> fontsRsp = new ArrayList<>();
        // group by family
        fontBOS.stream().collect(groupingBy(TdhFontBO::getFamily)).forEach((family, fonts) -> {
            TdhFontQueryRspBO fontRsp = new TdhFontQueryRspBO();
            fontRsp.setFamily(family);
            fontRsp.setFonts(fonts);
            fontsRsp.add(fontRsp);
        });
        return BaseRspUtils.createSuccessRspList(fontsRsp);
    }

    @Override
    public RspList getSubtitleStyleList(TdhSubtitleStyleQueryReqBO queryReqBO) {
        List<TdhSubtitleStyleQueryRspBO> rspBOList = new ArrayList<>();
        TdhSubtitleStyle condition = new TdhSubtitleStyle();
        BeanUtils.copyProperties(queryReqBO, condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        Page<TdhSubtitleStyle> page = PageHelper.startPage(queryReqBO.getPage(), queryReqBO.getLimit());
        tdhSubtitleStyleMapper.selectByAll(condition);
        if (CollectionUtils.isEmpty(page.getResult())){
            return BaseRspUtils.createSuccessRspList(rspBOList,0L);
        }
        NiccCommonUtil.copyList(page.getResult(),rspBOList,TdhSubtitleStyleQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspBOList,page.getTotal());
    }
}
