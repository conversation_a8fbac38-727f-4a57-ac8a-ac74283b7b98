package com.tydic.nbchat.train.core.web;


import com.tydic.nbchat.train.api.NbchatExamRuleTestApi;
import com.tydic.nbchat.train.api.bo.examRule.ExamRuleBO;
import com.tydic.nbchat.train.api.bo.examRule.ExamRuleReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/train/exam/rule")
@AllArgsConstructor
public class TrainExamRuleController {
    private final NbchatExamRuleTestApi nbchatExamRuleTestApi;

    /**
     * 题库配置保存
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    @PostMapping("/save")
    public Rsp save(@RequestBody ExamRuleBO request) {
        Rsp rsp = nbchatExamRuleTestApi.save(request);
        return rsp;
    }

    /**
     * 题库配置查询(详细)
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    @PostMapping("/query")
    public Rsp query(@RequestBody ExamRuleReqBO request) {
        Rsp rsp = nbchatExamRuleTestApi.query(request);
        return rsp;
    }

    /**
     * 题库配置查询(分页)
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    @PostMapping("/queryExamRule")
    public RspList queryExamRule(@RequestBody ExamRuleReqBO request){
        return nbchatExamRuleTestApi.getCourseExamList(request);
    }

    /**
     * 上架
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    @PostMapping("/putOnShelves")
    public Rsp putOnShelves(@RequestBody ExamRuleReqBO request){
        return nbchatExamRuleTestApi.putOnShelves(request);
    }

    @PostMapping("/export")
    public Rsp export(@RequestBody ExamRuleReqBO request) {
        return nbchatExamRuleTestApi.export(request);
    }

    @PostMapping("/export/question")
    public Rsp exportQuestion(@RequestBody ExamRuleReqBO request) {
        return nbchatExamRuleTestApi.exportQuestion(request);
    }

    @PostMapping("/import")
    public Rsp importFile(HttpServletRequest request) throws Exception {
        String courseId = request.getParameter("courseId");
        String tenantCode = request.getParameter("tenantCode");
        if (StringUtils.isAnyEmpty(courseId, tenantCode)) {
            return BaseRspUtils.createErrorRsp("课程id、租户编码不得为空");
        }

        log.info("试题导入请求体: {}|{}", courseId,tenantCode);
        MultipartHttpServletRequest req = (MultipartHttpServletRequest) request;
        List<MultipartFile> files = req.getFiles("files");
        InputStream inputStream = files.get(0).getInputStream();

        return nbchatExamRuleTestApi.importFile(inputStream,courseId,tenantCode);
    }
}
