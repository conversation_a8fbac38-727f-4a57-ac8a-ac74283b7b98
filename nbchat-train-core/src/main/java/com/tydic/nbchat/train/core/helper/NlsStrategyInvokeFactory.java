package com.tydic.nbchat.train.core.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.tydic.nbchat.robot.api.NbchatRobotToolsApi;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nbchat.train.api.bo.asr_tts.*;
import com.tydic.nbchat.train.api.bo.eums.AnchorType;
import com.tydic.nbchat.train.core.config.NbchatTrainConfigProperties;
import com.tydic.nbchat.train.core.util.OptimizedSubtitlesUtil;
import com.tydic.nbchat.train.core.util.TrainCommonUtil;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class NlsStrategyInvokeFactory {

    private final static String TTS_CACHE_KEY_PREFIX = "nbchat-train:tts-cache:";

    private final RedisHelper redisHelper;
    private final NbchatTrainConfigProperties nbchatTrainConfigProperties;
    private final List<NlsHelperApi> nlsHelperApis;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 30000)
    private NbchatRobotToolsApi nbchatRobotToolsApi;


    public NlsStrategyInvokeFactory(RedisHelper redisHelper,
                                    NbchatTrainConfigProperties nbchatTrainConfigProperties,
                                    List<NlsHelperApi> nlsHelperApis) {
        this.redisHelper = redisHelper;
        this.nbchatTrainConfigProperties = nbchatTrainConfigProperties;
        this.nlsHelperApis = nlsHelperApis;
    }

    public String getAnchorConfig() {
        return nbchatTrainConfigProperties.getAnchorConfig();
    }

    public NlsHelperApi getApi() {
        return getApi(nbchatTrainConfigProperties.getAnchorConfig());
    }

    public NlsHelperApi getApi(String anchorType) {
        for (NlsHelperApi nlsHelperApi : nlsHelperApis) {
            if (anchorType.equals(nlsHelperApi.anchorConfig())) {
                return nlsHelperApi;
            }
        }
        throw new RuntimeException("找不到NlsHelperApi实现接口: anchorType=" + anchorType);
    }

    /**
     * 语音合成任务
     *
     * @param request
     * @return
     */
    public TtsVoiceTaskContext createAudioTask(TtsVoiceTaskRequest request) {
        if (StringUtils.isBlank(request.getAnchorType())) {
            request.setAnchorType(nbchatTrainConfigProperties.getTtsType());
            if (StringUtils.isBlank(request.getVoice())) {
                request.setVoice(AnchorType.getDefaultVoiceByCode(request.getAnchorType()));
            }
        }
        request.setText(TrainCommonUtil.trimXmlTagName(request.getText(), "mark"));
        String cacheKey = TTS_CACHE_KEY_PREFIX + NiccCommonUtil.stringToMD5(request.getCacheHashCode());
        TtsVoiceTaskContext cacheContext = (TtsVoiceTaskContext) redisHelper.get(cacheKey);
        if (cacheContext != null) {
            log.info("从缓存中获取TTS任务：{}|{}", request, cacheContext);
            return cacheContext;
        }
        TtsVoiceTaskContext context = getApi(request.getAnchorType()).createAudioTask(request);
        try {
            if (context.getSentences() != null) {
                List<TtsVoiceResult> subtitles;
                if (OptimizedSubtitlesUtil.isEnglishSubtitles(context.getSentences())) {
                    subtitles = OptimizedSubtitlesUtil.optimizeEnglishSubtitles(context.getSentences());
                } else {
                    subtitles = OptimizedSubtitlesUtil.optimizeSubtitles(context.getSentences());
                }
                List<TtsWordResult> words = new ArrayList<>();
                for (TtsVoiceResult subtitle : subtitles) {
                    if (request.isWordSplit()) {
                        List<TtsWordResult> wordResults = OptimizedSubtitlesUtil.calculateWordTime(subtitle.getText(),
                                subtitle.getBegin_Time(), subtitle.getEnd_time());
                        words.addAll(wordResults);
                    }
                    subtitle.setText(OptimizedSubtitlesUtil.removeLastPunctuation(subtitle.getText()));
                }
                context.setSubtitles(subtitles);
                context.setWords(words);
                if (request.isBilingual()) {
                    try {
                        //执行翻译
                        translateSubtitles(request,context);
                    } catch (Exception e) {
                        log.error("字幕优化-翻译异常：{}", request, e);
                    }
                }
            }
            if (StringUtils.isNotBlank(context.getAudio_address())) {
                redisHelper.set(cacheKey, context, 3600 * 24 * 3);
            }
        } catch (Exception e) {
            log.error("字幕优化异常：{}", request, e);
            context.setSubtitles(context.getSentences());
        }
        return context;
    }

    /**
     * 语音识别任务
     *
     * @param request
     * @return
     */
    public AsrVoiceTaskContext createAsrTask(AsrVoiceTaskRequest request) {
        if (StringUtils.isBlank(request.getAnchorType())) {
            request.setAnchorType(nbchatTrainConfigProperties.getAsrType());
        }
        return getApi(request.getAnchorType()).createAsrTask(request);
    }


    /***
     * 好
     * 翻译拆分字幕
     * id  text_split_trans
     * 入参格式：
         [
             {
             "text": "There are common types of social practice"
             },
             {
             "text": "Volunteer service is a practice activity "
             }
         ]
     输出格式：
         [
             { "text": "There are common types of social practice ", "trans": "有常见的社会实践类型" },
             { "text": "Volunteer service is a practice activity ", "trans": "志愿服务是一种实践活动" }
         ]
     */
    private void translateSubtitles(TtsVoiceTaskRequest _request, TtsVoiceTaskContext context) {
        //入参转换
        List<TtsVoiceResult> subtitles = context.getSubtitles();
        if (subtitles == null || subtitles.isEmpty()) {
            return;
        }
        List<TtsVoiceResult> inputList = subtitles.stream()
                .map(subtitle -> {
                    TtsVoiceResult ttsVoiceResult = new TtsVoiceResult();
                    ttsVoiceResult.setText(subtitle.getText());
                    return ttsVoiceResult;
                }).collect(Collectors.toList());
        boolean isCh = OptimizedSubtitlesUtil.isChineseDominant(_request.getText());
        //调用大模型翻译
        RobotPromptMessageRequest request = new RobotPromptMessageRequest();
        List<String> promptsList = new ArrayList<>();
        promptsList.add(JSONObject.toJSONString(inputList, SerializerFeature.SkipTransientField));
        if (isCh) {
            promptsList.add("英文");
        } else {
            //非中文翻译为中文
            promptsList.add("中文");
        }
        request.setUserId(_request.getUserId());
        request.setTenantCode(_request.getTenantCode());
        request.setPresetPrompts(promptsList);
        request.setRobotType("doubao_lite_32k");
        request.setPresetId("text_split_trans");
        request.setTrim(true);

        log.info("翻译字幕-请求：{}, ARG0 = {}", request, JSONObject.toJSONString(inputList, SerializerFeature.SkipTransientField));
        Rsp<RobotToolsChatResponse> resultMap = nbchatRobotToolsApi.getChatResult(request);
        log.info("翻译字幕-结果：{}", resultMap);
        if (resultMap.isSuccess()) {
            RobotToolsChatResponse response = resultMap.getData();
            if (StringUtils.isNotBlank(response.getContent())) {
                JSONArray subtitlesArray = JSONArray.parseArray(response.getContent());
                //校验输入的数组长度和返回的数组长度是否一致
                if (subtitlesArray.size() != subtitles.size()) {
                    log.warn("翻译字幕-结果-字幕长度不一致：{} -> {}", subtitles.size(), subtitlesArray.size());
                }
                for (int i = 0; i < subtitlesArray.size(); i++) {
                    if (i >= subtitles.size()) {
                        break;
                    }
                    JSONObject subtitleObject = subtitlesArray.getJSONObject(i);
                    String text = subtitleObject.getString("trans");
                    subtitles.get(i).setBilingualText(text);
                    subtitles.get(i).setIsCh(isCh);
                }
            }
        }
    }

}
