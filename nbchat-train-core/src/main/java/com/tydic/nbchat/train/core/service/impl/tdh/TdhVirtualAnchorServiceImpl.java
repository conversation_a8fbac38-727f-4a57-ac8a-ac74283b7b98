package com.tydic.nbchat.train.core.service.impl.tdh;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysDictConfigService;
import com.tydic.nbchat.admin.api.bo.SysDictConfigQueryReqBO;
import com.tydic.nbchat.admin.api.bo.SysDictConfigRspBO;
import com.tydic.nbchat.train.api.bo.eums.LanguageType;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualAnchorQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualAnchorQueryRspBO;
import com.tydic.nbchat.train.api.tdh.TdhVirtualAnchorApi;
import com.tydic.nbchat.train.mapper.TdhCustomizeRecordMapper;
import com.tydic.nbchat.train.mapper.TdhVirtualAnchorMapper;
import com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord;
import com.tydic.nbchat.train.mapper.po.TdhVirtualAnchor;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数字人：音色
 */
@Slf4j
@Service
public class TdhVirtualAnchorServiceImpl implements TdhVirtualAnchorApi {

    @Resource
    private TdhVirtualAnchorMapper tdhVirtualAnchorMapper;
    @Resource
    TdhCustomizeRecordMapper tdhCustomizeRecordMapper;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3 * 1000)
    SysDictConfigService sysDictConfigService;

    private final String DICT_CODE = "language";

    @Override
    public RspList<TdhVirtualAnchorQueryRspBO> getVirtualAnchorList(TdhVirtualAnchorQueryReqBO reqBO) {
        // 构建查询条件
        TdhVirtualAnchor cond = new TdhVirtualAnchor();
        BeanUtils.copyProperties(reqBO, cond);
        cond.setBusiType(StringUtils.isBlank(reqBO.getBusiType()) ?
                EntityValidType.NORMAL.getCode() : reqBO.getBusiType());
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        // 分页查询
        Page<TdhVirtualAnchor> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        tdhVirtualAnchorMapper.selectAll(cond);
        // 转换结果
        List<TdhVirtualAnchorQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), rspList, TdhVirtualAnchorQueryRspBO.class);
        // 填充语言类型信息
        fillLanguageType(rspList);
        // 填充订单信息
        fillOrderInfo(rspList);
        return BaseRspUtils.createSuccessRspList(rspList, page.getTotal());
    }

    private void fillLanguageType(List<TdhVirtualAnchorQueryRspBO> rspList) {
        if (CollectionUtils.isEmpty(rspList)) {
            return;
        }
        // 构建语言映射
        Map<String, String> languageMap = new HashMap<>();
        try {
            // 尝试从远程服务获取
            SysDictConfigQueryReqBO dictQuery = new SysDictConfigQueryReqBO();
            dictQuery.setDictCode(DICT_CODE);
            RspList<SysDictConfigRspBO> dictResult = sysDictConfigService.getSysDictValues(dictQuery);

            if (dictResult != null && dictResult.isSuccess() && !CollectionUtils.isEmpty(dictResult.getRows())) {
                // 使用远程获取的映射
                dictResult.getRows().forEach(dict ->
                        languageMap.put(dict.getDictName(), dict.getDictDesc()));
                log.info("成功从字典服务获取语言映射");
            } else {
                log.warn("远程获取语种失败，使用本地映射");
                languageMap.putAll(LanguageType.getLanguageMap());
            }
        } catch (Exception e) {
            log.error("远程获取语种异常，使用本地映射: {}", e.getMessage());
            languageMap.putAll(LanguageType.getLanguageMap());
        }
        // 处理每个语言项
        rspList.forEach(bo -> {
            if (StringUtils.isNotBlank(bo.getLanguage())) {
                String languageType = Arrays.stream(bo.getLanguage().split(","))
                        .map(String::trim)
                        .map(lang -> languageMap.getOrDefault(lang, lang))
                        .collect(Collectors.joining(","));
                bo.setLanguageType(languageType);
            }
        });
    }

    /**
     * 填充订单信息
     */
    private void fillOrderInfo(List<TdhVirtualAnchorQueryRspBO> rspList) {
        for (TdhVirtualAnchorQueryRspBO bo : rspList) {
            if (StringUtils.isEmpty(bo.getOrderNo())) {
                continue;
            }
            TdhCustomizeRecord record = tdhCustomizeRecordMapper.findByOrderNo(bo.getOrderNo());
            if (record == null) {
                continue;
            }
            bo.setCustomizeStatus(record.getCustomizeStatus());
            bo.setOrderStatus(record.getOrderStatus());
            // 设置过期状态
            if (record.getEndTime() != null) {
                bo.setIsExpire(record.getEndTime().before(new Date()) ? "1" : "0");
            }
        }
    }

    @Override
    public Rsp updateEmotion(TdhVirtualAnchorQueryReqBO reqBO) {
        String emotion = reqBO.getEmotion();
        JSONObject jsonObject = JSONObject.parseObject(emotion);
        String value = jsonObject.getString("value");
        String url = jsonObject.getString("url");

        TdhVirtualAnchor tdhVirtualAnchor = tdhVirtualAnchorMapper.queryById(reqBO.getAnchorId());
        String emotionArr = tdhVirtualAnchor.getEmotion();
        if (!JSONArray.isValidArray(emotionArr)){
            return BaseRspUtils.createErrorRsp("配置情感信息错误");
        }
        JSONArray jsonArray = JSONArray.parseArray(emotionArr);
        for (Object obj : jsonArray) {
            JSONObject object = (JSONObject) obj;
            if (object.getString("value").equals(value)) {
                object.put("url", url);
                break;
            }
        }
        TdhVirtualAnchor po = new TdhVirtualAnchor();
        po.setAnchorId(reqBO.getAnchorId());
        po.setEmotion(jsonArray.toString());
        tdhVirtualAnchorMapper.update(po);
        return BaseRspUtils.createSuccessRsp("");
    }
}
