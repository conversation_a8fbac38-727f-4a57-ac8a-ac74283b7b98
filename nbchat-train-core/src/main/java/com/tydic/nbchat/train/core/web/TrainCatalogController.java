package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainCatalogApi;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBO;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBatchSaveBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/catalog")
public class TrainCatalogController {

    @Resource
    NbchatTrainCatalogApi nbchatTrainCatalogApi;


    @PostMapping("save")
    public Rsp save(@RequestBody TrainCatalogBO request){
        return nbchatTrainCatalogApi.save(request);
    }

    @PostMapping("save/batch")
    public Rsp saveBatch(@RequestBody TrainCatalogBatchSaveBO request){
        return nbchatTrainCatalogApi.saveBatch(request);
    }

}
