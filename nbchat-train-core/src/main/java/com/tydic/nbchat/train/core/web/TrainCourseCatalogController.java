package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainCourseCatalogApi;
import com.tydic.nbchat.train.api.bo.catalog.TrainCourseCategoryBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 一二级目录管理
 */
@Slf4j
@RestController
@RequestMapping("/train/course/catalog")
public class TrainCourseCatalogController {

    @Resource
    NbchatTrainCourseCatalogApi nbchatTrainCourseCatalogApi;

    @PostMapping("save")
    public Rsp save(@RequestBody TrainCourseCategoryBO reqBO){
        return nbchatTrainCourseCatalogApi.save(reqBO);
    }

    @PostMapping("update")
    public Rsp update(@RequestBody TrainCourseCategoryBO reqBO){
        return nbchatTrainCourseCatalogApi.update(reqBO);
    }

    @PostMapping("get")
    public RspList get(@RequestBody TrainCourseCategoryBO reqBO){
        return nbchatTrainCourseCatalogApi.get(reqBO);
    }

}
