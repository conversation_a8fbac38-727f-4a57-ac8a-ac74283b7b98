package com.tydic.nbchat.train.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.fastjson.TypeReference;
import com.tydic.nbchat.train.api.NbcahtExamQuestionApi;
import com.tydic.nbchat.train.api.bo.QuestionSaveRequest;
import com.tydic.nbchat.train.api.bo.eums.QuestionType;
import com.tydic.nbchat.train.api.bo.question.ChoiceQuestion;
import com.tydic.nbchat.train.api.bo.question.FillBlanksQuestion;
import com.tydic.nbchat.train.api.bo.question.QaQuestion;
import com.tydic.nbchat.train.api.bo.question.Question;
import com.tydic.nbchat.train.mapper.NbchatExamQuestionItemsMapper;
import com.tydic.nbchat.train.mapper.NbchatExamQuestionMapper;
import com.tydic.nbchat.train.mapper.po.NbchatExamQuestion;
import com.tydic.nbchat.train.mapper.po.NbchatExamQuestionItems;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NbchatExamQuestionServiceImpl implements NbcahtExamQuestionApi {

    @Resource
    NbchatExamQuestionMapper nbchatExamQuestionMapper;
    @Resource
    NbchatExamQuestionItemsMapper nbchatExamQuestionItemsMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp saveQuestion(QuestionSaveRequest saveRequest) {
        log.info("保存试题-开始:{}", saveRequest);
        if (!JSONValidator.from(saveRequest.getContent()).validate()) {
            return BaseRspUtils.createErrorRsp("试题结构异常");
        }
        if (QuestionType.isChoice(saveRequest.getQuestionType()) || QuestionType.isJudgment(saveRequest.getQuestionType())) {
            return handleChoiceType(saveRequest);
        }
        if (QuestionType.isFillBlank(saveRequest.getQuestionType())) {
            return handleFillBlank(saveRequest);
        }
        if (QuestionType.isQA(saveRequest.getQuestionType())) {
            return handleQA(saveRequest);
        }
        log.warn("保存试题-未预设的试题类型:{}", saveRequest.getQuestionType());
        return BaseRspUtils.createErrorRsp("未预设的试题类型");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp appendQuestion(QuestionSaveRequest request) {
        //保存试题
        if (!JSONValidator.from(request.getContent()).validate()) {
            log.info("保存考试配置:试题结构异常:{}", request.getContent());
            return BaseRspUtils.createErrorRsp("试题结构异常");
        }
        saveQuestions(request, QuestionType.CHOICE_S.getCode());
        saveQuestions(request, QuestionType.CHOICE_M.getCode());
        saveQuestions(request, QuestionType.TRUE_FALSE.getCode());
        return BaseRspUtils.createSuccessRsp("保存成功");
    }

    private void saveQuestions(QuestionSaveRequest questionSaveRequest, String questionType) {
        List<String> list = JSON.parseObject(questionSaveRequest.getContent(), new TypeReference<List<String>>() {
        });
        List<String> questionsList = list.stream()
                .filter(json -> questionType.equals(JSON.parseObject(json).getString("type")))
                .collect(Collectors.toList());
        log.info("保存试题-开始:{}", questionsList.size());
        if (CollectionUtils.isNotEmpty(questionsList)) {
            QuestionSaveRequest request = new QuestionSaveRequest();
            request.setContent(questionsList.toString());
            request.setCourseId(questionSaveRequest.getCourseId());
            request.setTenantCode(questionSaveRequest.getTenantCode());
            request.setQuestionType(questionType);
            request.setUserId(questionSaveRequest.getUserId());
            request.setPartId(questionSaveRequest.getPartId());
            Rsp rsp = saveQuestion(request);
            if (rsp.isSuccess()) {
                log.info("保存试题-成功:{}|{}", questionType, rsp);
            } else {
                log.error("保存试题-失败:{}|{}", questionType, rsp);
            }
        }
    }

    public Rsp handleChoiceType(QuestionSaveRequest saveRequest) {
        List<ChoiceQuestion> choiceQuestions = JSONObject.parseArray(saveRequest.getContent(), ChoiceQuestion.class);
        for (ChoiceQuestion question : choiceQuestions) {
            NbchatExamQuestion record = saveQuestionRecord(saveRequest, question);
            List<String> items = question.getItems();
            List<Integer> answers = question.getAnswers();
            saveAnswerRecord(record, items, answers);
        }
        return BaseRspUtils.createSuccessRsp(null);
    }

    public Rsp handleFillBlank(QuestionSaveRequest saveRequest) {
        String content = saveRequest.getContent();
        List<FillBlanksQuestion> fillBlanksQuestions = JSONObject.parseArray(content, FillBlanksQuestion.class);
        for (FillBlanksQuestion question : fillBlanksQuestions) {
            NbchatExamQuestion record = saveQuestionRecord(saveRequest, question);
            List<String> items = question.getAnswers();
            saveAnswerRecord(record, items);
        }
        return BaseRspUtils.createSuccessRsp(null);
    }

    public Rsp handleQA(QuestionSaveRequest saveRequest) {
        String content = saveRequest.getContent();
        List<QaQuestion> questions = JSONObject.parseArray(content, QaQuestion.class);
        for (QaQuestion question : questions) {
            NbchatExamQuestion record = saveQuestionRecord(saveRequest, question);
            String answerContent = question.getAnswer();
            List<String> items = Collections.singletonList(answerContent);
            saveAnswerRecord(record, items);
        }
        return BaseRspUtils.createSuccessRsp(null);
    }

    public void consumerQuestion(QuestionSaveRequest saveRequest, Question question,List<String> items,List<Integer> answers) {
        NbchatExamQuestion nbchatExamQuestion = this.saveQuestionRecord(saveRequest, question);
        NbchatExamQuestion quest = new NbchatExamQuestion();
        quest.setQuestionId(nbchatExamQuestion.getQuestionId());
        quest.setQuestionType(nbchatExamQuestion.getQuestionType());
        this.saveAnswerRecord(quest, items, answers);
    }



    public <T extends Question> NbchatExamQuestion saveQuestionRecord(QuestionSaveRequest saveRequest, T question) {
        NbchatExamQuestion record = new NbchatExamQuestion();
        record.setQuestionId(IdWorker.nextAutoIdStr());
        record.setCourseId(saveRequest.getCourseId());
        record.setTenantCode(saveRequest.getTenantCode());
        record.setQuestionName(question.getQuestion());
        record.setQuestionType(saveRequest.getQuestionType());
        record.setCreateTime(new Date());
        record.setPartId(saveRequest.getPartId());
        record.setDifficulty(question.getDifficulty());
        record.setExplan(question.getExplan());
        record.setKnowledges(saveRequest.getKnowledges());
        nbchatExamQuestionMapper.insertSelective(record);
        return record;
    }
    public void saveAnswerRecord(NbchatExamQuestion record,List<String> items) {
        saveAnswerRecord(record,items,null);
    }

    public void saveAnswerRecord(NbchatExamQuestion record,List<String> items,List<Integer> answers) {
        NbchatExamQuestionItems answer = new NbchatExamQuestionItems();
        for (String item : items) {
            answer.setItemId(IdWorker.nextAutoIdStr());
            answer.setQuestionId(record.getQuestionId());
            answer.setQuestionType(record.getQuestionType());
            answer.setItemValue(item);
            answer.setItemIndex((short) items.indexOf(item));
            if (CollectionUtils.isNotEmpty(answers)) {
                String isRight = answers.contains(items.indexOf(item)) ? "1" : "0";
                answer.setIsRight(isRight);
            } else {
                answer.setIsRight("1");
            }
            answer.setCreateTime(new Date());
            nbchatExamQuestionItemsMapper.insertSelective(answer);
        }
    }
}
