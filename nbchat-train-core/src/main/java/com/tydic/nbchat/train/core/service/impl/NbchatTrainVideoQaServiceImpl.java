package com.tydic.nbchat.train.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.train.api.NbchatTrainVideoQaApi;
import com.tydic.nbchat.train.api.NbchatVideoQaRecordApi;
import com.tydic.nbchat.train.api.bo.exam.ExamQuestion;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQaBO;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQaRecordBO;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQaRspBO;
import com.tydic.nbchat.train.api.bo.video.VideoExamSubmitReqBO;
import com.tydic.nbchat.train.mapper.NbchatVideoQaMapper;
import com.tydic.nbchat.train.mapper.po.NbchatVideoQa;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class NbchatTrainVideoQaServiceImpl implements NbchatTrainVideoQaApi {

    private final NbchatVideoQaRecordApi nbchatVideoQaRecordApi;
    private final NbchatVideoQaMapper nbchatVideoQaMapper;
    private final NbchatExamRuleTestServiceImpl nbchatExamRuleTestService;

    NbchatTrainVideoQaServiceImpl(NbchatVideoQaRecordApi nbchatVideoQaRecordApi,
                                  NbchatVideoQaMapper nbchatVideoQaMapper,
                                  NbchatExamRuleTestServiceImpl nbchatExamRuleTestService){
        this.nbchatVideoQaRecordApi = nbchatVideoQaRecordApi;
        this.nbchatVideoQaMapper = nbchatVideoQaMapper;
        this.nbchatExamRuleTestService = nbchatExamRuleTestService;
    }

    @Override
    public Rsp add(NbchatVideoQaBO request) {
        log.info("视频插入试题开始：{}",request);
        NbchatVideoQa rec = new NbchatVideoQa();
        BeanUtils.copyProperties(request,rec);
        int i = nbchatVideoQaMapper.insertSelective(rec);
        if (i > 0 ) {
            return BaseRspUtils.createSuccessRsp(null);
        }
        return BaseRspUtils.createErrorRsp("插入失败");
    }

    @Override
    public RspList query(NbchatVideoQaBO request) {
        log.info("视频查询试题开始：{}",request);
        NbchatVideoQa cond = new NbchatVideoQa();
        BeanUtils.copyProperties(request,cond);
        cond.setUserId(null);
        cond.setIsValid(Integer.valueOf(EntityValidType.NORMAL.getCode()));
        List<NbchatVideoQa> qas = nbchatVideoQaMapper.selectAll(cond);
        List<String> ids = qas.stream().map(NbchatVideoQa::getQid).collect(Collectors.toList());
        List<ExamQuestion> questions = nbchatExamRuleTestService.queryQuestion(ids, null);
        Map<String, ExamQuestion> questionMap = questions.stream().collect(Collectors.toMap(ExamQuestion::getQuestionId, v -> v));
        List<NbchatVideoQaRspBO> res = new ArrayList<>();
        for (NbchatVideoQa qa : qas) {
            NbchatVideoQaRspBO entity = new NbchatVideoQaRspBO();
            entity.setQuestion(questionMap.get(qa.getQid()));
            entity.setKeyFrameTime(qa.getKeyFrameTime());
            res.add(entity);
        }
        return BaseRspUtils.createSuccessRspList(res);
    }

    @Override
    public Rsp submit(VideoExamSubmitReqBO request) {
        log.info("提交视频考试答案：{}",request);
        NbchatVideoQaRecordBO req = new NbchatVideoQaRecordBO();
        BeanUtils.copyProperties(request,req);
        String answerContent = JSON.toJSONString(request.getAnswers());
        req.setAnswerContent(answerContent);
        nbchatVideoQaRecordApi.save(req);
        return BaseRspUtils.createSuccessRsp(null);
    }

}
