package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.tdh.TdhBackgroundQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhBackgroundQueryRspBO;
import com.tydic.nbchat.train.api.tdh.TdhBackgroundApi;
import com.tydic.nbchat.train.mapper.TdhBackgroundMapper;
import com.tydic.nbchat.train.mapper.po.TdhBackground;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数字人：背景管理相关接口
 */
@Slf4j
@Service
public class TdhBackgroundServiceImpl implements TdhBackgroundApi {

    @Resource
    private TdhBackgroundMapper tdhBackgroundMapper;

    @Override
    public RspList<TdhBackgroundQueryRspBO> getBackgroundList(TdhBackgroundQueryReqBO reqBO) {
        TdhBackground cond = new TdhBackground();
        BeanUtils.copyProperties(reqBO,cond);
        cond.setTenantCode(null);
        cond.setIsValid(EntityValidType.NORMAL.getCode());
        Page<TdhBackground> info = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        tdhBackgroundMapper.selectAll(cond);
        List<TdhBackground> result = info.getResult();
        ArrayList<TdhBackgroundQueryRspBO> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(result,rspList,TdhBackgroundQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspList,info.getTotal());
    }

    @Override
    public RspList<TdhBackgroundQueryRspBO> save(TdhBackgroundQueryReqBO reqBO) {
        log.info("数字人面板-保存/更新背景：{}",reqBO);
        List<TdhBackgroundQueryRspBO> rspBOList = new ArrayList<>();
        TdhBackground cond = new TdhBackground();
        BeanUtils.copyProperties(reqBO,cond);
        reqBO.getUrlList().forEach(tdhBackgroundUrlBO -> {
            cond.setName(tdhBackgroundUrlBO.getName());
            cond.setCategory(tdhBackgroundUrlBO.getCategory());
            cond.setObjectUrl(tdhBackgroundUrlBO.getObjectUrl());
            cond.setObjectSize(tdhBackgroundUrlBO.getObjectSize());
            cond.setObjectType(tdhBackgroundUrlBO.getObjectType());
            cond.setOrderIndex(tdhBackgroundUrlBO.getOrderIndex());
            if (StringUtils.isNotEmpty(tdhBackgroundUrlBO.getObjectId())) {
                cond.setObjectId(tdhBackgroundUrlBO.getObjectId());
                tdhBackgroundMapper.update(cond);
            }else {
                cond.setIsValid(EntityValidType.NORMAL.getCode());
                cond.setCreateTime(new Date());
                cond.setObjectSource(StateEnum.SOURCE.SELF.getCode());
                cond.setObjectId(IdWorker.nextAutoIdStr());
                tdhBackgroundMapper.insertSelective(cond);
            }
            TdhBackgroundQueryRspBO rsp = new TdhBackgroundQueryRspBO();
            BeanUtils.copyProperties(cond,rsp);
            rspBOList.add(rsp);
        });
        return BaseRspUtils.createSuccessRspList(rspBOList,rspBOList.size());
    }
}
