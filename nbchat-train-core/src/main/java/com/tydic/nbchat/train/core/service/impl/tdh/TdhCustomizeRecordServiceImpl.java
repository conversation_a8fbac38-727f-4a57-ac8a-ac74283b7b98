package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.eums.CustomizeStatusEnum;
import com.tydic.nbchat.train.api.bo.eums.CustomizeType;
import com.tydic.nbchat.train.api.bo.tdh.*;
import com.tydic.nbchat.train.api.tdh.TdhCustomizeRecordApi;
import com.tydic.nbchat.train.core.busi.TdhAnchorBusiService;
import com.tydic.nbchat.train.core.busi.TdhHumanBusiService;
import com.tydic.nbchat.train.core.busi.TrainEventSender;
import com.tydic.nbchat.train.mapper.TdhCustomizeRecordMapper;
import com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord;
import com.tydic.nbchat.user.api.NoticeApi;
import com.tydic.nbchat.user.api.bo.notice.NoticeBO;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class TdhCustomizeRecordServiceImpl implements TdhCustomizeRecordApi {
    private final TdhCustomizeRecordMapper tdhCustomizeRecordMapper;
    private final TdhAnchorBusiService tdhAnchorBusiService;
    private final TrainEventSender trainEventSender;
    private final TdhHumanBusiService tdhHumanBusiService;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NoticeApi noticeApi;

    public TdhCustomizeRecordServiceImpl(TdhCustomizeRecordMapper tdhCustomizeRecordMapper,
                                         TdhAnchorBusiService tdhAnchorBusiService,
                                         TdhHumanBusiService tdhHumanBusiService,
                                         TrainEventSender trainEventSender) {
        this.tdhCustomizeRecordMapper = tdhCustomizeRecordMapper;
        this.tdhAnchorBusiService = tdhAnchorBusiService;
        this.tdhHumanBusiService = tdhHumanBusiService;
        this.trainEventSender = trainEventSender;
    }

    @Override
    public RspList orderList(TdhCustomizeRecordQueryReqBO reqBO) {
        List<TdhCustomizeRecordQueryRspBO> result = new ArrayList<>();
        TdhCustomizeRecord customizeRecord = new TdhCustomizeRecord();
        BeanUtils.copyProperties(reqBO, customizeRecord);
        customizeRecord.setIsValid(EntityValidType.NORMAL.getCode());
        Page<TdhCustomizeRecord> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        tdhCustomizeRecordMapper.findByAll(customizeRecord);
        if (CollectionUtils.isEmpty(page)) {
            return BaseRspUtils.createSuccessRspList(result, page.getTotal());
        }
        NiccCommonUtil.copyList(page.getResult(), result, TdhCustomizeRecordQueryRspBO.class);
        log.info("我的定制-形象定制-订单列表查询成功，reqBO:{}|{}", reqBO, result.size());
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    @Override
    @MethodParamVerifyEnable
    public Rsp orderInfo(TdhCustomizeRecordQueryReqBO reqBO) {
        TdhCustomizeRecord customizeRecord = tdhCustomizeRecordMapper.findByOrderNoAndUserId(reqBO.getOrderNo(), reqBO.getUserId());
        if (customizeRecord == null) {
            log.error("我的定制-形象定制-订单明细查询-未查询到记录，id:{}", reqBO.getId());
            return BaseRspUtils.createErrorRsp("未查询到记录");
        }
        TdhCustomizeRecordQueryRspBO rspBO = new TdhCustomizeRecordQueryRspBO();
        BeanUtils.copyProperties(customizeRecord, rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    @MethodParamVerifyEnable
    @Transactional(rollbackFor = Exception.class)
    public Rsp updateInfo(TdhCustomizeRecordUpdateInfoReqBO request) {
        TdhCustomizeRecord tdhCustomizeRecord = tdhCustomizeRecordMapper.findByOrderNoAndUserId(request.getOrderNo(), request.getUserId());
        if (tdhCustomizeRecord == null) {
            log.error("我的定制-形象定制-订单信息更新-未查询到记录，reqBO:{}", request);
            return BaseRspUtils.createErrorRsp("未查询到记录");
        }
        if (EntityValidType.DELETE.getCode().equals(request.getIsValid()) &&
                CustomizeStatusEnum.CUSTOMIZE_COMPLETED.getCode().equals(tdhCustomizeRecord.getCustomizeType())) {
            log.warn("我的定制-形象定制-订单信息更新-已完成的订单不能取消，reqBO:{}", request);
            return BaseRspUtils.createErrorRsp("已完成的订单不能取消");
        }
        if (CustomizeType.AUDIO.getCode().equals(request.getCustomizeType())) {
            request.setTdhName(null);
        } else {
            request.setVoiceName(null);
        }
        TdhCustomizeRecord customizeRecord = new TdhCustomizeRecord();
        BeanUtils.copyProperties(request, customizeRecord);
        int result = tdhCustomizeRecordMapper.updateByOrderNoAndUserId(customizeRecord);
        if (result <= 0) {
            log.error("我的定制-形象定制-订单信息更新失败，reqBO:{}|{}", request, result);
            return BaseRspUtils.createErrorRsp("更新失败");
        }
        NoticeBO noticeBO = new NoticeBO();
        noticeBO.setTenantCode(request.getTenantCode());
        if (CustomizeType.AUDIO.getCode().equals(customizeRecord.getCustomizeType())) {
            TdhVirtualAnchorQueryReqBO reqBO = new TdhVirtualAnchorQueryReqBO();
            reqBO.setOrderNo(request.getOrderNo());
            reqBO.setUserId(request.getUserId());
            reqBO.setName(request.getVoiceName());
            if (EntityValidType.DELETE.getCode().equals(request.getIsValid())) {
                reqBO.setCustomizeStatus(CustomizeStatusEnum.CANCEL.getCode());
                // 退款成功后，将语音文件删除
                reqBO.setIsValid(EntityValidType.DELETE.getCode());
                //发送通知
                noticeBO.setOrderId(request.getOrderNo());
                noticeBO.setUserId(request.getUserId());
                noticeBO.setType(NoticeBO.Type.AUDIO);
                noticeBO.setAction(NoticeBO.Action.CANCEL);
            }
            tdhAnchorBusiService.updateAnchor(reqBO);
            log.info("我的定制-声音定制-修改状态成功，request:{}|{}", request, customizeRecord);
        } else {
            // 退款成功后，将图片文件删除
            TdhVirtualHumanReqBO reqBO = new TdhVirtualHumanReqBO();
            reqBO.setOrderNo(request.getOrderNo());
            reqBO.setUserId(request.getUserId());
            reqBO.setTdhName(request.getTdhName());
            if (EntityValidType.DELETE.getCode().equals(request.getIsValid())) {
                // 退款成功后，将语音文件删除
                reqBO.setIsValid(EntityValidType.DELETE.getCode());
                reqBO.setCustomizeStatus(CustomizeStatusEnum.CANCEL.getCode());
                //发送通知
                noticeBO.setOrderId(request.getOrderNo());
                noticeBO.setUserId(request.getUserId());
                noticeBO.setType(NoticeBO.Type.TDH);
                noticeBO.setAction(NoticeBO.Action.CANCEL);
            }
            tdhHumanBusiService.updateHuman(reqBO);
            log.info("我的定制-形象定制-修改状态成功，request:{}|{}", request, customizeRecord);
        }
        TdhCustomizeRecord record = tdhCustomizeRecordMapper.findByOrderNoAndUserId(request.getOrderNo(), request.getUserId());
        if (EntityValidType.DELETE.getCode().equals(request.getIsValid())) {
            try {
                noticeApi.notice(noticeBO);
            } catch (Exception e) {
                log.error("我的定制-形象定制-修改状态失败-发送通知失败，reqBO:{}|{}", request, e.getMessage());
            }
            // 发送用户维度报表事件
            record.setCustomizeStatus(CustomizeStatusEnum.CANCEL.getCode());
            trainEventSender.sendUserRpEventByTdhCust(record, null);
        }
        TdhCustomizeRecordQueryRspBO rspBO = new TdhCustomizeRecordQueryRspBO();
        BeanUtils.copyProperties(record, rspBO);
        log.info("我的定制-形象定制-订单信息更新成功，reqBO:{}|{}", request, result);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    public Rsp updateFirstViewTime(TdhCustomizeRecordQueryReqBO request) {
        int i = tdhCustomizeRecordMapper.updateViewTime(request.getOrderNo(), request.getUserId());
        return BaseRspUtils.createSuccessRsp(null);
    }
}
