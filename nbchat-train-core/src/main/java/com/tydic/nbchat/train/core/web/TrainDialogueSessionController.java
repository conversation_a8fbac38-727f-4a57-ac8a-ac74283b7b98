package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainDialogueApi;
import com.tydic.nbchat.train.api.bo.dialogue.DialogueReqBO;
import com.tydic.nbchat.train.api.bo.dialogue.DialogueSessionQueryReqBO;
import com.tydic.nbchat.train.api.bo.dialogue.DialogueSessionSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/dialogue")
public class TrainDialogueSessionController {

    private final NbchatTrainDialogueApi nbchatTrainDialogueApi;

    public TrainDialogueSessionController(NbchatTrainDialogueApi nbchatTrainDialogueApi) {
        this.nbchatTrainDialogueApi = nbchatTrainDialogueApi;
    }

    @Deprecated
    @PostMapping("list")
    public RspList getCourseDialogues(@RequestBody DialogueReqBO reqBO){
        return nbchatTrainDialogueApi.getCourseDialogues(reqBO);
    }

    @PostMapping("session/save")
    public Rsp saveSession(@RequestBody DialogueSessionSaveReqBO request){
        return nbchatTrainDialogueApi.saveSession(request);
    }

    @PostMapping("session/info")
    public Rsp getSessionInfo(@RequestBody DialogueSessionQueryReqBO request){
        return nbchatTrainDialogueApi.getSessionInfo(request);
    }

}
