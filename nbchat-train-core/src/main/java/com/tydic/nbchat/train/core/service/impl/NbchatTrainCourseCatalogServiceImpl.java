package com.tydic.nbchat.train.core.service.impl;

import com.tydic.nbchat.train.api.NbchatTrainCourseCatalogApi;
import com.tydic.nbchat.train.api.bo.catalog.TrainCourseCategoryBO;
import com.tydic.nbchat.train.mapper.NbchatTrainCatalogMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseCategoryMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourseCategory;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class NbchatTrainCourseCatalogServiceImpl implements NbchatTrainCourseCatalogApi {

    @Resource
    NbchatTrainCourseCategoryMapper nbchatTrainCourseCategoryMapper;

    @Override
    public Rsp save(TrainCourseCategoryBO reqBO) {
        log.info("保存培训课程分类：{}",reqBO);
        NbchatTrainCourseCategory record = new NbchatTrainCourseCategory();
        BeanUtils.copyProperties(reqBO,record);
        record.setCateId(IdWorker.nextAutoIdStr());
        record.setCreateTime(new Date());
        nbchatTrainCourseCategoryMapper.insertSelective(record);
        return BaseRspUtils.createSuccessRsp(null);
    }

    @Override
    public Rsp update(TrainCourseCategoryBO reqBO) {
        log.info("更新培训课程分类:{}",reqBO);
        NbchatTrainCourseCategory record = new NbchatTrainCourseCategory();
        BeanUtils.copyProperties(reqBO,record);
        nbchatTrainCourseCategoryMapper.update(record);
        return BaseRspUtils.createSuccessRsp(null);
    }

    @Override
    public RspList get(TrainCourseCategoryBO reqBO) {
        log.info("查询培训课程目录:{}",reqBO);
        NbchatTrainCourseCategory record = new NbchatTrainCourseCategory();
        BeanUtils.copyProperties(reqBO,record);
        List<NbchatTrainCourseCategory> categorys = nbchatTrainCourseCategoryMapper.selectAll(record);
        List<TrainCourseCategoryBO> bos = new ArrayList<>();
        NiccCommonUtil.copyList(categorys,bos,TrainCourseCategoryBO.class);
        return BaseRspUtils.createSuccessRspList(bos);
    }
}
