package com.tydic.nbchat.train.core.service.impl.tdh;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.TdhMaterialPackageApi;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialLibraryQueryBo;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialLibraryRspBo;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialPackageQueryBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialPackageRspBo;
import com.tydic.nbchat.train.mapper.TdhMaterialLibraryMapper;
import com.tydic.nbchat.train.mapper.TdhMaterialPackageMapper;
import com.tydic.nbchat.train.mapper.po.TdhMaterialLibrary;
import com.tydic.nbchat.train.mapper.po.TdhMaterialPackage;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 图库素材包服务实现类
 */

@Slf4j
@Service
public class TdhMaterialPackageServiceImpl implements TdhMaterialPackageApi {

    private final TdhMaterialPackageMapper tdhMaterialPackageMapper;
    private final TdhMaterialLibraryMapper tdhMaterialLibraryMapper;

    public TdhMaterialPackageServiceImpl(TdhMaterialPackageMapper tdhMaterialPackageMapper, TdhMaterialLibraryMapper tdhMaterialLibraryMapper) {
        this.tdhMaterialPackageMapper = tdhMaterialPackageMapper;
        this.tdhMaterialLibraryMapper = tdhMaterialLibraryMapper;
    }

    /**
     * 查询图库素材包列表
     * @param request
     * @return
     */
    @Override
    public RspList<TdhMaterialPackageRspBo> getMaterialPackageList(TdhMaterialPackageQueryBO request) {
        log.info("查询图库素材包列表-开始:{}", request);
        TdhMaterialPackage tdhMaterialPackage = new TdhMaterialPackage();
        BeanUtils.copyProperties(request, tdhMaterialPackage);
        Page<TdhMaterialPackageRspBo> page = PageHelper.startPage(request.getPage(), request.getLimit());
        List<TdhMaterialPackage> list = tdhMaterialPackageMapper.selectByCondition(tdhMaterialPackage);
        List<TdhMaterialPackageRspBo> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(list, rspList, TdhMaterialPackageRspBo.class);
        return BaseRspUtils.createSuccessRspList(rspList, page.getTotal());
    }

    /**
     * 保存、更新图库素材包
     * @param request
     * @return
     */
    @Override
    public Rsp<TdhMaterialPackageRspBo> saveMaterialPackage(TdhMaterialPackageQueryBO request) {
        log.info("保存、更新图库素材包-开始:{}", request);
        TdhMaterialPackage tdhMaterialPackage = new TdhMaterialPackage();
        BeanUtils.copyProperties(request, tdhMaterialPackage);
        if (StringUtils.isBlank(request.getPkgId())) {
            tdhMaterialPackage.setCreateTime(new Date());
            tdhMaterialPackage.setUpdateTime(new Date());
            tdhMaterialPackage.setIsValid("1");
            tdhMaterialPackage.setStatus("0");
            tdhMaterialPackageMapper.insertSelective(tdhMaterialPackage);
        } else {
            tdhMaterialPackage.setUpdateTime(new Date());
            tdhMaterialPackageMapper.updateByPrimaryKeySelective(tdhMaterialPackage);
        }
        TdhMaterialPackageRspBo rspBo = new TdhMaterialPackageRspBo();
        BeanUtils.copyProperties(tdhMaterialPackage, rspBo);
        return BaseRspUtils.createSuccessRsp(rspBo);
    }


    /**
     * 删除素材包以及图片素材
     * @param pkgId
     * @param isValid
     * @return
     */
    @Override
    public Rsp deleteMaterialPackage(String pkgId, String isValid) {
        log.info("删除素材包-开始:{}", pkgId);
        if (StringUtils.isBlank(pkgId)) {
            return BaseRspUtils.createErrorRsp("素材包ID不能为空");
        }
        TdhMaterialPackage materialPackage = new TdhMaterialPackage();
        materialPackage.setPkgId(pkgId);
        materialPackage.setIsValid("0");
        materialPackage.setUpdateTime(new Date());
        tdhMaterialPackageMapper.updateByPrimaryKeySelective(materialPackage);

        TdhMaterialLibrary materialLibrary = new TdhMaterialLibrary();
        materialLibrary.setPkgId(pkgId);
        materialLibrary.setIsValid("0");
        materialLibrary.setUpdateTime(new Date());
        tdhMaterialLibraryMapper.updateByPkgId(materialLibrary);

        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 用户获取素材包和图片素材信息
     * @param queryBo
     * @return
     */
    @Override
    public RspList<TdhMaterialLibraryRspBo> getMaterialPackageAndLibrary(TdhMaterialLibraryQueryBo queryBo) {
        log.info("用户获取素材包和图片素材信息-开始:{}", queryBo);
        Page<TdhMaterialLibraryRspBo> page = PageHelper.startPage(queryBo.getPage(), queryBo.getLimit());
        List<TdhMaterialLibrary> tdhMaterialLibraries = tdhMaterialLibraryMapper.selectByPkgId(queryBo.getPkgId());
        List<TdhMaterialLibraryRspBo> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(tdhMaterialLibraries, rspList, TdhMaterialLibraryRspBo.class);
        return BaseRspUtils.createSuccessRspList(rspList, page.getTotal());
    }


    /**
     * 根据素材包id查询图片素材信息
     * @param queryBo
     * @return
     */
    @Override
    public RspList<TdhMaterialLibraryRspBo> getMaterialLibraryId(TdhMaterialLibraryQueryBo queryBo) {
        log.info("根据素材包id查询图片素材信息-开始:{}", queryBo);
        Page<TdhMaterialLibraryRspBo> page = PageHelper.startPage(queryBo.getPage(), queryBo.getLimit());
        List<TdhMaterialLibrary> tdhMaterialLibraries = tdhMaterialLibraryMapper.selectByPkgId(queryBo.getPkgId());
        List<TdhMaterialLibraryRspBo> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(tdhMaterialLibraries, rspList, TdhMaterialLibraryRspBo.class);
        return BaseRspUtils.createSuccessRspList(rspList, page.getTotal());
    }

    /**
     * 根据素材包id，批量保存图片素材信息
     * @param queryBo
     * @return
     */
    @Override
    public Rsp<TdhMaterialLibraryRspBo> saveMaterialLibrary(TdhMaterialLibraryQueryBo queryBo) {
        log.info("保存、更新图片素材-开始:{}", queryBo);
        List<TdhMaterialLibrary> materialLibraryList = new ArrayList<>();
        // 遍历待处理的图片信息
        for (TdhMaterialLibraryRspBo item : queryBo.getMaterialLibraryList()) {
            TdhMaterialLibrary materialLibrary = new TdhMaterialLibrary();
            BeanUtils.copyProperties(item, materialLibrary);

            // 截取图片名称前45个字符
            if (StringUtils.isNotBlank(materialLibrary.getName()) && materialLibrary.getName().length() > 45) {
                materialLibrary.setName(materialLibrary.getName().substring(0, 45));
            }

            if (StringUtils.isBlank(item.getId())) {
                materialLibrary.setPkgId(queryBo.getPkgId());
                materialLibrary.setCreateTime(new Date());
                materialLibrary.setUpdateTime(new Date());
                materialLibrary.setIsValid("1");
                materialLibraryList.add(materialLibrary);
            } else {
                materialLibrary.setUpdateTime(new Date());
                tdhMaterialLibraryMapper.updateByPrimaryKeySelective(materialLibrary);
            }
        }
        // 批量插入新增的图片素材
        if (!materialLibraryList.isEmpty()) {
            tdhMaterialLibraryMapper.batchInsert(materialLibraryList);
        }
        List<TdhMaterialLibraryRspBo> rspList = new ArrayList<>();
        NiccCommonUtil.copyList(materialLibraryList, rspList, TdhMaterialLibraryRspBo.class);
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

}
