package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatTrainDialogueManageApi;
import com.tydic.nbchat.train.api.bo.dialogue.manage.DialogueManageBO;
import com.tydic.nbchat.train.api.bo.dialogue.manage.DialogueManageReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/dialogue/manage")
public class TrainDialogueManageController {

    @Resource
    NbchatTrainDialogueManageApi nbchatTrainDialogueManageApi;

    /**
     * 场景实践配置保存
     * @param request
     * @return
     */
    @PostMapping("scene/save")
    public Rsp save(@RequestBody DialogueManageBO request){
        return nbchatTrainDialogueManageApi.save(request);
    }

    /**
     * 场景实践列表查询
     * @param request
     * @return
     */
    @PostMapping("scene/query")
    public RspList query(@RequestBody DialogueManageReqBO request){
        return nbchatTrainDialogueManageApi.queryScene(request);
    }

    /**
     * 场景实践详情查询
     * @param request
     * @return
     */
    @PostMapping("scene/info")
    public Rsp info(@RequestBody DialogueManageReqBO request){
        return nbchatTrainDialogueManageApi.queryInfo(request);
    }

}
