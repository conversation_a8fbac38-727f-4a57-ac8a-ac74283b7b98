package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.OuterCreationResourceApi;
import com.tydic.nbchat.train.api.outer.OuterResourceBO;
import com.tydic.nbchat.train.api.outer.OuterResourceQueryReqBO;
import com.tydic.nbchat.train.api.outer.OuterResourceSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/outer")
public class OuterCreationSourceController {

    private final OuterCreationResourceApi outerCreationResourceApi;

    public OuterCreationSourceController(OuterCreationResourceApi outerCreationResourceApi) {
        this.outerCreationResourceApi = outerCreationResourceApi;
    }

    @PostMapping("/resource/save")
    public Rsp<String> saveResource(@RequestBody OuterResourceSaveReqBO request){
        try {
            return outerCreationResourceApi.saveResource(request);
        } catch (Exception e) {
            log.error("资源保存异常:", e);
            return BaseRspUtils.createErrorRsp("资源保存异常:" + e.getMessage());
        }
    }

    @PostMapping("/resource/get")
    public Rsp<OuterResourceBO> getResource(@RequestBody OuterResourceQueryReqBO request){
        return outerCreationResourceApi.getResource(request);
    }

}
