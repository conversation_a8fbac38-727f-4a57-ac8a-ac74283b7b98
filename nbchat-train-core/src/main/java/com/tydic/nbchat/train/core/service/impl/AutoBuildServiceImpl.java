package com.tydic.nbchat.train.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.alibaba.nacos.shaded.com.google.common.base.Function;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nbchat.train.api.*;
import com.tydic.nbchat.train.api.bo.QuestionSaveRequest;
import com.tydic.nbchat.train.api.bo.autoBuild.AutoBuildBO;
import com.tydic.nbchat.train.api.bo.autoBuild.CatalogTmpl;
import com.tydic.nbchat.train.api.bo.course.TranCourseBO;
import com.tydic.nbchat.train.api.bo.course.TranCourseQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TranCourseSaveReqBO;
import com.tydic.nbchat.train.api.bo.dialogue.DialogueBO;
import com.tydic.nbchat.train.api.bo.eums.AiCourseGeneType;
import com.tydic.nbchat.train.api.bo.eums.QuestionType;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.generate.TranCourseGenerateRequest;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBO;
import com.tydic.nbchat.train.api.bo.train.section.SectionsSaveRequest;
import com.tydic.nbchat.train.core.helper.NlsStrategyInvokeFactory;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCatalog;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.FutureTask;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class AutoBuildServiceImpl implements AutoBuildApi {
    @Resource
    NbchatCourseAiToolApi nbchatCourseAiToolApi;
    @Resource
    NbchatTrainCourseApi nbchatTrainCourseApi;
    @Resource
    NbchatTrainCatalogApi nbchatTrainCatalogApi;
    @Resource
    NbchatTrainSectionApi nbchatTrainSectionApi;
    @Resource
    NbcahtExamQuestionApi nbcahtExamQuestionApi;
    @Resource
    NbchatTrainDialogueApi nbchatTrainDialogueApi;
    @Resource
    private NlsStrategyInvokeFactory nlsStrategyInvokeFactory;


    private FutureTask<Rsp> taskFunction(Function<AutoBuildBO, Rsp> function, AutoBuildBO request, String code){
        String name = AiCourseGeneType.getNameByCode(code);
        Callable<Rsp> call = () -> {
            log.info("一键生成-{}-开始",name);
            long start = System.currentTimeMillis();
            Rsp rsp1 = function.apply(request);
            log.info("一键生成-{}-结束：{}|{}/ms",name,rsp1,System.currentTimeMillis() - start);
            return rsp1;
        };
        return new FutureTask<>(call);
    }

    @Deprecated
    @Override
    public void build(AutoBuildBO request) {
        log.info("自动生成：{}",request);
        if (this.checkTaskIsRunning(request.getCourseId())) {
            return;
        }
        if (CollectionUtils.isEmpty(request.getSteps())) {
            List<String> steps = Arrays.asList(
                    AiCourseGeneType.train_course_desc.getCode(),
                    AiCourseGeneType.train_course_catalog.getCode(),
                    AiCourseGeneType.train_course_section.getCode()
                    //默认不创建试题和对话
                    //AiCourseGeneType.train_course_exam_s_choice.getCode(),
                    //AiCourseGeneType.train_course_scene_dialogue.getCode()
                    );
            request.setSteps(steps);
        }

        updateCourseTaskState(request.getCourseId(), StateEnum.TASK.RUNNING.getCode(),null);
        Map<String,FutureTask<Rsp>> map = new HashMap<>();

        try {
            if(request.getSteps().contains(AiCourseGeneType.train_course_desc.getCode())) {
                Function<AutoBuildBO, Rsp> buildCourseDesc = this::buildCourseDesc;
                FutureTask<Rsp> descTask = taskFunction(buildCourseDesc, request, AiCourseGeneType.train_course_desc.getCode());
                new Thread(descTask).start();
                map.put(AiCourseGeneType.train_course_desc.getCode(),descTask);
            }

            if (request.getSteps().contains(AiCourseGeneType.train_course_catalog.getCode())) {
                Function<AutoBuildBO, Rsp> buildCourseCategory = this::buildCourseCategory;
                FutureTask<Rsp> cateTask = taskFunction(buildCourseCategory, request, AiCourseGeneType.train_course_catalog.getCode());
                new Thread(cateTask).start();
                map.put(AiCourseGeneType.train_course_catalog.getCode(),cateTask);
            }

            if (request.getSteps().contains(AiCourseGeneType.train_course_section.getCode())) {
                boolean cateTaskIsOk = true;
                if (map.containsKey(AiCourseGeneType.train_course_catalog.getCode())) {
                    FutureTask<Rsp> cateTask = map.get(AiCourseGeneType.train_course_catalog.getCode());
                    Rsp rsp = null;
                    try {
                        rsp = cateTask.get();
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                    if (!rsp.isSuccess()) {
                        log.info("一键生成-目录-失败：{}",rsp);
                        cateTaskIsOk = false;
                    }
                }
                if (cateTaskIsOk) {
                    Function<AutoBuildBO, Rsp> buildCourseContent = this::buildSectionContent;
                    FutureTask<Rsp> sectionTask = taskFunction(buildCourseContent, request, AiCourseGeneType.train_course_section.getCode());
                    new Thread(sectionTask).start();
                    map.put(AiCourseGeneType.train_course_section.getCode(), sectionTask);
                }
            }

            if (request.getSteps().contains(AiCourseGeneType.train_course_exam_s_choice.getCode())) {
                Function<AutoBuildBO, Rsp> buildExam = this::buildExam;
                FutureTask<Rsp> examTask = taskFunction(buildExam, request, AiCourseGeneType.train_course_exam_s_choice.getCode());
                new Thread(examTask).start();
                map.put(AiCourseGeneType.train_course_exam_s_choice.getCode(),examTask);
            }

            if (request.getSteps().contains(AiCourseGeneType.train_course_scene_dialogue.getCode())) {
                Function<AutoBuildBO, Rsp> buildDialogue = this::buildDialogue;
                FutureTask<Rsp> dialogueTask = taskFunction(buildDialogue, request, AiCourseGeneType.train_course_scene_dialogue.getCode());
                new Thread(dialogueTask).start();
                map.put(AiCourseGeneType.train_course_scene_dialogue.getCode(),dialogueTask);
            }
        } catch (Exception e) {
            updateCourseTaskState(request.getCourseId(), StateEnum.TASK.ERROR.getCode(),null);
            throw new RuntimeException(e);
        }
        //任务状态检查
        loopModifyTask(map,request.getCourseId());
    }

    public void loopModifyTask(Map<String,FutureTask<Rsp>> map,String courseId){
        int size = map.size();
        Set<String> finishSet = new HashSet<>();
        boolean state = true;
        while (state) {
            log.info("一键生成-总步骤{},已完成{}",map.keySet(),finishSet);
            if (finishSet.size() == size) {
                log.info("一键生成-任务结束");
                updateCourseTaskState(courseId, StateEnum.TASK.FINISH.getCode(),StateEnum.COURSE.EDIT.getCode());
                //nlsStrategyInvokeFactory.getApi().createAudioTask(courseId, "",false);
                //nlsStrategyInvokeFactory.getApi().createAudioTask(courseId, "",true);
                break;
            }

            for (Map.Entry<String, FutureTask<Rsp>> task : map.entrySet()) {
                Rsp rsp = null;
                try {
                    rsp = task.getValue().get();
                } catch (Exception e) {
                    updateCourseTaskState(courseId, StateEnum.TASK.ERROR.getCode(),null);
                    throw new RuntimeException(e);
                }
                if (rsp!= null) {
                    log.info("任务状态检查：{}",rsp);
                    if (rsp.isSuccess()) {
                        finishSet.add(task.getKey());
                    } else {
                        updateCourseTaskState(courseId, StateEnum.TASK.ERROR.getCode(),null,rsp.getRspDesc());
                        state = false;
                        break;
                    }
                }
            }
            try {
                TimeUnit.SECONDS.sleep(5L);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public void updateCourseTaskState(String courseId,String taskState,String courseState){
       this.updateCourseTaskState( courseId, taskState, courseState,"");
    }
    public void updateCourseTaskState(String courseId,String taskState,String courseState,String errorDesc){
        log.info("一键生成-更新课程任务状态：{}|{}|{}",courseId,taskState,courseState);
        TranCourseSaveReqBO course = new TranCourseSaveReqBO();
        course.setCourseId(courseId);
        course.setTaskState(taskState);
        course.setCourseState(courseState);
        course.setTaskFailDesc(errorDesc);
        nbchatTrainCourseApi.saveCourse(course);
    }

    public boolean checkTaskIsRunning(String courseId){
        log.info("一键生成-检查课程任务状态：{}",courseId);
        TranCourseQueryReqBO course = new TranCourseQueryReqBO();
        course.setCourseId(courseId);
        Rsp<TranCourseBO> res = nbchatTrainCourseApi.getCourse(course);
        if (res.isSuccess()) {
            String taskState = res.getData().getTaskState();
            if (StringUtils.isNotEmpty(taskState) && StateEnum.TASK.RUNNING.getCode().equals(taskState)) {
                log.info("生成课程任务正在执行，请勿重复提交");
                return true;
            }
        }
        return false;
    }

    /**
     * 生成课程简介
     * @param request
     * @return
     */
    public Rsp buildCourseDesc(AutoBuildBO request){
        String content = process(AiCourseGeneType.train_course_desc.getCode(), request.getCourseId());
        if (StringUtils.isEmpty(content)) {
            log.error("一键生成-生成课程简介-失败");
            return BaseRspUtils.createErrorRsp("一键生成-生成课程简介-失败");
        }
        TranCourseSaveReqBO course = new TranCourseSaveReqBO();
        BeanUtils.copyProperties(request,course);
        course.setCourseDesc(content);
        return nbchatTrainCourseApi.saveCourse(course);
    }

    /**
     * 生成课程类目
     * @param request
     * @return
     */
    public Rsp buildCourseCategory(AutoBuildBO request){
        String content = process(AiCourseGeneType.train_course_catalog.getCode(), request.getCourseId());
        if (StringUtils.isEmpty(content)) {
            return BaseRspUtils.createErrorRsp("生成类目失败");
        }
        List<CatalogTmpl> catalogTmpls = JSONObject.parseArray(content, CatalogTmpl.class);

        TrainCatalogBO catalogBO = new TrainCatalogBO();
        BeanUtils.copyProperties(request,catalogBO);
        Rsp delete = nbchatTrainCatalogApi.delete(catalogBO);
        log.info("一键生成-类目-删除结果：{}",delete);
        int index = 1;
        for (CatalogTmpl tmpl : catalogTmpls) {
            catalogBO.setCatalogTitle(tmpl.getTitle());
            catalogBO.setCatalogLevel((short) 1);
            catalogBO.setCatalogIndex((short) index ++);
            catalogBO.setParentId("-1");
            Rsp<String> saveRes = nbchatTrainCatalogApi.save(catalogBO);
            if (saveRes.isSuccess()) {
                String parentId = saveRes.getData();
                for (String title : tmpl.getSubtitle()) {
                    catalogBO.setCatalogTitle(title);
                    catalogBO.setCatalogLevel((short) 2);
                    catalogBO.setCatalogIndex((short) index ++);
                    catalogBO.setParentId(parentId);
                    nbchatTrainCatalogApi.save(catalogBO);
                }
            }
        }
        log.info("一键生成-类目-个数：{}",index);
        return BaseRspUtils.createSuccessRsp(null);
    }

    /**
     * 生成章节内容
     * @param request
     * @return
     */
    public Rsp buildSectionContent(AutoBuildBO request) {
        TrainCatalogBO reqBO = new TrainCatalogBO();
        reqBO.setCourseId(request.getCourseId());
        RspList<NbchatTrainCatalog> query = nbchatTrainCatalogApi.query(reqBO);
        List<NbchatTrainCatalog> catalogs = query.getRows();
        if (!query.isSuccess() || CollectionUtils.isEmpty(catalogs)) {
            return BaseRspUtils.createErrorRsp("一键生成-章节内容-获取类目失败");
        }
        Rsp delete = nbchatTrainSectionApi.delete(request.getCourseId());
        List<CatalogTmpl> catalogTmpls = buildTmpl(catalogs);

        int sectionIndex = 1;
        for (CatalogTmpl tmpl : catalogTmpls) {
            String catalog = JSON.toJSONString(tmpl);
            log.info("一键生成-章节内容-目录入参：{}",catalog);
            String content = process(AiCourseGeneType.train_course_section.getCode(), request.getCourseId(),catalog);
            if (StringUtils.isEmpty(content)) {
                log.info("一键生成-章节内容-失败：{}",tmpl);
                continue;
            }
            if (!JSONValidator.from(content).validate()) {
                log.info("一键生成-章节内容-json格式化失败：{}",content);
                continue;
            }
           /* SectionTmpl sectionTmpl = JSONObject.parseObject(content, SectionTmpl.class);
            List<SectionTmpl.Node> subTitle = sectionTmpl.getSubTitle();
            if (CollectionUtils.isEmpty(subTitle)) {
                log.error("一键生成-章节内容-失败：{}",sectionTmpl);
                continue;
            }
            String sectionContent = subTitle.stream().map(SectionTmpl.Node::getContent).collect(Collectors.joining("\n"));*/
            SectionsSaveRequest section = new SectionsSaveRequest();
            BeanUtils.copyProperties(request,section);
            section.setCatalogId(tmpl.getCatalogId());
            section.setContent(content);
            section.setCreateTime(new Date());
            section.setSectionsIndex((short) sectionIndex++);
            nbchatTrainSectionApi.saveSection(section);
        }
        return BaseRspUtils.createSuccessRsp(null);
    }


    public Rsp buildExam(AutoBuildBO request) {
        String content = process(AiCourseGeneType.train_course_exam_s_choice.getCode(), request.getCourseId());
        if (StringUtils.isEmpty(content)) {
            log.info("一键生成-试题-失败：{}",request);
            return BaseRspUtils.createErrorRsp("一键生成-试题-失败");
        }
        QuestionSaveRequest saveRequest = new QuestionSaveRequest();
        BeanUtils.copyProperties(request,saveRequest);
        saveRequest.setQuestionType(QuestionType.CHOICE_S.getCode());
        saveRequest.setContent(content);
        return nbcahtExamQuestionApi.saveQuestion(saveRequest);
    }

    public Rsp buildDialogue(AutoBuildBO request) {
        String content = process(AiCourseGeneType.train_course_scene_dialogue.getCode(), request.getCourseId());
        if (StringUtils.isEmpty(content)) {
            log.info("一键生成-对话-失败：{}",request);
            return BaseRspUtils.createErrorRsp("一键生成-对话-失败");
        }
        DialogueBO dialogueBO = new DialogueBO();
        dialogueBO.setCourseId(request.getCourseId());
        dialogueBO.setTenantCode(request.getTenantCode());
        dialogueBO.setContent(content);
        return nbchatTrainDialogueApi.save(dialogueBO);
    }

    public List<CatalogTmpl> buildTmpl(List<NbchatTrainCatalog> catalogs) {
        List<CatalogTmpl> tmpls = new ArrayList<>();
        for (NbchatTrainCatalog catalog : catalogs) {
            if (catalog.getParentId().equals("-1")) {
                CatalogTmpl tmpl = new CatalogTmpl();
                tmpl.setCatalogId(catalog.getCatalogId());
                tmpl.setTitle(catalog.getCatalogTitle());
                tmpl.setSubtitle(new ArrayList<>());
                tmpls.add(tmpl);
                for (NbchatTrainCatalog subCatalog : catalogs) {
                    if (subCatalog.getParentId().equals(catalog.getCatalogId())) {
                        tmpl.getSubtitle().add(subCatalog.getCatalogTitle());
                    }
                }
            }
        }
        return tmpls;
    }

    public String process(String presetId,String courseId) {
        return process(presetId, courseId,"");
    }

    public String process(String presetId,String courseId,String catalog){
        TranCourseGenerateRequest desc = new TranCourseGenerateRequest();
        desc.setPresetId(presetId);
        desc.setCourseId(courseId);
        desc.setCatalog(catalog);
        if (AiCourseGeneType.train_course_exam_s_choice.getCode().equals(presetId)) {
            desc.setCount("25");
        }
        if (AiCourseGeneType.train_course_scene_dialogue.getCode().equals(presetId)) {
            desc.setCount("10");
        }
        Rsp<RobotToolsChatResponse> generate = nbchatCourseAiToolApi.generate(desc);
        log.info("一键生成-调用生成工具-结果：{}",generate);
        if (!generate.isSuccess()){
            log.error("一键生成-课程 [{}]-失败",AiCourseGeneType.getNameByCode(presetId));
            return "";
        }
        RobotToolsChatResponse data = generate.getData();
        return data.getContent();
    }
}
