package com.tydic.nbchat.train.core.service.impl.degree.zhongjiao;

import com.tydic.nbchat.train.api.bo.train.task.ImageInfo;
import com.tydic.nbchat.train.api.bo.train.task.TextInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;

class ImageProcessor {

    private static final String FONT_NAME = "Source Han Sans K Normal";
    private static final int FONT_SIZE_NORMAL = 21;
    private static final int FONT_SIZE_BOLD = 28;
    private static final int FONT_SIZE_TITLE = 55;
    private static final int FONT_LINE_HEIGHT_TITLE = 60;
    private static final Color FONT_COLOR_NORMAL = Color.BLACK;
    private static final Color FONT_COLOR_BOLD = Color.WHITE;
    private static final Color FONT_COLOR_TITLE = Color.decode("#0042A7");

    private Graphics2D graphics;
    private BufferedImage image;

    public ImageProcessor() throws IOException {
        this.initGraphics();
    }

    private void initGraphics() throws IOException {
        image = ImageIO.read(new ClassPathResource("images/degree_template_zj.png").getInputStream());
        graphics = (Graphics2D) image.getGraphics();
        graphics.setFont(new Font(FONT_NAME, Font.PLAIN, FONT_SIZE_NORMAL));
        graphics.setColor(FONT_COLOR_NORMAL);
        graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);//抗锯齿
    }

    public ImageProcessor texts(List<String> texts) {
        //常规信息
        this.drawText(texts);
        return this;
    }

    public ImageProcessor textInfos(List<TextInfo> textInfos) {
        graphics.setFont(new Font(FONT_NAME, Font.BOLD, FONT_SIZE_BOLD));
        for (TextInfo textInfo : textInfos) {
            this.drawText(textInfo.getText(), textInfo.getX(), textInfo.getY());
        }
        return this;
    }

    public ImageProcessor imageInfos(List<ImageInfo> imageInfos) throws IOException {
        for (ImageInfo imageInfo : imageInfos) {
            String localPath = imageInfo.getUrl();
            if (imageInfo.getUrl().startsWith("http")) {
                localPath = this.downloadImage(imageInfo.getUrl());
            }
            this.drawImage(localPath, imageInfo.getX(), imageInfo.getY(), imageInfo.getWidth(), imageInfo.getHeight());
        }
        return this;
    }

    public ImageProcessor issueDate(String issueDate) {
        graphics.setFont(new Font(FONT_NAME, Font.PLAIN, FONT_SIZE_BOLD));
        graphics.setColor(FONT_COLOR_BOLD);
        issueDate = "证书有效期截至：" + issueDate;
        FontMetrics fontMetrics = graphics.getFontMetrics();
        int textWidth = fontMetrics.stringWidth(issueDate);
        int x = (375 - textWidth / 2);
        this.drawText(issueDate, x, 1625);
        return this;
    }

    public ImageProcessor title(String title) {
        graphics.setFont(new Font(FONT_NAME, Font.BOLD, FONT_SIZE_TITLE));
        graphics.setColor(FONT_COLOR_TITLE);
        if (title.length() > 10) {
            String firstPart = title.substring(0, 10);
            String secondPart = title.substring(10);

            FontMetrics fontMetrics1 = graphics.getFontMetrics();
            int textWidth1 = fontMetrics1.stringWidth(firstPart);
            int x1 = (375 - textWidth1 / 2);
            this.drawText(firstPart, x1, 267);

            FontMetrics fontMetrics2 = graphics.getFontMetrics();
            int textWidth2 = fontMetrics2.stringWidth(secondPart);
            int x2 = (375 - textWidth2 / 2);
            this.drawText(secondPart, x2, 267 + FONT_LINE_HEIGHT_TITLE); // add the height of the first line to the y-coordinate
        } else {
            FontMetrics fontMetrics = graphics.getFontMetrics();
            int textWidth = fontMetrics.stringWidth(title);
            int x = (375 - textWidth / 2);
            this.drawText(title, x, 267);
        }
        return this;
    }

    public String build() throws IOException {
        return this.saveImage();
    }

    private void drawText(List<String> args) {
        int padding = 57;
        int x = 409, y = 373;
        for (int i = 0; i < args.size(); i++) {
            if (StringUtils.isEmpty(args.get(i))) {
                graphics.drawString(" ", x, y + padding * i);
            } else {
                graphics.drawString(args.get(i), x, y + padding * i);
            }
        }
    }

    private void drawImage(String path, int x, int y, int width, int height) throws IOException {
        // 读取要添加的图片
        BufferedImage imageToAdd = ImageIO.read(new File(path));
        graphics.drawImage(imageToAdd, x, y, width, height, null);
    }

    private void drawText(String text, int x, int y) {
        graphics.drawString(text, x, y);
    }

    private String saveImage() throws IOException {
        String path = "/tmp/" + System.currentTimeMillis() + "_degree.png";
        ImageIO.write(image, "png", new File(path));
        return path;
    }

    private String downloadImage(String url) throws IOException {
        URL imageUrl = new URL(url);
        String destination = "/tmp/" + System.currentTimeMillis() + ".png";
        try (InputStream in = imageUrl.openStream()) {
            Files.copy(in, Path.of(destination), StandardCopyOption.REPLACE_EXISTING);
        }
        return destination;
    }
}
