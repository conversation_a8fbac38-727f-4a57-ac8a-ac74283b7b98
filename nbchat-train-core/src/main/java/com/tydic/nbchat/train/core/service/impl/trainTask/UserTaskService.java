package com.tydic.nbchat.train.core.service.impl.trainTask;

import com.tydic.nbchat.admin.api.NbchatSysTenantApi;
import com.tydic.nbchat.admin.api.bo.SysTenantUserQueryRspBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryReqBO;
import com.tydic.nbchat.train.api.bo.task.NbchatTaskRecordBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskBO;
import com.tydic.nbchat.train.api.bo.train.task.UserTaskProgressBO;
import com.tydic.nbchat.train.api.trainTask.TrainTaskApi;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
public class UserTaskService {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    NbchatSysTenantApi nbchatSysTenantApi;

    private final TrainTaskApi trainTaskApi;

    public UserTaskService(TrainTaskApi trainTaskApi) {
        this.trainTaskApi = trainTaskApi;
    }

    public RspList userProgress(SysDeptUserQueryReqBO request) {
        log.info("学员学习进度查询:{}",request);
        RspList<SysTenantUserQueryRspBO> rspList = nbchatSysTenantApi.getTenantUsers(request);
        if (!rspList.isSuccess()) {
            return rspList;
        }
        List<UserTaskProgressBO> res = new ArrayList<>();
        List<SysTenantUserQueryRspBO> rows = rspList.getRows();
        for (SysTenantUserQueryRspBO row : rows) {
            UserTaskProgressBO bo = new UserTaskProgressBO();
            BeanUtils.copyProperties(row, bo);
            this.calData(bo, request.getTenantCode(), row.getUserId());
            res.add(bo);
        }
        return BaseRspUtils.createSuccessRspList(res,rspList.getCount());
    }

    public void calData(UserTaskProgressBO bo, String tenantCode, String userId){
        NbchatTrainTaskBO request = new NbchatTrainTaskBO();
        request.setTenantCode(tenantCode);
        request.setUserId(userId);
        request.setStartStatus("1");
        RspList rspList = trainTaskApi.queryTask(request);
        if (!rspList.isSuccess()) {
            log.warn("查询用户学习任务失败:{}",rspList);
            return;
        }
        List<NbchatTaskRecordBO> rows = rspList.getRows();
        long finished = rows.stream().filter(v -> v.getFinishStatus().equals("1")).count();
        bo.setFinished((int) finished);
        bo.setShouldFinished((int) rspList.getCount());
        if (bo.getShouldFinished() > 0) {
            bo.setFinishedRate(String.format("%.2f", finished * 100.0 / rspList.getCount()) + "%");
        } else {
            bo.setFinishedRate("0.00%");
        }

    }

}
