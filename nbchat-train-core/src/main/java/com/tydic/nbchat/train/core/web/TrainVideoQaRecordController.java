package com.tydic.nbchat.train.core.web;

import com.tydic.nbchat.train.api.NbchatVideoQaRecordApi;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQaRecordBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * (NbchatVideoQaRecord)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-20 16:07:20
 */
@RestController
@RequestMapping("/train/video/qa/record")
public class TrainVideoQaRecordController {
    /**
     * 服务对象
     */
    @Resource
    private NbchatVideoQaRecordApi nbchatVideoQaRecordApi;

    @PostMapping("query")
    public Rsp query(){
        return nbchatVideoQaRecordApi.query(new NbchatVideoQaRecordBO());
    }

    @PostMapping("save")
    public Rsp save(){
        return nbchatVideoQaRecordApi.save(new NbchatVideoQaRecordBO());
    }

}

