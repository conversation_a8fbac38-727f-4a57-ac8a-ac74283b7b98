package com.tydic.nbchat.train.core.service.impl;

import com.tydic.nbchat.train.api.NbchatUserTrainRecordApi;
import com.tydic.nbchat.train.api.TrainSectionVideoApi;
import com.tydic.nbchat.train.api.bo.course.TranUserCourseSaveReqBO;
import com.tydic.nbchat.train.api.bo.video.SectionVideoRspBO;
import com.tydic.nbchat.train.api.bo.video.SectionVideoStstReqBO;
import com.tydic.nbchat.train.mapper.NbchatTrainRecordMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSectionsMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSectionsVideoMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRecord;
import com.tydic.nbchat.train.mapper.po.NbchatTrainSections;
import com.tydic.nbchat.train.mapper.po.NbchatTrainSectionsVideo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class TrainSectionVideoServiceImpl implements TrainSectionVideoApi {

    @Resource
    NbchatTrainRecordMapper nbchatTrainRecordMapper;
    @Resource
    NbchatTrainSectionsMapper nbchatTrainSectionsMapper;
    @Resource
    NbchatTrainSectionsVideoMapper nbchatTrainSectionsVideoMapper;

    @Resource
    NbchatUserTrainRecordApi nbchatUserTrainRecordApi;

    @Override
    public Rsp handleVideoStst(SectionVideoStstReqBO request) {
        log.debug("处理视频统计信息：{}",request);
        //更新学习记录-收藏
        if (StringUtils.isNoneEmpty(request.getCourseId(),request.getStar())) {
            TranUserCourseSaveReqBO reqBO = new TranUserCourseSaveReqBO();
            reqBO.setCourseId(request.getCourseId());
            reqBO.setUserId(request.getUserId());
            reqBO.setStar(request.getStar());
            Rsp rsp = nbchatUserTrainRecordApi.collectCourse(reqBO);
            log.info("收藏结果：{}",rsp);
            return rsp;
        }
        NbchatTrainSectionsVideo secRecord = nbchatTrainSectionsVideoMapper.queryBySecIdUserId(request.getSectionId(), request.getUserId());
        if (ObjectUtils.isEmpty(secRecord)) {
            NbchatTrainSectionsVideo record = new NbchatTrainSectionsVideo();
            BeanUtils.copyProperties(request,record);
            //record.setIsStar(request.getStar());
            record.setIsZan(request.getLike());
            record.setViewProcess(request.getProgress());
            record.setCreateTime(new Date());
            nbchatTrainSectionsVideoMapper.insertSelective(record);
        } else {
            //secRecord.setIsStar(request.getStar());
            secRecord.setIsZan(request.getLike());
            secRecord.setViewProcess(request.getProgress());
            nbchatTrainSectionsVideoMapper.update(secRecord);
        }
        //更新章节视频指标
        NbchatTrainSections index = new NbchatTrainSections();
        index.setSectionId(request.getSectionId());
        /*if (StringUtils.isNotEmpty(request.getStar())) {
            index.setStarNum(Integer.parseInt(request.getStar()));
        }*/
        if (StringUtils.isNotEmpty(request.getLike())) {
            index.setZanNum(1);
        }
        if (ObjectUtils.isNotEmpty(request.getViewNum())) {
            index.setViewNum(1);
        }
        if (ObjectUtils.anyNotNull(index.getZanNum(),index.getViewNum())) {
            nbchatTrainSectionsMapper.updateVideoIndex(index);
        }
        return BaseRspUtils.createSuccessRsp(null);
    }

    @Override
    public Rsp queryVideoIndex(SectionVideoStstReqBO request) {
        log.info("查询视频点击指标：{}",request);
        NbchatTrainSections section = nbchatTrainSectionsMapper.selectByPrimaryKey(request.getSectionId());
        if (ObjectUtils.isEmpty(section)) {
            return BaseRspUtils.createErrorRsp("无此章节信息");
        }
        SectionVideoRspBO rsp = new SectionVideoRspBO();
        BeanUtils.copyProperties(section,rsp);

        NbchatTrainSectionsVideo sectionsVideo = nbchatTrainSectionsVideoMapper.queryBySecIdUserId(request.getSectionId(), request.getUserId());
        if (ObjectUtils.isNotEmpty(sectionsVideo)) {
            rsp.setViewProcess(sectionsVideo.getViewProcess());
            rsp.setIsZan(sectionsVideo.getIsZan());
        }

        NbchatTrainRecord trainRecord = nbchatTrainRecordMapper.selectByUserAndCourseId(request.getUserId(), request.getCourseId());
        if (ObjectUtils.isNotEmpty(trainRecord)) {
            rsp.setIsStar(trainRecord.getStar());
        }
        int countStar = nbchatTrainRecordMapper.countStar(request.getCourseId());
        rsp.setStarNum(countStar);
        return BaseRspUtils.createSuccessRsp(rsp);
    }
}
