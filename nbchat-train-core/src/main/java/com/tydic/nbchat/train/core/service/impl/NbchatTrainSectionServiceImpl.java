package com.tydic.nbchat.train.core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.tydic.nbchat.train.api.NbchatTrainSectionApi;
import com.tydic.nbchat.train.api.bo.eums.StateEnum;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogQueryReqBO;
import com.tydic.nbchat.train.api.bo.train.catalog.TrainCatalogBO;
import com.tydic.nbchat.train.api.bo.train.section.SectionInfo;
import com.tydic.nbchat.train.api.bo.train.section.SectionsSaveRequest;
import com.tydic.nbchat.train.api.bo.train.section.TrainSectionQueryRspBO;
import com.tydic.nbchat.train.mapper.*;
import com.tydic.nbchat.train.api.bo.train.section.TrainSectionsBO;
import com.tydic.nbchat.train.mapper.NbchatTrainCatalogMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSectionsMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainSectionsPageMapper;
import com.tydic.nbchat.train.mapper.po.*;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class NbchatTrainSectionServiceImpl implements NbchatTrainSectionApi {

    @Resource
    private NbchatTrainSectionsMapper nbchatTrainSectionsMapper;
    @Resource
    private NbchatTrainSectionsPageMapper nbchatTrainSectionsPageMapper;
    @Resource
    private NbchatTrainCatalogMapper nbchatTrainCatalogMapper;
    @Resource
    private NbchatTrainUserRecordMapper nbchatTrainUserRecordMapper;
    @Resource
    private NbchatTrainCourseMapper nbchatTrainCourseMapper;

    @MethodParamVerifyEnable
    @Override
    public Rsp addTrainCount(TrainCatalogQueryReqBO reqBO) {
        log.info("保存学习人数,保存学习章节记录:{}",reqBO);
        int update = nbchatTrainCatalogMapper.addTrainCount(reqBO.getCourseId(),reqBO.getCatalogId());
        NbchatTrainUserRecord record = new NbchatTrainUserRecord();
        BeanUtils.copyProperties(reqBO,record);
        record.setCreateTime(new Date());
        nbchatTrainUserRecordMapper.insertSelective(record);
        return BaseRspUtils.createSuccessRsp(update);
    }

    @Override
    public Rsp saveCatalog(TrainCatalogBO reqBO) {
        log.info("保存目录：{}",reqBO);
        NbchatTrainCatalog catalog = new NbchatTrainCatalog();
        BeanUtils.copyProperties(reqBO,catalog);
        catalog.setCreateTime(new Date());
        nbchatTrainCatalogMapper.insertSelective(catalog);
        return BaseRspUtils.createSuccessRsp("");
    }

    @MethodParamVerifyEnable
    @Override
    public RspList<TrainCatalogBO> getSectionCatalogs(TrainCatalogQueryReqBO reqBO) {
        log.info("查询章节目录:{}",reqBO);
        List<NbchatTrainCatalog> catalogs = nbchatTrainCatalogMapper.selectByCourseId(reqBO.getCourseId(),reqBO.getCatalogId(),"-1");
        for (NbchatTrainCatalog catalog : catalogs) {
            int count = nbchatTrainUserRecordMapper.queryStudied(catalog.getCatalogId(), reqBO.getUserId());
            if (count > 0) {
                catalog.setStudied(1);
            }
            if (catalog.getParentId().equals("-1")) {
                if (StringUtils.isEmpty(catalog.getSectionIds())) {
                    continue;
                }
                JSONArray array = JSONArray.parseArray(catalog.getSectionIds());
                if (CollectionUtils.isEmpty(array)) {
                    continue;
                }
                String sectionId = (String)array.get(0);
                NbchatTrainSections trainSections = nbchatTrainSectionsMapper.selectByPrimaryKey(sectionId);
                if (ObjectUtils.isEmpty(trainSections)) {
                    continue;
                }
                catalog.setSectionId(sectionId);
                catalog.setSectionVideoUrl(trainSections.getVideoUrl());
                catalog.setSectionContent(trainSections.getContent());
            }
        }
        List<TrainCatalogBO> catalogBOS = Lists.newArrayList();
        NiccCommonUtil.copyList(catalogs,catalogBOS,TrainCatalogBO.class);
        parseCatalogs(catalogBOS);
        return BaseRspUtils.createSuccessRspList(catalogBOS);
    }

    @Override
    public Rsp<TrainSectionQueryRspBO> getSectionContents(TrainCatalogQueryReqBO reqBO) {
        log.info("查询章节内容:{}",reqBO);
        TrainSectionQueryRspBO rspBO = new TrainSectionQueryRspBO();
        List<String> sectionIds = reqBO.getSectionIds();
        if( ( sectionIds == null || sectionIds.isEmpty() ) && StringUtils.isNotBlank(reqBO.getCatalogId())){
            NbchatTrainCatalog catalog = nbchatTrainCatalogMapper.selectByPrimaryKey(reqBO.getCatalogId());
            if(catalog != null && StringUtils.isNotBlank(catalog.getSectionIds())){
                sectionIds = JSONArray.parseArray(catalog.getSectionIds(),String.class);
                rspBO.setCatalogTitle(catalog.getCatalogTitle());
                rspBO.setTrainCount(catalog.getTrainCount());

            }
        }
        List<NbchatTrainSections> sections = nbchatTrainSectionsMapper.selectBySectionIds(reqBO.getCourseId(), sectionIds);
        List<SectionInfo> sectionBOS = Lists.newArrayList();
        NiccCommonUtil.copyList(sections,sectionBOS, SectionInfo.class);
        rspBO.setSectionInfos(sectionBOS);
        if (CollectionUtils.isNotEmpty(sectionBOS)) {
            rspBO.setVideoUrl(sections.get(0).getVideoUrl());
            rspBO.setVideoImg(sections.get(0).getVideoImg());
        }
        NbchatTrainCourse nbchatTrainCourse = nbchatTrainCourseMapper.selectByPrimaryKey(reqBO.getCourseId());
        if (ObjectUtils.isNotEmpty(nbchatTrainCourse)) {
            String pdfUrl = nbchatTrainCourse.getCourseFileUrl();
            rspBO.setPdfUrl(pdfUrl);
        }
        NbchatTrainSectionsPage query = new NbchatTrainSectionsPage();
        query.setSectionIds(sectionIds);
        query.setCourseId(reqBO.getCourseId());
        int pageNum = nbchatTrainSectionsPageMapper.selectSectionMinPageNum(query);
        rspBO.setPdfNum(pageNum);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    public Rsp saveSection(SectionsSaveRequest reqBO) {
        log.info("保存章节：{}",reqBO);
        NbchatTrainSections section = new NbchatTrainSections();
        BeanUtils.copyProperties(reqBO,section);
        if (StringUtils.isEmpty(reqBO.getSectionId())) {
            section.setSectionId(IdWorker.nextAutoIdStr());
            section.setCreateTime(new Date());
            nbchatTrainSectionsMapper.insertSelective(section);
        } else {
            nbchatTrainSectionsMapper.updateByPrimaryKeySelective(section);
        }
        //更新课程目录
        if (StringUtils.isNotEmpty(reqBO.getCatalogId())) {
            NbchatTrainCatalog catalog = nbchatTrainCatalogMapper.selectByPrimaryKey(reqBO.getCatalogId());
            String sectionIds = catalog.getSectionIds();
            if (StringUtils.isEmpty(sectionIds)) {
                sectionIds = JSON.toJSONString(Collections.singletonList(section.getSectionId()));
            } else {
                JSONArray array = JSONObject.parseArray(sectionIds);
                array.add(section.getSectionId());
                HashSet hs = new HashSet(array);
                sectionIds = JSON.toJSONString(hs);
            }
            NbchatTrainCatalog record = new NbchatTrainCatalog();
            record.setCatalogId(reqBO.getCatalogId());
            record.setSectionIds(sectionIds);
            nbchatTrainCatalogMapper.updateByPrimaryKeySelective(record);
        }
        //更新课程创建进度
        nbchatTrainCourseMapper.updateStepState(reqBO.getCourseId(), StateEnum.STEP.CONTENT.getCode());
        //更新课程课时
        nbchatTrainCourseMapper.updateClassHour(reqBO.getCourseId());
        return BaseRspUtils.createSuccessRsp(section.getSectionId());
    }

    @Override
    public Rsp<String> querySection(String sectionId) {
        log.info("查询章节：{}",sectionId);
        NbchatTrainSections section = nbchatTrainSectionsMapper.selectByPrimaryKey(sectionId);
        return BaseRspUtils.createSuccessRsp(section.getContent());
    }

    @Override
    public RspList<TrainSectionsBO> queryCourse(String courseId) {
        log.info("查询课程下章节内容：{}",courseId);
        List<TrainSectionsBO> res = Lists.newArrayList();
        List<NbchatTrainSections> nbchatTrainSections = nbchatTrainSectionsMapper.selectBySectionIds(courseId, null);
        NiccCommonUtil.copyList(nbchatTrainSections,res,TrainSectionsBO.class);
        return BaseRspUtils.createSuccessRspList(res);
    }

    private void parseCatalogs(List<TrainCatalogBO> catalogs){
        for (TrainCatalogBO catalog : catalogs) {
            List<NbchatTrainCatalog> list = nbchatTrainCatalogMapper.selectByCourseId(catalog.getCourseId(),"",catalog.getCatalogId());
            if(!list.isEmpty()){
                List<TrainCatalogBO> tempList = Lists.newArrayList();
                NiccCommonUtil.copyList(list,tempList,TrainCatalogBO.class);
                catalog.setChildren(tempList);
                parseCatalogs(catalog.getChildren());
            }
        }
    }

    @Override
    public Rsp delete(String courseId) {
        log.info("删除章节：{}",courseId);
        int update = nbchatTrainSectionsMapper.update(courseId);
        return BaseRspUtils.createSuccessRsp(update);
    }
}
