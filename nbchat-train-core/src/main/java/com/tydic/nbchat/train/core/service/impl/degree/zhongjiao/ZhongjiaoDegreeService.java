package com.tydic.nbchat.train.core.service.impl.degree.zhongjiao;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.train.api.bo.eums.ProjectType;
import com.tydic.nbchat.train.api.bo.train.task.ImageBuilder;
import com.tydic.nbchat.train.api.bo.train.task.ImageInfo;
import com.tydic.nbchat.train.api.bo.train.task.TextBuilder;
import com.tydic.nbchat.train.api.bo.train.task.TextInfo;
import com.tydic.nbchat.train.core.config.NbchatTrainConfigProperties;
import com.tydic.nbchat.train.core.service.impl.degree.DegreeApi;
import com.tydic.nbchat.train.core.util.QRCodeGenerator;
import com.tydic.nbchat.train.mapper.NameMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainTaskDegreeMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainTaskDegree;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class ZhongjiaoDegreeService implements DegreeApi {

    @Resource
    NbchatTrainTaskDegreeMapper nbchatTrainTaskDegreeMapper;
    @Resource
    NameMapper nameMapper;

    private final NbchatTrainConfigProperties nbchatTrainConfigProperties;

    public ZhongjiaoDegreeService(NbchatTrainConfigProperties nbchatTrainConfigProperties) {
        this.nbchatTrainConfigProperties = nbchatTrainConfigProperties;
    }

    @Override
    public String projectType() {
        return ProjectType.Zhongjiao.getCode();
    }

    @Override
    public String createDegree(String degreeId) throws Exception {
        NbchatTrainTaskDegree taskDegree = nbchatTrainTaskDegreeMapper.queryById(degreeId);
        log.info("证书信息：{}", taskDegree);
        JSONObject userInfo = nameMapper.queryUserInfo(taskDegree.getUserId(), taskDegree.getTenantCode());
        if (isAnyEmpty(userInfo)) {
            log.error("制作证书-用户信息非法，请检查：{}", userInfo);
            return "";
        }
        String ancestors = nameMapper.queryDeptAncestors(taskDegree.getDeptId());
        log.info("用户所属机构祖先：{}", ancestors);
        if (StringUtils.isEmpty(ancestors)) {
            log.error("用户所属机构祖先为空");
            return "";
        }
        //添加用户信息
        List<String> texts = new ArrayList<>();
        texts.add(userInfo.getString("user_reality_name"));
        texts.add(userInfo.getString("gender"));
        texts.add(userInfo.getString("birthday"));
        texts.add(userInfo.getString("id_card"));
        texts.add(degreeId);
        String group = "";
        String postName = "";
        List<String> userPostIds = nameMapper.queryPostId(taskDegree.getUserId(), taskDegree.getTenantCode());
        String postIds = taskDegree.getPostId();
        if (StringUtils.isEmpty(postIds)) {
            postName = nameMapper.queryPostNameList(userPostIds);
        } else {
            List<String> pids = new ArrayList<>();
            for (String pid : userPostIds) {
                if (postIds.contains(pid)) {
                    pids.add(pid);
                }
            }
            if (CollectionUtils.isNotEmpty(pids)) {
                postName = nameMapper.queryPostNameList(pids);
            }
        }

        String[] arr = ancestors.split(",");
        texts.add(arr.length >= 2 ? nameMapper.queryDeptName(arr[1]) : "");
        texts.add(arr.length >= 3 ? nameMapper.queryDeptName(arr[2]) : "");
        if (arr.length >= 4) {
            group = Optional.ofNullable(nameMapper.queryDeptName(arr[3])).orElse("");
            postName = Optional.ofNullable(postName).orElse(" ");
        }
        texts.add(group + "(" + (StringUtils.isEmpty(postName) ? "" : postName) + ")");


        //添加领证日期、分数、等级
        List<TextInfo> textInfos = new ArrayList<>();
        LocalDate localDate = taskDegree.getIssueDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        textInfos.add(TextBuilder.year(String.valueOf(localDate.getYear())));
        textInfos.add(TextBuilder.month(String.valueOf(localDate.getMonthValue())));
        textInfos.add(TextBuilder.day(String.valueOf(localDate.getDayOfMonth())));
        Integer score = nameMapper.queryUserScore(taskDegree.getUserId(), taskDegree.getTaskId());
        textInfos.add(TextBuilder.score(score + "分"));
        textInfos.add(TextBuilder.level(getScoreLevel(score)));

        //添加头像、二维码
        String urlPrefix = nbchatTrainConfigProperties.getDegreeTaskUrlPrefix();
        urlPrefix = urlPrefix.replace("{TASKID}", taskDegree.getTaskId()).replace("{USERID}", taskDegree.getUserId());
        String qrImgPath = QRCodeGenerator.generateQRCodeImage(urlPrefix);
        List<ImageInfo> imageInfos = new ArrayList<>();
        imageInfos.add(ImageBuilder.avatar(userInfo.getString("avatar")));
        imageInfos.add(ImageBuilder.qrCode(qrImgPath));

        if (taskDegree.getValidityPeriod() >= 99) {
            localDate = localDate.withYear(2099).withMonth(1).withDayOfYear(1);
        } else {
            localDate = localDate.plusMonths(taskDegree.getValidityPeriod());
        }
        String issueDate = localDate.getYear() + "年" + localDate.getMonthValue() + "月" + localDate.getDayOfMonth() + "日";
        String imgPath = new ImageProcessor().texts(texts).textInfos(textInfos).imageInfos(imageInfos).issueDate(issueDate).title(taskDegree.getDegreeName()).build();
        log.info("生成图片成功，图片路径：{}", imgPath);
        return imgPath;
    }

    public static String getScoreLevel(Integer score) {
        if (score >= 90) {
            return "优秀";
        } else if (score >= 70) {
            return "良好";
        } else if (score >= 60) {
            return "合格";
        } else {
            return "不合格";
        }
    }

    public static boolean isAnyEmpty(JSONObject userInfo) {
        return StringUtils.isAnyEmpty(
                userInfo.getString("user_reality_name"),
                userInfo.getString("gender"),
                userInfo.getString("birthday"),
                userInfo.getString("id_card"),
                userInfo.getString("avatar"));
    }


}
