{"info": {"_postman_id": "12345678-1234-1234-1234-123456789abc", "name": "首页模板管理接口", "description": "首页模板管理相关的管理员接口，包括查询、新增、更新、删除和排序功能", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "管理员查询首页模板列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('rspCode');", "    pm.expect(jsonData).to.have.property('rspDesc');", "    pm.expect(jsonData).to.have.property('rows');", "    pm.expect(jsonData).to.have.property('count');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}", "description": "管理员权限token"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 1,\n  \"limit\": 10,\n  \"tplName\": \"\",\n  \"tplType\": \"\",\n  \"status\": \"\",\n  \"isValid\": \"\",\n  \"categoryCode\": \"\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/templates", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "templates"]}, "description": "管理员查询首页模板列表，支持分页和条件筛选。需要系统管理员或租户管理员权限。"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 1,\n  \"limit\": 10\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/templates", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "templates"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"业务处理成功!\",\n  \"rows\": [\n    {\n      \"tplId\": \"123456789\",\n      \"tenantCode\": \"tenant001\",\n      \"tplName\": \"示例PPT模板\",\n      \"tplDesc\": \"这是一个示例PPT模板\",\n      \"tplType\": \"ppt\",\n      \"categoryCode\": \"cat001\",\n      \"tplSize\": \"16:9\",\n      \"orderIndex\": 1,\n      \"status\": \"1\",\n      \"isValid\": \"1\",\n      \"createTime\": \"2024-01-01T10:00:00\"\n    }\n  ],\n  \"count\": 1\n}"}]}, {"name": "新增首页模板", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Template created successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.rspCode).to.eql('0000');", "    pm.expect(jsonData.data).to.not.be.null;", "    // 保存模板ID用于后续测试", "    if (jsonData.data) {", "        pm.environment.set('templateId', jsonData.data);", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}", "description": "管理员权限token"}], "body": {"mode": "raw", "raw": "{\n  \"tplName\": \"新PPT模板\",\n  \"tplDesc\": \"这是一个新创建的PPT模板\",\n  \"tplType\": \"ppt\",\n  \"tplConfig\": \"{\\\"theme\\\": \\\"default\\\", \\\"layout\\\": \\\"standard\\\"}\",\n  \"tplContent\": \"模板内容JSON数据\",\n  \"categoryCode\": \"cat001\",\n  \"tplSize\": \"16:9\",\n  \"status\": \"0\",\n  \"orderIndex\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/add", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "add"]}, "description": "新增首页模板，需要管理员权限。tplName和tplType为必填字段。"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tplName\": \"新PPT模板\",\n  \"tplType\": \"ppt\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/add", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "add"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"新增模板成功\",\n  \"data\": \"123456789\"\n}"}, {"name": "参数错误示例", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tplDesc\": \"缺少必填字段\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/add", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "add"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"rspCode\": \"9999\",\n  \"rspDesc\": \"模板名称不能为空\",\n  \"data\": null\n}"}]}, {"name": "删除首页模板", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Template deleted successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.rspCode).to.eql('0000');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}", "description": "管理员权限token"}], "body": {"mode": "raw", "raw": "{\n  \"tplId\": \"{{templateId}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/delete", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "delete"]}, "description": "删除首页模板（逻辑删除），需要管理员权限。"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tplId\": \"123456789\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/delete", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "delete"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"删除模板成功\",\n  \"data\": \"123456789\"\n}"}]}, {"name": "首页模板排序", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Templates sorted successfully\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.rspCode).to.eql('0000');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{token}}", "description": "管理员权限token"}], "body": {"mode": "raw", "raw": "{\n  \"tplIds\": [\"123456789\", \"987654321\", \"456789123\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/sort", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "sort"]}, "description": "首页模板排序，根据传入的模板ID列表顺序更新排序字段。需要管理员权限。"}, "response": [{"name": "成功响应示例", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"tplIds\": [\"123456789\", \"987654321\", \"456789123\"]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/train/tdh/index/admin/template/sort", "host": ["{{baseUrl}}"], "path": ["train", "tdh", "index", "admin", "template", "sort"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"rspCode\": \"0000\",\n  \"rspDesc\": \"模板排序成功\",\n  \"data\": 3\n}"}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 可以在这里添加全局的预请求脚本", "console.log('执行首页模板管理接口请求');"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局测试脚本", "pm.test(\"Response time is less than 5000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string", "description": "API服务器基础URL"}, {"key": "token", "value": "", "type": "string", "description": "管理员认证token"}, {"key": "templateId", "value": "", "type": "string", "description": "模板ID，用于测试更新和删除接口"}]}