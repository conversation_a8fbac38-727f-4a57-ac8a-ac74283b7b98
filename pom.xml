<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.tydic.nicc</groupId>
        <artifactId>nicc-common</artifactId>
        <version>1.6.6</version>
    </parent>

    <groupId>com.tydic.nbchat</groupId>
    <artifactId>nbchat-robot</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>nbchat-robot</name>
    <description>机器人中心</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <revision>1.7.6</revision>
        <pdfbox.version>2.0.27</pdfbox.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.tydic.nicc.dc</groupId>
                <artifactId>dc-boot-starter</artifactId>
                <version>${nicc-boot-starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.lettuce</groupId>
                        <artifactId>lettuce-core</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>kryo</artifactId>
                        <groupId>com.esotericsoftware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>aws-java-sdk-s3</artifactId>
                        <groupId>com.amazonaws</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>cos_api</artifactId>
                        <groupId>com.qcloud</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.huaweicloud</groupId>
                        <artifactId>esdk-obs-java-bundle</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.tydic.nicc</groupId>
                <artifactId>nicc-common-tools</artifactId>
                <version>${nicc-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-robot-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-admin-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-robot-mapper</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.tydic.nbchat</groupId>
                <artifactId>nbchat-robot-core</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox-tools</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.3.0</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
                <inherited>true</inherited>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire.version}</version>
                <configuration>
                    <!--默认关掉单元测试 -->
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>http://**************:8081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://**************:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <modules>
        <module>nbchat-robot-api</module>
        <module>nbchat-robot-service</module>
        <module>nbchat-robot-core</module>
        <module>nbchat-robot-mapper</module>
    </modules>

</project>