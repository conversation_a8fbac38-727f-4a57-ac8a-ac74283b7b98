package com.tydic.nbchat.admin.report.service.impl;

import com.tydic.nbchat.admin.api.bo.rp.*;
import com.tydic.nbchat.admin.api.rp.UserRpApi;
import com.tydic.nbchat.admin.mapper.*;
import com.tydic.nbchat.admin.mapper.po.*;
import com.tydic.nicc.common.nbchat.emus.UserRpEventType;
import com.tydic.nicc.common.nbchat.msg.RebateInviteMsgContext;
import com.tydic.nicc.common.nbchat.msg.UserRpEventContext;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.tydic.nbchat.admin.api.bo.constants.RedisConstants.USER_RP_BY_OPERATE_HASH_KEY_PREFIX;
import static com.tydic.nbchat.admin.api.bo.constants.RedisConstants.USER_RP_BY_OPERATE_REDIS_KEY_PREFIX;
import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_REBATE_INVITE_TOPIC;
import static com.tydic.nicc.common.nbchat.emus.RebateInviteEnum.INVITE_REFUND;

/**
 * <AUTHOR>
 * @date 2025/3/21 17:12
 * @description 用户维度报表数据记录
 */
@Slf4j
@Service
public class UserRpServiceImpl implements UserRpApi {

    @Value("${rebate.enable:true}")
    private boolean rebateEnable;

    @Resource
    private RedisHelper redisHelper;
    @Resource
    private KKMqProducerHelper kkMqProducerHelper;
    @Resource
    private NbchatUserVipMapper nbchatUserVipMapper;
    @Resource
    private RpUserTdhInfoMapper rpUserTdhInfoMapper;
    @Resource
    private OpRpUserDetailMapper opRpUserDetailMapper;
    @Resource
    private RpUserExamInfoMapper rpUserExamInfoMapper;
    @Resource
    private RpHourVideoCountMapper rpHourVideoCountMapper;
    @Resource
    private RpUserPaymentInfoMapper rpUserPaymentInfoMapper;
    @Resource
    private RpUserActivityInfoMapper rpUserActivityInfoMapper;
    @Resource
    private RpUserPptVideoInfoMapper rpUserPptVideoInfoMapper;
    @Resource
    private RpUserPromotionInfoMapper rpUserPromotionInfoMapper;
    @Resource
    private RpUserPayInvoiceInfoMapper rpUserPayInvoiceInfoMapper;

    /**
     * 根据给定的请求参数查询用户维度报表详细信息。
     *
     * @param param 查询请求参数，包含租户和用户的标识信息
     * @return 包含用户详细信息的响应对象，其中封装了用户的基本信息、支付信息、会员信息以及平台使用情况等
     */
    @Override
    public Rsp<UserRpDetailBO> info(TenantUserReqBO param) {
        log.info("根据给定的请求参数查询用户维度报表详细信息: {}", param);
        // 查询用户基础信息表
        UserRpDetailPO userRpDetail = opRpUserDetailMapper.selectRpByTenantCodeAndUserId(param.getTenantCode(), param.getUserId());
        // 如果用户基础信息表中不存在数据，则无需继续查下去了
        if (userRpDetail == null) {
            return BaseRspUtils.createErrorRsp("指定租户下找不到用户数据");
        }
        UserRpDetailBO result = new UserRpDetailBO();
        BeanUtils.copyProperties(userRpDetail, result);

        // 查询用户会员信息
        List<NbchatUserVip> userVips = nbchatUserVipMapper.selectRpByUserId(param.getTenantCode(), param.getUserId());
        if (CollectionUtils.isNotEmpty(userVips)) {
            // 用户存在会员数据
            List<UserRpVipBO> list = new ArrayList<>();
            NiccCommonUtil.copyList(userVips, list, UserRpVipBO.class);
            result.setUserRpVipList(list);
        }

        // 查询数字人付费信息
        List<RpUserTdhInfo> userTdhInfos = rpUserTdhInfoMapper.selectRpByUserId(param.getTenantCode(), param.getUserId());
        if (CollectionUtils.isNotEmpty(userTdhInfos)) {
            // 用户存在数据人付费数据
            List<UserRpTdhBO> list = new ArrayList<>();
            NiccCommonUtil.copyList(userTdhInfos, list, UserRpTdhBO.class);
            result.setUserRpTdhList(list);
        }

        return BaseRspUtils.createSuccessRsp(result);
    }

    /**
     * 处理用户维度报表事件。
     *
     * @param context 用户维度报表事件上下文，包含事件处理所需的信息
     */
    @Override
    @Transactional
    public void handleUserRpEvent(UserRpEventContext context) {
        log.info("用户维度报表: {}", context);
        // 解析事件类型
        if (StringUtils.isBlank(context.getEventType())) {
            log.warn("事件上下文中，事件类型字段异常，无法解析");
            return;
        }
        if (StringUtils.isAnyBlank(context.getTenantCode(), context.getUserId())) {
            log.warn("事件上下文中，不存在租户CODE或者用户ID，无法解析");
            return;
        }
        // 根据不同的事件类型区分
        UserRpEventType userRpEventType = UserRpEventType.getEnumByType(context.getEventType());
        if (userRpEventType == null) {
            log.warn("用户维度报表类型【{}】未收录到枚举中", context.getEventType());
            throw new RuntimeException(String.format("用户维度报表类型【%s】未收录到枚举中", context.getEventType()));
        }
        switch (userRpEventType) {
            case PPT_DOWNLOAD: // ppt下载
            case VIDEO_DOWNLOAD: // 视频下载
            case EXAM_MAKE: // 出题总数
            case EXAM_GENE: // 试题生成
            case EXAM_EXPORT: // 试题导出
            case ASSIST: // 助力信息
            case VIDEO_TASK: // 视频制作任务
                log.info("{}-报表数据为定时任务刷新", userRpEventType.getName());
                break;
            case PAY_INVOICE: // 发票数据
                analysisPayInvoice(context);
                break;
            case PAY_INFO: // 支付信息
                analysisPayInfo(context);
                break;
            case SCORE_CONSUME: // 算力点消费
                analysisScoreConsume(context);
                break;
            case TDH_CUST: // 数字人定制
                analysisTdhCust(context);
                break;
            case PPT_MAKE: // ppt制作
                analysisPptMake(context);
                break;
            case VIDEO_MAKE: // 视频制作
                analysisVideoMake(context);
                break;
        }
    }

    /**
     * 在Redis中为用户维度报表指定的事件类型进行自增操作
     *
     * @param type       事件类型
     * @param tenantCode 租户代码
     * @param userId     用户ID
     */
    @Override
    public void rpAutoIncrement(String type, String tenantCode, String userId) {
        log.info("用户维度报表统计-redis自增: type-{} tenantCode-{} userId-{}", type, tenantCode, userId);
        String redisKey = USER_RP_BY_OPERATE_REDIS_KEY_PREFIX.replace("{type}", type);
        String hashKey = USER_RP_BY_OPERATE_HASH_KEY_PREFIX.replace("{tenantCode}", tenantCode).replace("{userId}", userId);
        redisHelper.hincr(redisKey, hashKey, 1);
    }

    @Override
    @Async(value = "manualUpdateTaskExecutor")
    public void manualUpdateRpUserPromotionInfo() {
        rpUserPromotionInfoMapper.deleteAll();
        int num = rpUserPromotionInfoMapper.insertForAll();
        log.info("用户维度-用户推广裂变信息表-数据新增，新增量：{}", num);
    }

    @Override
    @Async(value = "manualUpdateTaskExecutor")
    public void manualUpdateRpUserPaymentInfo(List<RpUserPaymentInfoBO> list) {
        log.info("用户维度-付费基本信息-数据更新，开始：{}", list.size());
        List<RpUserPaymentInfo> counts = new ArrayList<>();
        NiccCommonUtil.copyList(list, counts, RpUserPaymentInfo.class);
        List<RpUserPaymentInfo> insertList = new ArrayList<>();
        List<RpUserPaymentInfo> updateList = new ArrayList<>();
        counts.forEach(item -> {
            RpUserPaymentInfo count = rpUserPaymentInfoMapper.findOneByTenantCodeAndUserId(item.getTenantCode(), item.getUserId());
            if (count == null) {
                insertList.add(item);
            } else {
                item.setId(count.getId());
                updateList.add(item);
            }
        });
        insertList.forEach(record -> {
            if (StringUtils.isAnyBlank(record.getTenantCode(), record.getUserId())) {
                log.info("数据异常，{}", record);
                return;
            }
            rpUserPaymentInfoMapper.insertSelective(record);
        });
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.forEach(record -> {
                if (StringUtils.isAnyBlank(record.getTenantCode(), record.getUserId())) {
                    log.info("数据异常，{}", record);
                    return;
                }
                rpUserPaymentInfoMapper.updateByPrimaryKeySelective(record);
            });
        }
        log.info("用户维度-付费基本信息-数据更新，结束");
    }

    @Override
    @Async(value = "manualUpdateTaskExecutor")
    public void manualUpdateRpUserTdhInfo() {
        rpUserTdhInfoMapper.deleteAll();
        int num = rpUserTdhInfoMapper.insertForAll();
        log.info("用户维度-数字人付费信息-数据新增，新增量：{}", num);
    }

    @Override
    @Async(value = "manualUpdateTaskExecutor")
    public void manualUpdateRpUserPayInvoiceInfo() {
        rpUserPayInvoiceInfoMapper.deleteAll();
        int num = rpUserPayInvoiceInfoMapper.insertForAll();
        log.info("用户维度-开票信息-数据新增，新增量：{}", num);
    }

    @Override
    @Async(value = "manualUpdateTaskExecutor")
    public void manualUpdateRpUserActivityInfo() {
        rpUserActivityInfoMapper.deleteAll();
        int num = rpUserActivityInfoMapper.insertForAll();
        log.info("用户维度-活跃信息-数据新增，新增量：{}", num);
    }

    @Override
    @Async(value = "manualUpdateTaskExecutor")
    public void manualUpdateRpPptVideoInfo(List<RpUserPptVideoInfoBO> list) {
        log.info("用户维度-ppt/视频制作信息-数据更新，开始：{}", list.size());
        List<RpUserPptVideoInfo> counts = new ArrayList<>();
        NiccCommonUtil.copyList(list, counts, RpUserPptVideoInfo.class);
        List<RpUserPptVideoInfo> insertList = new ArrayList<>();
        List<RpUserPptVideoInfo> updateList = new ArrayList<>();
        counts.forEach(item -> {
            RpUserPptVideoInfo count = rpUserPptVideoInfoMapper.findOneByTenantCodeAndUserId(item.getTenantCode(), item.getUserId());
            if (count == null) {
                insertList.add(item);
            } else {
                item.setId(count.getId());
                updateList.add(item);
            }
        });
        insertList.forEach(record1 -> {
            if (StringUtils.isAnyBlank(record1.getTenantCode(), record1.getUserId())) {
                log.info("数据异常，{}", record1);
                return;
            }
            rpUserPptVideoInfoMapper.insertSelective(record1);
        });
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.forEach(record -> {
                if (StringUtils.isAnyBlank(record.getTenantCode(), record.getUserId())) {
                    log.info("数据异常，{}", record);
                    return;
                }
                rpUserPptVideoInfoMapper.updateSelective(record);
            });
        }
        log.info("用户维度-ppt/视频制作信息-数据更新，结束");
    }

    @Override
    @Async(value = "manualUpdateTaskExecutor")
    public void manualUpdateRpUserExamInfo() {
        rpUserExamInfoMapper.deleteAll();
        int num = rpUserExamInfoMapper.insertForAll();
        log.info("用户维度-试题制作信息-数据新增，新增量：{}", num);
    }

    @Override
    @Async(value = "manualUpdateTaskExecutor")
    public void manualUpdateRpHour(List<RpHourVideoCountBO> list) {
        log.info("刷新小时数据，开始：{}", list.size());
        List<RpHourVideoCount> counts = new ArrayList<>();
        NiccCommonUtil.copyList(list, counts, RpHourVideoCount.class);
        List<RpHourVideoCount> insertList = new ArrayList<>();
        List<RpHourVideoCount> updateList = new ArrayList<>();
        counts.forEach(item -> {
            RpHourVideoCount count = rpHourVideoCountMapper.selectOneByTenantCodeAndUserIdAndDayDataAndHourData(item.getTenantCode(), item.getUserId(), item.getDayData(), item.getHourData());
            if (count == null) {
                insertList.add(item);
            } else {
                item.setId(count.getId());
                updateList.add(item);
            }
        });
        insertList.forEach(record -> {
            if (StringUtils.isAnyBlank(record.getTenantCode(), record.getUserId())) {
                log.info("数据异常，{}", record);
                return;
            }
            rpHourVideoCountMapper.insertSelective(record);
        });
        if (CollectionUtils.isNotEmpty(updateList)) {
            rpHourVideoCountMapper.updateBatchSelective(updateList);
        }
        log.info("刷新小时数据，结束");
    }

    /**
     * 根据提供的上下文分析和处理付款发票事件。
     *
     * @param context UserRpEventContext包含发票处理所需的参数
     */
    private void analysisPayInvoice(UserRpEventContext context) {
        // 租户ID
        String tenantCode = context.getTenantCode();
        // 用户ID
        String userId = context.getUserId();
        // 发票金额: 分
        Integer invoiceAmount = (Integer) context.getExtParams().get("invoiceAmount");
        // 发票抬头
        String invoiceTitle = (String) context.getExtParams().get("invoiceTitle");
        // 发票状态 0-待开具 1-开票中 2-已开票 3-作废
        String invoiceStatus = (String) context.getExtParams().get("invoiceStatus");

        if ("2".equals(invoiceStatus)) {
            // 已开票状态无需操作
            return;
        }

        if ("1".equals(invoiceStatus)) {
            // 开票中状态需要将发票金额累加
            RpUserPayInvoiceInfo payInvoiceInfo = new RpUserPayInvoiceInfo();
            payInvoiceInfo.setTenantCode(tenantCode);
            payInvoiceInfo.setUserId(userId);
            payInvoiceInfo.setTotalAmount(invoiceAmount);
            rpUserPayInvoiceInfoMapper.updateBySelective(payInvoiceInfo);
            return;
        }

        if ("3".equals(invoiceStatus)) {
            // 作废状态需要将废弃的金额减回去
            RpUserPayInvoiceInfo payInvoiceInfo = new RpUserPayInvoiceInfo();
            payInvoiceInfo.setTenantCode(tenantCode);
            payInvoiceInfo.setUserId(userId);
            payInvoiceInfo.setTotalAmount(-invoiceAmount);
            rpUserPayInvoiceInfoMapper.updateBySelective(payInvoiceInfo);
            return;
        }

        // 待开具状态需要对launch_times字段自增，当是第一次申请时需要将发票抬头记录
        RpUserPayInvoiceInfo payInvoiceInfo = rpUserPayInvoiceInfoMapper.findOneByTenantCodeAndUserId(tenantCode, userId);
        if (payInvoiceInfo == null) {
            // 第一次申请开票
            payInvoiceInfo = new RpUserPayInvoiceInfo();
            payInvoiceInfo.setTenantCode(tenantCode);
            payInvoiceInfo.setUserId(userId);
            payInvoiceInfo.setLaunchTimes(1);
            payInvoiceInfo.setTotalAmount(invoiceAmount);
            payInvoiceInfo.setInvoiceTitle(invoiceTitle);
            rpUserPayInvoiceInfoMapper.insertSelective(payInvoiceInfo);
            return;
        }

        // 非第一次开票
        payInvoiceInfo = new RpUserPayInvoiceInfo();
        payInvoiceInfo.setTenantCode(tenantCode);
        payInvoiceInfo.setUserId(userId);
        payInvoiceInfo.setLaunchTimes(1);
        payInvoiceInfo.setInvoiceTitle(invoiceTitle);
        rpUserPayInvoiceInfoMapper.updateBySelective(payInvoiceInfo);
    }

    /**
     * 根据提供的上下文分析和处理支付信息事件。
     *
     * @param context UserRpEventContext包含支付信息处理所需的参数
     */
    private void analysisPayInfo(UserRpEventContext context) {
        // 租户ID
        String tenantCode = context.getTenantCode();
        // 用户ID
        String userId = context.getUserId();
        // 事件时间
        Date eventTime = context.getEventTime();
        // 订单编号
        String orderNo = (String) context.getExtParams().get("orderNo");
        // 是否为退款事件 0-否 1-是
        String isRefund = (String) context.getExtParams().get("isRefund");
        // 支付类型 0-加油包 1-体验会员 2-高级会员 3-专业会员
        String payType = (String) context.getExtParams().get("payType");
        // 支付金额，单位：分
        Integer payAmount = (Integer) context.getExtParams().getOrDefault("payAmount", 0);
        // 算力点数量
        Integer scoreNum = (Integer) context.getExtParams().getOrDefault("scoreNum", 0);

        // 查询用户是否有历史数据
        RpUserPaymentInfo info = rpUserPaymentInfoMapper.findOneByTenantCodeAndUserId(tenantCode, userId);
        if (info == null) {
            initPaymentInfo(tenantCode, userId, null);
            return;
        }

        RpUserPaymentInfo paymentInfo = new RpUserPaymentInfo();
        paymentInfo.setTenantCode(tenantCode);
        paymentInfo.setUserId(userId);
        if (payAmount > 0 && "0".equals(info.getIsSubscriber())) {
            paymentInfo.setIsSubscriber("1");
        }
        if ("1".equals(isRefund)) {
            if ("0".equals(info.getIsRefund())) {
                paymentInfo.setIsRefund("1");
            }
            paymentInfo.setTotalRefundAmount(-payAmount);

            // 如果要请拉新开关开启，发送邀请拉新消息
            if (rebateEnable) {
                // 发送邀请拉新消息
                RebateInviteMsgContext rebateInviteMsgContext = new RebateInviteMsgContext();
                rebateInviteMsgContext.setEventType(INVITE_REFUND.getCode());
                rebateInviteMsgContext.setEventTime(new Date());
                rebateInviteMsgContext.setTenantCode(context.getTenantCode());
                rebateInviteMsgContext.setUserId(context.getUserId());
                Map<String, Object> extParams = new HashMap<>();
                extParams.put("orderNo", orderNo);
                rebateInviteMsgContext.setExtParams(extParams);
                log.info("发送邀请用户消费事件消息: {}", context);
                kkMqProducerHelper.sendMsg(NBCHAT_REBATE_INVITE_TOPIC, rebateInviteMsgContext);
            }
        } else {
            paymentInfo.setIsRefund("0");
            paymentInfo.setTotalPayAmount(payAmount);
        }
        paymentInfo.setLastPayTime(eventTime);
        if ("0".equals(payType)) {
            // 算力点相关
            if ("0".equals(isRefund)) {
                // 本次事件不是退款事件
                paymentInfo.setScoreRechargeTotal(scoreNum);
                paymentInfo.setScoreTotalNum(scoreNum);
                paymentInfo.setScoreBalance(scoreNum);
            } else {
                // 本次事件是退款事件
                paymentInfo.setScoreRechargeTotal(-scoreNum);
                paymentInfo.setScoreTotalNum(-scoreNum);
                paymentInfo.setScoreBalance(-scoreNum);
            }
            paymentInfo.setScoreTotalAmount(payAmount);
            analysisScore(scoreNum, paymentInfo, payAmount);
        } else {
            // 会员相关
            if ("0".equals(isRefund)) {
                // 本次事件不是退款事件
                paymentInfo.setScoreRechargeTotal(scoreNum);
                paymentInfo.setScoreBalance(scoreNum);
            } else {
                // 本次事件是退款事件
                paymentInfo.setScoreRechargeTotal(-scoreNum);
                paymentInfo.setScoreBalance(-scoreNum);
            }
        }
        rpUserPaymentInfoMapper.updateForStat(paymentInfo);
    }

    /**
     * 根据提供的事件上下文分析和处理分数消耗。此方法更新或初始化用户的支付信息，相应地调整总消费分数和余额。它还通过减少消耗的分数来处理退款情况。
     *
     * @param context UserRpEventContext包含操作所需的参数，包括tenantCode、userId、isRefund（作为扩展参数）和scoreNum（作为扩展变量）。
     */
    private void analysisScoreConsume(UserRpEventContext context) {
        // 租户ID
        String tenantCode = context.getTenantCode();
        // 用户ID
        String userId = context.getUserId();
        // 是否为退款事件 0-否 1-是
        String isRefund = (String) context.getExtParams().get("isRefund");
        // 是否为算力点失效事件 0-否 1-是
        String isLose = (String) context.getExtParams().get("isLose");
        // 支付类型 0-个人账户 1-企业账户
        String payType = (String) context.getExtParams().get("payType");
        // 算力点数量
        Integer scoreNum = (Integer) context.getExtParams().get("scoreNum");

        // 查询用户是否有历史数据
        RpUserPaymentInfo info = rpUserPaymentInfoMapper.findOneByTenantCodeAndUserId(tenantCode, userId);
        if (info == null) {
            initPaymentInfo(tenantCode, userId, null);
            return;
        }

        RpUserPaymentInfo paymentInfo = new RpUserPaymentInfo();
        paymentInfo.setTenantCode(tenantCode);
        paymentInfo.setUserId(userId);
        if ("0".equals(isRefund)) {
            if ("1".equals(isLose)) {
                // 本次事件为算力点失效事件
                paymentInfo.setScoreLoseTotal(scoreNum);
            } else {
                // 本次事件为算力点扣除事件
                if ("0".equals(payType)) {
                    if (info.getScoreBalance() < scoreNum) {
                        // 个人账户中的算力点数据小于扣除的算力点，有可能有异常，刷新算力点数据
                        initPaymentInfo(tenantCode, userId, info.getId());
                        return;
                    }
                    // 个人账户消费
                    paymentInfo.setScoreConsumeTotalPerson(scoreNum);
                } else {
                    // 企业账户消费
                    paymentInfo.setScoreConsumeTotal(scoreNum);
                }
            }
        } else {
            // 本次事件为算力点扣除失败，退款事件
            if ("0".equals(payType)) {
                // 个人账户消费退款
                paymentInfo.setScoreConsumeTotalPerson(-scoreNum);
            } else {
                // 企业账户消费退款
                paymentInfo.setScoreConsumeTotal(-scoreNum);
            }
        }
        rpUserPaymentInfoMapper.updateForStat(paymentInfo);
    }

    /**
     * 根据提供的事件上下文分析和更新数字人付费信息。
     *
     * @param context 用户相关的事件上下文包含必要的信息，如租户代码、用户ID和扩展参数
     */
    private void analysisTdhCust(UserRpEventContext context) {
        // 租户ID
        String tenantCode = context.getTenantCode();
        // 用户ID
        String userId = context.getUserId();
        // 数字人类型 0-照片数字人 1-视频数字人 2-声音音色
        String tdhType = (String) context.getExtParams().get("tdhType");
        // 数字人状态 0: 草稿（上传中） 1: 订单创建（待支付） 2: 支付完成（审核中） 3: 审核完成（定制中） 4: 定制完成  5: 取消
        String tdhStatus = (String) context.getExtParams().get("tdhStatus");
        // 支付金额 单位: 分
        Integer payAmount = (Integer) context.getExtParams().getOrDefault("payAmount", 0);
        // 旧支付金额 单位: 分
        Integer oldPayAmount = (Integer) context.getExtParams().get("oldPayAmount");

        // 如果存在旧支付金额的话，这次消息为订单改价
        if (oldPayAmount != null) {
            RpUserTdhInfo tdhInfo = new RpUserTdhInfo();
            tdhInfo.setTenantCode(context.getTenantCode());
            tdhInfo.setUserId(context.getTenantCode());
            tdhInfo.setTotalAmount(payAmount - oldPayAmount);
            rpUserTdhInfoMapper.updateSelective(tdhInfo);
            return;
        }

        // 查询用户是否有历史数据
        RpUserTdhInfo info = rpUserTdhInfoMapper.findOneByTenantCodeAndUserIdAndTdhType(tenantCode, userId, tdhType);
        if (info == null) {
            // 数字人付费报表初始化
            List<RpUserTdhInfo> tdhInfos = rpUserTdhInfoMapper.selectTdhInfoByUserId(tenantCode, userId);
            if (CollectionUtils.isNotEmpty(tdhInfos)) {
                tdhInfos.forEach(rpUserTdhInfoMapper::insertSelective);
            }
            return;
        }

        RpUserTdhInfo tdhInfo = new RpUserTdhInfo();
        tdhInfo.setTenantCode(context.getTenantCode());
        tdhInfo.setUserId(context.getTenantCode());
        tdhInfo.setTdhType(tdhType);
        switch (tdhStatus) {
            case "1":
                // 数字人付费初始化
                tdhInfo.setLaunchTimes(1);
                break;
            case "2":
                // 数字人付费成功
                tdhInfo.setTotalAmount(payAmount);
            case "4":
                // 定制完成
                tdhInfo.setActualTimes(1);
            case "5":
                // 取消数字人生成
                tdhInfo.setActualTimes(-1);
                tdhInfo.setTotalAmount(-payAmount);
                break;
        }
        rpUserTdhInfoMapper.updateSelective(tdhInfo);
    }

    /**
     * 分析和处理给定用户的PPT制作事件。
     *
     * @param context UserRpEventContext包含必要的信息，如租户代码、用户ID和PPT类型
     */
    private void analysisPptMake(UserRpEventContext context) {
        // 租户ID
        String tenantCode = context.getTenantCode();
        // 用户ID
        String userId = context.getUserId();
        // PPT类型 1-AI生成PPT 2-上传文档转PPT 3-资料PPT
        String pptType = (String) context.getExtParams().get("pptType");

        if (StringUtils.isBlank(pptType)) {
            log.info("用户维度PPT制作消息错误，没有ppt制作类型");
            return;
        }

        // 查询用户是否有历史数据
        RpUserPptVideoInfo info = rpUserPptVideoInfoMapper.findOneByTenantCodeAndUserId(tenantCode, userId);
        if (info == null) {
            // 用户第一次PPT制作
            info = new RpUserPptVideoInfo();
            info.setTenantCode(context.getTenantCode());
            info.setUserId(context.getTenantCode());
            info.setPptMakeTimes(1);
            analysisPptType(pptType, info);
            rpUserPptVideoInfoMapper.insertSelective(info);
            return;
        }

        RpUserPptVideoInfo pptInfo = new RpUserPptVideoInfo();
        pptInfo.setTenantCode(context.getTenantCode());
        pptInfo.setUserId(context.getTenantCode());
        pptInfo.setPptMakeTimes(1);
        analysisPptType(pptType, info);
        rpUserPptVideoInfoMapper.updateSelective(pptInfo);
    }

    /**
     * 为用户分析和更新视频制作信息。
     *
     * @param context 包含租户和用户信息的事件上下文
     */
    private void analysisVideoMake(UserRpEventContext context) {
        // 租户ID
        String tenantCode = context.getTenantCode();
        // 用户ID
        String userId = context.getUserId();

        // 查询用户是否有历史数据
        RpUserPptVideoInfo info = rpUserPptVideoInfoMapper.findOneByTenantCodeAndUserId(tenantCode, userId);
        if (info == null) {
            // 用户第一次视频制作
            info = new RpUserPptVideoInfo();
            info.setTenantCode(context.getTenantCode());
            info.setUserId(context.getTenantCode());
            info.setVideoMakeTimes(1);
            rpUserPptVideoInfoMapper.insertSelective(info);
            return;
        }

        RpUserPptVideoInfo videoInfo = new RpUserPptVideoInfo();
        videoInfo.setTenantCode(context.getTenantCode());
        videoInfo.setUserId(context.getTenantCode());
        videoInfo.setVideoMakeTimes(1);
        rpUserPptVideoInfoMapper.updateSelective(videoInfo);
    }

    /**
     * 分析分数并更新用户支付信息中的相应分数金额。
     *
     * @param scoreNum  要分析的分数，期望值为500、2000或5000
     * @param info      RpUserPaymentInfo对象，其中将更新分数金额
     * @param payAmount 为给定分数设置的付款金额
     */
    private static void analysisScore(Integer scoreNum, RpUserPaymentInfo info, Integer payAmount) {
        switch (scoreNum) {
            case 500:
                info.setScore500Amount(payAmount);
                break;
            case 2000:
                info.setScore2000Amount(payAmount);
                break;
            case 5000:
                info.setScore5000Amount(payAmount);
                break;
        }
    }

    /**
     * 分析PowerPoint（PPT）的类型，并更新提供的RpUserPptVideoInfo对象中的相应计数。
     *
     * @param pptType 表示PPT类型的字符串，其中
     *                “1”表示AI生成的PPT，
     *                “2”表示基于文档的PPT，
     *                “3”表示基于文本的PPT。
     * @param info    基于pptType更新RpUserPptVideoInfo的实例。
     */
    private static void analysisPptType(String pptType, RpUserPptVideoInfo info) {
        switch (pptType) {
            case "1":
                info.setPptAiCount(1);
                break;
            case "2":
                info.setPptDocCount(1);
                break;
            case "3":
                info.setPptTxtCount(1);
                break;
        }
    }

    private void initPaymentInfo(String tenantCode, String userId, Integer id) {
        RpUserPaymentInfo scoreBalance = rpUserPaymentInfoMapper.selectScoreBalanceByUserId(tenantCode, userId);
        RpUserPaymentInfo scoreDetail = rpUserPaymentInfoMapper.selectScoreDetailByUserId(tenantCode, userId);
        RpUserPaymentInfo refund = rpUserPaymentInfoMapper.selectRefundByUserId(tenantCode, userId);
        RpUserPaymentInfo payInfo = rpUserPaymentInfoMapper.selectPayInfoByUserId(tenantCode, userId);

        // 用户维度报表支付数据初始化
        RpUserPaymentInfo info = new RpUserPaymentInfo();
        info.setId(id);
        info.setTenantCode(tenantCode);
        info.setUserId(userId);

        if (scoreBalance != null) {
            info.setScoreBalance(scoreBalance.getScoreBalance());
        }

        if (scoreDetail != null) {
            info.setScoreRechargeTotal(scoreDetail.getScoreRechargeTotal());
            info.setScoreLoseTotal(scoreDetail.getScoreLoseTotal());
            info.setScoreConsumeTotalPerson(scoreDetail.getScoreConsumeTotalPerson());
            info.setScoreConsumeTotal(scoreDetail.getScoreConsumeTotal());
        }

        if (refund != null && refund.getRefundNum() > 0) {
            info.setIsRefund("1");
            info.setRefundNum(refund.getRefundNum());
            info.setTotalRefundAmount(refund.getTotalRefundAmount());
        } else {
            info.setIsRefund("0");
        }

        if (payInfo != null && payInfo.getPayNum() > 0) {
            info.setIsSubscriber("1");
            info.setPayNum(payInfo.getPayNum());
            info.setTotalPayAmount(payInfo.getTotalPayAmount());
            info.setFirstPayTime(payInfo.getFirstPayTime());
            info.setLastPayTime(payInfo.getLastPayTime());
            info.setScoreTotalAmount(payInfo.getScoreTotalAmount());
            info.setScoreTotalNum(payInfo.getScoreTotalNum());
            info.setScore500Count(payInfo.getScore500Count());
            info.setScore500Amount(payInfo.getScore500Amount());
            info.setScore2000Count(payInfo.getScore2000Count());
            info.setScore2000Amount(payInfo.getScore2000Amount());
            info.setScore5000Count(payInfo.getScore5000Count());
            info.setScore5000Amount(payInfo.getScore5000Amount());
        } else {
            info.setIsSubscriber("0");
        }

        if (id == null) {
            rpUserPaymentInfoMapper.insertSelective(info);
        } else {
            rpUserPaymentInfoMapper.updateByPrimaryKeySelective(info);
        }
    }
}
