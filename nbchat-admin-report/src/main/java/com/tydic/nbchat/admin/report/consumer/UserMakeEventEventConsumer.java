package com.tydic.nbchat.admin.report.consumer;

import com.tydic.nbchat.admin.api.rp.RpUserDetailService;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.common.nbchat.msg.UserMakeEventContext;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import com.tydic.nicc.mq.starter.entity.eum.ConsumeMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@KKMqConsumer(
        consumeMode = ConsumeMode.ORDERLY,
        consumerGroup = NbchatTopicsConstants.NBCHAT_USER_MAKE_EVENT_RP_CID,
        topic = NbchatTopicsConstants.NBCHAT_USER_MAKE_EVENT_TOPIC)
@Component
public class UserMakeEventEventConsumer implements KKMqConsumerListener<UserMakeEventContext> {

    private final RpUserDetailService rpUserDetailService;

    public UserMakeEventEventConsumer(RpUserDetailService rpUserDetailService) {
        this.rpUserDetailService = rpUserDetailService;
    }

    @Override
    public void onMessage(UserMakeEventContext context) {
        log.info("用户创作事件消费: {}", context);
        try {
            rpUserDetailService.handleMakeEventEvent(context);
        } catch (Exception e) {
            log.error("用户创作事件消费-异常: {}", context, e);
        }
    }
}
