package com.tydic.nbchat.admin.report.consumer;

import com.tydic.nbchat.admin.api.rp.RpUserDetailService;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.common.nbchat.msg.UserLoginEventContext;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@KKMqConsumer(
        consumerGroup = NbchatTopicsConstants.NBCHAT_USER_LOGIN_EVENT_RP_CID,
        topic = NbchatTopicsConstants.NBCHAT_USER_LOGIN_EVENT_TOPIC)
@Component
public class UserLoginEventConsumer implements KKMqConsumerListener<UserLoginEventContext> {

    private final RpUserDetailService rpUserDetailService;

    public UserLoginEventConsumer(RpUserDetailService rpUserDetailService) {
        this.rpUserDetailService = rpUserDetailService;
    }

    @Override
    public void onMessage(UserLoginEventContext context) {
        try {
            log.info("用户登录事件消费: {}", context);
            rpUserDetailService.handleLoginEvent(context);
        } catch (Exception e) {
            log.error("用户登录事件消费-异常: {}", context, e);
        }
    }
}
