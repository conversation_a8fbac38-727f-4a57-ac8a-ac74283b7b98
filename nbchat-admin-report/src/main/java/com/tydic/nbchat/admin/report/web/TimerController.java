package com.tydic.nbchat.admin.report.web;

import com.tydic.nbchat.admin.report.timer.RpTimer;
import com.tydic.nbchat.admin.report.timer.UserRpByUserOperateTimer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/timer/")
public class TimerController {

    private final RpTimer rpTimer;
    private final UserRpByUserOperateTimer userRpByUserOperateTimer;

    @PostMapping("dotask")
    public void doTask() {
        rpTimer.run();
    }

    @GetMapping("user/rp/runForDay")
    public void runForDay() {
        userRpByUserOperateTimer.runForDay();
    }

    @GetMapping("user/rp/runForMinute")
    public void runForMinute() {
        userRpByUserOperateTimer.runForMinute();
    }
}
