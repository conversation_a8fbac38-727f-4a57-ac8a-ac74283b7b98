package com.tydic.nbchat.admin.report.web;

import com.tydic.nbchat.admin.api.bo.rp.UserOperateInfoBO;
import com.tydic.nbchat.admin.api.rp.UserOperateApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/3/25 10:36
 * @description 用户操作记录控制器，提供用户操作记录相关的管理接口。
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/user/operate")
public class UserOperateController {

    private final UserOperateApi userOperateApi;

    /**
     * 保存用户操作记录。
     *
     * @param param 用户操作信息，包括租户ID、用户ID、操作类型、业务ID和操作内容
     * @return 操作结果响应对象，包含操作状态及相关消息
     */
    @PostMapping("/save")
    public Rsp<?> save(@RequestBody UserOperateInfoBO param) {
        return userOperateApi.save(param);
    }
}
