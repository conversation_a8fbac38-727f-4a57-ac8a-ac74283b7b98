package com.tydic.nbchat.admin.report.consumer;

import com.tydic.nbchat.admin.api.rp.UserRpApi;
import com.tydic.nicc.common.nbchat.msg.UserRpEventContext;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_USER_RP_EVENT_CID;
import static com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants.NBCHAT_USER_RP_EVENT_TOPIC;

/**
 * <AUTHOR>
 * @date 2025/3/20 18:58
 * @description 用户维度报表事件消费类
 */
@Slf4j
@Component
@AllArgsConstructor
@KKMqConsumer(topic = NBCHAT_USER_RP_EVENT_TOPIC, consumerGroup = NBCHAT_USER_RP_EVENT_CID)
public class UserRpEventConsumer implements KKMqConsumerListener<UserRpEventContext> {

    private final UserRpApi userRpApi;

    @Override
    public void onMessage(UserRpEventContext context) {
        try {
            log.info("用户维度报表事件消费: {}", context);
            userRpApi.handleUserRpEvent(context);
        } catch (Exception e) {
            log.error("用户维度报表事件消费-异常: {}", context, e);
        }
    }
}
