package com.tydic.nbchat.admin.report.service;

import com.tydic.nbchat.admin.api.rp.RpActiveUserMetrics;
import com.tydic.nbchat.admin.mapper.RpActivateMapper;
import com.tydic.nbchat.admin.mapper.po.RpActivatePO;
import com.tydic.nbchat.admin.mapper.po.SysLoginLog;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RpActivateService {

    @Resource
    RpActivateMapper rpActivateMapper;


    public Rsp calculateUserActivity(Date beginDate, Date endDate) {
        RpActivatePO po = new RpActivatePO();
        po.setBeginDate(beginDate);
        po.setEndDate(endDate);
        //活跃用户数
        int activateUserCount = rpActivateMapper.activateUserCount(po);

        //免费产品活跃用户数
        int activateFreeUserCount = rpActivateMapper.activateFreeUserCount(po);

        //付费产品活跃数
        int activateVipUserCount = rpActivateMapper.activateVipUserCount(po);

        //新注册用户活跃数
        List<SysLoginLog> sysLoginLogs = rpActivateMapper.selectBaseData(po);
        int activateNewUserCount = 0;
        Map<String, List<SysLoginLog>> groupedByUserId = sysLoginLogs.stream()
                .filter(v -> v.getRegTime().after(beginDate) && v.getRegTime().before(endDate))
                .collect(Collectors.groupingBy(SysLoginLog::getUserId));

        for (Map.Entry<String, List<SysLoginLog>> entry : groupedByUserId.entrySet()) {
            List<SysLoginLog> logs = entry.getValue();
            if (logs.size() >= 2) {
                logs.sort(Comparator.comparing(SysLoginLog::getLoginTime));
                long diff = ChronoUnit.HOURS.between(logs.get(0).getLoginTime().toInstant(), logs.get(1).getLoginTime().toInstant());
                if (diff <= 7 * 24) {
                    activateNewUserCount++;
                }
            }
        }

        RpActiveUserMetrics metrics = RpActiveUserMetrics.builder()
                .activateUserCount(activateUserCount)
                .activateFreeUserCount(activateFreeUserCount)
                .activateVipUserCount(activateVipUserCount)
                .activateNewUserCount(activateNewUserCount)
                .build();
        return BaseRspUtils.createSuccessRsp(metrics);
    }

}
