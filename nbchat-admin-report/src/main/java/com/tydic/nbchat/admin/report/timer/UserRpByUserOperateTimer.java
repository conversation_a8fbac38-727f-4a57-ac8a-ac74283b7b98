package com.tydic.nbchat.admin.report.timer;

import com.tydic.nbchat.admin.api.rp.UserRpApi;
import com.tydic.nbchat.admin.mapper.*;
import com.tydic.nbchat.admin.mapper.po.*;
import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.tydic.nbchat.admin.api.bo.constants.RedisConstants.USER_RP_BY_OPERATE_REDIS_KEY_PREFIX;
import static com.tydic.nbchat.admin.api.bo.constants.RedisConstants.USER_RP_BY_TIMER_REDIS_KEY;
import static com.tydic.nicc.common.nbchat.emus.UserRpEventType.*;

/**
 * <AUTHOR>
 * @date 2025/3/24 16:02
 * @description 定时统计用户操作日志的数据至用户维度报表
 */
@Slf4j
@Component
@EnableScheduling
@AllArgsConstructor
public class UserRpByUserOperateTimer {

    private final UserRpApi userRpApi;
    private final RedisHelper redisHelper;
    private final RpUserTdhInfoMapper rpUserTdhInfoMapper;
    private final RpUserExamInfoMapper rpUserExamInfoMapper;
    private final RpHourVideoCountMapper rpHourVideoCountMapper;
    private final SysUserOperateLogMapper sysUserOperateLogMapper;
    private final RpUserPaymentInfoMapper rpUserPaymentInfoMapper;
    private final ExamCreationRecordMapper examCreationRecordMapper;
    private final RpUserActivityInfoMapper rpUserActivityInfoMapper;
    private final RpUserPptVideoInfoMapper rpUserPptVideoInfoMapper;
    private final RpUserPromotionInfoMapper rpUserPromotionInfoMapper;
    private final RpUserPayInvoiceInfoMapper rpUserPayInvoiceInfoMapper;
    private final NbchatUserAssistDetailMapper nbchatUserAssistDetailMapper;

    public static final String RP_DAY_REDIS_LOCK_KEY = "RP_DAY_REDIS_LOCK_KEY";
    public static final String RP_HOUR_REDIS_LOCK_KEY = "RP_HOUR_REDIS_LOCK_KEY";
    public static final String RP_MINUTE_REDIS_LOCK_KEY = "RP_MINUTE_REDIS_LOCK_KEY";

    /**
     * 定时任务，每天执行一次，用于统计用户维度报表。
     * 该方法通过加锁机制确保同一时间只有一个实例在执行此任务，以避免数据冲突。
     * 统计的数据包括用户的访问次数和最后访问时间，并根据情况更新或插入新的记录到用户活动信息表中。
     *
     * <p>定时任务配置：每天凌晨0点5分执行</p>
     */
    @Scheduled(cron = "0 5 4 1/1 * ?")
    public void runForDay() {
        //加锁
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().lockKey(RP_DAY_REDIS_LOCK_KEY).requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity, 5L, 3 * 60L); //设置锁3分钟，任务执行完自动释放
        if (!locked) {
            log.info("定时统计用户维度报表-每天-未获取到锁");
            return;
        }
        try {
            log.info("定时统计用户维度报表-每天-任务开始");
            // 用户操作日志中会统计进用户维度报表的数据
            List<SysUserOperateLog> operateLogs = sysUserOperateLogMapper.countGroupByTenantCodeAndUserIdSelective();
            // 遍历统计数据区分insert与update
            if (CollectionUtils.isEmpty(operateLogs)) {
                return;
            }
            List<RpUserActivityInfo> insert = new ArrayList<>();
            List<RpUserActivityInfo> update = new ArrayList<>();
            for (SysUserOperateLog operateLog : operateLogs) {
                RpUserActivityInfo activity = rpUserActivityInfoMapper.selectByUserId(operateLog.getTenantCode(), operateLog.getUserId());
                if (activity != null) {
                    RpUserActivityInfo info = new RpUserActivityInfo();
                    info.setId(activity.getId());
                    info.setAccessTimes(activity.getAccessTimes() + 1);
                    info.setLastAccessTime(operateLog.getLastAccessTime());
                    update.add(info);
                    continue;
                }

                RpUserActivityInfo info = new RpUserActivityInfo();
                info.setTenantCode(operateLog.getTenantCode());
                info.setUserId(operateLog.getUserId());
                info.setAccessTimes(1);
                info.setFirstAccessTime(operateLog.getFirstAccessTime());
                info.setLastAccessTime(operateLog.getLastAccessTime());
                insert.add(info);
            }

            if (CollectionUtils.isNotEmpty(insert)) {
                rpUserActivityInfoMapper.batchInsert(insert);
            }
            if (CollectionUtils.isNotEmpty(update)) {
                rpUserActivityInfoMapper.updateBatchSelective(update);
            }
            log.info("定时统计用户维度报表-每天-任务结束");
        } catch (Exception e) {
            log.error("【定时统计用户维度报表-每天】,执行异常:{}", redisLockEntity, e);
        } finally {
            redisHelper.unlockLua(redisLockEntity);
        }
    }

    @Scheduled(cron = "0 5 0/1 * * ?")
    @Transactional
    public void runForHour() {
        // 加锁
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().lockKey(RP_HOUR_REDIS_LOCK_KEY).requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity, 5L, 3 * 60L); //设置锁3分钟，任务执行完自动释放
        if (!locked) {
            log.info("定时统计用户维度报表-每1小时-未获取到锁");
            return;
        }
        try {
            log.info("定时统计报表数据-每1小时-任务开始");
            // 记录视频小时统计报表
            List<RpHourVideoCount> videoRpList = rpHourVideoCountMapper.selectVideo();
            insertOrUpdatePptVideoHour(videoRpList);

            // 记录ppt小时统计报表
            List<RpHourVideoCount> pptRpList = rpHourVideoCountMapper.selectPpt();
            insertOrUpdatePptVideoHour(pptRpList);
            log.info("定时统计用户维度报表-每1小时-任务结束");
        } catch (Exception e) {
            log.error("【定时统计报表数据-每1小时】,执行异常:{}", redisLockEntity, e);
        } finally {
            redisHelper.unlockLua(redisLockEntity);
        }
    }

    private void insertOrUpdatePptVideoHour(List<RpHourVideoCount> list) {
        List<RpHourVideoCount> insertList = new ArrayList<>();
        List<RpHourVideoCount> updateList = new ArrayList<>();
        list.forEach(item -> {
            RpHourVideoCount count = rpHourVideoCountMapper.selectOneByTenantCodeAndUserIdAndDayDataAndHourData(item.getTenantCode(), item.getUserId(), item.getDayData(), item.getHourData());
            if (count == null) {
                insertList.add(item);
            } else {
                item.setId(count.getId());
                updateList.add(item);
            }
        });
        insertList.forEach(rpHourVideoCountMapper::insertSelective);
        if (CollectionUtils.isNotEmpty(updateList)) {
            rpHourVideoCountMapper.updateBatchSelective(updateList);
        }
    }

    /**
     * 定时任务方法，每10分钟执行一次，用于统计用户维度的报表数据。
     * 该方法首先尝试获取一个分布式锁以确保同一时间只有一个实例在执行此任务。
     * 成功获取锁后，从Redis中读取并处理PPT下载量、视频下载量、试题生成量和试题导出量的数据，
     * 并进行相应的数据分析。此外，还统计了上一任务执行周期到当前任务执行期间的增量数据。
     * 在任务执行完毕或遇到异常时，会释放持有的锁。
     */
    @Scheduled(cron = "0 0/10 * * * ?")
    public void runForMinute() {
        // 加锁
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().lockKey(RP_MINUTE_REDIS_LOCK_KEY).requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity, 5L, 3 * 60L); //设置锁3分钟，任务执行完自动释放
        if (!locked) {
            log.info("定时统计用户维度报表-每10分钟-未获取到锁");
            return;
        }
        try {
            log.info("定时统计用户维度报表-每10分钟-任务开始");
            // TODO 目前还未做原子性操作（需要修改redisHelper类）
            // PPT下载量
            String pptDownloadRedisKey = USER_RP_BY_OPERATE_REDIS_KEY_PREFIX.replace("{type}", PPT_DOWNLOAD.getType());
            Map<Object, Object> pptDownloadDataMap = redisHelper.hmget(pptDownloadRedisKey);
            redisHelper.del(pptDownloadRedisKey);
            // 视频下载量
            String videoDownloadRedisKey = USER_RP_BY_OPERATE_REDIS_KEY_PREFIX.replace("{type}", VIDEO_DOWNLOAD.getType());
            Map<Object, Object> videoDownloadDataMap = redisHelper.hmget(videoDownloadRedisKey);
            redisHelper.del(videoDownloadRedisKey);
            // 处理从redis中获取到的数据
            analysisPptVideoDownloadData(pptDownloadDataMap, videoDownloadDataMap);

            // 试题生成量
            String examGeneRedisKey = USER_RP_BY_OPERATE_REDIS_KEY_PREFIX.replace("{type}", EXAM_GENE.getType());
            Map<Object, Object> examGeneDataMap = redisHelper.hmget(examGeneRedisKey);
            redisHelper.del(examGeneRedisKey);
            // 试题导出量
            String examExportRedisKey = USER_RP_BY_OPERATE_REDIS_KEY_PREFIX.replace("{type}", EXAM_EXPORT.getType());
            Map<Object, Object> examExportDataMap = redisHelper.hmget(examExportRedisKey);
            redisHelper.del(examExportRedisKey);
            // 处理从redis中获取到的数据
            analysisExamData(examGeneDataMap, examExportDataMap);

            // 统计上个任务执行时间到当前任务执行时间范围内的增量数据
            // 获取时间范围
            Date startTime;
            boolean incrementFlag;
            Date endTime = new Date();
            Map<Object, Object> startTimeMap = redisHelper.hmget(USER_RP_BY_TIMER_REDIS_KEY);

            // 出题总数
            analysisExamMakeTimes(startTimeMap, endTime);
            // 助力信息
            analysisAssist(startTimeMap, endTime);
            // 视频任务
            analysisVideoTask(startTimeMap, endTime);
            log.info("定时统计用户维度报表-每10分钟-任务结束");
        } catch (Exception e) {
            log.error("【定时统计用户维度报表-每10分钟】,执行异常:{}", redisLockEntity, e);
        } finally {
            redisHelper.unlockLua(redisLockEntity);
        }
    }

    /**
     * 分析和处理PPT和视频下载数据，更新或将信息插入数据库。
     *
     * @param pptDownloadDataMap   一个映射，其中键是“tenantCode:userId”格式的字符串，值是PPT下载次数
     * @param videoDownloadDataMap 一个映射，其中键是“tenantCode:userId”格式的字符串，值是视频下载的数量
     */
    private void analysisPptVideoDownloadData(Map<Object, Object> pptDownloadDataMap, Map<Object, Object> videoDownloadDataMap) {
        // 解析PPT下载量
        Map<String, RpUserPptVideoInfo> dataMap = pptDownloadDataMap.entrySet().stream().collect(Collectors.toMap(e -> (String) e.getKey(), e -> {
            // 解析租户ID与用户ID
            String key = (String) e.getKey();
            String[] keys = key.split(":");
            // 组装实体
            RpUserPptVideoInfo info = new RpUserPptVideoInfo();
            info.setTenantCode(keys[0]);
            info.setUserId(keys[1]);
            info.setPptDownloadTimes((Integer) e.getValue());
            // 获取该用户在在租户下的视频下载量
            Object videoDownloadData = videoDownloadDataMap.remove(e.getKey());
            if (videoDownloadData != null) {
                info.setVideoDownloadTimes((Integer) videoDownloadData);
            } else {
                info.setVideoDownloadTimes(0);
            }
            return info;
        }));
        // 判断是否存在有视频下载量，但是没有PPT下载量的数据
        if (MapUtils.isNotEmpty(videoDownloadDataMap)) {
            videoDownloadDataMap.forEach((k, v) -> {
                // 解析租户ID与用户ID
                String key = (String) k;
                String[] keys = key.split(":");
                // 组装实体
                RpUserPptVideoInfo info = new RpUserPptVideoInfo();
                info.setTenantCode(keys[0]);
                info.setUserId(keys[1]);
                info.setPptDownloadTimes(0);
                info.setVideoDownloadTimes((Integer) v);
                dataMap.put(key, info);
            });
        }
        // 获取所有统计数据
        List<RpUserPptVideoInfo> dataList = new ArrayList<>(dataMap.values());
        // 遍历列表判断数据库中是否存在数据
        List<RpUserPptVideoInfo> insert = new ArrayList<>();
        List<RpUserPptVideoInfo> update = new ArrayList<>();
        dataList.forEach(o -> {
            RpUserPptVideoInfo info = rpUserPptVideoInfoMapper.findOneByTenantCodeAndUserId(o.getTenantCode(), o.getUserId());
            if (info == null) {
                insert.add(o);
                return;
            }
            o.setId(info.getId());
            update.add(o);
        });
        // 数据库变更
        if (CollectionUtils.isNotEmpty(insert)) {
            rpUserPptVideoInfoMapper.batchInsertForTimer(insert);
        }
        if (CollectionUtils.isNotEmpty(update)) {
            rpUserPptVideoInfoMapper.batchUpdateForTimer(update);
        }
    }

    /**
     * 分析检查数据，包括检查的生成和导出，并在数据库中更新或插入相应的记录。
     *
     * @param examGeneDataMap   一个映射，其中键是“tenantId:userId”格式的字符串，值是生成检查的次数
     * @param examExportDataMap 一个映射，其中键是“tenantId:userId”格式的字符串，值是导出检查的次数
     */
    private void analysisExamData(Map<Object, Object> examGeneDataMap, Map<Object, Object> examExportDataMap) {
        // 解析试题生成量
        Map<String, RpUserExamInfo> dataMap = examGeneDataMap.entrySet().stream().collect(Collectors.toMap(e -> (String) e.getKey(), e -> {
            // 解析租户ID与用户ID
            String key = (String) e.getKey();
            String[] keys = key.split(":");
            // 组装实体
            RpUserExamInfo info = new RpUserExamInfo();
            info.setTenantCode(keys[0]);
            info.setUserId(keys[1]);
            info.setExamChickTimes((Integer) e.getValue());
            // 获取该用户在在租户下的试题导出量
            Object examExportData = examExportDataMap.remove(e.getKey());
            if (examExportData != null) {
                info.setExamDownloadTimes((Integer) examExportData);
            } else {
                info.setExamDownloadTimes(0);
            }
            return info;
        }));
        // 判断是否存在有试题导出量，但是没有试题生成量的数据
        if (MapUtils.isNotEmpty(examExportDataMap)) {
            examExportDataMap.forEach((k, v) -> {
                // 解析租户ID与用户ID
                String key = (String) k;
                String[] keys = key.split(":");
                // 组装实体
                RpUserExamInfo info = new RpUserExamInfo();
                info.setTenantCode(keys[0]);
                info.setUserId(keys[1]);
                info.setExamChickTimes(0);
                info.setExamDownloadTimes((Integer) v);
                dataMap.put(key, info);
            });
        }
        // 获取所有统计数据
        List<RpUserExamInfo> dataList = new ArrayList<>(dataMap.values());
        // 遍历列表判断数据库中是否存在数据
        List<RpUserExamInfo> insert = new ArrayList<>();
        List<RpUserExamInfo> update = new ArrayList<>();
        dataList.forEach(o -> {
            RpUserExamInfo info = rpUserExamInfoMapper.findOneByTenantCodeAndUserId(o.getTenantCode(), o.getUserId());
            if (info == null) {
                insert.add(o);
                return;
            }
            o.setId(info.getId());
            update.add(o);
        });
        // 数据库变更
        if (CollectionUtils.isNotEmpty(insert)) {
            rpUserExamInfoMapper.batchInsertForTimer(insert);
        }
        if (CollectionUtils.isNotEmpty(update)) {
            rpUserExamInfoMapper.batchUpdateForTimer(true, update);
        }
    }

    /**
     * 分析并处理指定时间范围内的考试创建记录。
     *
     * <p>此方法根据提供的开始时间和结束时间统计考试创建记录，并更新或插入用户考试信息。</p>
     *
     * @param startTimeMap 包含上次统计时间的映射，键为考试类型，值为上次统计的时间
     * @param endTime      统计的结束时间
     */
    private void analysisExamMakeTimes(Map<Object, Object> startTimeMap, Date endTime) {
        try {
            // 获取时间范围
            Date startTime;
            boolean incrementFlag;
            if (startTimeMap.containsKey(EXAM_MAKE.getType())) {
                // redis中存在上次统计时间，此次为增量统计
                startTime = (Date) startTimeMap.get(EXAM_MAKE.getType());
                incrementFlag = true;
            } else {
                // redis中不存在上次统计时间，此次为全量统计
                startTime = null;
                incrementFlag = false;
            }
            // 统计范围内的数据
            List<ExamCreationRecord> exams = examCreationRecordMapper.countByGroupUserIdWhenCreateTimeBetween(startTime, endTime);
            // 遍历列表判断数据库中是否存在数据
            List<RpUserExamInfo> insert = new ArrayList<>();
            List<RpUserExamInfo> update = new ArrayList<>();
            exams.forEach(exam -> {
                RpUserExamInfo info = rpUserExamInfoMapper.findOneByTenantCodeAndUserId(exam.getTenantCode(), exam.getUserId());
                if (info == null) {
                    info = new RpUserExamInfo();
                    info.setTenantCode(exam.getTenantCode());
                    info.setUserId(exam.getUserId());
                    info.setExamMakeTimes(exam.getExamMakeTimes());
                    insert.add(info);
                    return;
                }
                info.setExamMakeTimes(exam.getExamMakeTimes());
                update.add(info);
            });
            // 数据库变更
            if (CollectionUtils.isNotEmpty(insert)) {
                rpUserExamInfoMapper.batchInsertForTimer(insert);
            }
            if (CollectionUtils.isNotEmpty(update)) {
                rpUserExamInfoMapper.batchUpdateForTimer(incrementFlag, update);
            }
            redisHelper.hset(USER_RP_BY_TIMER_REDIS_KEY, EXAM_MAKE.getType(), endTime);
        } catch (Exception e) {
            log.error("出题总数统计失败-startTimeMap-{}-endTime-{}", startTimeMap, endTime);
        }
    }

    /**
     * 协助分析方法，用于处理用户助力详情数据，并更新或插入到RpUserPromotionInfo表中。
     *
     * @param startTimeMap 一个映射，键为ASSIST类型，值为上次统计的开始时间。如果存在该键，则表示此次为增量统计。
     * @param endTime      统计结束时间。
     */
    private void analysisAssist(Map<Object, Object> startTimeMap, Date endTime) {
        try {
            // 获取时间范围
            Date startTime;
            boolean incrementFlag;
            if (startTimeMap.containsKey(ASSIST.getType())) {
                // redis中存在上次统计时间，此次为增量统计
                startTime = (Date) startTimeMap.get(ASSIST.getType());
                incrementFlag = true;
            } else {
                // redis中不存在上次统计时间，此次为全量统计
                startTime = null;
                incrementFlag = false;
            }
            // 统计范围内的数据
            List<NbchatUserAssistDetail> assists = nbchatUserAssistDetailMapper.countByGroupUserIdWhenCreateTimeBetween(startTime, endTime);
            // 遍历列表判断数据库中是否存在数据
            List<RpUserPromotionInfo> insert = new ArrayList<>();
            List<RpUserPromotionInfo> update = new ArrayList<>();
            assists.forEach(assist -> {
                RpUserPromotionInfo info = rpUserPromotionInfoMapper.findOneByTenantCodeAndUserId(assist.getTenantCode(), assist.getUserId());
                if (info == null) {
                    info = new RpUserPromotionInfo();
                    info.setTenantCode(assist.getTenantCode());
                    info.setUserId(assist.getUserId());
                    info.setTotalNum(0); // TODO 字段还未定义
                    info.setAssistNum(assist.getAssistNum());
                    info.setFissionNum(0); // TODO 字段还未定义
                    insert.add(info);
                    return;
                }
                info.setAssistNum(assist.getAssistNum());
                update.add(info);
            });
            // 数据库变更
            if (CollectionUtils.isNotEmpty(insert)) {
                rpUserPromotionInfoMapper.batchInsert(insert);
            }
            if (CollectionUtils.isNotEmpty(update)) {
                rpUserPromotionInfoMapper.batchUpdate(incrementFlag, update);
            }
            redisHelper.hset(USER_RP_BY_TIMER_REDIS_KEY, ASSIST.getType(), endTime);
        } catch (Exception e) {
            log.error("出题总数统计失败-startTimeMap-{}-endTime-{}", startTimeMap, endTime);
        }
    }

    private void analysisVideoTask(Map<Object, Object> startTimeMap, Date endTime) {
        try {
            // 获取时间范围
            Date startTime;
            boolean incrementFlag;
            if (startTimeMap.containsKey(VIDEO_TASK.getType())) {
                // redis中存在上次统计时间，此次为增量统计
                startTime = (Date) startTimeMap.get(VIDEO_TASK.getType());
            } else {
                // redis中不存在上次统计时间，此次为全量统计
                startTime = null;
            }
            // 统计范围内的数据
            rpUserPptVideoInfoMapper.updateByStartTimeBetween(startTime, endTime);
            rpUserPptVideoInfoMapper.insertByStartTimeBetween(startTime, endTime);
            redisHelper.hset(USER_RP_BY_TIMER_REDIS_KEY, VIDEO_TASK.getType(), endTime);
        } catch (Exception e) {
            log.error("视频任务统计失败-startTimeMap-{}-endTime-{}", startTimeMap, endTime);
        }
    }
}
