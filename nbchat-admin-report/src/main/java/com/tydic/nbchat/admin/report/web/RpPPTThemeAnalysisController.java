package com.tydic.nbchat.admin.report.web;

import com.tydic.nbchat.admin.api.bo.rp.RpPPTUsageSaveReqBO;
import com.tydic.nbchat.admin.api.bo.rp.RpPPTUsageUpdateReqBO;
import com.tydic.nbchat.admin.api.rp.RpPPTThemeAnalysisService;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/rp/ppt/analysis")
public class RpPPTThemeAnalysisController {

    private final RpPPTThemeAnalysisService rpPPTThemeAnalysisService;

    public RpPPTThemeAnalysisController(RpPPTThemeAnalysisService rpPPTThemeAnalysisService) {
        this.rpPPTThemeAnalysisService = rpPPTThemeAnalysisService;
    }

    @RequestMapping("/usage/save")
    public Rsp saveUserUsage(@Validated @RequestBody RpPPTUsageSaveReqBO reqBO) {
        return rpPPTThemeAnalysisService.saveUserUsage(reqBO);
    }

}
