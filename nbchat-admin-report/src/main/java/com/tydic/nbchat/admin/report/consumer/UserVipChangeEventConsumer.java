package com.tydic.nbchat.admin.report.consumer;

import com.tydic.nbchat.admin.api.rp.RpUserDetailService;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.common.nbchat.msg.UserVipChangeContext;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@KKMqConsumer(
        consumerGroup = NbchatTopicsConstants.NBCHAT_USER_VIP_CHANGE_RP_CID,
        topic = NbchatTopicsConstants.NBCHAT_USER_VIP_CHANGE_TOPIC)
@Component
public class UserVipChangeEventConsumer implements KKMqConsumerListener<UserVipChangeContext> {

    private final RpUserDetailService rpUserDetailService;

    public UserVipChangeEventConsumer(RpUserDetailService rpUserDetailService) {
        this.rpUserDetailService = rpUserDetailService;
    }

    @Override
    public void onMessage(UserVipChangeContext context) {
        try {
            log.info("用户vip变更事件消费: {}", context);
            rpUserDetailService.handleVipChangeEvent(context);
        } catch (Exception e) {
            log.error("用户vip变更事件消费-异常: {}", context, e);
        }

    }
}
