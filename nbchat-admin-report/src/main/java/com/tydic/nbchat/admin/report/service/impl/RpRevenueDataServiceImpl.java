package com.tydic.nbchat.admin.report.service.impl;

import com.tydic.nbchat.admin.api.bo.eum.OrderStatusEnum;
import com.tydic.nbchat.admin.api.bo.rp.RpRevenueDataRspBO;
import com.tydic.nbchat.admin.api.rp.RpRevenueDataService;
import com.tydic.nbchat.admin.mapper.RpRevenueDataMapper;
import com.tydic.nbchat.admin.mapper.po.PayOrderPO;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
@AllArgsConstructor
public class RpRevenueDataServiceImpl implements RpRevenueDataService {
    private final RpRevenueDataMapper rpRevenueDataMapper;

    @Override
    public Rsp getRpRevenueData(Date beginDate, Date endDate) {
        RpRevenueDataRspBO rspBO = new RpRevenueDataRspBO();
        PayOrderPO payOrderPO = new PayOrderPO();
        payOrderPO.setBeginDate(beginDate);
        payOrderPO.setEndDate(endDate);
        payOrderPO.setPayTime(new Date());
        payOrderPO.setOrderStatus(OrderStatusEnum.PAY_SUCCESS.getCode());
        payOrderPO.setIsValid(EntityValidType.NORMAL.getCode());
        // 查询订单列表(在时间范围内的)
        List<PayOrderPO> list = rpRevenueDataMapper.queryOrderList(payOrderPO);
        if (CollectionUtils.isEmpty(list)) {
            log.info("没有查询到订单数据");
            return BaseRspUtils.createSuccessRsp(rspBO.toString(), "没有查询到订单数据");
        }
        // 计算总营收
        String totalRevenue = String.format("%.2f", list.stream()
                .mapToDouble(order -> order.getTotalPrice() / 100.0)
                .sum());
        List<String> orderNoList = list.stream().map(PayOrderPO::getOrderNo).collect(Collectors.toList());
        //查询sku列表
        List<String> skuIdList = rpRevenueDataMapper.querySkuIdList(orderNoList);
        Map<String, Integer> skuIdMap = skuIdList.stream().collect(Collectors.toMap(Function.identity(), v -> 1, Integer::sum));
        Map<String, Integer> differentVersionUser = new LinkedHashMap<>();
        //查询不同版本的用户数
        skuIdMap.forEach((skuId, count)->{
            String spuName = rpRevenueDataMapper.querySpuName(skuId);
            differentVersionUser.merge(spuName, count, Integer::sum);
        });
        List<String> userIdList = list.stream().map(PayOrderPO::getUserId).collect(Collectors.toList());
        //查询续费用户数
        int renewalUser = rpRevenueDataMapper.queryRenewalUser(userIdList);
        //查询到期用户数
        int expireUser = rpRevenueDataMapper.queryExpireUser(beginDate, endDate);
        //计算用户续费率
        String userRenewalRate = expireUser == 0
                ? "0.00%"
                : String.format("%.2f%%", (double) renewalUser / expireUser * 100);
        rspBO.setTotalRevenue(totalRevenue);
        rspBO.setDifferentVersionUser(differentVersionUser);
        rspBO.setRenewalUser(renewalUser);
        rspBO.setExpireUser(expireUser);
        rspBO.setUserRenewalRate(userRenewalRate);
        log.info(rspBO.toString());
        return BaseRspUtils.createSuccessRsp(rspBO.toString(), "查询成功");
    }
}
