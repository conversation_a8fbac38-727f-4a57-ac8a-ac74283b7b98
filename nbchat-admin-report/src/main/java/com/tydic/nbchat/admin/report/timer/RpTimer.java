package com.tydic.nbchat.admin.report.timer;

import com.tydic.nbchat.admin.api.rp.RpRevenueDataService;
import com.tydic.nbchat.admin.api.rp.RpUserGrowService;
import com.tydic.nbchat.admin.core.config.properties.NbchatAdminConfigProperties;
import com.tydic.nbchat.admin.core.utils.DingtalkUtil;
import com.tydic.nbchat.admin.report.service.RpActivateService;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@EnableScheduling
@Component
public class RpTimer {

    @Resource
    RedisHelper redisHelper;
    @Resource
    private RpActivateService rpService;
    @Resource
    private RpUserGrowService rpUserGrowService;
    @Resource
    private RpRevenueDataService rpRevenueDataService;
    @Resource
    private NbchatAdminConfigProperties nbchatAdminConfigProperties;

    public static final String DINGTALK_PUSH_LOCK_KEY = "DINGTALK_PUSH_LOCK_KEY";

    @Scheduled(cron = "0 0 8 * * ?")
    public void run() {
        if (!nbchatAdminConfigProperties.getDingtalkEnable()) {
            log.info("【推送钉钉运营指标数据】任务关闭");
            return;
        }
        //加锁
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().
                lockKey(DINGTALK_PUSH_LOCK_KEY).
                requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity, 5L, 3 * 60L); //设置锁3分钟，任务执行完自动释放
        try {
            log.info("【推送钉钉运营指标数据】任务开始");
            if (locked) {
                Date sevenDaysAgo = DateTimeUtil.createTime(DateTimeUtil.DateAddDayOfYear(-7), 0, 0, 0);
                Date thirtyDaysAgo = DateTimeUtil.createTime(DateTimeUtil.DateAddDayOfYear(-30), 0, 0, 0);
                Date today = DateTimeUtil.createTime(new Date(), 0, 0, 0);

                String sevenMetric = this.mergeMetric(sevenDaysAgo, today);//七天内
                String thirtyMetric = this.mergeMetric(thirtyDaysAgo, today);//三十天内

                StringBuilder str = new StringBuilder();
                str.append("七天内数据").append(sevenMetric)
                        .append("\n\n三十天内数据").append(thirtyMetric);
                DingtalkUtil.sendMessageWebhook(str.toString(),"op");
            }
        } catch (Exception e) {
            log.error("【推送钉钉运营指标数据】,执行异常:{}", redisLockEntity, e);
        } finally {
            if (locked) {
                redisHelper.unlockLua(redisLockEntity);
            }
        }
    }

    public String mergeMetric(Date begin, Date end) {
        Rsp rsp1 = rpService.calculateUserActivity(begin, end);
        Rsp rsp2 = rpUserGrowService.userGrowUp(begin, end);
        Rsp rsp3 = rpRevenueDataService.getRpRevenueData(begin, end);

        StringBuilder str = new StringBuilder();
        if (rsp1.isSuccess()) {
            str.append(rsp1.getData()).append("\n");
        }
        if (rsp2.isSuccess()) {
            str.append(rsp2.getData()).append("\n");
        }
        if (rsp3.isSuccess()) {
            str.append(rsp3.getData());
        }
        return str.toString();
    }


}
