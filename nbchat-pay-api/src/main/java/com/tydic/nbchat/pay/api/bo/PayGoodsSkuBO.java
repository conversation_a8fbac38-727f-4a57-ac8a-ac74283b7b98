package com.tydic.nbchat.pay.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品sku表(PayGoodsSku)实体类
 *
 * <AUTHOR>
 * @since 2024-06-05 10:36:59
 */
@Data
public class PayGoodsSkuBO implements Serializable {
    private static final long serialVersionUID = 424096601011612015L;
/**
     * 主键
     */
    private String skuId;
/**
     * spu id
     */
    private String spuId;
/**
     * sku名称
     */
    private String skuName;
/**
     * sku描述
     */
    private String skuDesc;
/**
     * 原价/分
     */
    private Integer originPrice;
/**
     * 销售价/分
     */
    private Integer salePrice;
/**
     * 折扣，例如9折为0.9
     */
    private Double discount;
/**
     * 商品状态 0下架 1上架
     */
    private String skuStatus;
/**
     * 库存
     */
    private Integer stock;
/**
     * 销量
     */
    private Integer sales;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 更新时间
     */
    private Date updateTime;
/**
     * 是否有效 0无效 1有效
     */
    private String isValid;

}

