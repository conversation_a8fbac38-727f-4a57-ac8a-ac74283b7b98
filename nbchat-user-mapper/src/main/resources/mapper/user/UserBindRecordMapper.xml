<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tydic.nbchat.user.mapper.UserBindRecordMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.tydic.nbchat.user.mapper.po.UserBindRecord" id="BaseResultMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="phone" column="phone"/>
        <result property="userIdOld" column="user_id_old"/>
        <result property="tenantCodeOld" column="tenant_code_old"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="base_sql">
        id,user_id,tenant_code,phone,user_id_old,tenant_code_old,create_time
    </sql>


    <insert id="insert" parameterType="com.tydic.nbchat.user.mapper.po.UserBindRecord">
        INSERT INTO nbchat_user_bind_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="tenantCode != null">tenant_code,</if>
            <if test="phone != null">phone,</if>
            <if test="userIdOld != null">user_id_old,</if>
            <if test="tenantCodeOld != null">tenant_code_old,</if>
            <if test="createTime != null">create_time</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="tenantCode != null">#{tenantCode},</if>
            <if test="phone != null">#{phone},</if>
            <if test="userIdOld != null">#{userIdOld},</if>
            <if test="tenantCodeOld != null">#{tenantCodeOld},</if>
            <if test="createTime != null">#{createTime}</if>
        </trim>
    </insert>

</mapper>