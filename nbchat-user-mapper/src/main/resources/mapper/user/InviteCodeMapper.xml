<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tydic.nbchat.user.mapper.InviteCodeMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.tydic.nbchat.user.mapper.po.InviteCodePO" id="inviteCodeMap">
        <result property="id" column="id"/>
        <result property="inviteCode" column="phone"/>
        <result property="createdTime" column="created_time"/>
        <result property="expTime" column="exp_time"/>
        <result property="isDeleted" column="is_deleted"/>
    </resultMap>

    <update id="validateInviteCode">
        update nbchat_user as user , nbchat_invite_code as code
        set user.status=2, code.is_deleted=1
        where user.user_id=#{userId} and code.invite_code=#{inviteCode} and code.is_deleted=0
    </update>


    <update id="validateInviteCodeInExpTime">
        update nbchat_user as u , nbchat_invite_code as c
        set u.status=2, c.use_count = c.use_count + 1
        where u.user_id = #{userId}
        and c.invite_code=#{inviteCode}
        and c.is_deleted = 0
        and c.exp_time > NOW()
    </update>

    <select id="checkInviteCodeStatus" resultMap="inviteCodeMap">
        select id,is_deleted from nbchat_invite_code as code where code.invite_code=#{inviteCode}
    </select>

    <select id="selectInviteCode" resultMap="inviteCodeMap">
        select id,is_deleted,exp_time from nbchat_invite_code where invite_code=#{inviteCode}
    </select>

    <update id="updateUseCount">
        update nbchat_invite_code set use_count = use_count + 1 where invite_code=#{inviteCode}
    </update>
</mapper>
