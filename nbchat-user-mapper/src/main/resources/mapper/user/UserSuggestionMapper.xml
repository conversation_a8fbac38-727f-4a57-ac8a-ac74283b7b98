<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.tydic.nbchat.user.mapper.UserSuggestionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.tydic.nbchat.user.mapper.po.UserSuggestion" id="BaseResultMap">
        <result property="suggestId" column="suggest_id"/>
        <result property="userId" column="user_id"/>
        <result property="tenantCode" column="tenant_code"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="username" column="username"/>
        <result property="content" column="content"/>
        <result property="returnVisit" column="return_visit"/>
        <result property="pageSource" column="page_source"/>
        <result property="busiType" column="busi_type"/>
        <result property="createTime" column="create_time"/>
        <result column="issue_type" jdbcType="CHAR" property="issueType" />
        <result column="product_module" jdbcType="CHAR" property="productModule" />
        <result column="attachment" jdbcType="VARCHAR" property="attachment" />
    </resultMap>

    <sql id="base_sql">
        suggest_id,user_id,tenant_code,phone,email,username,content,return_visit,page_source,create_time,, busi_type, issue_type, product_module, attachment
    </sql>

    <insert id="insertSelective" parameterType="com.tydic.nbchat.user.mapper.po.UserSuggestion">
        insert into nbchat_user_suggestion
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="content != null and content != ''">
                content,
            </if>
            <if test="email != null and email != ''">
                email,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="username != null and username != ''">
                username,
            </if>
            <if test="returnVisit != null and returnVisit != ''">
                return_visit,
            </if>
            <if test="pageSource != null and pageSource != ''">
                page_source,
            </if>
            <if test="busiType!= null and busiType != ''">
                busi_type,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="busiType != null">
                busi_type,
            </if>
            <if test="issueType != null">
                issue_type,
            </if>
            <if test="productModule != null">
                product_module,
            </if>
            <if test="attachment != null">
                attachment,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="content != null and content != ''">
                #{content},
            </if>
            <if test="email != null and email != ''">
                #{email},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="username != null and username != ''">
                #{username},
            </if>
            <if test="returnVisit != null and returnVisit != ''">
                #{returnVisit},
            </if>
            <if test="pageSource != null and pageSource != ''">
                #{pageSource},
            </if>
            <if test="busiType!= null and busiType != ''">
                #{busiType},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="busiType != null">
                #{busiType,jdbcType=CHAR},
            </if>
            <if test="issueType != null">
                #{issueType,jdbcType=CHAR},
            </if>
            <if test="productModule != null">
                #{productModule,jdbcType=CHAR},
            </if>
            <if test="attachment != null">
                #{attachment,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>



</mapper>