<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.user.mapper.NbchatUserScoreTaskMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.user.mapper.po.NbchatUserScoreTask">
        <id column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="task_type" property="taskType" jdbcType="CHAR"/>
        <result column="score" property="score" jdbcType="INTEGER"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="exec_time" property="execTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="task_status" property="taskStatus" jdbcType="CHAR"/>
        <result column="task_desc" property="taskDesc" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_valid" property="isValid" jdbcType="CHAR"/>
        <result column="score_type" property="scoreType" jdbcType="VARCHAR"/>

    </resultMap>
    <sql id="Base_Column_List">
        task_id, tenant_code, user_id, task_type, score, order_no, exec_time, expire_time,
    task_status, task_desc, create_time, update_time, is_valid,score_type
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from nbchat_user_score_task
        where task_id = #{taskId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nbchat_user_score_task
        where task_id = #{taskId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserScoreTask">
        insert into nbchat_user_score_task (task_id, tenant_code, user_id,
                                            task_type, score, order_no,
                                            exec_time, expire_time, task_status,
                                            task_desc, create_time, update_time,
                                            is_valid)
        values (#{taskId,jdbcType=BIGINT}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
                #{taskType,jdbcType=CHAR}, #{score,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR},
                #{execTime,jdbcType=TIMESTAMP}, #{expireTime,jdbcType=TIMESTAMP}, #{taskStatus,jdbcType=CHAR},
                #{taskDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{isValid,jdbcType=CHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserScoreTask">
        insert into nbchat_user_score_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                task_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="taskType != null">
                task_type,
            </if>
            <if test="score != null">
                score,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="execTime != null">
                exec_time,
            </if>
            <if test="expireTime != null">
                expire_time,
            </if>
            <if test="taskStatus != null">
                task_status,
            </if>
            <if test="taskDesc != null">
                task_desc,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId,jdbcType=BIGINT},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="taskType != null">
                #{taskType,jdbcType=CHAR},
            </if>
            <if test="score != null">
                #{score,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="execTime != null">
                #{execTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                #{expireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taskStatus != null">
                #{taskStatus,jdbcType=CHAR},
            </if>
            <if test="taskDesc != null">
                #{taskDesc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserScoreTask">
        update nbchat_user_score_task
        <set>
            <if test="score != null">
                score = #{score,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="execTime != null">
                exec_time = #{execTime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime,jdbcType=TIMESTAMP},
            </if>
            <if test="taskStatus != null">
                task_status = #{taskStatus,jdbcType=CHAR},
            </if>
            <if test="taskDesc != null">
                task_desc = #{taskDesc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
        </set>
        where task_id = #{taskId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserScoreTask">
        update nbchat_user_score_task
        set tenant_code = #{tenantCode,jdbcType=VARCHAR},
            user_id     = #{userId,jdbcType=VARCHAR},
            task_type   = #{taskType,jdbcType=CHAR},
            score       = #{score,jdbcType=INTEGER},
            order_no    = #{orderNo,jdbcType=VARCHAR},
            exec_time   = #{execTime,jdbcType=TIMESTAMP},
            expire_time = #{expireTime,jdbcType=TIMESTAMP},
            task_status = #{taskStatus,jdbcType=CHAR},
            task_desc   = #{taskDesc,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            is_valid    = #{isValid,jdbcType=CHAR}
        where task_id = #{taskId,jdbcType=BIGINT}
    </update>

    <select id="selectWaitRechargeTask" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_user_score_task
        where exec_time between
            DATE_SUB(CURDATE(), INTERVAL 1 DAY) and now()
           and task_status = '0' and is_valid = '1'
    </select>

    <insert id="insertBatch" parameterType="list">
        insert into nbchat_user_score_task (tenant_code, user_id,
                                            task_type, score, order_no,
                                            exec_time, expire_time, task_status,
                                            task_desc, create_time, score_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR},
            #{item.taskType,jdbcType=CHAR}, #{item.score,jdbcType=INTEGER}, #{item.orderNo,jdbcType=VARCHAR},
            #{item.execTime,jdbcType=TIMESTAMP}, #{item.expireTime,jdbcType=TIMESTAMP}, #{item.taskStatus,jdbcType=CHAR},
            #{item.taskDesc,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.scoreType,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectByCondition"
            parameterType="com.tydic.nbchat.user.mapper.po.NbchatUserScoreTaskCondition"
            resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from nbchat_user_score_task
        <where>
            <if test="tenantCode != null">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="taskType != null">
                and task_type = #{taskType,jdbcType=CHAR}
            </if>
            <if test="taskStatus != null">
                and task_status = #{taskStatus,jdbcType=CHAR}
            </if>
            <if test="isValid != null">
                and is_valid = #{isValid,jdbcType=CHAR}
            </if>
            <if test="keyword != null">
                and task_desc like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </select>
</mapper>