package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.UserBindRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @date 2023/06/29
 * @email <EMAIL>
 * @description 用户绑定记录映射器
 */
@Mapper
public interface UserBindRecordMapper {
    /**
     * 插入用户绑定记录
     * @param @param userBindRecord 用户绑定记录
     * @return
     */
    void insert(UserBindRecord userBindRecord) ;
}
