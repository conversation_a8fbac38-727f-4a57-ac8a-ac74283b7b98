package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.UserSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserSettingsMapper {
    int insertSelective(UserSettings settings);

    UserSettings selectByUserId(String userId);

    int updateSetting(UserSettings settings);

    List<UserSettings> selectUserSettings(@Param("userIds") List<String> userIds);

    List<UserSettings> selectLikeRobotType(String robotType);
}
