package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.NbchatUserBalance;
import org.apache.ibatis.annotations.Param;

public interface NbchatUserBalanceMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(NbchatUserBalance record);

    int insertSelective(NbchatUserBalance record);

    NbchatUserBalance selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(NbchatUserBalance record);

    NbchatUserBalance selectByUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    /**
     * 查询企业账户
     *
     * @param tenantCode
     * @return
     */
    NbchatUserBalance selectByTenantCode(@Param("tenantCode") String tenantCode);

    int updateBalance(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

}