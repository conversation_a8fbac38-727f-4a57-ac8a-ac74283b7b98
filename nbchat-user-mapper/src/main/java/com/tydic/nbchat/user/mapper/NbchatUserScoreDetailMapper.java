package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.NbchatUserScoreDetail;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface NbchatUserScoreDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(NbchatUserScoreDetail record);

    int insertSelective(NbchatUserScoreDetail record);

    NbchatUserScoreDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(NbchatUserScoreDetail record);

    List<NbchatUserScoreDetail> selectByUserId(@Param("tenantCode") String tenantCode,
                                               @Param("userId") String userId,
                                               @Param("expireTime") Date expireTime);

    //查询可用积分
    List<NbchatUserScoreDetail> selectAvailableScore(@Param("tenantCode") String tenantCode,
                                               @Param("userId") String userId);

    //查询最长过期时间
    Date selectMaxExpireTime(@Param("tenantCode") String tenantCode,
                                               @Param("userId") String userId);

    /**
     * 查询已过期，但是未标记为过期状态的积分
     */
    List<NbchatUserScoreDetail> selectExpiredScore(@Param("nowTime") Date nowTime);

    /**
     * 增加积分
     *
     * @param id
     * @param score
     * @return
     */
    int refundScore(@Param("id") Long id, @Param("score") Integer score);

    /**
     * 扣减积分
     */
    int deductScore(@Param("id") Long id, @Param("score") Integer score);

    List<NbchatUserScoreDetail> queryByCond(NbchatUserScoreDetail record);


    /**
     * 冻结积分
     * @param tenantCode
     * @param userId
     * @param scoreType
     */
    void freezeScore(@Param("tenantCode") String tenantCode,
                     @Param("userId") String userId,
                     @Param("scoreType") String scoreType);

    /**
     * 解冻积分
     * @param tenantCode
     * @param userId
     * @param scoreType
     */
    void unfreezeScore(@Param("tenantCode") String tenantCode,
                       @Param("userId") String userId,
                       @Param("scoreType") String scoreType);


}