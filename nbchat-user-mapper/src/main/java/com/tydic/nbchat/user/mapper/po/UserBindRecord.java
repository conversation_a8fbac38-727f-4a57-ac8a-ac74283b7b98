package com.tydic.nbchat.user.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/06/29
 * @email <EMAIL>
 * @description 用户绑定记录
 */
@Data
public class UserBindRecord implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private String id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 电话
     */
    private String phone;
    /**
     * 旧用户ID
     */
    private String userIdOld;
    /**
     * 租户代码
     */
    private String tenantCode;
    /**
     * 旧租户代码
     */
    private String tenantCodeOld;
    /**
     * 创建时间
     */
    private Date createTime;
}