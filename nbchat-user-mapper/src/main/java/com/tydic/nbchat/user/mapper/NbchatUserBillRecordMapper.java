package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.NbchatUserBillRecord;
import com.tydic.nbchat.user.mapper.po.NbchatUserBillRecordQueryCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NbchatUserBillRecordMapper {
    int insertSelective(NbchatUserBillRecord record);
    NbchatUserBillRecord selectByPrimaryKey(Integer id);
    int updateByPrimaryKeySelective(NbchatUserBillRecord record);
    List<NbchatUserBillRecord> selectByCondition(NbchatUserBillRecordQueryCondition condition);

    NbchatUserBillRecord selectByTradeId(@Param("tradeId") String tradeId,@Param("bizId") String bizId);
}