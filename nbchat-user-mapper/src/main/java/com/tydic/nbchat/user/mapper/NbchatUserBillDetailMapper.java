package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.NbchatUserBillDetail;

import java.util.List;

public interface NbchatUserBillDetailMapper {
    int deleteByPrimaryKey(Long id);

    int insert(NbchatUserBillDetail record);

    int insertSelective(NbchatUserBillDetail record);

    NbchatUserBillDetail selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(NbchatUserBillDetail record);

    int updateByPrimaryKey(NbchatUserBillDetail record);

    int insertBatch(List<NbchatUserBillDetail> list);

    List<NbchatUserBillDetail> selectByTradeId(String tradeId);
}