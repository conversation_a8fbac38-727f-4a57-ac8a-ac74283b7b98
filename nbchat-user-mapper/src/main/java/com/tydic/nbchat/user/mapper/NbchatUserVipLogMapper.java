package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.NbchatUserVipLog;

import java.util.List;

/**
 * 用户vip开通记录(NbchatUserVipLog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-20 10:30:35
 */
public interface NbchatUserVipLogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NbchatUserVipLog queryById(Integer id);

    List<NbchatUserVipLog> selectAll(NbchatUserVipLog nbchatUserVipLog);

    /**
     * 新增数据
     *
     * @param nbchatUserVipLog 实例对象
     * @return 影响行数
     */
    int insert(NbchatUserVipLog nbchatUserVipLog);


    int insertSelective(NbchatUserVipLog nbchatUserVipLog);

      /**
     * 修改数据
     *
     * @param nbchatUserVipLog 实例对象
     * @return 影响行数
     */
    int update(NbchatUserVipLog nbchatUserVipLog);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

}

