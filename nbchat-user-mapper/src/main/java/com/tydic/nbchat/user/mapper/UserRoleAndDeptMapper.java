package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.UserDept;
import com.tydic.nbchat.user.mapper.po.UserInfoInTenant;
import com.tydic.nbchat.user.mapper.po.UserPost;
import com.tydic.nbchat.user.mapper.po.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserRoleAndDeptMapper {

    /**
     * 查询用户角色
     * @param userId
     * @return
     */
    List<UserRole> selectUserRoles(@Param("subsystem") String subsystem, @Param("userId") String userId);

    /**
     * 查询用户部门
     * @param userId
     * @return
     */
    List<UserDept> selectUserDepts(@Param("tenantCode") String tenantCode, @Param("userId")String userId);

    /**
     * 查询用户岗位
     * @param userId
     * @param tenantCode
     * @return
     */
    List<UserPost> selectUserPosts(@Param("tenantCode") String tenantCode, @Param("userId")String userId);

    /**
     * 插入默认部门关系
     * @param userId
     * @param tenantCode
     * @return
     */
    int insertDefaultDeptRel(@Param("tenantCode") String tenantCode, @Param("userId")String userId);

    /**
     * 更新术语在线首选角色
     * @param subsystem
     * @param userId
     * @param role
     * @return
     */
    int updateTmoRole(@Param("subsystem") String subsystem,
                      @Param("userId")String userId,
                      @Param("role")String role);

    /**
     * 查询租户下的用户信息
     * @param userId
     * @param tenantCode
     * @return
     */
    UserInfoInTenant selectUserInTenant(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    /**
     * 更新用户在租户下的信息
     * @param userId
     * @param tenantCode
     * @param realName
     * @param avatarUrl
     * @return
     */
    int updateUserInTenant(@Param("userId") String userId,
                           @Param("tenantCode") String tenantCode,
                           @Param("realName") String realName,
                           @Param("avatarUrl") String avatarUrl);
}
