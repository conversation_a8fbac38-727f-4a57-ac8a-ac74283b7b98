package com.tydic.nbchat.user.mapper;

import com.tydic.nbchat.user.mapper.po.NbchatUserVipRights;
import com.tydic.nbchat.user.mapper.po.UserRightFree;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户权益赠送记录表(NbchatUserVipRights)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-19 14:42:37
 */
public interface NbchatUserVipRightsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NbchatUserVipRights queryById(Integer id);

    List<NbchatUserVipRights> selectAll(NbchatUserVipRights nbchatUserVipRights);


    int insertSelective(NbchatUserVipRights nbchatUserVipRights);

      /**
     * 修改数据
     *
     * @param nbchatUserVipRights 实例对象
     * @return 影响行数
     */
    int update(NbchatUserVipRights nbchatUserVipRights);
    int updateRightsUsed(NbchatUserVipRights nbchatUserVipRights);

    NbchatUserVipRights queryRights(NbchatUserVipRights nbchatUserVipRights);

    NbchatUserVipRights queryRightsByType(NbchatUserVipRights nbchatUserVipRights);

    List<NbchatUserVipRights> queryAllRights(NbchatUserVipRights nbchatUserVipRights);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);
    int updateFinish();
    int updateExpire();

    /**
     * 统计用户权益
     *
     * @param tenantCode 租户编码
     * @param userId 用户ID
     * @return 权益统计结果
     */
    List<UserRightFree> countFreeRights(@Param("tenantCode") String tenantCode,
                                        @Param("userId") String userId);
}

