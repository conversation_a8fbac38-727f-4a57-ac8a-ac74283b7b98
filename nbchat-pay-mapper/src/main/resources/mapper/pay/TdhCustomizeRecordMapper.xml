<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.pay.mapper.TdhCustomizeRecordMapper">

    <resultMap type="com.tydic.nbchat.pay.mapper.po.TdhCustomizeRecord" id="TdhCustomizeRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="orderStatus" column="order_status" jdbcType="VARCHAR"/>
        <result property="originPrice" column="origin_price" jdbcType="INTEGER"/>
        <result property="orderPrice" column="order_price" jdbcType="INTEGER"/>
        <result property="payPrice" column="pay_price" jdbcType="INTEGER"/>
        <result property="customizeType" column="customize_type" jdbcType="VARCHAR"/>
        <result property="customizeStatus" column="customize_status" jdbcType="VARCHAR"/>
        <result property="identityUrl" column="identity_url" jdbcType="VARCHAR"/>
        <result property="voiceUrl" column="voice_url" jdbcType="VARCHAR"/>
        <result property="voiceName" column="voice_name" jdbcType="VARCHAR"/>
        <result property="voiceDemo" column="voice_demo" jdbcType="VARCHAR"/>
        <result property="volcId" column="volc_id" jdbcType="VARCHAR"/>
        <result property="tdhImg" column="tdh_img" jdbcType="VARCHAR"/>
        <result property="tdhId" column="tdh_id" jdbcType="VARCHAR"/>
        <result property="tdhName" column="tdh_name" jdbcType="VARCHAR"/>
        <result property="tdhDemo" column="tdh_demo" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="VARCHAR"/>
        <result property="poseType" column="pose_type" jdbcType="VARCHAR"/>
        <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
        <result property="communication" column="communication" jdbcType="VARCHAR"/>
        <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
        <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="skuId" column="sku_id" jdbcType="VARCHAR"/>
        <result property="progressing" column="progressing" jdbcType="VARCHAR"/>
        <result column="first_view_time" jdbcType="TIMESTAMP" property="firstViewTime"/>

    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
id, user_id, tenant_code, order_no, order_status, origin_price, order_price, pay_price, customize_type, customize_status,
  identity_url, voice_url, voice_name, voice_demo, volc_id, tdh_img, tdh_id, tdh_name, tdh_demo, gender, pose_type,
  video_url, communication, start_time, end_time, create_time, update_time, update_user, is_valid,sku_id,progressing,first_view_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="TdhCustomizeRecordMap">
        select
          <include refid="Base_Column_List" />
        from tdh_customize_record
        where id = #{id}
    </select>

    <select id="queryByOrderId" resultMap="TdhCustomizeRecordMap">
        select
          <include refid="Base_Column_List" />
        from tdh_customize_record
        where order_no = #{orderNo}
    </select>


    <select id="selectAll" resultMap="TdhCustomizeRecordMap" parameterType="com.tydic.nbchat.pay.mapper.po.TdhCustomizeRecord">
        select
          <include refid="Base_Column_List" />
        from tdh_customize_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and order_status = #{orderStatus}
            </if>
            <if test="originPrice != null">
                and origin_price = #{originPrice}
            </if>
            <if test="orderPrice != null">
                and order_price = #{orderPrice}
            </if>
            <if test="payPrice != null">
                and pay_price = #{payPrice}
            </if>
            <if test="customizeType != null and customizeType != ''">
                and customize_type = #{customizeType}
            </if>
            <if test="customizeStatus != null and customizeStatus != ''">
                and customize_status = #{customizeStatus}
            </if>
            <if test="identityUrl != null and identityUrl != ''">
                and identity_url = #{identityUrl}
            </if>
            <if test="voiceUrl != null and voiceUrl != ''">
                and voice_url = #{voiceUrl}
            </if>
            <if test="voiceName != null and voiceName != ''">
                and voice_name = #{voiceName}
            </if>
            <if test="voiceDemo != null and voiceDemo != ''">
                and voice_demo = #{voiceDemo}
            </if>
            <if test="volcId != null and volcId != ''">
                and volc_id = #{volcId}
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                and tdh_img = #{tdhImg}
            </if>
            <if test="tdhId != null and tdhId != ''">
                and tdh_id = #{tdhId}
            </if>
            <if test="tdhName != null and tdhName != ''">
                and tdh_name = #{tdhName}
            </if>
            <if test="tdhDemo != null and tdhDemo != ''">
                and tdh_demo = #{tdhDemo}
            </if>
            <if test="gender != null and gender != ''">
                and gender = #{gender}
            </if>
            <if test="poseType != null and poseType != ''">
                and pose_type = #{poseType}
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                and video_url = #{videoUrl}
            </if>
            <if test="communication != null and communication != ''">
                and communication = #{communication}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.pay.mapper.po.TdhCustomizeRecord">
        insert into tdh_customize_record
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="orderNo != null and orderNo != ''">
                order_no,
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                order_status,
            </if>
            <if test="originPrice != null">
                origin_price,
            </if>
            <if test="orderPrice != null">
                order_price,
            </if>
            <if test="payPrice != null">
                pay_price,
            </if>
            <if test="customizeType != null and customizeType != ''">
                customize_type,
            </if>
            <if test="customizeStatus != null and customizeStatus != ''">
                customize_status,
            </if>
            <if test="identityUrl != null and identityUrl != ''">
                identity_url,
            </if>
            <if test="voiceUrl != null and voiceUrl != ''">
                voice_url,
            </if>
            <if test="voiceName != null and voiceName != ''">
                voice_name,
            </if>
            <if test="voiceDemo != null and voiceDemo != ''">
                voice_demo,
            </if>
            <if test="volcId != null and volcId != ''">
                volc_id,
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                tdh_img,
            </if>
            <if test="tdhId != null and tdhId != ''">
                tdh_id,
            </if>
            <if test="tdhName != null and tdhName != ''">
                tdh_name,
            </if>
            <if test="tdhDemo != null and tdhDemo != ''">
                tdh_demo,
            </if>
            <if test="gender != null and gender != ''">
                gender,
            </if>
            <if test="poseType != null and poseType != ''">
                pose_type,
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                video_url,
            </if>
            <if test="communication != null and communication != ''">
                communication,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="orderNo != null and orderNo != ''">
                #{orderNo},
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                #{orderStatus},
            </if>
            <if test="originPrice != null">
                #{originPrice},
            </if>
            <if test="orderPrice != null">
                #{orderPrice},
            </if>
            <if test="payPrice != null">
                #{payPrice},
            </if>
            <if test="customizeType != null and customizeType != ''">
                #{customizeType},
            </if>
            <if test="customizeStatus != null and customizeStatus != ''">
                #{customizeStatus},
            </if>
            <if test="identityUrl != null and identityUrl != ''">
                #{identityUrl},
            </if>
            <if test="voiceUrl != null and voiceUrl != ''">
                #{voiceUrl},
            </if>
            <if test="voiceName != null and voiceName != ''">
                #{voiceName},
            </if>
            <if test="voiceDemo != null and voiceDemo != ''">
                #{voiceDemo},
            </if>
            <if test="volcId != null and volcId != ''">
                #{volcId},
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                #{tdhImg},
            </if>
            <if test="tdhId != null and tdhId != ''">
                #{tdhId},
            </if>
            <if test="tdhName != null and tdhName != ''">
                #{tdhName},
            </if>
            <if test="tdhDemo != null and tdhDemo != ''">
                #{tdhDemo},
            </if>
            <if test="gender != null and gender != ''">
                #{gender},
            </if>
            <if test="poseType != null and poseType != ''">
                #{poseType},
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                #{videoUrl},
            </if>
            <if test="communication != null and communication != ''">
                #{communication},
            </if>
            <if test="startTime != null">
                #{startTime},
            </if>
            <if test="endTime != null">
                #{endTime},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="updateUser != null and updateUser != ''">
                #{updateUser},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into tdh_customize_record(user_id,tenant_code,order_no,order_status,origin_price,order_price,pay_price,customize_type,customize_status,identity_url,voice_url,voice_name,voice_demo,volc_id,tdh_img,tdh_id,tdh_name,tdh_demo,gender,pose_type,video_url,communication,start_time,end_time,create_time,update_time,update_user,is_valid)
        values (#{userId}#{tenantCode}#{orderNo}#{orderStatus}#{originPrice}#{orderPrice}#{payPrice}#{customizeType}#{customizeStatus}#{identityUrl}#{voiceUrl}#{voiceName}#{voiceDemo}#{volcId}#{tdhImg}#{tdhId}#{tdhName}#{tdhDemo}#{gender}#{poseType}#{videoUrl}#{communication}#{startTime}#{endTime}#{createTime}#{updateTime}#{updateUser}#{isValid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update tdh_customize_record
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                order_status = #{orderStatus},
            </if>
            <if test="originPrice != null">
                origin_price = #{originPrice},
            </if>
            <if test="orderPrice != null">
                order_price = #{orderPrice},
            </if>
            <if test="payPrice != null">
                pay_price = #{payPrice},
            </if>
            <if test="customizeType != null and customizeType != ''">
                customize_type = #{customizeType},
            </if>
            <if test="customizeStatus != null and customizeStatus != ''">
                customize_status = #{customizeStatus},
            </if>
            <if test="identityUrl != null and identityUrl != ''">
                identity_url = #{identityUrl},
            </if>
            <if test="voiceUrl != null and voiceUrl != ''">
                voice_url = #{voiceUrl},
            </if>
            <if test="voiceName != null and voiceName != ''">
                voice_name = #{voiceName},
            </if>
            <if test="voiceDemo != null and voiceDemo != ''">
                voice_demo = #{voiceDemo},
            </if>
            <if test="volcId != null and volcId != ''">
                volc_id = #{volcId},
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                tdh_img = #{tdhImg},
            </if>
            <if test="tdhId != null and tdhId != ''">
                tdh_id = #{tdhId},
            </if>
            <if test="tdhName != null and tdhName != ''">
                tdh_name = #{tdhName},
            </if>
            <if test="tdhDemo != null and tdhDemo != ''">
                tdh_demo = #{tdhDemo},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender},
            </if>
            <if test="poseType != null and poseType != ''">
                pose_type = #{poseType},
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                video_url = #{videoUrl},
            </if>
            <if test="communication != null and communication != ''">
                communication = #{communication},
            </if>
            <if test="startTime != null">
                start_time = #{startTime},
            </if>
            <if test="endTime != null">
                end_time = #{endTime},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
        </set>
        where order_no = #{orderNo}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from tdh_customize_record where id = #{id}
    </delete>

    <update id="updateById">
        update tdh_customize_record set order_no = #{orderNo}, create_time = now() where id = #{id}
    </update>

    <update id="updateHumanOrderNo">
        update tdh_virtual_human set order_no = #{newOrderNo}, create_time = now() where order_no = #{orderNo}
    </update>

    <update id="updateVoiceOrderNo">
        update tdh_virtual_anchor set order_no = #{newOrderNo}, create_time = now() where order_no = #{orderNo}
    </update>

</mapper>

