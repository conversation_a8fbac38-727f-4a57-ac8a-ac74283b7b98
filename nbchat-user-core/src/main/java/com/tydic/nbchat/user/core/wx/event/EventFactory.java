package com.tydic.nbchat.user.core.wx.event;

import com.tydic.nbchat.user.core.wx.event.api.EventHandler;
import com.tydic.nbchat.user.core.wx.context.WxExtMsgContext;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class EventFactory {

    protected final Map<String, EventHandler> eventMap = new HashMap<>();

    public EventFactory(List<EventHandler> eventHandlers){
        this.initEventMap(eventHandlers);
    }

    private void initEventMap(List<EventHandler> eventHandlers){
        eventHandlers.forEach(eventHandler -> {
            eventMap.put(eventHandler.event(), eventHandler);
        });
    }

    protected EventHandler getEventHandler(String event){
        return eventMap.get(event);
    }

    public Rsp handle(WxExtMsgContext context){
        EventHandler eventHandler = getEventHandler(context.getEvent());
        if(eventHandler == null){
            log.warn("未找到对应的事件处理器:{}",context);
            return BaseRspUtils.createErrorRsp("未找到对应的事件处理器");
        }
        return eventHandler.handle(context);

    }


}
