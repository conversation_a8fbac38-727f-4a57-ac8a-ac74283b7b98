package com.tydic.nbchat.user.core.service.sms.strategy;

import com.tydic.nbchat.user.api.bo.constants.AreaCodeConstants;
import com.tydic.nbchat.user.api.bo.eums.SmsTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 区号模板策略类
 * 根据区号选择合适的短信模板
 */
@Slf4j
@Component
public class AreaCodeTemplateStrategy implements TemplateStrategy {

    private final String areaCode;
    private final Map<String, String> templateCodeToIdMap;
    private final TemplateStrategy fallbackStrategy;

    /**
     * 区号模板映射
     * 外层Map: 区号 -> 内层Map
     * 内层Map: 原始模板ID -> 区号特定模板ID
     */
    private final Map<String, Map<String, String>> areaCodeTemplateMap = new ConcurrentHashMap<>();

    /**
     * 默认构造函数，用于Spring注入
     */
    public AreaCodeTemplateStrategy() {
        this.areaCode = null;
        this.templateCodeToIdMap = new ConcurrentHashMap<>();
        this.fallbackStrategy = null;
    }

    /**
     * 带参数的构造函数，用于策略模式
     * @param areaCode 区号
     * @param templates 模板映射
     * @param fallbackStrategy 回退策略
     */
    public AreaCodeTemplateStrategy(String areaCode, Map<String, String> templates, TemplateStrategy fallbackStrategy) {
        this.areaCode = areaCode;
        this.templateCodeToIdMap = new ConcurrentHashMap<>(templates);
        this.fallbackStrategy = fallbackStrategy;
    }

    @PostConstruct
    public void init() {
        // 初始化香港区号(852)的模板映射
        Map<String, String> hkTemplateMap = new HashMap<>();
        // 登录验证码模板
        hkTemplateMap.put(SmsTemplateEnum.LOGIN.getTemplateID(), SmsTemplateEnum.LOGIN.getInternationalTemplateID());
        // 租户申请模板
        hkTemplateMap.put(SmsTemplateEnum.TENANT_APPLY.getTemplateID(), SmsTemplateEnum.TENANT_APPLY.getInternationalTemplateID());
        // 会员相关模板
        hkTemplateMap.put(SmsTemplateEnum.VIP_OPEN.getTemplateID(), SmsTemplateEnum.VIP_OPEN.getInternationalTemplateID());
        hkTemplateMap.put(SmsTemplateEnum.VIP_CLOSE.getTemplateID(), SmsTemplateEnum.VIP_CLOSE.getInternationalTemplateID());
        hkTemplateMap.put(SmsTemplateEnum.VIP_RENEW.getTemplateID(), SmsTemplateEnum.VIP_RENEW.getInternationalTemplateID());

        // 将香港模板映射添加到区号模板映射中
        areaCodeTemplateMap.put("852", hkTemplateMap);
        log.info("区号模板策略初始化完成，支持的区号数量: {}", areaCodeTemplateMap.size());
    }

    /**
     * 根据区号和原始模板ID获取适合的模板ID
     * @param areaCode 区号
     * @param originalTemplateId 原始模板ID
     * @return 适合该区号的模板ID，如果没有特定模板则返回原始模板ID
     */
    public String getTemplateId(String areaCode, String originalTemplateId) {
        if (StringUtils.isBlank(areaCode) || StringUtils.isBlank(originalTemplateId)) {
            return originalTemplateId;
        }

        // 如果是中国大陆区号，直接返回原始模板ID
        if (AreaCodeConstants.CHINA_MAINLAND.equals(areaCode)) {
            return originalTemplateId;
        }

        // 查找区号特定的模板映射
        Map<String, String> templateMap = areaCodeTemplateMap.get(areaCode);
        if (templateMap != null && templateMap.containsKey(originalTemplateId)) {
            String areaSpecificTemplateId = templateMap.get(originalTemplateId);
            log.debug("使用区号特定模板: 区号={}, 原始模板ID={}, 区号特定模板ID={}",
                    areaCode, originalTemplateId, areaSpecificTemplateId);
            return areaSpecificTemplateId;
        }

        // 如果没有找到区号特定的模板，尝试使用国际模板（暂时为香港模板）
        String internationalTemplateId = SmsTemplateEnum.getInternationalTemplateId(originalTemplateId);
        if (StringUtils.isNotBlank(internationalTemplateId)) {
            log.debug("使用国际模板: 区号={}, 原始模板ID={}, 国际模板ID={}",
                    areaCode, originalTemplateId, internationalTemplateId);
            return internationalTemplateId;
        }

        // 如果没有找到任何适合的模板，返回原始模板ID
        return originalTemplateId;
    }

    /**
     * 添加区号特定的模板映射
     * @param areaCode 区号
     * @param originalTemplateId 原始模板ID
     * @param areaSpecificTemplateId 区号特定的模板ID
     */
    public void addTemplateMapping(String areaCode, String originalTemplateId, String areaSpecificTemplateId) {
        if (StringUtils.isBlank(areaCode) || StringUtils.isBlank(originalTemplateId) ||
                StringUtils.isBlank(areaSpecificTemplateId)) {
            return;
        }

        Map<String, String> templateMap = areaCodeTemplateMap.computeIfAbsent(areaCode, k -> new HashMap<>());
        templateMap.put(originalTemplateId, areaSpecificTemplateId);
        log.info("添加区号特定模板映射: 区号={}, 原始模板ID={}, 区号特定模板ID={}",
                areaCode, originalTemplateId, areaSpecificTemplateId);
    }

    // 实现TemplateStrategy接口的方法
    @Override
    public String getTemplateId(String templateCode) {
        if (StringUtils.isBlank(templateCode)) {
            return templateCode;
        }

        // 如果有区号特定的模板，则使用
        if (templateCodeToIdMap.containsKey(templateCode)) {
            String templateId = templateCodeToIdMap.get(templateCode);
            log.debug("使用区号特定模板: 区号={}, 模板代码={}, 模板ID={}",
                    areaCode, templateCode, templateId);
            return templateId;
        }

        // 否则使用回退策略
        if (fallbackStrategy != null) {
            return fallbackStrategy.getTemplateId(templateCode);
        }

        return templateCode;
    }

    @Override
    public String getAreaCode() {
        return areaCode;
    }

    @Override
    public boolean isApplicable(String areaCode) {
        return StringUtils.isNotBlank(areaCode) &&
               (this.areaCode != null && this.areaCode.equals(areaCode));
    }
}
