package com.tydic.nbchat.user.core.service.impl;

import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.bo.*;
import com.tydic.nbchat.user.api.bo.eums.AuthType;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nbchat.user.api.bo.exception.UserLoginException;
import com.tydic.nbchat.user.core.busi.*;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.mapper.InviteCodeMapper;
import com.tydic.nbchat.user.mapper.po.InviteCodePO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/03/29
 * @email <EMAIL>
 * @description 用户api服务
 */
@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class NbchatUserApiServiceImpl implements NbchatUserApi {

    @Resource
    private InviteCodeMapper inviteCodeMapper;

    private final UserSettingHelper userSettingHelper;
    private final UserLoginEventService userLoginEventService;
    private final UserInfoBusiService userInfoBusiService;
    private final UserWchatLoginBusiService userWchatLoginBusiService;
    private final UserPhoneLoginBusiService userPhoneLoginBusiService;
    private final UserPhoneBindBusiService userPhoneBindBusiService;

    public NbchatUserApiServiceImpl(InviteCodeMapper inviteCodeMapper,
                                    UserSettingHelper userSettingHelper,
                                    UserLoginEventService userLoginEventService,
                                    UserInfoBusiService userInfoBusiService,
                                    UserWchatLoginBusiService userWchatLoginBusiService,
                                    UserPhoneLoginBusiService userPhoneLoginBusiService,
                                    UserPhoneBindBusiService userPhoneBindBusiService) {
        this.inviteCodeMapper = inviteCodeMapper;
        this.userSettingHelper = userSettingHelper;
        this.userLoginEventService = userLoginEventService;
        this.userInfoBusiService = userInfoBusiService;
        this.userWchatLoginBusiService = userWchatLoginBusiService;
        this.userPhoneLoginBusiService = userPhoneLoginBusiService;
        this.userPhoneBindBusiService = userPhoneBindBusiService;
    }

    @Override
    public void cleanCache(String tenantCode, String userId) {
        userSettingHelper.cleanUserCache(tenantCode, userId);
    }

    /**
     * 通过用户ID获取用户信息
     *
     * @param @param userId 用户id
     * @return @return {@link Rsp }<{@link NbchatUserInfo }>
     */
    @Override
    public Rsp<NbchatUserInfo> getUserInfo(String userId) {
        NbchatUserInfo userInfo = userInfoBusiService.getUserInfo(userId);
        if (userInfo == null) {
            return BaseRspUtils.createErrorRsp("查询失败:用户信息不存在！");
        }
        return BaseRspUtils.createSuccessRsp(userInfo, "查询成功");
    }

    @Override
    public Rsp<NbchatUserInfo> getUserInfoByPlatform(String userId, String platform) {
        try {
            NbchatUserInfo userInfo = userInfoBusiService.getUserInfo(userId,platform);
            if (userInfo == null) {
                return BaseRspUtils.createErrorRsp("查询失败:用户信息不存在！");
            }
            return BaseRspUtils.createSuccessRsp(userInfo, "查询成功");
        } catch (Exception e) {
            log.error("获取用户信息异常:{}", userId, e);
            return BaseRspUtils.createErrorRsp("获取用户信息异常");
        }
    }

    /**
     * 填写邀请码
     *
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Deprecated
    @Override
    public Rsp bindInviteCode(BindInviteCodeReqBO reqBO) {
        String userId = reqBO.getUserId();
        String inviteCode = reqBO.getInviteCode();
        InviteCodePO inviteCodePO = new InviteCodePO();
        BeanUtils.copyProperties(reqBO, inviteCodePO);
        log.info("验证邀请码-开始:{}", reqBO);
        if (StringUtils.isEmpty(userId) || StringUtils.isEmpty(inviteCode)) {
            log.warn("验证邀请码-填写邀请码为空:{}", userId);
            return BaseRspUtils.createErrorRsp("邀请码不能为空");
        }
        //验证邀请码是否被使用
        InviteCodePO invite = inviteCodeMapper.selectInviteCode(inviteCode);
        if (invite == null) {
            log.warn("验证邀请码-邀请码不存在:{}", userId);
            return BaseRspUtils.createErrorRsp("请填写正确的邀请码");
        }
        if (invite.getIsDeleted()) {
            log.warn("验证邀请码-邀请码已被使用:{}|{}", userId, inviteCode);
            return BaseRspUtils.createErrorRsp("邀请码已使用");
        }
        NbchatUserInfo userInfo = userInfoBusiService.getUserBaseInfo(userId);
        if (userInfo == null) {
            log.warn("验证邀请码-用户不存在:{}|{}", userId, inviteCode);
            return BaseRspUtils.createErrorRsp("请使用已注册的账号进行绑定");
        }
        //判断用户的status
        if (userInfo.getStatus() == 2) {
            log.warn("验证邀请码-重复消费邀请码:{}|{}", userId, inviteCode);
            return BaseRspUtils.createErrorRsp("请勿重复填写邀请码");
        }
        int rowsAffected = 0;
        try {
            //验证邀请码
            if (invite.getExpTime() == null) {
                //一次性使用邀请码
                rowsAffected = inviteCodeMapper.validateInviteCode(userId, inviteCode);
            } else {
                //有效期内多次使用邀请码
                rowsAffected = inviteCodeMapper.validateInviteCodeInExpTime(userId, inviteCode);
            }
            if (rowsAffected > 0) {
                log.info("验证邀请码-验证码邀请码成功:{}|{}", userId, inviteCode);
                userSettingHelper.removeInfo(userId);
                return BaseRspUtils.createSuccessRsp(userId, "验证邀请码成功");
            }
        } catch (Exception e) {
            log.error("验证邀请码-验证邀请码异常:{}|{}", userId, inviteCode);
        }
        return BaseRspUtils.createErrorRsp("验证邀请码失败");
    }


    @Override
    public Rsp<NbchatUserInfo> authUser(AuthUserReqBO authUserReqBO) {
        String type = authUserReqBO.getAuthType();
        String loginClient = authUserReqBO.getLoginClient();
        log.info("用户认证[{}]-开始: {}", type, authUserReqBO);
        NbchatUserInfo userInfo = null;
        try {
            if (AuthType.PHONE.getCode().equals(type)) {
                //手机号验证码登录
                userInfo = userPhoneLoginBusiService.mobileLogin(authUserReqBO);
            } else if (AuthType.WCHAT_MP_TDH_PHONE.getCode().equals(type)) {
                //小程序手机号一键登录
                loginClient = JoinTenantType.TDH_MINI_APP.getName();
                userInfo = userPhoneLoginBusiService.mobileMpLogin(authUserReqBO);
            } else if (AuthType.WCHAT.getCode().equals(type) ||
                    AuthType.WCHAT_PC.getCode().equals(type) ||
                    AuthType.WCHAT_PC_OFFICIAL.getCode().equals(type) ||
                    AuthType.WCHAT_MP_TDH.getCode().equals(type)) {
                log.info("用户认证[{}]-微信登录: {}", type, authUserReqBO);
                userInfo = userWchatLoginBusiService.wechatLogin(authUserReqBO);
                userInfo.setStatus(2);
            } else {
                log.warn("用户认证[{}]-未知认证类型: {}", type, authUserReqBO);
                return BaseRspUtils.createErrorRsp("未知认证类型");
            }
            log.info("用户认证[{}]-成功: {}", type, userInfo.getUserId());
            userLoginEventService.sendLoginEvent(userInfo,type,"",loginClient,"");
            return BaseRspUtils.createSuccessRsp(userInfo, "认证成功");
        } catch (UserLoginException e) {
            log.warn("用户认证[{}]-失败: {}", type, authUserReqBO);
            return BaseRspUtils.createErrorRsp(e.getMessage());
        } catch (Exception e1) {
            log.error("用户认证[{}]-异常: {}", type, authUserReqBO, e1);
            return BaseRspUtils.createErrorRsp("认证失败: 服务器内部错误!");
        }
    }



    /**
     * 微信用户绑定手机号
     * @param @param BindPhoneNumberReqBO 绑定电话号码申请bo
     * @return @return {@link Rsp }
     */
    public Rsp bindPhoneNumber(BindPhoneNumberReqBO reqBO) {
        return userPhoneBindBusiService.bindPhoneNumber(reqBO);
    }

    public Rsp bindPhoneByMPCode(BindPhoneNumberReqBO reqBO) {
        return userPhoneBindBusiService.bindPhoneByMPCode(reqBO);
    }

    @Override
    public Rsp updateById(UserBO userBO) {
        return userInfoBusiService.updateById(userBO);
    }

    @Override
    public Rsp<NbchatUserInfo> getUserByPhone(String phone) {
        return userInfoBusiService.getUserByPhone(phone);
    }

    @Override
    public RspList<NbchatUserInfo> getUserInfoList(List<String> userIdList) {
        return userInfoBusiService.getUserInfoList(userIdList);
    }

}
