package com.tydic.nbchat.user.core.service.trade;

import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.TradeBusiCodeDefine;
import com.tydic.nbchat.user.api.bo.eums.TradePayType;
import com.tydic.nbchat.user.api.bo.trade.*;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nbchat.user.core.busi.*;
import com.tydic.nbchat.user.core.config.NbchatUserConfigProperties;
import com.tydic.nbchat.user.mapper.CommonMapper;
import com.tydic.nbchat.user.mapper.NbchatUserBalanceMapper;
import com.tydic.nbchat.user.mapper.po.NbchatUserBalance;
import com.tydic.nicc.common.nbchat.emus.MakeEventType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;

@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class TradeBalanceServiceImpl implements TradeBalanceApi {

    @Resource
    private NbchatUserBalanceMapper nbchatUserBalanceMapper;
    @Resource
    private CommonMapper commonMapper;

    private final ScoreBalanceService scoreBalanceService;
    private final ScoreDeductService scoreDeductService;
    private final ScoreRechargeService scoreRechargeService;
    private final ScoreRefundService scoreRefundService;
    private final RedisHelper redisHelper;
    private final UserMakeEventSender userMakeEventSender;
    private final NbchatUserConfigProperties nbchatUserConfigProperties;

    public TradeBalanceServiceImpl(ScoreBalanceService scoreBalanceService,
                                   ScoreDeductService scoreDeductService,
                                   ScoreRechargeService scoreRechargeService,
                                   ScoreRefundService scoreRefundService,
                                   RedisHelper redisHelper,
                                   UserMakeEventSender userMakeEventSender,
                                   NbchatUserConfigProperties nbchatUserConfigProperties) {
        this.scoreBalanceService = scoreBalanceService;
        this.scoreDeductService = scoreDeductService;
        this.scoreRechargeService = scoreRechargeService;
        this.scoreRefundService = scoreRefundService;
        this.redisHelper = redisHelper;
        this.userMakeEventSender = userMakeEventSender;
        this.nbchatUserConfigProperties = nbchatUserConfigProperties;
    }

    @Override
    public Rsp<UserBalanceBO> getBalance(String tenantCode, String userId) {
        if (!nbchatUserConfigProperties.getScoreDeductEnable()) {
            //未开启算力点体系，返回默认账户
            UserBalanceBO balance = new UserBalanceBO();
            balance.setUserId(userId);
            balance.setTenantCode(tenantCode);
            balance.setScore(0);
            balance.setDetails(new ArrayList<>());
            UserBalanceBO tenantBalance = new UserBalanceBO();
            BeanUtils.copyProperties(balance, tenantBalance);
            balance.setTenantBalance(tenantBalance);
            return BaseRspUtils.createSuccessRsp(balance);
        }
        Rsp<UserBalanceBO> balance = scoreBalanceService.getBalance(tenantCode, userId);
        if (balance.isSuccess() && StringUtils.isNotBlank(tenantCode) && !tenantCode.equals(userId)) {
            //查询当前租户余额
            balance.getData().setTenantBalance(scoreBalanceService.getBalance(tenantCode, tenantCode).getData());
        }
        return balance;
    }


    private void sendMakeEvent(UserBalanceDeductReqBO deduct){
        if (TradeBusiCodeDefine.VIDEO_CREATION.getCode().equals(deduct.getBizCode())) {
            userMakeEventSender.sendMakeEvent(deduct.getTenantCode(), deduct.getUserId(),
                    MakeEventType.MAKE_TDH.getCode(), 1, 0);
        } else if (TradeBusiCodeDefine.PPT_CREATION.getCode().equals(deduct.getBizCode())) {
            userMakeEventSender.sendMakeEvent(deduct.getTenantCode(), deduct.getUserId(),
                    MakeEventType.MAKE_PPT.getCode(), 1, 0);
        } else if (TradeBusiCodeDefine.EXAM_CREATION.getCode().equals(deduct.getBizCode())) {
            //试题次数
            userMakeEventSender.sendMakeEvent(deduct.getTenantCode(), deduct.getUserId(),
                    MakeEventType.MAKE_EXAM.getCode(), deduct.getAmount(), 0);
        }
    }


    private static UserTradeResult defaultUserTradeResult(String tenantCode,String userId){
        UserTradeResult userTradeResult = new UserTradeResult();
        userTradeResult.setBalance(new UserBalanceBO());
        userTradeResult.setTenantCode(tenantCode);
        userTradeResult.setUserId(userId);
        userTradeResult.setScore(0);
        userTradeResult.setType("");
        userTradeResult.setRemark("");
        userTradeResult.setBizId("0");
        userTradeResult.setBizCode("0");
        userTradeResult.setBizName("默认");
        userTradeResult.setPayType("0");
        userTradeResult.setTradeId(NiccCommonUtil.createMsgId());
        userTradeResult.setTradeTime(new Date());
        userTradeResult.setAmount(0);
        userTradeResult.setPrice(0);
        return userTradeResult;
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<UserTradeResult> deduct(UserBalanceDeductReqBO deduct) {
        if (!nbchatUserConfigProperties.getScoreDeductEnable()) {
            return BaseRspUtils.createSuccessRsp(defaultUserTradeResult(deduct.getTenantCode(),deduct.getUserId()),"success");
        }
        String key = RedisConstants.USER_SCORE_DEDUCT_ACCELERATE + deduct.getTenantCode() + ":" + deduct.getUserId();
        RedisLockEntity lockEntity = RedisLockEntity.builder().lockKey(key).requestId(IdWorker.nextAutoIdStr()).build();
        boolean lock = redisHelper.lockLua(lockEntity, 10);
        if (lock) {
            try {
                Rsp<UserTradeResult> deductRsp;
                if (UserAttributeConstants.DEFAULT_APPID.equals(deduct.getTenantCode())) {
                    //个人用户
                    deduct.setPayType(TradePayType.PERSONAL.getCode());
                    log.info("算力点消费[个人]-参数: {}", deduct);
                    deductRsp = scoreDeductService.deduct(deduct);
                } else {
                    if (this.isEnterprisePay(deduct.getTenantCode())) {
                        deduct.setPayType(TradePayType.ENTERPRISE.getCode());
                        log.info("算力点消费[企业]-参数: {}", deduct);
                        deductRsp = scoreDeductService.deduct(deduct);
                    } else {
                        deduct.setPayType(TradePayType.PERSONAL.getCode());
                        deductRsp = scoreDeductService.deduct(deduct);
                    }
                }
                if (deductRsp.isSuccess()) {
                    sendMakeEvent(deduct);
                }
                return deductRsp;
            } catch (Exception e) {
                log.error("算力点消费-异常: {}", deduct, e);
            } finally {
                redisHelper.unlock(lockEntity);
            }
            return BaseRspUtils.createErrorRsp("算力点消费：扣减异常");
        }
        //没有获取到锁，重试
        log.warn("算力点消费-获取锁失败: {}", deduct);
        return BaseRspUtils.createErrorRsp("算力点消费：系统繁忙,交易失败");
    }

    public Boolean isEnterprisePay(String tenantCode) {
        String type = commonMapper.queryPayType(tenantCode);
        return UserAttributeConstants.ENTERPRISE_PAY.equals(type);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<UserTradeResult> tdhTrainDeduct(UserBalanceDeductReqBO deduct) {
        log.info("数字人定制服务-算力点消费[企业]-参数: {}", deduct);
        String key = RedisConstants.USER_SCORE_DEDUCT_ACCELERATE + deduct.getBizId();
        if (deduct.getScore() == null) {
            return BaseRspUtils.createErrorRsp("算力点消费：参数异常");
        }
        RedisLockEntity lockEntity = RedisLockEntity.builder().lockKey(key).requestId(IdWorker.nextAutoIdStr()).build();
        boolean lock = redisHelper.lockLua(lockEntity, 10);
        if (lock) {
            try {
                deduct.setPayType(TradePayType.ENTERPRISE.getCode());
                return scoreDeductService.deduct(deduct);
            } catch (Exception e) {
                log.error("数字人定制服务-算力点消费-异常: {}", deduct, e);
            } finally {
                redisHelper.unlock(lockEntity);
            }
            return BaseRspUtils.createErrorRsp("算力点消费：扣减异常");
        }
        //没有获取到锁，重试
        log.warn("数字人定制服务-算力点消费-获取锁失败: {}", deduct);
        return BaseRspUtils.createErrorRsp("算力点消费：系统繁忙,交易失败");
    }


    @MethodParamVerifyEnable
    @Override
    public Rsp<UserTradeResult> refund(UserBalanceRefundReqBO refund) {
        if (!nbchatUserConfigProperties.getScoreDeductEnable()) {
            return BaseRspUtils.createSuccessRsp(defaultUserTradeResult(refund.getTenantCode(),refund.get_userId()),"success");
        }
        String key = RedisConstants.USER_SCORE_REFUND_ACCELERATE + refund.getTradeId();
        RedisLockEntity lockEntity = RedisLockEntity.builder().lockKey(key).requestId(IdWorker.nextAutoIdStr()).build();
        boolean lock = redisHelper.lockLua(lockEntity, 10);
        if (lock) {
            try {
                return scoreRefundService.refund(refund);
            } catch (Exception e) {
                log.error("余额退款-异常:{}", refund, e);
            } finally {
                redisHelper.unlock(lockEntity);
            }
            return BaseRspUtils.createErrorRsp("退款异常");
        }
        return BaseRspUtils.createErrorRsp("退款：系统繁忙,交易失败");
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<Boolean> deductCheck(UserBalanceRechargeReqBO check) {
        if (!nbchatUserConfigProperties.getScoreDeductEnable()) {
            return BaseRspUtils.createSuccessRsp(true);
        }
        log.info("余额校验-参数: {}", check);
        NbchatUserBalance balance = nbchatUserBalanceMapper.selectByTenantCode(check.getTenantCode());
        if (balance == null || check.getScore() > balance.getScore()) {
            //企业账户不存在或余额不足
            log.warn("余额校验-账户不存在或余额不足: {}", check);
            nbchatUserBalanceMapper.selectByUserId(check.getTenantCode(), check.getUserId());
        }
        if (balance != null) {
            int score = check.getScore();
            if (score > balance.getScore()) {
                return BaseRspUtils.createSuccessRsp(false, "余额不足");
            }
            return BaseRspUtils.createSuccessRsp(true);
        }
        return BaseRspUtils.createErrorRsp(false, "账户异常");
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<UserTradeResult> recharge(UserBalanceRechargeReqBO recharge) {
        String key = RedisConstants.USER_SCORE_RECHARGE_ACCELERATE + recharge.getTenantCode() + ":" + recharge.getUserId();
        RedisLockEntity lockEntity = RedisLockEntity.builder().lockKey(key).requestId(IdWorker.nextAutoIdStr()).build();
        boolean lock = redisHelper.lockLua(lockEntity, 10);
        if (lock){
            try {
                return scoreRechargeService.recharge(recharge);
            } catch (Exception e) {
                log.error("算力点充值-异常:{}", recharge, e);
            }
            return BaseRspUtils.createErrorRsp("算力点充值失败");
        }
        log.warn("算力点充值-获取锁失败:{}", recharge);
        return BaseRspUtils.createErrorRsp("算力点充值：系统繁忙,交易失败");
    }

    @Override
    public Rsp createBalanceAccount(UserBalanceAccountReqBO accountReqBO) {
        try {
            if (StringUtils.isAllBlank(accountReqBO.getTenantCode(), accountReqBO.getUserId())) {
                return BaseRspUtils.createErrorRsp("创建账户参数异常");
            }
            return scoreBalanceService.createBalanceAccount(accountReqBO);
        } catch (Exception e) {
            log.error("创建账户-异常:{}", accountReqBO, e);
        }
        return BaseRspUtils.createErrorRsp("创建账户异常");
    }

}
