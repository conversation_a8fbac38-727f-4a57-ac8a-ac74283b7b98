package com.tydic.nbchat.user.core.service.sms.strategy;

import com.tydic.nbchat.user.api.bo.constants.AreaCodeConstants;
import com.tydic.nbchat.user.core.config.SmsTemplateProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模板策略工厂
 * 用于创建和管理不同的模板策略
 */
@Slf4j
@Component
public class TemplateStrategyFactory {
    
    private final SmsTemplateProperties smsTemplateProperties;
    private final Map<String, TemplateStrategy> strategyMap = new ConcurrentHashMap<>();
    private TemplateStrategy defaultStrategy;
    
    public TemplateStrategyFactory(SmsTemplateProperties smsTemplateProperties) {
        this.smsTemplateProperties = smsTemplateProperties;
    }
    
    @PostConstruct
    public void init() {
        // 创建默认策略
        defaultStrategy = new DefaultTemplateStrategy(smsTemplateProperties.getDefaultTemplates());
        strategyMap.put(AreaCodeConstants.CHINA_MAINLAND, defaultStrategy);
        log.info("初始化默认模板策略");
        
        // 创建区号特定策略
        smsTemplateProperties.getAreaCodes().forEach((areaCode, templates) -> {
            if (templates != null && !templates.isEmpty()) {
                TemplateStrategy strategy = new AreaCodeTemplateStrategy(areaCode, templates, defaultStrategy);
                strategyMap.put(areaCode, strategy);
                log.info("初始化区号特定模板策略: 区号={}, 模板数量={}", areaCode, templates.size());
            }
        });
        
        log.info("模板策略工厂初始化完成，支持的区号数量: {}", strategyMap.size());
    }
    
    /**
     * 获取适用于指定区号的模板策略
     * @param areaCode 区号
     * @return 模板策略
     */
    public TemplateStrategy getStrategy(String areaCode) {
        if (StringUtils.isBlank(areaCode)) {
            return defaultStrategy;
        }
        
        // 标准化区号，去除前导零
        areaCode = normalizeAreaCode(areaCode);
        
        // 查找区号特定的策略
        TemplateStrategy strategy = strategyMap.get(areaCode);
        if (strategy != null) {
            return strategy;
        }
        
        // 如果没有找到区号特定的策略，返回默认策略
        return defaultStrategy;
    }
    
    /**
     * 添加新的模板策略
     * @param areaCode 区号
     * @param strategy 模板策略
     */
    public void addStrategy(String areaCode, TemplateStrategy strategy) {
        if (StringUtils.isNotBlank(areaCode) && strategy != null) {
            strategyMap.put(areaCode, strategy);
            log.info("添加模板策略: 区号={}", areaCode);
        }
    }
    
    /**
     * 标准化区号，去除前导零
     * @param areaCode 原始区号，可能包含前导零，如 0086
     * @return 标准化后的区号，如 86
     */
    private String normalizeAreaCode(String areaCode) {
        if (StringUtils.isBlank(areaCode)) {
            return AreaCodeConstants.CHINA_MAINLAND;
        }
        
        // 去除前导零
        areaCode = areaCode.replaceFirst("^0+", "");
        
        // 如果区号为空，返回默认区号
        if (StringUtils.isBlank(areaCode)) {
            return AreaCodeConstants.CHINA_MAINLAND;
        }
        
        return areaCode;
    }
}
