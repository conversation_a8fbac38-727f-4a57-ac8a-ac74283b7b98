package com.tydic.nbchat.user.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.NbchatUserAssistDetailApi;
import com.tydic.nbchat.user.api.bo.NbchatUserAssisReqBo;
import com.tydic.nbchat.user.api.bo.NbchatUserAssistDetailReqBo;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nbchat.user.api.bo.constants.UserAssistConstants;
import com.tydic.nbchat.user.api.bo.eums.AssistType;
import com.tydic.nbchat.user.api.bo.vip.UserVipOrderContext;
import com.tydic.nbchat.user.mapper.NbchatUserAssistDetailMapper;
import com.tydic.nbchat.user.mapper.NbchatUserAssistMapper;
import com.tydic.nbchat.user.mapper.po.NbchatUserAssist;
import com.tydic.nbchat.user.mapper.po.NbchatUserAssistDetail;
import com.tydic.nbchat.user.mapper.po.NbchatUserAssistResult;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class NbchatUserAssistDetailServicelmpl implements NbchatUserAssistDetailApi {

    private final NbchatUserAssistDetailMapper nbchatUserAssistDetailMapper;
    private final NbchatUserAssistMapper nbchatUserAssistMapper;
    private final NbchatUserApi nbchatUserApi;

    private final RedisHelper redisHelper;
    private final KKMqProducerHelper kkMqProducerHelper;

    public NbchatUserAssistDetailServicelmpl(NbchatUserAssistDetailMapper nbchatUserAssistDetailMapper,
                                             RedisHelper redisHelper,
                                             NbchatUserAssistMapper nbchatUserAssistMapper,
                                             NbchatUserApi nbchatUserApi, KKMqProducerHelper kkMqProducerHelper) {
        this.nbchatUserAssistDetailMapper = nbchatUserAssistDetailMapper;
        this.redisHelper = redisHelper;
        this.nbchatUserAssistMapper = nbchatUserAssistMapper;
        this.nbchatUserApi = nbchatUserApi;
        this.kkMqProducerHelper = kkMqProducerHelper;
    }

    /**
     * 用户发起助力, 记录发起助力信息,创建邀请码
     * @param reqBo
     * @return
     */

    @Override
    public Rsp createAssist(NbchatUserAssistDetailReqBo reqBo) {
        log.info("生成邀请码: {}|{}", reqBo.getTenantCode(), reqBo.getUserId());
        // 创建用户发起助力主表记录
        NbchatUserAssist nbchatUserAssist = new NbchatUserAssist();
        nbchatUserAssist.setAssistId(IdWorker.nextAutoIdStr());
        nbchatUserAssist.setTenantCode(reqBo.getTenantCode());
        nbchatUserAssist.setUserId(reqBo.getUserId());
        nbchatUserAssist.setAssistType(AssistType.EXPERIENCE.getCode()); // 设置助力类型为1—免费体验会员
        nbchatUserAssist.setNeedNum(UserAssistConstants.USER_ASSIST_STATUS_INVITE); // 设置需要邀请的人数为
        nbchatUserAssist.setBusiType(""); // 设置业务类型
        nbchatUserAssist.setAssistStatus("0"); // 设置初始状态为未完成
        nbchatUserAssist.setStartTime(new Date());
        // 设置当前日期的24点过期
        Date endTime = DateTimeUtil.getEndTimeOfDay();
        nbchatUserAssist.setEndTime(endTime);
        nbchatUserAssistMapper.insert(nbchatUserAssist);
        // 缓存助力信息到Redis
        cacheAssistInfo(reqBo.getUserId(), 0, "0", nbchatUserAssist.getNeedNum());
        return BaseRspUtils.createSuccessRsp(nbchatUserAssist);
    }

    private void cacheAssistInfo(String userId, int assistCount, String assistStatus, int needNum) {
        String key = RedisConstants.USER_ASSIST_KEY_PREFIX + ":" + userId;
        JSONObject assistInfo = new JSONObject();
        assistInfo.put("assistCount", assistCount);
        assistInfo.put("assistStatus", assistStatus);
        assistInfo.put("needNum", needNum);
        redisHelper.set(key, assistInfo.toJSONString());
        redisHelper.expire(key, 24 * 60 * 60);
    }

    private boolean isNewUser(Date createTime) {
        if (createTime == null) return false;
        long diffHours = (System.currentTimeMillis() - createTime.getTime()) / (60 * 60 * 1000);
        return diffHours < 24; // 24小时内视为新用户
    }

    /**
     * 用户点击助力，记录助力明细，检查是否达到目标，发放奖励
     * @param reqBo
     * @return
     */

    @Override
    @Transactional
    public Rsp submitAssist(NbchatUserAssistDetailReqBo reqBo) {
        // 获取助力用户(新用户)信息
        Rsp<NbchatUserInfo> userRsp = nbchatUserApi.getUserInfo(reqBo.getUserId());
        if (!userRsp.isSuccess() || userRsp.getData() == null) {
            return BaseRspUtils.createSuccessRsp("用户信息获取失败");
        }
        NbchatUserInfo userInfo = userRsp.getData();
        // 判断是否新用户
        if (!isNewUser(userInfo.getRegTime())) {
            log.info("老用户不允许助力，用户ID: {}", reqBo.getUserId());
            return BaseRspUtils.createSuccessRsp("仅限新注册用户参与助力");
        }
        NbchatUserAssistDetail existingHelp = nbchatUserAssistDetailMapper.selectByUserId(reqBo.getUserId());
        if (existingHelp != null) {
            return BaseRspUtils.createSuccessRsp("您已经参与过其他助力活动");
        }
        // 获取被助力的活动信息
        NbchatUserAssist assist = nbchatUserAssistMapper.selectByPrimaryKey(reqBo.getAssistId());
        if (assist == null || assist.getEndTime().before(new Date())) {
            return BaseRspUtils.createSuccessRsp("助力已过期或不存在");
        }
        // 检查新用户是否已助力过
        NbchatUserAssistDetail existingDetail = nbchatUserAssistDetailMapper.selectByUserIdAndAssistId(
                reqBo.getUserId(), reqBo.getAssistId());
        if (existingDetail != null) {
            return BaseRspUtils.createSuccessRsp("您已经助力过了");
        }
        // 记录助力明细
        NbchatUserAssistDetail detail = new NbchatUserAssistDetail();
        detail.setTenantCode(reqBo.getTenantCode());
        detail.setUserId(reqBo.getUserId());
        detail.setAssistId(reqBo.getAssistId());
        detail.setAssistTime(new Date());
        nbchatUserAssistDetailMapper.insert(detail);
        // 获取当前邀请总人数并检查是否达到助力目标
        int currentAssists = nbchatUserAssistDetailMapper.countByAssistId(reqBo.getAssistId());
        String assistStatus = assist.getAssistStatus();

        if (currentAssists >= assist.getNeedNum()) {
            // 设置当前日期的24点过期
            Date endTime = DateTimeUtil.getEndTimeOfDay();
            assist.setEndTime(endTime);
            // 更新数据库中的助力状态
            nbchatUserAssistMapper.updateByPrimaryKey(assist);
            assistStatus = "1";
        }
        // 更新缓存
        cacheAssistInfo(assist.getUserId(), currentAssists, assistStatus, assist.getNeedNum());
        // 发送消息队列通知为老用户开通体验会员
        sendExperienceVipMessage(assist, currentAssists);
        if ("1".equals(assistStatus)) {
            return BaseRspUtils.createSuccessRsp("助力成功，会员奖励成功！");
        } else {
            // 计算还需要多少助力人数
            int remainingAssists = assist.getNeedNum() - currentAssists;
            return BaseRspUtils.createSuccessRsp("助力成功，还需邀请 " + remainingAssists + " 人");
        }
    }

    private void sendExperienceVipMessage(NbchatUserAssist assist,int days) {
        String id = IdWorker.nextAutoIdStr();
        UserVipOrderContext eventContext = UserVipOrderContext.builder()
                .tenantCode(assist.getTenantCode()).userId(assist.getUserId()).orderNo("assist_" + id).tradeNo("assist_" + id)
                .orderTime(new Date()).score(0).cycle(1).days(days).vipType(AssistType.EXPERIENCE.getCode()).payPrice(0).build();
        try {
            log.info("助力会员充值-推送事件消息: {}", eventContext);
            kkMqProducerHelper.sendMsg(NbchatTopicsConstants.NBCHAT_USER_RECHARGE_ORDER_TOPIC, eventContext);
            // 更新 assistStatus 状态
            assist.setAssistStatus("1");
            nbchatUserAssistMapper.updateByPrimaryKeySelective(assist);
            log.info("assistStatus 更新成功: assistId={}, userId={}", assist.getAssistId(), assist.getUserId());
        } catch (Exception e) {
            log.info("助力会员充值-推送事件消息-异常: {}", eventContext, e);
            throw new RuntimeException("发送会员变更消息失败", e);
        }
    }

    /**
     * 利用缓存校验助力是否通过
     */
    @Override
    public Rsp checkAssist(NbchatUserAssisReqBo reqBo) {
        // 从缓存中获取助力信息
        String key = RedisConstants.USER_ASSIST_KEY_PREFIX + ":" + reqBo.getUserId();
        String cachedAssistInfo = (String) redisHelper.get(key);

        if (cachedAssistInfo != null) {
            try {
                JSONObject result = JSON.parseObject(cachedAssistInfo);
                return BaseRspUtils.createSuccessRsp(result);
            } catch (Exception e) {
                log.info("解析缓存失败: {}", e.getMessage());
                return BaseRspUtils.createErrorRsp("解析缓存失败，请重试");
            }
        }
        NbchatUserAssist assist = nbchatUserAssistMapper.selectById(reqBo.getAssistId(),
                reqBo.getUserId(), reqBo.getTenantCode());
        if (assist == null) {
            return BaseRspUtils.createErrorRsp("助力信息未找到");
        }
        if (assist.getEndTime().before(new Date())) {
            return BaseRspUtils.createErrorRsp("助力已过期");
        }
        int assistCount = nbchatUserAssistDetailMapper.countByAssistId(reqBo.getAssistId());
        String assistStatus = assist.getAssistStatus();
        int needNum = assist.getNeedNum();

        // 重建缓存
        cacheAssistInfo(reqBo.getUserId(), assistCount, assistStatus, needNum);
        JSONObject result = new JSONObject();
        result.put("assistStatus", assistStatus);
        result.put("assistCount", assistCount);
        result.put("needNum", needNum);
        return BaseRspUtils.createSuccessRsp(result);
    }
    /**
     * 查询助力信息
     */
    @Override
    public Rsp getAssistInfo(NbchatUserAssisReqBo reqBo) {
        List<NbchatUserAssistResult> assistDetails = nbchatUserAssistMapper.selectUserAssistInfo(reqBo.getUserId(),reqBo.getTenantCode());
        // 处理手机号，隐藏中间四位
        for (NbchatUserAssistResult detail : assistDetails) {
            String phone = detail.getPhone();
            if (phone != null && phone.length() == 11) {
                String hiddenPhone = phone.substring(0, 3) + "****" + phone.substring(7);
                detail.setPhone(hiddenPhone);
            }
        }
        return BaseRspUtils.createSuccessRsp(assistDetails);
    }

}
