package com.tydic.nbchat.user.core.service.sms.strategy;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 缓存装饰器
 * 用于缓存模板策略的结果
 */
@Slf4j
public class CachingTemplateStrategyDecorator extends TemplateStrategyDecorator {
    
    private final Map<String, String> cache = new ConcurrentHashMap<>();
    
    public CachingTemplateStrategyDecorator(TemplateStrategy decoratedStrategy) {
        super(decoratedStrategy);
    }
    
    @Override
    public String getTemplateId(String templateCode) {
        // 如果缓存中有结果，直接返回
        String cacheKey = getAreaCode() + ":" + templateCode;
        if (cache.containsKey(cacheKey)) {
            String cachedTemplateId = cache.get(cacheKey);
            log.debug("使用缓存的模板ID: 区号={}, 模板代码={}, 模板ID={}", getAreaCode(), templateCode, cachedTemplateId);
            return cachedTemplateId;
        }
        
        // 否则调用被装饰的策略获取结果，并缓存
        String templateId = decoratedStrategy.getTemplateId(templateCode);
        cache.put(cacheKey, templateId);
        log.debug("缓存模板ID: 区号={}, 模板代码={}, 模板ID={}", getAreaCode(), templateCode, templateId);
        return templateId;
    }
    
    /**
     * 清除缓存
     */
    public void clearCache() {
        //TODO后期扩展
        cache.clear();
        log.debug("清除模板ID缓存");
    }
}
