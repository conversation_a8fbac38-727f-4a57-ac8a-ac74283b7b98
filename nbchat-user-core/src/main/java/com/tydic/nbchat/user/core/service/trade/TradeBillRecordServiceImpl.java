package com.tydic.nbchat.user.core.service.trade;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.user.api.UserBaseInfoApi;
import com.tydic.nbchat.user.api.bo.eums.TradeBusiCodeDefine;
import com.tydic.nbchat.user.api.bo.eums.TradePayType;
import com.tydic.nbchat.user.api.bo.trade.UserBillRecordBO;
import com.tydic.nbchat.user.api.bo.trade.UserBillRecordQueryReqBO;
import com.tydic.nbchat.user.api.bo.user.UserBaseInfo;
import com.tydic.nbchat.user.api.trade.TradeBillRecordApi;
import com.tydic.nbchat.user.core.config.NbchatUserConfigProperties;
import com.tydic.nbchat.user.mapper.NbchatUserBillRecordMapper;
import com.tydic.nbchat.user.mapper.po.NbchatUserBillRecord;
import com.tydic.nbchat.user.mapper.po.NbchatUserBillRecordQueryCondition;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class TradeBillRecordServiceImpl implements TradeBillRecordApi {

    @Resource
    private NbchatUserBillRecordMapper nbchatUserBillRecordMapper;
    private final UserBaseInfoApi userBaseInfoApi;
    private final NbchatUserConfigProperties nbchatUserConfigProperties;

    public TradeBillRecordServiceImpl(UserBaseInfoApi userBaseInfoApi,
                                      NbchatUserConfigProperties nbchatUserConfigProperties) {
        this.userBaseInfoApi = userBaseInfoApi;
        this.nbchatUserConfigProperties = nbchatUserConfigProperties;
    }

    @MethodParamVerifyEnable
    @Override
    public RspList<UserBillRecordBO> getBillRecord(UserBillRecordQueryReqBO queryReqBO) {
        if (!nbchatUserConfigProperties.getScoreDeductEnable()) {
            return BaseRspUtils.createSuccessRspList(new ArrayList<>());
        }
        //分页查询
        log.info("查询用户账单记录:{}", queryReqBO);
        NbchatUserBillRecordQueryCondition condition = new NbchatUserBillRecordQueryCondition();
        BeanUtils.copyProperties(queryReqBO, condition);
        if (TradePayType.ENTERPRISE.getCode().equals(queryReqBO.getPayType())) {
            condition.setUserId("");
        }
        Page<NbchatUserBillRecord> page = PageHelper.startPage(queryReqBO.getPage(), queryReqBO.getLimit());
        nbchatUserBillRecordMapper.selectByCondition(condition);
        List<UserBillRecordBO> list = Lists.newArrayList();
        NiccCommonUtil.copyList(page.getResult(), list, UserBillRecordBO.class);
        Map<String,UserBaseInfo> userMap = new HashMap<>();
        for (UserBillRecordBO bill : list) {
            bill.setBizCodeDesc(TradeBusiCodeDefine.getNameByCode(bill.getBizCode()));
            if (!userMap.containsKey(bill.getUserId())) {
                Rsp<UserBaseInfo> rsp = userBaseInfoApi.getByUserId(bill.getTenantCode(), bill.getUserId());
                if (rsp.isSuccess()) {
                    userMap.put(bill.getUserId(), rsp.getData());
                }
            }
            if (userMap.containsKey(bill.getUserId())) {
                bill.setRealName(userMap.get(bill.getUserId()).getRealName());
            }
        }
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }
}
