package com.tydic.nbchat.user.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.user.api.UserVipApi;
import com.tydic.nbchat.user.api.bo.eums.ScoreTaskType;
import com.tydic.nbchat.user.api.bo.eums.UserVipStatusType;
import com.tydic.nbchat.user.api.bo.eums.UserVipType;
import com.tydic.nbchat.user.api.bo.vip.UserVipBO;
import com.tydic.nbchat.user.api.bo.vip.UserVipGiveDouBO;
import com.tydic.nbchat.user.api.bo.vip.UserVipQueryReqBO;
import com.tydic.nbchat.user.core.busi.UserVipService;
import com.tydic.nbchat.user.mapper.NbchatUserScoreTaskMapper;
import com.tydic.nbchat.user.mapper.po.NbchatUserScoreTask;
import com.tydic.nbchat.user.mapper.po.NbchatUserScoreTaskCondition;
import com.tydic.nbchat.user.mapper.po.NbchatUserVip;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
public class UserVipServiceImpl implements UserVipApi {

    private final UserVipService userVipService;

    @Resource
    private NbchatUserScoreTaskMapper nbchatUserScoreTaskMapper;

    public UserVipServiceImpl(UserVipService userVipService) {
        this.userVipService = userVipService;
    }

    @Override
    public Rsp<UserVipBO> getUserVip(UserVipQueryReqBO userVipReqBO) {
        List<NbchatUserVip> vips = userVipService.getVipInfo(userVipReqBO.getTenantCode(),
                userVipReqBO.get_userId());
        if (CollectionUtils.isNotEmpty(vips)) {
            NbchatUserVip vip = vips.get(0);
            UserVipBO userVipBO = new UserVipBO();
            BeanUtils.copyProperties(vip, userVipBO);

            List<UserVipBO> bos = new ArrayList<>();
            NiccCommonUtil.copyList(vips, bos, UserVipBO.class);
            userVipBO.setVipList(bos);
            return BaseRspUtils.createSuccessRsp(userVipBO);
        }
        return BaseRspUtils.createSuccessRsp(null,"未查询到用户VIP信息");
    }

    @Override
    public RspList<UserVipGiveDouBO> getVipGiveDouList(UserVipQueryReqBO userVipReqBO) {
        List<UserVipGiveDouBO> list = new ArrayList<>();
        NbchatUserScoreTaskCondition condition = new NbchatUserScoreTaskCondition();
        condition.setUserId(userVipReqBO.get_userId());
        condition.setTenantCode(userVipReqBO.getTenantCode());
        condition.setTaskType(ScoreTaskType.RECHARGE.getCode());
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        condition.setKeyword("会员");
        Page<NbchatUserScoreTask> page = PageHelper.startPage(userVipReqBO.getPage(), userVipReqBO.getLimit());
        nbchatUserScoreTaskMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(page.getResult(), list, UserVipGiveDouBO.class);
        return BaseRspUtils.createSuccessRspList(list,page.getTotal());
    }
}
