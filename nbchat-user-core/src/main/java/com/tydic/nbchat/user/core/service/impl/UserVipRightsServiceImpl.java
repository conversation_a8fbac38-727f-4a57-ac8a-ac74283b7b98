package com.tydic.nbchat.user.core.service.impl;

import com.tydic.nbchat.user.api.UserVipRightsApi;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.vip.NbchatUserVipRightsBO;
import com.tydic.nbchat.user.mapper.NbchatUserVipRightsMapper;
import com.tydic.nbchat.user.mapper.po.NbchatUserVipRights;
import com.tydic.nbchat.user.mapper.po.UserRightFree;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class UserVipRightsServiceImpl implements UserVipRightsApi {

    @Resource
    NbchatUserVipRightsMapper nbchatUserVipRightsMapper;


    @Override
    public RspList query(NbchatUserVipRightsBO request) {
        log.info("查询用户权益:{}", request);
        NbchatUserVipRights cond = new NbchatUserVipRights();
        BeanUtils.copyProperties(request, cond);
        List<NbchatUserVipRights> rights = nbchatUserVipRightsMapper.selectAll(cond);
        List<NbchatUserVipRightsBO> res = new ArrayList<>();
        NiccCommonUtil.copyList(rights, res, NbchatUserVipRightsBO.class);
        return BaseRspUtils.createSuccessRspList(res);
    }

    @Override
    public Rsp saveRights(NbchatUserVipRightsBO request) {
        NbchatUserVipRights cond = new NbchatUserVipRights();
        cond.setUserId(request.getUserId());
        cond.setTenantCode(request.getTenantCode());
        cond.setRightsType(request.getRightsType());
        NbchatUserVipRights vipRights = nbchatUserVipRightsMapper.queryRightsByType(cond);

        NbchatUserVipRights po = new NbchatUserVipRights();
        BeanUtils.copyProperties(request, po);
        if (ObjectUtils.isEmpty(vipRights)) {
            if (StringUtils.isNotEmpty(request.getTargetTenantCode())) {
                po.setUserId(request.getTargetTenantCode());
            }
            if (ObjectUtils.allNull(po.getStartTime(),po.getEndTime())) {
                po.setStartTime(new Date());
                po.setEndTime(DateTimeUtil.DateAddYear(1));
            }
            nbchatUserVipRightsMapper.insertSelective(po);
        } else {
            if (ObjectUtils.isNotEmpty(request.getAddCount())) {
                po.setRightsCount(vipRights.getRightsCount() + request.getAddCount());
            }
            po.setId(vipRights.getId());
            po.setRightsStatus("0"); //可用
            po.setEndTime(DateTimeUtil.DateAddYear(1));
            nbchatUserVipRightsMapper.update(po);
        }
        return BaseRspUtils.createSuccessRsp("更新用户VIP权益成功");
    }

    @Override
    public Rsp queryTenantRights(NbchatUserVipRightsBO request) {
        NbchatUserVipRights po = new NbchatUserVipRights();
        po.setTenantCode(request.getTargetTenantCode());
        po.setUserId(request.getTargetTenantCode());
        po.setCustomizeType(request.getCustomizeType());
        NbchatUserVipRights right = nbchatUserVipRightsMapper.queryRights(po);
        NbchatUserVipRightsBO rsp = new NbchatUserVipRightsBO();
        if (ObjectUtils.isEmpty(right)) {
            rsp.setRightsCount(0);
            rsp.setUsedCount(0);
            return BaseRspUtils.createSuccessRsp(rsp);
        }
        BeanUtils.copyProperties(right, rsp);
        rsp.setUsableCount(right.getRightsCount() - right.getUsedCount());
        return BaseRspUtils.createSuccessRsp(rsp);
    }


    @Override
    public Rsp deductRights(NbchatUserVipRightsBO request) {
        log.info("更新用户VIP权益:{}", request);
        NbchatUserVipRights po = new NbchatUserVipRights();
        po.setUserId(request.getUserId());
        po.setTenantCode(request.getTenantCode());
        po.setRightsType(request.getRightsType());
        if (!"00000000".equals(request.getTenantCode())) {
            po.setUserId(request.getTenantCode());
        }
        NbchatUserVipRights right = nbchatUserVipRightsMapper.queryRights(po);
        if (ObjectUtils.isEmpty(right) || right.getUsableCount() <= 0) {
            log.error("未找到用户有效权益：{}",request);
            return BaseRspUtils.createErrorRsp("用户无权益");
        }
        po.setId(right.getId());
        int i = nbchatUserVipRightsMapper.updateRightsUsed(po);
        return BaseRspUtils.createSuccessRsp("更新用户VIP权益成功");
    }

    @Override
    public RspList queryRights(NbchatUserVipRightsBO request) {
        log.info("查询权益:{}", request);
        NbchatUserVipRights po = new NbchatUserVipRights();
        po.setTenantCode(request.getTargetTenantCode());
        po.setUserId(request.getTargetTenantCode());
        List<NbchatUserVipRights> rights = nbchatUserVipRightsMapper.queryAllRights(po);
        if (CollectionUtils.isEmpty(rights)) {
            return BaseRspUtils.createErrorRspList("未找到权益");
        }
        List<NbchatUserVipRightsBO> res = new ArrayList<>();
        NiccCommonUtil.copyList(rights, res, NbchatUserVipRightsBO.class);
        return BaseRspUtils.createSuccessRspList(res);
    }

    @Override
    public Rsp getRightsFree(BaseInfo request) {
        if (!UserAttributeConstants.DEFAULT_APPID.equals(request.getTenantCode())) {
            request.set_userId(request.getTenantCode());
        }
        List<UserRightFree> rights = nbchatUserVipRightsMapper.
                countFreeRights(request.getTenantCode(), request.get_userId());
        //转换为 map ,key=type ,value=free
        Map<String, Integer> map = rights.stream()
                .collect(HashMap::new, (m, v) ->
                        m.put(v.getType(), v.getFree()), HashMap::putAll);
        return BaseRspUtils.createSuccessRsp(map);
    }
}
