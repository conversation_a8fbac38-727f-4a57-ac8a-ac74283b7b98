package com.tydic.nbchat.user.core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.tydic.nbchat.user.api.UserSuggestionService;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.suggestion.SuggestionSaveReqBO;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.mapper.UserSuggestionMapper;
import com.tydic.nbchat.user.mapper.po.UserSuggestion;
import com.tydic.nbchat.user.core.utils.DingtalkUtil;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Slf4j
@Service
public class UserSuggestionServiceImpl implements UserSuggestionService {

    @Resource
    private UserSuggestionMapper userSuggestionMapper;

    private final UserSettingHelper userSettingHelper;

    public UserSuggestionServiceImpl(UserSettingHelper userSettingHelper) {
        this.userSettingHelper = userSettingHelper;
    }

    private static final DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @MethodParamVerifyEnable
    @Override
    public Rsp saveSuggestion(SuggestionSaveReqBO saveReqBO) {
        log.info("保存用户建议:{}",saveReqBO);
        if(StringUtils.isNotBlank(saveReqBO.getContent()) &&
                saveReqBO.getContent().length() > 500){
            return BaseRspUtils.createErrorRsp("内容不得超过500字符!");
        }
        UserSuggestion suggestion = new UserSuggestion();
        BeanUtils.copyProperties(saveReqBO,suggestion);
        suggestion.setCreateTime(new Date());
        if(!CollectionUtils.isEmpty(saveReqBO.getAttachmentList())){
            String attachment = JSONArray.toJSONString(saveReqBO.getAttachmentList());
            suggestion.setAttachment(attachment);
        }else{
            suggestion.setAttachment("[]");
        }
        int i = userSuggestionMapper.insertSelective(suggestion);
        //推送钉钉消息
        sendDingNotification(saveReqBO);
        return BaseRspUtils.createSuccessRsp(i,"操作成功!");
    }

/* 钉钉推送消息格式：
    【测试/生产环境】
    用户意见反馈数据
    类型：
    产品模块：
    反馈内容：
    上传截图或视频：
    姓名：
    联系方式：*/
private void sendDingNotification(SuggestionSaveReqBO reqBO) {
    try {
        StringBuilder message = new StringBuilder()
                .append("用户意见反馈数据\n")
                .append("类型：").append(reqBO.getIssueTypeName()).append("\n")
                .append("产品模块：").append(reqBO.getProductModuleName()).append("\n")
                .append("反馈内容：").append(reqBO.getContent()).append("\n");
        StringBuilder accessUrls = new StringBuilder();
        if(!CollectionUtils.isEmpty(reqBO.getAttachmentList())){
            reqBO.getAttachmentList().forEach(attachment -> accessUrls.append(attachment.getAccessUrl()).append("\n"));
        }else{
            accessUrls.append("\n");
        }
        message.append("上传截图或视频：").append(accessUrls)
                .append("姓名：").append(reqBO.getUsername()).append("\n")
                .append("联系方式：").append(reqBO.getPhone());
        //获取注册手机号
        NbchatUserInfo userInfo = userSettingHelper.getUserInfo(reqBO.getUserId());
        if (userInfo != null && StringUtils.isNotEmpty(userInfo.getPhone())) {
            message.append("\n").append("平台注册手机号：").append(userInfo.getPhone());
        }
        DingtalkUtil.sendMessageSuggestion(message.toString(), "tdh-portal");
    } catch (Exception e) {
        log.error("钉钉推送失败，手机号: {}", reqBO.getPhone(), e);
        throw new RuntimeException("消息推送失败", e); // 触发事务回滚
    }
}

}
