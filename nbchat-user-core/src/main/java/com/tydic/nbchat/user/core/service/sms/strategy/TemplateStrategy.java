package com.tydic.nbchat.user.core.service.sms.strategy;

/**
 * 模板策略接口
 * 定义获取模板ID的策略
 */
public interface TemplateStrategy {
    
    /**
     * 获取模板ID
     * @param templateCode 模板代码
     * @return 模板ID
     */
    String getTemplateId(String templateCode);
    
    /**
     * 获取策略适用的区号
     * @return 区号
     */
    String getAreaCode();
    
    /**
     * 检查策略是否适用于给定的区号
     * @param areaCode 区号
     * @return 是否适用
     */
    boolean isApplicable(String areaCode);
}
