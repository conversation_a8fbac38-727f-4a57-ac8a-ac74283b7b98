package com.tydic.nbchat.user.core.consumer;

import com.tydic.nbchat.user.api.bo.eums.UserVipType;
import com.tydic.nbchat.user.api.bo.eums.WxTemplateEnum;
import com.tydic.nbchat.user.api.bo.notice.NoticeContext;
import com.tydic.nbchat.user.api.bo.vip.UserVipOrderContext;
import com.tydic.nbchat.user.core.busi.UserScoreRechargeTaskService;
import com.tydic.nbchat.user.core.busi.WxOfficialNoticeService;
import com.tydic.nicc.common.nbchat.constants.NbchatTopicsConstants;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@KKMqConsumer(
        consumerGroup = NbchatTopicsConstants.NBCHAT_USER_RECHARGE_ORDER_CID,
        topic = NbchatTopicsConstants.NBCHAT_USER_RECHARGE_ORDER_TOPIC)
@Component
public class UserOrderRechargeConsumer implements KKMqConsumerListener<UserVipOrderContext> {

    private final UserScoreRechargeTaskService userScoreRechargeTaskService;
    private final WxOfficialNoticeService wxOfficialNoticeService;


    public UserOrderRechargeConsumer(UserScoreRechargeTaskService userScoreRechargeTaskService,
                                     WxOfficialNoticeService wxOfficialNoticeService) {
        this.userScoreRechargeTaskService = userScoreRechargeTaskService;
        this.wxOfficialNoticeService = wxOfficialNoticeService;
    }

    @Override
    public void onMessage(UserVipOrderContext userVipOrderContext) {
        userScoreRechargeTaskService.handleScoreRechargeTask(userVipOrderContext);
        //发送微信通知-充值通知
        this.sendRechargeNotice(userVipOrderContext);
        //赠送权益
        //userVipRightsService.addRights(userVipOrderContext);
    }

    public void sendRechargeNotice(UserVipOrderContext userVipOrderContext) {
        List<String> params = new ArrayList<>();
        params.add(userVipOrderContext.getOrderNo());
        params.add(String.valueOf((userVipOrderContext.getPayPrice() / 100f)));
        params.add(UserVipType.getNameByCode(userVipOrderContext.getVipType()));

        NoticeContext context = new NoticeContext();
        context.setUserId(userVipOrderContext.getUserId());
        context.setTemplateType(WxTemplateEnum.RECHARGE.getCode());
        context.setParams(params);
        wxOfficialNoticeService.sendNotice(context);
    }
}
