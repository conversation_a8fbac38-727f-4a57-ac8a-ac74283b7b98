package com.tydic.nbchat.user.core.service.impl;

import com.tydic.nbchat.user.api.UserAccountApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nbchat.user.api.bo.account.UserAccountUpdateReqBO;
import com.tydic.nbchat.user.core.busi.UserAuthBusiService;
import com.tydic.nbchat.user.core.busi.UserInfoBusiService;
import com.tydic.nbchat.user.core.busi.UserLoginLockBusiService;
import com.tydic.nbchat.user.core.utils.UserSettingHelper;
import com.tydic.nbchat.user.mapper.UserMapper;
import com.tydic.nbchat.user.mapper.UserRoleAndDeptMapper;
import com.tydic.nbchat.user.mapper.po.UserPO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class UserAccountServiceImpl implements UserAccountApi {

    private final UserLoginLockBusiService userLoginLockBusiService;
    private final UserAuthBusiService userAuthBusiService;
    private final UserInfoBusiService userInfoBusiService;
    private final UserSettingHelper userSettingHelper;

    @Resource
    private UserMapper userMapper;
    @Resource
    private UserRoleAndDeptMapper userRoleAndDeptMapper;


    public UserAccountServiceImpl(UserLoginLockBusiService userLoginLockBusiService,
                                  UserAuthBusiService userAuthBusiService,
                                  UserInfoBusiService userInfoBusiService,
                                  UserSettingHelper userSettingHelper) {
        this.userLoginLockBusiService = userLoginLockBusiService;
        this.userAuthBusiService = userAuthBusiService;
        this.userInfoBusiService = userInfoBusiService;
        this.userSettingHelper = userSettingHelper;
    }


    @Override
    public Rsp accountDisable(String userId, String reason) {
        try {
            NbchatUserInfo info = userInfoBusiService.getUserBaseInfo(userId);
            //退出登录
            userAuthBusiService.authLogout(userId);
            //永久锁定
            userLoginLockBusiService.lockAccount(info.getPhone(),null, reason);
            return BaseRspUtils.createSuccessRsp(userId);
        } catch (Exception e) {
            log.error("账号禁用失败: userId = {}",userId, e);
        }
        return BaseRspUtils.createErrorRsp("账号禁用失败");
    }

    @Override
    public Rsp accountEnable(String userId) {
        try {
            NbchatUserInfo info = userInfoBusiService.getUserBaseInfo(userId);
            userLoginLockBusiService.unlockAccount(info.getPhone());
            return BaseRspUtils.createSuccessRsp(userId);
        } catch (Exception e) {
            log.error("账号解锁失败: userId = {}",userId, e);
        }
        return BaseRspUtils.createErrorRsp("账号解锁失败");
    }


    public Rsp updateAccount(UserAccountUpdateReqBO reqBO) {
        log.info("更新用户信息: {}", reqBO);
        if (StringUtils.isAllBlank(reqBO.getUserId(), reqBO.getName(), reqBO.getAvatar())) {
            return BaseRspUtils.createErrorRsp("参数错误");
        }
        UserPO userPO = new UserPO();
        userPO.setUserId(reqBO.getUserId());
        userPO.setName(reqBO.getName());
        userPO.setAvatar(reqBO.getAvatar());
        userPO.setUpdatedTime(new Date());
        userPO.setRealName(reqBO.getRealName());
        userMapper.updateById(userPO);
        if (StringUtils.isNotBlank(reqBO.getAvatarUrl()) || StringUtils.isNotBlank(reqBO.getRealName())) {
            userRoleAndDeptMapper.updateUserInTenant(reqBO.getUserId(), reqBO.getTenantCode(), reqBO.getRealName(), reqBO.getAvatarUrl());
        }
        userSettingHelper.cleanUserCache(reqBO.getTenantCode(),reqBO.getUserId());
        return BaseRspUtils.createSuccessRsp("", "更新成功");
    }
}
