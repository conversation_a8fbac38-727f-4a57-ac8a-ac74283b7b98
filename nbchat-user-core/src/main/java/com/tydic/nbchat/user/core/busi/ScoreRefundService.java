package com.tydic.nbchat.user.core.busi;

import com.tydic.nbchat.user.api.bo.constants.DicDouConstants;
import com.tydic.nbchat.user.api.bo.eums.TradeBusiCodeDefine;
import com.tydic.nbchat.user.api.bo.eums.TradePayType;
import com.tydic.nbchat.user.api.bo.eums.TradeType;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceBO;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceRefundReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserTradeResult;
import com.tydic.nbchat.user.mapper.NbchatUserBalanceMapper;
import com.tydic.nbchat.user.mapper.NbchatUserBillDetailMapper;
import com.tydic.nbchat.user.mapper.NbchatUserBillRecordMapper;
import com.tydic.nbchat.user.mapper.NbchatUserScoreDetailMapper;
import com.tydic.nbchat.user.mapper.po.NbchatUserBillDetail;
import com.tydic.nbchat.user.mapper.po.NbchatUserBillRecord;
import com.tydic.nicc.common.nbchat.emus.MakeEventType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class ScoreRefundService {

    @Resource
    private NbchatUserBalanceMapper nbchatUserBalanceMapper;
    @Resource
    private NbchatUserBillRecordMapper nbchatUserBillRecordMapper;
    @Resource
    private NbchatUserBillDetailMapper nbchatUserBillDetailMapper;
    @Resource
    private NbchatUserScoreDetailMapper nbchatUserScoreDetailMapper;

    private final ScoreBalanceService scoreBalanceService;
    private final UserMakeEventSender userMakeEventSender;

    public ScoreRefundService(ScoreBalanceService scoreBalanceService, UserMakeEventSender userMakeEventSender) {
        this.scoreBalanceService = scoreBalanceService;
        this.userMakeEventSender = userMakeEventSender;
    }

    @Transactional(rollbackFor = Exception.class)
    public Rsp<UserTradeResult> refund(UserBalanceRefundReqBO refund) {
        /**
         * 1. 校验交易流水
         * 2. 退款
         */
        log.info("余额退款-参数: {}", refund);
        NbchatUserBillRecord record = nbchatUserBillRecordMapper.selectByTradeId(refund.getTradeId(), refund.getBizId());
        if (record != null) {
            if (record.getBizCode().equals(TradeBusiCodeDefine.VIDEO_CREATION.getCode())) {
                //发送事件
                userMakeEventSender.sendMakeEvent(record.getTenantCode(), record.getUserId(),
                                                  MakeEventType.MAKE_TDH.getCode(), 0, 1);
            }
            if (record.getScore() > 0) {
                return BaseRspUtils.createErrorRsp("退款失败：交易流水异常");
            }
            if (StringUtils.isNotBlank(record.getRefundId())) {
                return BaseRspUtils.createErrorRsp("退款失败：重复操作");
            }
            String type = TradeType.REFUND.getCode();
            //默认全部退款
            int refundScore = Math.abs(record.getScore());
            if (refund.getRefundScore() != null && refund.getRefundScore() > 0) {
                if (refund.getRefundScore() > refundScore) {
                    return BaseRspUtils.createErrorRsp("退款失败：退款积分大于交易积分");
                }
                refundScore = refund.getRefundScore();
                type = TradeType.PART_REFUND.getCode();
            }
            NbchatUserBillRecord refundRecord = new NbchatUserBillRecord();
            // 记录交易流水
            refundRecord.setTradeId(IdWorker.nextAutoIdStr());
            refundRecord.setScore(refundScore);
            refundRecord.setType(type);
            refundRecord.setTradeTime(new Date());
            refundRecord.setBizId(record.getBizId());
            refundRecord.setBizCode(record.getBizCode());
            refundRecord.setBizName(record.getBizName());
            refundRecord.setTenantCode(record.getTenantCode());
            refundRecord.setUserId(record.getUserId());
            refundRecord.setPayType(record.getPayType());
            if (StringUtils.isBlank(refund.getRemark())) {
                refundRecord.setRemark(DicDouConstants.REFUND_DOU_REMARK);
            } else {
                refundRecord.setRemark(refund.getRemark());
            }
            nbchatUserBillRecordMapper.insertSelective(refundRecord);

            record.setRefundTime(refundRecord.getRefundTime());
            record.setRefundId(refundRecord.getTradeId());
            nbchatUserBillRecordMapper.updateByPrimaryKeySelective(record);

            //查询交易明细,根据交易明细，退款到对应的子账户
            List<NbchatUserBillDetail> details = nbchatUserBillDetailMapper.selectByTradeId(refund.getTradeId());
            if (!details.isEmpty()) {
                //退到最后一个账户里
                NbchatUserBillDetail detail = details.get(details.size() - 1);
                nbchatUserScoreDetailMapper.refundScore(detail.getAccountId(), refundScore);
                //更新余额
                if (TradePayType.ENTERPRISE.getCode().equals(record.getPayType())) {
                    nbchatUserBalanceMapper.updateBalance(record.getTenantCode(), record.getTenantCode());
                } else {
                    nbchatUserBalanceMapper.updateBalance(record.getTenantCode(), record.getUserId());
                }
                //记录明细
                NbchatUserBillDetail refundDetail = getNbchatUserBillDetail(detail, refundRecord, refundScore);
                nbchatUserBillDetailMapper.insertSelective(refundDetail);
            }
            //返回结果
            UserTradeResult result = new UserTradeResult();
            BeanUtils.copyProperties(refundRecord, result);
            Rsp<UserBalanceBO> balanceRsp = scoreBalanceService.getBalance(record.getTenantCode(), record.getUserId());
            result.setBalance(balanceRsp.getData());
            //发送算力点退款事件
            userMakeEventSender.sendScoreChangeEvent(result, balanceRsp.getData().getScore());
            // 发送用户维度报表事件
            userMakeEventSender.sendUserRpEventForScoreConsume(refundRecord, "1", "0");
            return BaseRspUtils.createSuccessRsp(result, "交易成功");
        }
        return BaseRspUtils.createErrorRsp("退款失败：查询交易流水异常");
    }

    @NotNull
    private static NbchatUserBillDetail getNbchatUserBillDetail(NbchatUserBillDetail detail,
                                                                NbchatUserBillRecord refundRecord,
                                                                int refundScore) {
        NbchatUserBillDetail refundDetail = new NbchatUserBillDetail();
        refundDetail.setCreateTime(new Date());
        refundDetail.setUserId(detail.getUserId());
        refundDetail.setTenantCode(detail.getTenantCode());
        refundDetail.setAccountId(detail.getAccountId());
        refundDetail.setTradeId(refundRecord.getTradeId());
        refundDetail.setScore(refundScore);
        refundDetail.setBillDesc(refundRecord.getRemark());
        return refundDetail;
    }

}
