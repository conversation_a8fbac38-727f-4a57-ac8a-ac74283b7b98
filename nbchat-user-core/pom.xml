<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<parent>
		<groupId>com.tydic.nbchat</groupId>
		<artifactId>nbchat-user</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>nbchat-user-core</artifactId>
	<description>核心服务</description>

	<dependencies>
		<!--项目依赖开始 -->
		<dependency>
			<groupId>com.tydic.nbchat</groupId>
			<artifactId>nbchat-user-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.tydic.nbchat</groupId>
			<artifactId>nbchat-user-mapper</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-captcha</artifactId>
		</dependency>
		<!-- 阿里云短信SDK -->
		<!--<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.6.3</version>
		</dependency>-->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-xml</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>alibaba-dingtalk-service-sdk</artifactId>
		</dependency>
	</dependencies>

</project>
