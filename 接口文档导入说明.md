# 首页模板管理接口文档导入说明

## 文件说明

我已经为您生成了3个接口文档文件：

### 1. 首页模板管理接口_Postman.json
- **格式**: Postman Collection v2.1
- **适用工具**: Postman
- **特点**:
  - 包含完整的请求示例和响应示例
  - 包含测试脚本
  - 支持环境变量
  - 包含权限认证配置

### 2. 首页模板管理接口_ApiPost.json
- **格式**: OpenAPI 3.0 (Swagger)
- **适用工具**: ApiPost、Swagger UI、Apifox等
- **特点**:
  - 标准的OpenAPI格式
  - 包含详细的Schema定义
  - 支持自动生成文档
  - 包含安全认证配置

### 3. 首页模板管理接口文档.json
- **格式**: 简化的JSON格式
- **适用工具**: 通用JSON格式，可手动转换
- **特点**: 基础的接口信息

## 导入步骤

### 导入到ApiPost

1. **方法一：导入OpenAPI文档**
   - 打开ApiPost
   - 点击"导入" -> "OpenAPI"
   - 选择文件：`首页模板管理接口_ApiPost.json`
   - 点击导入

2. **方法二：导入Postman Collection**
   - 打开ApiPost
   - 点击"导入" -> "Postman"
   - 选择文件：`首页模板管理接口_Postman.json`
   - 点击导入

### 导入到Postman

1. 打开Postman
2. 点击"Import"按钮
3. 选择文件：`首页模板管理接口_Postman.json`
4. 点击"Import"

### 导入到Apifox

1. 打开Apifox
2. 点击"导入数据"
3. 选择"OpenAPI/Swagger"
4. 选择文件：`首页模板管理接口_ApiPost.json`
5. 点击导入

## 环境变量配置

导入后需要配置以下环境变量：

### 必需变量
- **baseUrl**: API服务器地址
  - 示例：`http://localhost:8080`
  - 描述：后端服务的基础URL

- **token**: 管理员认证token
  - 示例：`Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
  - 描述：具有管理员权限的JWT token

### 可选变量
- **templateId**: 模板ID
  - 描述：用于测试更新和删除接口的模板ID
  - 可以通过新增接口获取

## 接口测试顺序建议

1. **管理员查询首页模板列表**
   - 验证权限和基础功能

2. **新增首页模板**
   - 创建测试数据
   - 保存返回的模板ID到环境变量

3. **更新首页模板**
   - 使用步骤2中的模板ID

4. **首页模板上下架**
   - 测试上架功能（status: "1"）
   - 测试下架功能（status: "0"）

5. **首页模板排序**
   - 测试排序功能

6. **删除首页模板**
   - 清理测试数据

## 权限说明

所有接口都需要以下权限之一：
- 系统管理员（sysAdmin）
- 租户管理员（tenantAdmin）

请确保您的token具有相应权限。

## 接口列表

现在包含6个管理员接口：

1. **管理员查询首页模板列表** - `POST /train/tdh/index/admin/templates`
2. **新增首页模板** - `POST /train/tdh/index/admin/template/add`
3. **更新首页模板** - `POST /train/tdh/index/admin/template/update`
4. **删除首页模板** - `POST /train/tdh/index/admin/template/delete`
5. **首页模板排序** - `POST /train/tdh/index/admin/template/sort`
6. **首页模板上下架** - `POST /train/tdh/index/admin/template/status`

## 常见问题

### Q: 导入后接口无法访问？
A: 检查以下项目：
1. baseUrl是否正确
2. token是否有效且具有管理员权限
3. 服务是否正常运行

### Q: 权限验证失败？
A: 确认：
1. token格式是否正确（Bearer + 空格 + token）
2. token是否过期
3. 用户是否具有管理员权限

### Q: 参数验证失败？
A: 检查：
1. 必填字段是否都已填写
2. 字段格式是否正确
3. 参考示例请求进行调整

## 技术支持

如果在使用过程中遇到问题，请检查：
1. 接口文档中的示例
2. 响应错误信息
3. 服务端日志

祝您使用愉快！
