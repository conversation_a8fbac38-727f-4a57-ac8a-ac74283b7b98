package com.tydic.nbchat.train.report.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.NbchatTrainExamReportApi;
import com.tydic.nbchat.train.api.bo.ScoreRangeCount;
import com.tydic.nbchat.train.api.bo.constants.CourseScoreConstants;
import com.tydic.nbchat.train.api.bo.eums.RpCourseItemType;
import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseCountItemRspBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseDataAnalysisRspBO;
import com.tydic.nbchat.train.api.bo.report.dialogue.TrainRpDialogueRankRspBO;
import com.tydic.nbchat.train.api.bo.report.exam.TrainRpExamStudentsQueryReqBO;
import com.tydic.nbchat.train.api.bo.report.exam.TrainRpExamStudentsQueryRspBO;
import com.tydic.nbchat.train.api.bo.report.item.StstCountItemValue;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpCourseCountApi;
import com.tydic.nbchat.train.mapper.NbchatExamTestRecordMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainRpDayItemMapper;
import com.tydic.nbchat.train.mapper.po.NbchatExamTestRecordCodition;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpDayItem;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpDayItemCondition;
import com.tydic.nbchat.train.mapper.po.QoqPO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@AllArgsConstructor
public class NbChatTrainRpExamImpl implements NbchatTrainExamReportApi {

    private final NbchatTrainRpDayItemMapper nbchatTrainRpDayItemMapper;
    private final NbchatExamTestRecordMapper nbchatExamTestRecordMapper;

    private final NbcahtTrainRpCourseCountApi nbcahtTrainRpCourseCountApi;

    /**
     * 获取测评人次/获取测评人数
     *
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp getEvaluationCount(TrainRpExamStudentsQueryReqBO reqBO) {
        log.info("统计环比数据：{}", reqBO);
        Rsp rsp = nbcahtTrainRpCourseCountApi.getEvaluationCount(reqBO);
        log.info("统计环比数据-结束：{}", rsp);
        return BaseRspUtils.createSuccessRsp(rsp);
    }

    /**
     * 获取综合评分
     *
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp getCompositeScore(TrainRpExamStudentsQueryReqBO reqBO) {
        log.info("获取综合评分：{}", reqBO);
        TrainRpExamStudentsQueryRspBO trainRpExamStudentsQueryRspBO = new TrainRpExamStudentsQueryRspBO();
        NbchatExamTestRecordCodition nbchatExamTestRecordCodition = new NbchatExamTestRecordCodition();
        BeanUtils.copyProperties(reqBO, nbchatExamTestRecordCodition);
        nbchatExamTestRecordCodition = nbchatExamTestRecordMapper.getCompositeScore(nbchatExamTestRecordCodition);
        if (Objects.isNull(nbchatExamTestRecordCodition)) {
            log.warn("获取综合评分-查询结果为空");
            return BaseRspUtils.createSuccessRsp(trainRpExamStudentsQueryRspBO, "查询失败");
        }
        BeanUtils.copyProperties(nbchatExamTestRecordCodition, trainRpExamStudentsQueryRspBO);
        log.info("获取综合评分-结束：{}", trainRpExamStudentsQueryRspBO);
        return BaseRspUtils.createSuccessRsp(trainRpExamStudentsQueryRspBO, "查询成功");
    }

    /**
     * 获取数据趋势
     *
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public RspList getDataTrend(QueryReportRequest request) {
        log.info("统计课程学习趋势：{}", request);
        if (request.getStartDate() == null) {
            request.setStartDate(DateTimeUtil.DateAddDayOfYear(-30));
        }
        if (request.getEndDate() == null) {
            request.setEndDate(new Date());
        }
        //学习人数趋势
        List<TrainRpCourseDataAnalysisRspBO> res = new ArrayList<>();
        for (RpCourseItemType value : RpCourseItemType.examItems()) {
            TrainRpCourseDataAnalysisRspBO bo = nbcahtTrainRpCourseCountApi.calGraph(request, value);
            res.add(bo);
        }
        return BaseRspUtils.createSuccessRspList(res);
    }

    /**
     * 获取测评排行
     *
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public RspList getEvaluationRanking(TrainRpExamStudentsQueryReqBO reqBO) {
        log.info("获取测评排行：{}", reqBO);
        DateTimeUtil.DateAddDayOfYear(1);
        if (reqBO.getStartDate() == null || reqBO.getEndDate() == null) {
            reqBO.setStartDate(DateTimeUtil.DateAddDayOfYear(-7));
            reqBO.setEndDate(DateTimeUtil.DateAddDayOfYear(-1));
        }
        //测评人数
        List<TrainRpExamStudentsQueryRspBO> res = new ArrayList<>();
        NbchatTrainRpDayItemCondition nbchatTrainRpDayItemCondition = new NbchatTrainRpDayItemCondition();
        BeanUtils.copyProperties(reqBO, nbchatTrainRpDayItemCondition);
        List<NbchatTrainRpDayItemCondition> nbchatTrainRpDayItemConditionList = nbchatTrainRpDayItemMapper.getEvaluationRanking(nbchatTrainRpDayItemCondition);
        log.info("获取测评排行-数据库查询结果共有：{}条", nbchatTrainRpDayItemConditionList.size());
        if (CollectionUtils.isEmpty(nbchatTrainRpDayItemConditionList)) {
            log.warn("获取测评排行-查询结果为空");
            return BaseRspUtils.createSuccessRspList(res, Long.parseLong(CourseScoreConstants.ZERO));
        }
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, TrainRpExamStudentsQueryRspBO> resultMap = new HashMap<>();

        for (NbchatTrainRpDayItemCondition item : nbchatTrainRpDayItemConditionList) {
            List<ScoreRangeCount> scoreData = new ArrayList<>();

            try {
                List<Map<String, Integer>> rawScoreData = objectMapper.readValue(item.getItemValues(), new TypeReference<List<Map<String, Integer>>>() {
                });
                for (Map<String, Integer> map : rawScoreData) {
                    for (Map.Entry<String, Integer> entry : map.entrySet()) {
                        scoreData.add(new ScoreRangeCount(entry));
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            Map<String, Integer> scoreCounts = scoreData.stream().collect(Collectors.toMap(ScoreRangeCount::getRange, ScoreRangeCount::getCount));

            TrainRpExamStudentsQueryRspBO rspBO = resultMap.getOrDefault(item.getCourseId(), new TrainRpExamStudentsQueryRspBO());

            // Set or aggregate the courseName, students, avgScore
            rspBO.setCourseName(item.getCourseName());

            int existingStudents = 0;
            if (StringUtils.isNotBlank(rspBO.getStudents())) {
                existingStudents = Integer.parseInt(rspBO.getStudents());
            }

            int newStudents = (int) Double.parseDouble(item.getStudents());
            rspBO.setStudents(String.valueOf(existingStudents + newStudents));

            float existingAvgScore = 0;
            if (StringUtils.isNotBlank(rspBO.getAvgScore())) {
                existingAvgScore = Float.parseFloat(rspBO.getAvgScore());
            }
            float combinedAvgScore = 0.0f;

            if (existingStudents + newStudents != 0) {
                combinedAvgScore = ((existingAvgScore * existingStudents) + (Float.parseFloat(item.getAvgScore()) * newStudents)) / (existingStudents + newStudents);
            }
            rspBO.setAvgScore(String.format("%.2f", combinedAvgScore));

            rspBO.setFailedStudents(String.valueOf(Integer.parseInt(rspBO.getFailedStudents()) + scoreCounts.getOrDefault("0-59", 0)));
            rspBO.setPassStudents(String.valueOf(Integer.parseInt(rspBO.getPassStudents()) + scoreCounts.getOrDefault("60-79", 0)));
            rspBO.setGoodStudents(String.valueOf(Integer.parseInt(rspBO.getGoodStudents()) + scoreCounts.getOrDefault("80-89", 0)));
            rspBO.setExcellentStudents(String.valueOf(Integer.parseInt(rspBO.getExcellentStudents()) + scoreCounts.getOrDefault("90-100", 0)));

            int totalStudents = Integer.parseInt(rspBO.getStudents());
            rspBO.setFailed(calculatePercentage(Integer.parseInt(rspBO.getFailedStudents()), totalStudents));
            rspBO.setPass(calculatePercentage(Integer.parseInt(rspBO.getPassStudents()), totalStudents));
            rspBO.setGood(calculatePercentage(Integer.parseInt(rspBO.getGoodStudents()), totalStudents));
            rspBO.setExcellent(calculatePercentage(Integer.parseInt(rspBO.getExcellentStudents()), totalStudents));

            resultMap.put(item.getCourseId(), rspBO);
        }
        res = new ArrayList<>(resultMap.values());

        log.info("获取测评排行-计算结果：{}", res.size());
        if (StringUtils.isNotBlank(reqBO.getSort()) && reqBO.getSort().equals("1")) {
            res = (ArrayList<TrainRpExamStudentsQueryRspBO>) res.stream()
                    .sorted(Comparator.comparing((TrainRpExamStudentsQueryRspBO bo) -> Double.parseDouble(sortKey(reqBO.getSortField()).apply(bo))))
                    .collect(Collectors.toList());
        } else {
            res = (ArrayList<TrainRpExamStudentsQueryRspBO>) res.stream()
                    .sorted(Comparator.comparing((TrainRpExamStudentsQueryRspBO bo) -> Double.parseDouble(sortKey(reqBO.getSortField()).apply(bo))).reversed())
                    .collect(Collectors.toList());
        }
        ArrayList resList = res.stream().skip((reqBO.getPage() - 1) * reqBO.getLimit()).limit(reqBO.getLimit()).collect(Collectors.toCollection(ArrayList::new));
        log.info("获取测评排行-排序结果：{},共有：{}条", resList, nbchatTrainRpDayItemConditionList.size());
        return BaseRspUtils.createSuccessRspList(resList, nbchatTrainRpDayItemConditionList.size());
    }


    /**
     * 计算平均分数
     * @param @param      numerator 分子
     * @param denominator 分母
     * @return @return {@link String }
     */
    private static String calculatePercentage(int numerator, int denominator) {
        if (denominator == 0) {
            return "0.00";  // or whatever default you'd like if there are no students
        }
        return String.format("%.2f", ((double) numerator / denominator) * 100);
    }

    /**
     * 排序字段
     * @param @param key 关键
     * @return @return {@link Function }<{@link TrainRpExamStudentsQueryRspBO }, {@link String }>
     */
    public Function<TrainRpExamStudentsQueryRspBO, String> sortKey(String key) {
        if (StringUtils.isBlank(key)) {
            return TrainRpExamStudentsQueryRspBO::getStudents;
        }
        if (key.equals("failed")) {
            return TrainRpExamStudentsQueryRspBO::getFailed;
        }
        if (key.equals("pass")) {
            return TrainRpExamStudentsQueryRspBO::getPass;
        }
        if (key.equals("good")) {
            return TrainRpExamStudentsQueryRspBO::getGood;
        }
        if (key.equals("excellent")) {
            return TrainRpExamStudentsQueryRspBO::getExcellent;
        }
        if (key.equals("avgScore")) {
            return TrainRpExamStudentsQueryRspBO::getAvgScore;
        }
        if (key.equals("students")) {
            return TrainRpExamStudentsQueryRspBO::getStudents;
        }
        log.info("不支持的排序字段：{}", key);
        return null;
    }
}
