package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.api.NbchatTrainStudentsReportApi;
import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.bo.report.students.TrainRpStudentsQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/train/rp/students")
public class NbchatTrainRpStudentsController {
    private final NbchatTrainStudentsReportApi nbchatTrainStudentsReportApi;

    /**
     * 查询新增学员数/查询学员总数
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/count")
    public Rsp getNewStudentsCount(@RequestBody TrainRpStudentsQueryReqBO reqBO){
        Rsp rsp = nbchatTrainStudentsReportApi.getNewStudentsCount(reqBO);
        return rsp;
    }

    /**
     * 查询数据趋势
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/trend")
    public RspList getDataTrend(@RequestBody QueryReportRequest reqBO){
        RspList rsp = nbchatTrainStudentsReportApi.getDataTrend(reqBO);
        return rsp;
    }

    /**
     * 数据分析
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/analysis")
    public Rsp analyzeData(@RequestBody TrainRpStudentsQueryReqBO reqBO){
        Rsp rsp = nbchatTrainStudentsReportApi.analyzeData(reqBO);
        return rsp;
    }
}
