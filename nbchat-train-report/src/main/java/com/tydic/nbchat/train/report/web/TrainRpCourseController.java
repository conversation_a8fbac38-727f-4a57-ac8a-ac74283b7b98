package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.api.bo.report.course.*;
import com.tydic.nbchat.train.api.bo.report.exam.TrainRpExamStudentsQueryReqBO;
import com.tydic.nbchat.train.api.bo.report.students.TrainRpStudentsQueryReqBO;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpCourseCountApi;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpCourseSaveApi;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpCourseTopApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/rp/course")
public class TrainRpCourseController {


    private final NbcahtTrainRpCourseSaveApi nbcahtTrainRpCourseSaveApi;
    private final NbcahtTrainRpCourseTopApi nbcahtTrainRpCourseTopApi;
    private final NbcahtTrainRpCourseCountApi nbcahtTrainRpCourseCountApi;

    public TrainRpCourseController(@Autowired(required = false) NbcahtTrainRpCourseSaveApi nbcahtTrainRpCourseSaveApi,
                                   @Autowired(required = false) NbcahtTrainRpCourseTopApi nbcahtTrainRpCourseTopApi,
                                   @Autowired(required = false) NbcahtTrainRpCourseCountApi nbcahtTrainRpCourseCountApi) {
        this.nbcahtTrainRpCourseSaveApi = nbcahtTrainRpCourseSaveApi;
        this.nbcahtTrainRpCourseTopApi = nbcahtTrainRpCourseTopApi;
        this.nbcahtTrainRpCourseCountApi = nbcahtTrainRpCourseCountApi;
    }

    /**
     * 学习人次记录保存
     * @param reqBO
     * @return
     */
    @PostMapping("/pt/save")
    public Rsp saveRpStudyPtRecord(@RequestBody TrainRpCoursePtSaveReqBO reqBO) {
        return nbcahtTrainRpCourseSaveApi.saveRpCoursePtRecord(reqBO);
    }

    @PostMapping("/pn/save")
    public Rsp saveRpCoursePnRecord(@RequestBody TrainRpCoursePnSaveReqBO reqBO){
        return nbcahtTrainRpCourseSaveApi.saveRpCoursePnRecord(reqBO);
    }

    /**
     * top5列表
     * @param top5QueryReqBO
     * @return
     */
    @PostMapping("/top5")
    public RspList getCourseHotTopList(@RequestBody TrainRpCourseTop5QueryReqBO top5QueryReqBO) {
        return nbcahtTrainRpCourseTopApi.getCourseHotTopList(top5QueryReqBO);
    }

    /**
     * 排行列表
     * @param queryReqBO
     * @return
     */
    @PostMapping("/ranking")
    public RspList getCourseRankingList(@RequestBody TrainRpCourseRankingQueryReqBO queryReqBO) {
        return nbcahtTrainRpCourseTopApi.getCourseRankingList(queryReqBO);
    }

    /**
     * 学习指标统计查询
     * @param itemReqBO
     * @return
     */
    @PostMapping("/study/item")
    public Rsp<TrainRpCourseCountItemRspBO> countCourseStudyItem(@RequestBody TrainRpCourseCountItemReqBO itemReqBO) {
        return nbcahtTrainRpCourseCountApi.countCourseStudyItem(itemReqBO);
    }

    /**
     * 数据图标分析
     * @param analysisReqBO
     * @return
     */
    @PostMapping("/study/analysis")
    public RspList getCourseItemAnalysisDataList(@RequestBody TrainRpCourseDataAnalysisReqBO analysisReqBO) {
        return nbcahtTrainRpCourseCountApi.getCourseItemAnalysisDataList(analysisReqBO);
    }

}
