package com.tydic.nbchat.train.report.web;


import com.tydic.nbchat.train.api.NbchatOpRpStstDayItemService;
import com.tydic.nbchat.train.api.NbchatOpRpStstPtService;
import com.tydic.nbchat.train.api.bo.report.op.TrainOpStstPtRequestBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/train/rp/op")
public class TrainRpOperationsController {

    private final NbchatOpRpStstDayItemService ststDayItemService;
    private final NbchatOpRpStstPtService ststPtService;

    /**
     * 统计视频制作
     * 统计ppt制作
     * 统计出题次数
     * @param requestBO
     * @return
     */
    @PostMapping("/save")
    public Rsp save(@RequestBody TrainOpStstPtRequestBO requestBO){
        Rsp rsp = ststPtService.save(requestBO);
        return rsp;
    }

    /**
     * 查询报表
     * @param requestBO
     * @return
     */
    @PostMapping("/query/report")
    public Rsp queryReport(@RequestBody TrainOpStstPtRequestBO requestBO){
        Rsp rsp = ststPtService.queryReport(requestBO);
        return rsp;
    }

    @PostMapping("/query/report/list")
    public RspList queryReportList(@RequestBody TrainOpStstPtRequestBO requestBO){
        RspList rsp = ststPtService.queryTenantDataList(requestBO);
        return rsp;
    }

//    @PostMapping("/query/report/tenant/list")
//    public Rsp queryReportTenantList(@RequestBody TrainOpStstPtRequestBO requestBO){
//        Rsp rsp = ststPtService.queryReportTenantList(requestBO);
//        return rsp;
//    }
}
