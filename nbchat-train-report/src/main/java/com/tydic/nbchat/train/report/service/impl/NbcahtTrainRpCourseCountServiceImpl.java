package com.tydic.nbchat.train.report.service.impl;

import com.tydic.nbchat.train.api.bo.eums.ReportType;
import com.tydic.nbchat.train.api.bo.eums.RpCourseItemType;
import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.bo.report.course.*;
import com.tydic.nbchat.train.api.bo.report.exam.TrainRpExamStudentsQueryReqBO;
import com.tydic.nbchat.train.api.bo.report.item.StstAnalysisItemValue;
import com.tydic.nbchat.train.api.bo.report.item.StstCountItemValue;
import com.tydic.nbchat.train.api.bo.report.dialogue.TrainRpDialogueCountItemRspBO;
import com.tydic.nbchat.train.api.bo.report.students.TrainRpStudentsQueryReqBO;
import com.tydic.nbchat.train.api.bo.report.students.TrainRpstudentsCountItemRspBO;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpCourseCountApi;
import com.tydic.nbchat.train.mapper.NbchatTrainRpDayItemMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpDayItem;
import com.tydic.nbchat.train.mapper.po.QoqPO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class NbcahtTrainRpCourseCountServiceImpl implements NbcahtTrainRpCourseCountApi {

    @Resource
    NbchatTrainRpDayItemMapper nbchatTrainRpDayItemMapper;

    @Override
    public Rsp<TrainRpCourseCountItemRspBO> countCourseStudyItem(TrainRpCourseCountItemReqBO itemReqBO) {
        log.info("统计环比数据：{}",itemReqBO);
        TrainRpCourseCountItemRspBO rspBO = new TrainRpCourseCountItemRspBO();

        StstCountItemValue studyCount = countQoq(RpCourseItemType.course_pn_count.getCode(), itemReqBO.getTenantCode());
        StstCountItemValue studyEndCount = countQoq(RpCourseItemType.course_pn_end_count.getCode(), itemReqBO.getTenantCode());

        rspBO.setStudyCount(studyCount);
        rspBO.setStudyEndCount(studyEndCount);
        log.info("统计环比数据-结束：{}",rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }
    /**
     * 获取测评人次/获取测评人数
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp getEvaluationCount(TrainRpExamStudentsQueryReqBO request) {
        log.info("获取测评人次/获取测评人数-统计环比数据：{}",request);
        TrainRpDialogueCountItemRspBO rspBO = new TrainRpDialogueCountItemRspBO();
        StstCountItemValue ptCount = countQoq(RpCourseItemType.exam_pt_count.getCode(), request.getTenantCode());
        StstCountItemValue pnCount = countQoq(RpCourseItemType.exam_pn_count.getCode(), request.getTenantCode());
        StstCountItemValue avgScoreCount = countQoq(RpCourseItemType.exam_avg_score_count.getCode(), request.getTenantCode());

        rspBO.setPtCount(ptCount);
        rspBO.setPnCount(pnCount);
        rspBO.setAvgScoreCount(avgScoreCount);
        log.info("获取测评人次/获取测评人数-统计环比数据-结束：{}",rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    /**
     * 查询新增学员数/查询学员总数
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp getNewStudentsCount(TrainRpStudentsQueryReqBO reqBO) {
        log.info("查询新增学员数/查询学员总数-统计环比数据：{}",reqBO);
        TrainRpstudentsCountItemRspBO rspBO = new TrainRpstudentsCountItemRspBO();
        StstCountItemValue userNewCount = countQoq(RpCourseItemType.user_new_count.getCode(), reqBO.getTenantCode());
        StstCountItemValue userTotalCount = countQoq(RpCourseItemType.user_total_count.getCode(), reqBO.getTenantCode());
        rspBO.setUserNewCount(userNewCount);
        rspBO.setUserTotalCount(userTotalCount);
        log.info("查询新增学员数/查询学员总数-统计环比数据-结束：{}",rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    @Override
    public RspList<TrainRpCourseDataAnalysisRspBO> getCourseItemAnalysisDataList(TrainRpCourseDataAnalysisReqBO reqBO) {
        log.info("统计课程学习趋势：{}",reqBO);
        if (reqBO.getStartDate() == null) {
            reqBO.setStartDate(DateTimeUtil.DateAddDayOfYear(-30));
        }
        if (reqBO.getEndDate() == null) {
            reqBO.setEndDate(new Date());
        }
        //学习人数趋势
        List<TrainRpCourseDataAnalysisRspBO> res = new ArrayList<>();
        for (RpCourseItemType value : RpCourseItemType.courseItems()) {
            TrainRpCourseDataAnalysisRspBO bo = calGraph(reqBO, value);
            res.add(bo);
        }
        return BaseRspUtils.createSuccessRspList(res);
    }

    @Override
    public Rsp<TrainRpDialogueCountItemRspBO> countDialogueItem(TrainRpCourseCountItemReqBO request) {
        log.info("统计场景实践环比：{}",request);
        TrainRpDialogueCountItemRspBO rspBO = new TrainRpDialogueCountItemRspBO();

        StstCountItemValue ptCount = countQoq(RpCourseItemType.dialogue_pt_count.getCode(), request.getTenantCode());
        StstCountItemValue pnCount = countQoq(RpCourseItemType.dialogue_pn_count.getCode(), request.getTenantCode());
        StstCountItemValue avgScoreCount = countQoq(RpCourseItemType.dialogue_avg_score_count.getCode(), request.getTenantCode());

        rspBO.setPtCount(ptCount);
        rspBO.setPnCount(pnCount);
        rspBO.setAvgScoreCount(avgScoreCount);
        log.info("统计环比数据-结束：{}",rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO);
    }

    public TrainRpCourseDataAnalysisRspBO calGraph(QueryReportRequest request,RpCourseItemType itemType){
        TrainRpCourseDataAnalysisReqBO reqBO = new TrainRpCourseDataAnalysisReqBO();
        BeanUtils.copyProperties(request,reqBO);
        return calGraph(reqBO,itemType);
    }

    public TrainRpCourseDataAnalysisRspBO calGraph(TrainRpCourseDataAnalysisReqBO reqBO,RpCourseItemType itemType) {
        NbchatTrainRpDayItem rec = new NbchatTrainRpDayItem();
        BeanUtils.copyProperties(reqBO,rec);
        rec.setItemCode(itemType.getCode());
        List<NbchatTrainRpDayItem> pns = nbchatTrainRpDayItemMapper.countGraph(rec);
        TrainRpCourseDataAnalysisRspBO pnBO = new TrainRpCourseDataAnalysisRspBO();
        pnBO.setItemName(itemType.getName());
        pnBO.setItemCode(itemType.getCode());
        List<StstAnalysisItemValue> itemValues = new ArrayList<>();
        for (NbchatTrainRpDayItem pn : pns) {
            StstAnalysisItemValue value = new StstAnalysisItemValue();
            value.setCountDay(DateTimeUtil.getTimeShortString(pn.getCountDay(),DateTimeUtil.DATE_FORMAT_SHORT));
            value.setItemValue(pn.getValue());
            itemValues.add(value);
        }
        pnBO.setItemValues(itemValues);
        return pnBO;
    }


    /**
     * 统计环比
     * @param @param     itemCode 项目代码
     * @param tenantCode 租户代码
     * @return @return {@link StstCountItemValue }
     */
    public StstCountItemValue countQoq(String itemCode, String tenantCode){
        StstCountItemValue countRec = new StstCountItemValue();
        NbchatTrainRpDayItem param = new NbchatTrainRpDayItem();
        param.setCountDay(DateTimeUtil.DateAddDayOfYear(-1));
        param.setItemCode(itemCode);
        param.setTenantCode(tenantCode);
        int coursePnCount = nbchatTrainRpDayItemMapper.countByCode(param);
        countRec.setTotal(coursePnCount);
        //日环比
        int dayQoq = calQoq(param, -(1 + 1));
        countRec.setDayQoq(dayQoq);
        //周环比
        int weekQoq = calQoq(param, -(1 + 7));
        countRec.setWeekQoq(weekQoq);
        //月环比
        int monQoq = calQoq(param, -(1 + 30));
        countRec.setMonthQoq(monQoq);
        return countRec;
    }

    /**
     * 计算比例
     * @param @param    param 参数
     * @param targetDay 目标一天
     * @return @return int
     */
    public int calQoq(NbchatTrainRpDayItem param,Integer targetDay){
        param.setCountDay2(DateTimeUtil.DateAddDayOfYear(targetDay));
        List<QoqPO> qoqRecDay = new ArrayList<>();
        qoqRecDay = nbchatTrainRpDayItemMapper.countQoqRec(param);
        if (CollectionUtils.isEmpty(qoqRecDay)) {
            return 0;
        }
        if (qoqRecDay.size() == 1) {
            String countDay = qoqRecDay.get(0).getCountDay();
            String day1 = DateTimeUtil.getTimeShortString(param.getCountDay(), DateTimeUtil.DATE_FORMAT_NORMAL);
            String day2 = DateTimeUtil.getTimeShortString(param.getCountDay2(), DateTimeUtil.DATE_FORMAT_NORMAL);
            if (Objects.equals(countDay, day2)) {
                return 0;
            }
            return qoqRecDay.get(0).getValue() * 100;
        }
        QoqPO qoqPO1 = qoqRecDay.get(0);
        QoqPO qoqPO2 = qoqRecDay.get(1);
        if (qoqPO1.getValue() == 0) {
            return qoqPO2.getValue() * 100;
        }
        return 100 * (qoqPO2.getValue() - qoqPO1.getValue()) / qoqPO1.getValue();
    }
}
