package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.api.NbchatTrainRpVideoService;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQueryReqBO;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/train/creation_admin/tdh")
class NbchatTrainRpVideoController {
    private final NbchatTrainRpVideoService nbchatTrainRpVideoService;

    @PostMapping("/list")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public RspList getVideoList(@RequestBody NbchatVideoQueryReqBO reqBO){
        return  nbchatTrainRpVideoService.getVideoList(reqBO);
    }

    @PostMapping("/count")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp getVideoCount(@RequestBody NbchatVideoQueryReqBO reqBO){
        Rsp rsp = nbchatTrainRpVideoService.getVideoCount(reqBO);
        return rsp;
    }

}
