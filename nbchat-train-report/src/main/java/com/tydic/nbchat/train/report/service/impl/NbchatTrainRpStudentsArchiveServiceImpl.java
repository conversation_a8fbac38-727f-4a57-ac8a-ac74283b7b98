package com.tydic.nbchat.train.report.service.impl;

import com.tydic.nbchat.train.api.NbchatTrainStudentsArchiveReportApi;
import com.tydic.nbchat.train.api.bo.course.NbchatTrainStudentCourseBO;
import com.tydic.nbchat.train.api.bo.course.NbchatTrainStudentTrainingBO;
import com.tydic.nbchat.train.api.bo.course.TranStudentArchiveQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TranStudentArchiveRspBO;
import com.tydic.nbchat.train.api.bo.task.NbchatTaskRecordBO;
import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskBO;
import com.tydic.nbchat.train.api.trainTask.TrainTaskApi;
import com.tydic.nbchat.train.mapper.NameMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainRpStudentsArchiveMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainStudentArchivePO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime：2024/8/26
 * @description:类描述
 */
@Service
@Slf4j
public class NbchatTrainRpStudentsArchiveServiceImpl implements NbchatTrainStudentsArchiveReportApi {
    @Resource
    private NbchatTrainRpStudentsArchiveMapper nbchatTrainRpStudentsArchiveMapper;

    @Resource
    private NameMapper nameMapper;

    @Resource
    private TrainTaskApi trainTaskApi;

    @Override
    public Rsp getStudentArchive(TranStudentArchiveQueryReqBO reqBO) {
        log.info("学员档案查询请求参数：{}", reqBO);
        String deptName = nameMapper.queryOrganizeName(reqBO.getDeptId());
        NbchatTrainStudentArchivePO nbchatTrainStudentArchivePO = nbchatTrainRpStudentsArchiveMapper.queryArchive(reqBO.getId(), reqBO.getTenantCode());
        String formattedDate = DateTimeUtil.getTimeShortString(nbchatTrainStudentArchivePO.getCreateTime(), "yyyy-MM-dd");
        nbchatTrainStudentArchivePO.setEntryTime(formattedDate);
        String name = splitDeptName(deptName);
        nbchatTrainStudentArchivePO.setProjectName(name);
        TranStudentArchiveRspBO rspBO = new TranStudentArchiveRspBO();
        BeanUtils.copyProperties(nbchatTrainStudentArchivePO, rspBO);
        NbchatTrainTaskBO taskBO = new NbchatTrainTaskBO();

        taskBO.setUserId(rspBO.getUserId());
        taskBO.setDeptId(reqBO.getDeptId());
        taskBO.setTenantCode(reqBO.getTenantCode());
        RspList<NbchatTaskRecordBO> rspList = trainTaskApi.queryTask(taskBO);
        List<NbchatTaskRecordBO> rows = rspList.getRows();
        rspBO.setTrainingList(new ArrayList<>());

        List<NbchatTrainStudentTrainingBO> list = new ArrayList<>();
        rows.forEach(s -> {
            if (s.getFinishStatus().equals("1")) {
                if (s.getInfoList() != null) {
                    NbchatTrainStudentTrainingBO bo = new NbchatTrainStudentTrainingBO();
                    bo.setTaskId(s.getTaskId());
                    bo.setTaskName(s.getTaskName());
                    s.getInfoList().forEach(item -> {
                        NbchatTrainStudentCourseBO courseBO = new NbchatTrainStudentCourseBO();
                        courseBO.setTaskId(s.getTaskId());
                        courseBO.setCourseId(item.getCourseId());
                        courseBO.setCourseName(item.getCourseName());
                        courseBO.setScore(item.getTestScore());
                        Double videoDuration = nbchatTrainRpStudentsArchiveMapper.queryVideoDuration(item.getCourseId(), rspBO.getTenantCode());
                        if (videoDuration != null) {
                            Double hours = calculateVideoDuration(videoDuration);
                            courseBO.setVideoDuration(hours);
                        }
                        if (bo.getCourseList() == null) {
                            bo.setCourseList(new ArrayList<>());
                        }
                        bo.getCourseList().add(courseBO);
                    });
                    list.add(bo);
                }
            }
        });
        list.forEach(item -> {
            if (item.getCourseList() != null && !item.getCourseList().isEmpty()) {
                rspBO.getTrainingList().add(item);
            }
        });
        return BaseRspUtils.createSuccessRsp(rspBO, "查询成功");
    }

    @Override
    public RspList getStudentArchiveList(TranStudentArchiveQueryReqBO reqBO) {
        log.info("批量查询学员档案请求参数：{}", reqBO);
        List<TranStudentArchiveRspBO> list = new ArrayList<>();
        if (reqBO.getIds() != null) {
            reqBO.getIds().forEach(item -> {
                reqBO.setId(item);
                Rsp studentArchive = getStudentArchive(reqBO);
                list.add((TranStudentArchiveRspBO) studentArchive.getData());
            });
            return BaseRspUtils.createSuccessRspList(list, list.size());
        } else {
            List<NbchatTrainStudentArchivePO> nbchatTrainStudentArchivePOS = nbchatTrainRpStudentsArchiveMapper.queryIds(reqBO.getDeptId(), reqBO.getTenantCode(), reqBO.getSupportSubDept());
            List<TranStudentArchiveQueryReqBO> reqBOList = new ArrayList<>();
            NiccCommonUtil.copyList(nbchatTrainStudentArchivePOS, reqBOList, TranStudentArchiveQueryReqBO.class);
            reqBOList.forEach(item -> {
                Rsp studentArchive = getStudentArchive(item);
                list.add((TranStudentArchiveRspBO) studentArchive.getData());
            });
            return BaseRspUtils.createSuccessRspList(list, list.size());
        }

    }

    /**
     * 分割部门名称
     *
     * @param deptName
     * @return
     */
    public String splitDeptName(String deptName) {
        if (deptName != null && !deptName.isEmpty()) {
            String[] split = deptName.split("-");
            if (split.length < 3) {
                deptName = split[split.length - 1];
            } else {
                String[] strings = Arrays.copyOfRange(split, 1, 3);
                deptName = String.join("-", strings);
            }
        }
        return deptName;
    }

    /**
     * 计算学时
     *
     * @param videoDuration
     * @return
     */
    public Double calculateVideoDuration(Double videoDuration) {
        double video = (videoDuration / 60);
        if (video < 20) {
            return 0.2;
        } else if (video < 45) {
            return 0.5;
        } else {
            double fullHours = Math.floor(video / 45);
            double remainingMinutes = video % 45;
            return fullHours + (remainingMinutes < 20 ? 0.2 : 0.5);
        }
    }
}
