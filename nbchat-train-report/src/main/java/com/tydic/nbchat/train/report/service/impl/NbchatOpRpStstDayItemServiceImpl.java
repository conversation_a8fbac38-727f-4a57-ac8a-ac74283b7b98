package com.tydic.nbchat.train.report.service.impl;

import com.tydic.nbchat.train.api.NbchatOpRpStstDayItemService;
import com.tydic.nbchat.train.api.bo.report.op.TrainOpStstPtRequestBO;
import com.tydic.nbchat.train.mapper.OpRpStstPtMapper;
import com.tydic.nbchat.train.mapper.po.OpRpStstPt;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class NbchatOpRpStstDayItemServiceImpl implements NbchatOpRpStstDayItemService {

}
