package com.tydic.nbchat.train.report.service.impl.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.train.api.NbchatTrainStudentsArchiveReportApi;
import com.tydic.nbchat.train.api.bo.course.NbchatTrainStudentCourseBO;
import com.tydic.nbchat.train.api.bo.course.NbchatTrainStudentTrainingBO;
import com.tydic.nbchat.train.api.bo.course.TranStudentArchiveQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TranStudentArchiveRspBO;
import com.tydic.nbchat.train.report.service.impl.excel.entity.TaskCourseExcelEntity;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ArchiveExportService {


    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private FileManageService fileManageService;

    @Resource
    private NbchatTrainStudentsArchiveReportApi nbchatTrainStudentsArchiveReportApi;

    public Rsp export(TranStudentArchiveQueryReqBO request) throws Exception {
        Rsp rsp = nbchatTrainStudentsArchiveReportApi.getStudentArchive(request);
        if (!rsp.isSuccess()) {
            return rsp;
        }
        TranStudentArchiveRspBO data = (TranStudentArchiveRspBO) rsp.getData();
        String filePath = this.export(data);
        log.info("档案本地路径：{}", filePath);
        RspList rspList = this.upload(filePath);
        log.info("上传结果：{}", rspList);
        if (rspList.isSuccess()) {
            FileManageSaveBO res = (FileManageSaveBO) rspList.getRows().get(0);
            return BaseRspUtils.createSuccessRsp(res.getAccessUrl());
        } else {
            return BaseRspUtils.createErrorRsp("导出失败");
        }
    }

    public RspList upload(String filePath) throws Exception {
        log.info("上传档案请求参数：{}", filePath);
        String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
        byte[] byteArray = IOUtils.toByteArray(new FileInputStream(filePath));
        FileUploadRequest uploadRequest = FileUploadRequest.builder().
                file(byteArray).fileName(fileName)
                .uploadUser("2").tenantCode("00000000").build();
        return fileManageService.fileUploadRequest(uploadRequest);
    }

    public String export(TranStudentArchiveRspBO data) throws Exception {
        int mergeIndex = 5;
        String filePath = "/tmp/" + System.currentTimeMillis() + ".xlsx";
        ClassPathResource resource = new ClassPathResource("template/archive_tmpl.xlsx");
        InputStream inputStream = resource.getInputStream();

        try (ExcelWriter excelWriter = EasyExcel.write(filePath).withTemplate(inputStream).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1")
                    .registerWriteHandler(new TaskMergeStrategy()) //设置合并策略
                    .registerWriteHandler(new CustomCellStyleStrategy()) //设置样式
                    .relativeHeadRowIndex(4)
                    .build();
            //写入表单
            Map<String, String> fillMap = this.fillData(data);
            excelWriter.fill(fillMap, writeSheet);

            //写入任务-课程列表
            List<NbchatTrainStudentTrainingBO> taskList = data.getTrainingList();
            for (NbchatTrainStudentTrainingBO task : taskList) {
                int size = task.getCourseList().size();
                List<TaskCourseExcelEntity> rows = this.getRow(task);
                excelWriter.write(rows, writeSheet);

                if (size > 1) {
                    CellRangeAddress cellRangeAddress = new CellRangeAddress(mergeIndex, size + mergeIndex - 1, 0, 0);
                    excelWriter.writeContext().writeSheetHolder().getSheet().addMergedRegionUnsafe(cellRangeAddress);
                }
                mergeIndex += task.getCourseList().size();
            }
        }
        return filePath;
    }

    public List<TaskCourseExcelEntity> getRow(NbchatTrainStudentTrainingBO task) {
        List<NbchatTrainStudentCourseBO> list = task.getCourseList();
        List<TaskCourseExcelEntity> rows = new ArrayList<>();
        NiccCommonUtil.copyList(list, rows, TaskCourseExcelEntity.class);
        rows.get(0).setTaskName(task.getTaskName());
        return rows;
    }

    public Map<String, String> fillData(TranStudentArchiveRspBO data) {
        Map<String, String> map = new HashMap<>();
        map.put("projectName", data.getProjectName());
        map.put("name", data.getUserRealityName());
        map.put("idCard", data.getIdCard());
        map.put("phone", data.getPhone());
        map.put("entryTime", data.getEntryTime());
        return map;
    }



}
