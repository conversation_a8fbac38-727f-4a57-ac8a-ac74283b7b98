package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.api.bo.report.CountRpDayItemRequest;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpDayItemApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/train/rp/day")
public class TrainRpDayItemController {


    private final NbcahtTrainRpDayItemApi nbcahtTrainRpDayItemApi;

    public TrainRpDayItemController(NbcahtTrainRpDayItemApi nbcahtTrainRpDayItemApi) {
        this.nbcahtTrainRpDayItemApi = nbcahtTrainRpDayItemApi;
    }

    /**
     * 统计日报表
     * @param request
     * @return
     */
    @PostMapping("/count")
    public Rsp countRpDayItem(@RequestBody CountRpDayItemRequest request) {
        return nbcahtTrainRpDayItemApi.countRpDayItem(request);
    }

}
