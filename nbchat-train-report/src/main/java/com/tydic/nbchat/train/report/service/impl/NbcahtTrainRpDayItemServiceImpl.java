package com.tydic.nbchat.train.report.service.impl;

import com.tydic.nbchat.train.api.bo.eums.RpCourseItemType;
import com.tydic.nbchat.train.api.bo.report.CountRpDayItemRequest;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpDayItemApi;
import com.tydic.nbchat.train.mapper.NbchatTrainRpDayItemMapper;
import com.tydic.nbchat.train.mapper.OpRpStstDayItemMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpDayItemCountParam;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class NbcahtTrainRpDayItemServiceImpl implements NbcahtTrainRpDayItemApi {

    private final NbchatTrainRpDayItemMapper nbchatTrainRpDayItemMapper;
    private final OpRpStstDayItemMapper opRpStstDayItemMapper;

    public NbcahtTrainRpDayItemServiceImpl(NbchatTrainRpDayItemMapper nbchatTrainRpDayItemMapper,
                                           OpRpStstDayItemMapper opRpStstDayItemMapper) {
        this.nbchatTrainRpDayItemMapper = nbchatTrainRpDayItemMapper;
        this.opRpStstDayItemMapper = opRpStstDayItemMapper;
    }

    @Override
    public Rsp countRpDayItem(CountRpDayItemRequest request) {
        log.info("指标日报表统计:{}",request);
        if(ObjectUtils.allNull(request.getStartTime(),request.getEndTime())){
            //默认统计当天数据
            request.setStartTime(DateTimeUtil.getStartTimeOfDay());
            request.setEndTime(DateTimeUtil.getEndTimeOfDay());
        }
        NbchatTrainRpDayItemCountParam countParam = new NbchatTrainRpDayItemCountParam();
        countParam.setItemCode(request.getItemCode());
        countParam.setStartTime(request.getStartTime());
        countParam.setEndTime(request.getEndTime());
        int del = nbchatTrainRpDayItemMapper.deleteItem(countParam);
        int del2 = opRpStstDayItemMapper.deleteItem(countParam);
        log.info("指标日报表统计-删除数据:{}|{}|{}",countParam,del,del2);
        int count = 0;
        if(RpCourseItemType.course_pn_count.getCode().equals(request.getItemCode())){
            //统计学习人数
            count = nbchatTrainRpDayItemMapper.countCoursePnItems(countParam);
        } else if (RpCourseItemType.course_pn_end_count.getCode().equals(request.getItemCode())){
            //统计学习完成人数
            countParam.setIsFinish(EntityValidType.NORMAL.getCode());
            count = nbchatTrainRpDayItemMapper.countCoursePnItems(countParam);
        } else if (RpCourseItemType.course_pt_count.getCode().equals(request.getItemCode())){
            //统计学习人次
            count = nbchatTrainRpDayItemMapper.countCoursePtItems(countParam);
        } else if (RpCourseItemType.dialogue_pt_count.getCode().equals(request.getItemCode())){
            //统计场景对话人次
            count = nbchatTrainRpDayItemMapper.countDialoguePtItems(countParam);
        } else if (RpCourseItemType.dialogue_pn_count.getCode().equals(request.getItemCode())){
            //统计场景对话人数
            count = nbchatTrainRpDayItemMapper.countDialoguePnItems(countParam);
        }  else if (RpCourseItemType.dialogue_avg_score_count.getCode().equals(request.getItemCode())){
            //统计场景对话人均分
            count = nbchatTrainRpDayItemMapper.countDialogueAvgScoreItems(countParam);
        } else if (RpCourseItemType.dialogue_level_count.getCode().equals(request.getItemCode())){
            //统计场景实践评分级别
            count = nbchatTrainRpDayItemMapper.countDialogueLevelItems(countParam);
        } else if (RpCourseItemType.dialogue_rank_count.getCode().equals(request.getItemCode())){
            //统计场景实践排行
            count = nbchatTrainRpDayItemMapper.countDialogueRankList(countParam);
        } else if (RpCourseItemType.exam_pt_count.getCode().equals(request.getItemCode())){
            //统计测评人次
            count = nbchatTrainRpDayItemMapper.countExamPtItems(countParam);
        } else if (RpCourseItemType.exam_pn_count.getCode().equals(request.getItemCode())){
            //统计测评人数
            count = nbchatTrainRpDayItemMapper.countExamPnItems(countParam);
        } else if (RpCourseItemType.exam_avg_score_count.getCode().equals(request.getItemCode())){
            //统计测评人均分
            count = nbchatTrainRpDayItemMapper.countExamAvgScoreItems(countParam);
        } else if (RpCourseItemType.user_new_count.getCode().equals(request.getItemCode())){
            //统计新增用户数
            count = nbchatTrainRpDayItemMapper.countUserNewItems(countParam);
        } else if (RpCourseItemType.exam_comprehensive_score_count.getCode().equals(request.getItemCode())){
            //统计测评综合得分
            count = nbchatTrainRpDayItemMapper.countExamComprehensiveScoreItems(countParam);
        } else if (RpCourseItemType.user_total_count.getCode().equals(request.getItemCode())){
            //统计累计用户数
            count = nbchatTrainRpDayItemMapper.countUserTotalItems(countParam);
        } else if (RpCourseItemType.new_registrations.getCode().equals(request.getItemCode())) {
            //统计新增用户数
            count = opRpStstDayItemMapper.countUserNewItems(countParam);
            opRpStstDayItemMapper.countEnterpriseUserNewItems(countParam);
        } else if (RpCourseItemType.question_creators.getCode().equals(request.getItemCode())) {
            //统计出题次数
            count = opRpStstDayItemMapper.countQuestionCreatorsNewItems(countParam);
        } else if (RpCourseItemType.ppt_creators.getCode().equals(request.getItemCode())) {
            //统计PPT创作数量
            count = opRpStstDayItemMapper.countQuestionCreatorsNewItems(countParam);
        } else if (RpCourseItemType.video_creators.getCode().equals(request.getItemCode())) {
            //统计视频制作数量
            count = opRpStstDayItemMapper.countVideoCreatorsNewItems(countParam);
            int countCommonTenant = opRpStstDayItemMapper.countCommonTenantVideoCreatorsNewItems(countParam);
            count = count+countCommonTenant;
        } else if (RpCourseItemType.video_success.getCode().equals(request.getItemCode())) {
            //统计视频制作成功数量
            count = opRpStstDayItemMapper.countVideoSuccessNewItems(countParam);
            int countCommonTenant = opRpStstDayItemMapper.countCommonTenantVideoSuccessNewItems(countParam);
            count =count+countCommonTenant;
        } else {
            return BaseRspUtils.createErrorRsp("指标暂不支持:" + request.getItemCode());
        }
        log.info("指标日报表统计-完成:{}|{}",countParam,count);
        return BaseRspUtils.createSuccessRsp(count,"指标统计完成:"+request.getItemCode());
    }


}
