package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.api.NbchatTrainCourseScoreApi;
import com.tydic.nbchat.train.api.bo.course.TrainCourseScoreQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TrainUserQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/train/course/score")
public class TrainCourseScoreController {
    private final NbchatTrainCourseScoreApi nbchatTrainCourseScoreApi;
    /**
     * 分页查询学习记录
     * @param reqBO
     * @return
     */
    @PostMapping("/study-records")
    public RspList getStudyRecordsByPage(@RequestBody TrainCourseScoreQueryReqBO reqBO) {
        return nbchatTrainCourseScoreApi.getStudyRecordsByPage(reqBO);
    }

    /**
     * 导出学习记录
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/export")
    public Rsp export(@RequestBody TrainCourseScoreQueryReqBO reqBO) {
        return nbchatTrainCourseScoreApi.export(reqBO);
    }

    /**
     * 获取用户姓名-手机号
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/users")
    public RspList getUserInformation(@RequestBody TrainUserQueryReqBO reqBO){
        return nbchatTrainCourseScoreApi.getUserInformation(reqBO);
    }
}
