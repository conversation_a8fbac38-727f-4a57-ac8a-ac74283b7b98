package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.rp_dialogue.NbchatTrainRpDialogueApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/train/rp/dialogue")
public class TrainRpDialogueController {

    @Resource
    NbchatTrainRpDialogueApi nbchatTrainRpDialogueApi;

    /**
     * 场景实践指标统计查询
     * @param request
     * @return
     */
    @PostMapping("/item")
    public Rsp countItem(@RequestBody QueryReportRequest request) {
        return nbchatTrainRpDialogueApi.countItem(request);
    }

    /**
     * 综合评分
     * @return
     */
    @PostMapping("/score")
    public Rsp circleScore(@RequestBody QueryReportRequest request){
        return nbchatTrainRpDialogueApi.circleScore(request);
    }

    /**
     * 趋势图
     * @param analysisReqBO
     * @return
     */
    @PostMapping("/analysis")
    public RspList analysis(@RequestBody QueryReportRequest request) {
        return nbchatTrainRpDialogueApi.analysis(request);
    }

    /**
     * 排行
     * @param queryReqBO
     * @return
     */
    @PostMapping("/rank")
    public RspList rank(@RequestBody QueryReportRequest request) {
        return nbchatTrainRpDialogueApi.rank(request);
    }



}
