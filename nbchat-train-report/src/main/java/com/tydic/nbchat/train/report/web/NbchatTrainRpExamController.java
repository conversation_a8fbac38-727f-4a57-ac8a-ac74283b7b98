package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.api.NbchatTrainExamReportApi;
import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.bo.report.exam.TrainRpExamStudentsQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/train/rp/exam")
public class NbchatTrainRpExamController {
    private final NbchatTrainExamReportApi nbchatTrainExamReportApi;

    /**
     * 获取测评人次/获取测评人数
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/count")
    public Rsp getEvaluationCount(@RequestBody TrainRpExamStudentsQueryReqBO reqBO){
        Rsp rsp = nbchatTrainExamReportApi.getEvaluationCount(reqBO);
        return rsp;
    }

    /**
     * 获取综合评分
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/compositeScore")
    public Rsp getCompositeScore(@RequestBody TrainRpExamStudentsQueryReqBO reqBO){
        Rsp rsp = nbchatTrainExamReportApi.getCompositeScore(reqBO);
        return rsp;
    }

    /**
     * 获取数据趋势
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/trend")
    public RspList getDataTrend(@RequestBody QueryReportRequest request){
        RspList rsp = nbchatTrainExamReportApi.getDataTrend(request);
        return rsp;
    }

    /**
     * 获取测评排行
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/ranking")
    public RspList getEvaluationRanking(@RequestBody TrainRpExamStudentsQueryReqBO reqBO){
        RspList rsp = nbchatTrainExamReportApi.getEvaluationRanking(reqBO);
        return rsp;
    }

}
