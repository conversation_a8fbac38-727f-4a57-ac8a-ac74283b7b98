package com.tydic.nbchat.train.report.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.NbchatTrainRpPptService;
import com.tydic.nbchat.train.api.bo.constants.RedisConstants;
import com.tydic.nbchat.train.api.bo.ppt.NbchatPptQueryReqBO;
import com.tydic.nbchat.train.api.bo.ppt.NbchatPptQueryRspBO;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoCountQueryRspBO;
import com.tydic.nbchat.train.api.bo.video.NbchatVideoQueryRspBO;
import com.tydic.nbchat.train.mapper.PptCreationRecordMapper;
import com.tydic.nbchat.train.mapper.po.NbchatPptCondition;
import com.tydic.nbchat.train.mapper.po.NbchatPptDTO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class NbchatTrainRpPptServiceImpl implements NbchatTrainRpPptService {
    @Resource
    private PptCreationRecordMapper pptCreationRecordMapper;

    @Resource
    private RedisHelper redisHelper;

    @Override
    public Rsp getPPTCount(NbchatPptQueryReqBO reqBO) {
        log.info("查询PPT制作数量:{}",reqBO.get_userId());
        NbchatVideoCountQueryRspBO rspBO = new NbchatVideoCountQueryRspBO();

        try {
            Map<String, Integer> statsMap = new HashMap<>();

            // 从Redis取数据
            if (redisHelper.hasKey(RedisConstants.PPT_COUNT)) {
                log.debug("Redis中有缓存数据，从缓存获取PPT统计数据");
                Object video = redisHelper.get(RedisConstants.PPT_COUNT);
                // 解析Map
                statsMap = (Map<String, Integer>) video;
            } else {
                log.debug("Redis中无缓存数据，从数据库获取PPT统计数据");
                // 查询历史数据并存入Redis
                statsMap.put("lastWeek", pptCreationRecordMapper.getPptCount(6, null));
                statsMap.put("lastMonth", pptCreationRecordMapper.getPptCount(29, null));
                statsMap.put("enterpriseLastWeek", pptCreationRecordMapper.getPptCount(6, "1"));
                statsMap.put("enterpriseLastMonth", pptCreationRecordMapper.getPptCount(29, "1"));
                statsMap.put("personalLastWeek", pptCreationRecordMapper.getPptCount(6, "0"));
                statsMap.put("personalLastMonth", pptCreationRecordMapper.getPptCount(29, "0"));

                // 存入Redis，设置过期时间为当天结束
                LocalDate now = LocalDate.now();
                LocalDateTime endOfDay = now.atTime(23, 59, 59);
                long seconds = Duration.between(LocalDateTime.now(), endOfDay).toSeconds();

                redisHelper.set(RedisConstants.PPT_COUNT, statsMap, seconds);
            }

            // 获取当天数据（始终从数据库获取实时数据）
            int today = pptCreationRecordMapper.getPptCount(0, null);
            int enterpriseToday = pptCreationRecordMapper.getPptCount(0, "1");
            int personalToday = pptCreationRecordMapper.getPptCount(0, "0");

            // 填充返回结果
            rspBO.setToday(today);
            rspBO.setLastWeek(statsMap.getOrDefault("lastWeek", 0) + today);
            rspBO.setLastMonth(statsMap.getOrDefault("lastMonth", 0) + today);

            rspBO.setEnterpriseToday(enterpriseToday);
            rspBO.setEnterpriseLastWeek(statsMap.getOrDefault("enterpriseLastWeek", 0) + enterpriseToday);
            rspBO.setEnterpriseLastMonth(statsMap.getOrDefault("enterpriseLastMonth", 0) + enterpriseToday);

            rspBO.setPersonalToday(personalToday);
            rspBO.setPersonalLastWeek(statsMap.getOrDefault("personalLastWeek", 0) + personalToday);
            rspBO.setPersonalLastMonth(statsMap.getOrDefault("personalLastMonth", 0) + personalToday);

            log.debug("PPT统计数据查询完成: {}", rspBO);
            return BaseRspUtils.createSuccessRsp(rspBO);
        } catch (Exception e) {
            log.error("获取PPT统计数据异常", e);
            return BaseRspUtils.createSuccessRsp(new NbchatVideoCountQueryRspBO()); // 返回空统计，避免前端异常
        }
    }

    @Override
    public RspList getPPTList(NbchatPptQueryReqBO reqBO) {
        log.info("查询PPT制作列表:{}",reqBO);
        List<NbchatVideoQueryRspBO> rspBOList = new ArrayList<>();
        NbchatPptCondition condition = new NbchatPptCondition();
        BeanUtils.copyProperties(reqBO, condition);
        Page page = null;
        if (Boolean.TRUE.equals(reqBO.getIsPaged())) {
            page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        }
        List<NbchatPptDTO> pptList = pptCreationRecordMapper.getPptList(condition);
        if (CollectionUtils.isEmpty(pptList)) {
            return BaseRspUtils.createSuccessRspList(rspBOList,0L);
        }
        NiccCommonUtil.copyList(pptList,rspBOList, NbchatPptQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspBOList,page != null ? page.getTotal() : pptList.size());
    }
}
