package com.tydic.nbchat.train.report.config;

import com.tydic.nbchat.train.api.bo.eums.RpCourseItemType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-train.config.rp-config")
public class NbchatTrainRpConfigProperties {
    private Boolean timerEnable;
    private String timerCron = "0 0 2 * * ?";
    private List<String> itemCodes = Arrays.asList(
            RpCourseItemType.course_pn_count.getCode(),
            RpCourseItemType.course_pn_end_count.getCode(),
            RpCourseItemType.course_pt_count.getCode(),
            RpCourseItemType.dialogue_pt_count.getCode(),
            RpCourseItemType.dialogue_pn_count.getCode(),
            RpCourseItemType.dialogue_avg_score_count.getCode(),
            RpCourseItemType.dialogue_level_count.getCode(),
            RpCourseItemType.dialogue_rank_count.getCode(),
            RpCourseItemType.exam_pt_count.getCode(),
            RpCourseItemType.exam_pn_count.getCode(),
            RpCourseItemType.exam_avg_score_count.getCode(),
            RpCourseItemType.user_new_count.getCode(),
            RpCourseItemType.exam_comprehensive_score_count.getCode(),
            RpCourseItemType.user_total_count.getCode(),
            RpCourseItemType.new_registrations.getCode(),
            RpCourseItemType.question_creators.getCode(),
            RpCourseItemType.ppt_creators.getCode(),
            RpCourseItemType.video_creators.getCode(),
            RpCourseItemType.video_success.getCode()
            );

}
