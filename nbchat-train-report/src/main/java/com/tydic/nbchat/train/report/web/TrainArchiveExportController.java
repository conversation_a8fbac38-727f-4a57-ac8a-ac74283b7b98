package com.tydic.nbchat.train.report.web;


import com.tydic.nbchat.train.api.bo.course.TranStudentArchiveQueryReqBO;
import com.tydic.nbchat.train.report.service.impl.excel.ArchiveExportService;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/train/archive")
public class TrainArchiveExportController {

    private final ArchiveExportService archiveExportService;


    @PostMapping("/export")
    public Rsp export(@RequestBody TranStudentArchiveQueryReqBO request) {
        try {
            return archiveExportService.export(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
