package com.tydic.nbchat.train.report.service.impl.excel.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class TaskCourseExcelEntity {

    @ExcelProperty(value = "培训类型", index = 1)
    private String taskName;

    @ExcelProperty(value = "培训课程", index = 2)
    private String courseName;

    @ExcelProperty(value = "培训课程", index = 3)
    private String courseName2;

    @ExcelProperty(value = "学时", index = 4)
    private Double videoDuration;

    @ExcelProperty(value = "考试成绩", index = 5)
    private Integer score;
}
