package com.tydic.nbchat.train.report.service.impl.excel;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.util.CellRangeAddress;

@Slf4j
public class TaskMergeStrategy implements Row<PERSON>riteHandler {


    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {
        if (context.getRowIndex() > 4) {
            CellRangeAddress cellRangeAddress = new CellRangeAddress(context.getRowIndex(),context.getRowIndex(),1,2);
            context.getWriteSheetHolder().getSheet().addMergedRegionUnsafe(cellRangeAddress);
        }
    }

}
