package com.tydic.nbchat.train.report.web;

import com.tydic.nbchat.train.api.NbchatTrainRpPptService;
import com.tydic.nbchat.train.api.bo.ppt.NbchatPptQueryReqBO;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@RequestMapping("/train/creation_admin/ppt")
public class NbchatTrainRpPptController {

    private NbchatTrainRpPptService nbchatTrainRpPptService;

    @PostMapping("/list")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public RspList getPPTList(@RequestBody NbchatPptQueryReqBO reqBO){
        return  nbchatTrainRpPptService.getPPTList(reqBO);
    }

    @PostMapping("/count")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp getPPTCount(@RequestBody NbchatPptQueryReqBO reqBO){
        Rsp rsp = nbchatTrainRpPptService.getPPTCount(reqBO);
        return rsp;
    }

}
