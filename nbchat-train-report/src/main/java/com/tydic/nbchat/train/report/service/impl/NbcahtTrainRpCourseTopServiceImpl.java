package com.tydic.nbchat.train.report.service.impl;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseRankingQueryReqBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseRankingQueryRspBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseTop5QueryReqBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseTop5QueryRspBO;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpCourseTopApi;
import com.tydic.nbchat.train.mapper.NbchatTrainCourseMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainRpStudyPnMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainCourse;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPn;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPnSelectCondition;
import com.tydic.nbchat.user.api.NbchatUserApi;
import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class NbcahtTrainRpCourseTopServiceImpl implements NbcahtTrainRpCourseTopApi {

    @Resource
    NbchatTrainRpStudyPnMapper nbchatTrainRpStudyPnMapper;
    @Resource
    NbchatTrainCourseMapper nbchatTrainCourseMapper;
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private NbchatUserApi nbchatUserApi;

    @Override
    public RspList<TrainRpCourseTop5QueryRspBO> getCourseHotTopList(TrainRpCourseTop5QueryReqBO top5QueryReqBO) {
        Date date = DateTimeUtil.DateAddDayOfYear(-1);
        String timeShortString = DateTimeUtil.getTimeShortString(date, DateTimeUtil.DATE_FORMAT_NORMAL);
        List<NbchatTrainRpStudyPn> top5 = nbchatTrainRpStudyPnMapper.queryTop5(timeShortString, top5QueryReqBO.getTenantCode());

        List<TrainRpCourseTop5QueryRspBO> res = new ArrayList<>();
        int i = 1;
        for (NbchatTrainRpStudyPn pn : top5) {
            NbchatTrainCourse nbchatTrainCourse = nbchatTrainCourseMapper.selectByPrimaryKey(pn.getCourseId());
            if (ObjectUtils.isEmpty(nbchatTrainCourse)) {
                continue;
            }
            TrainRpCourseTop5QueryRspBO rspBO = new TrainRpCourseTop5QueryRspBO();
            rspBO.setTopIndex(i ++);
            rspBO.setCourseId(pn.getCourseId());
            rspBO.setStudyCount(pn.getValue());
            rspBO.setCourseName(nbchatTrainCourse.getCourseName());
            res.add(rspBO);
        }
        return BaseRspUtils.createSuccessRspList(res);
    }


    /**
     * 课程排行统计查询
     *
     * @param @param queryReqBO 查询要求博
     * @return @return {@link RspList }<{@link TrainRpCourseRankingQueryRspBO }>
     */
    @Override
    public RspList<TrainRpCourseRankingQueryRspBO> getCourseRankingList(TrainRpCourseRankingQueryReqBO queryReqBO) {
        List<TrainRpCourseRankingQueryRspBO> result = new ArrayList<>();
        //查询用户所属租户
        String tenantCode = GetTenantByUserID(queryReqBO.getUserId());
        if (StringUtils.isBlank(tenantCode)) {
            return BaseRspUtils.createErrorRspList("查询用户信息失败！");
        }
        //查询租户下的课程的排行信息
        //查询课程的学习人数、完结人数、完结率  1是学完
        NbchatTrainRpStudyPnSelectCondition nbchatTrainRpStudyPnSelectCondition = new NbchatTrainRpStudyPnSelectCondition();
        BeanUtils.copyProperties(queryReqBO, nbchatTrainRpStudyPnSelectCondition);
        nbchatTrainRpStudyPnSelectCondition.setTenantCode(tenantCode);
        Page<NbchatTrainRpStudyPn> page = PageHelper.startPage(queryReqBO.getPage(), queryReqBO.getLimit());
        List<NbchatTrainRpStudyPn> nbchatTrainRpStudyPnList = nbchatTrainRpStudyPnMapper.getCourseRankingByTenantCode(nbchatTrainRpStudyPnSelectCondition);
        log.info("课程排行统计查询-{}", nbchatTrainRpStudyPnList);
        if (CollectionUtils.isEmpty(page.getResult())) {
            log.error("课程排行统计查询-查询课程排行信息失败-未录入课程");
            return BaseRspUtils.createSuccessRspList(result);
        }
        NiccCommonUtil.copyList(page.getResult(), result, TrainRpCourseRankingQueryRspBO.class);
        //查询课程名称
        result.forEach(userQueryRspBO -> {
            NbchatTrainCourse nbchatTrainCourse = nbchatTrainCourseMapper.selectByPrimaryKey(userQueryRspBO.getCourseId());
            if (!ObjectUtils.isEmpty(nbchatTrainCourse)) {
                userQueryRspBO.setStudyEndRate(userQueryRspBO.getStudyEndRate() * 100);
                userQueryRspBO.setCourseName(nbchatTrainCourse.getCourseName());
            } else {
                log.error("课程排行统计查询-课程：{}不存在", userQueryRspBO.getCourseId());
            }
        });
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    /**
     * 查询用户所属租户
     * @param @param userId 用户id
     * @return @return {@link String }
     */
    private String GetTenantByUserID(String userId){
        Rsp rsp = nbchatUserApi.getUserInfo(userId);
        if (rsp.isSuccess()) {
            NbchatUserInfo nbchatUserInfo = (NbchatUserInfo)rsp.getData();
            return nbchatUserInfo.getTenantCode();
        }
        log.error("课程排行统计查询-查询用户所属租户：{}|{}",userId,rsp.getRspDesc());
        return null;
    }
}
