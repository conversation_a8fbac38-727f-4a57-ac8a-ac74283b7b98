package com.tydic.nbchat.train.report.service.impl;

import com.tydic.nbchat.train.api.NbchatTrainStudentsReportApi;
import com.tydic.nbchat.train.api.bo.eums.RpCourseItemType;
import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseCountItemRspBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseDataAnalysisRspBO;
import com.tydic.nbchat.train.api.bo.report.item.StstCountItemValue;
import com.tydic.nbchat.train.api.bo.report.students.TrainRpStudentsQueryReqBO;
import com.tydic.nbchat.train.api.bo.report.students.TrainRpStudentsQueryRspBO;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpCourseCountApi;
import com.tydic.nbchat.train.mapper.NbchatTrainRpDayItemMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpDayItem;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpDayItemCondition;
import com.tydic.nbchat.train.mapper.po.QoqPO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@AllArgsConstructor
public class NbchatTrainRpStudentsServiceImpl implements NbchatTrainStudentsReportApi {

    private final NbchatTrainRpDayItemMapper nbchatTrainRpDayItemMapper;
    private final NbcahtTrainRpCourseCountApi nbcahtTrainRpCourseCountApi;
    /**
     * 查询新增学员数/查询学员总数
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp getNewStudentsCount(TrainRpStudentsQueryReqBO reqBO) {
        log.info("查询新增学员数/查询学员总数-统计环比数据：{}",reqBO);
        Rsp rsp = nbcahtTrainRpCourseCountApi.getNewStudentsCount(reqBO);
        log.info("查询新增学员数/查询学员总数-统计环比数据-结束：{}",rsp);
        return BaseRspUtils.createSuccessRsp(rsp);
    }

    /**
     * 查询数据趋势
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public RspList getDataTrend(QueryReportRequest request) {
        log.info("统计课程学习趋势：{}",request);
        if (request.getStartDate() == null) {
            request.setStartDate(DateTimeUtil.DateAddDayOfYear(-30));
        }
        if (request.getEndDate() == null) {
            request.setEndDate(new Date());
        }
        //学习人数趋势
        List<TrainRpCourseDataAnalysisRspBO> res = new ArrayList<>();
        for (RpCourseItemType value : RpCourseItemType.studentsItems()) {
            TrainRpCourseDataAnalysisRspBO bo = nbcahtTrainRpCourseCountApi.calGraph(request, value);
            res.add(bo);
        }
        return BaseRspUtils.createSuccessRspList(res);
    }

    /**
     * 数据分析
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    @Override
    public Rsp analyzeData(TrainRpStudentsQueryReqBO reqBO) {
        log.info("数据分析：{}",reqBO);
        TrainRpStudentsQueryRspBO trainRpStudentsQueryRspBO = new TrainRpStudentsQueryRspBO();
        NbchatTrainRpDayItemCondition nbchatTrainRpDayItemCondition = new NbchatTrainRpDayItemCondition();
        BeanUtils.copyProperties(reqBO, nbchatTrainRpDayItemCondition);
        NbchatTrainRpDayItemCondition analyzeData = nbchatTrainRpDayItemMapper.analyzeData(nbchatTrainRpDayItemCondition);
        if (ObjectUtils.isEmpty(analyzeData)) {
            log.warn("数据分析返回为空");
            return BaseRspUtils.createSuccessRsp("未找到匹配的数据");
        }
        BeanUtils.copyProperties(analyzeData, trainRpStudentsQueryRspBO, TrainRpStudentsQueryRspBO.class);
        log.info("数据分析-结束：{}", trainRpStudentsQueryRspBO);
        return BaseRspUtils.createSuccessRsp(trainRpStudentsQueryRspBO, "查询成功");
    }
}
