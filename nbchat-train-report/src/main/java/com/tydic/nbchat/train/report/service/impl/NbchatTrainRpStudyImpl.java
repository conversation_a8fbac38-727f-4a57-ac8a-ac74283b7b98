package com.tydic.nbchat.train.report.service.impl;

import com.tydic.nbchat.train.api.bo.report.course.TrainRpCoursePnSaveReqBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCoursePtSaveReqBO;
import com.tydic.nbchat.train.api.rp_course.NbcahtTrainRpCourseSaveApi;
import com.tydic.nbchat.train.mapper.NbchatTrainRecordMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainRpStudyPnMapper;
import com.tydic.nbchat.train.mapper.NbchatTrainRpStudyPtMapper;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRecord;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPn;
import com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPt;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class NbchatTrainRpStudyImpl implements NbcahtTrainRpCourseSaveApi {
    @Resource
    NbchatTrainRpStudyPtMapper nbchatTrainRpStudyPtMapper;
    @Resource
    NbchatTrainRpStudyPnMapper nbchatTrainRpStudyPnMapper;
    @Resource
    NbchatTrainRecordMapper nbchatTrainRecordMapper;

    /**
     * 历史数据
     */
    private static final String HISTORY_DATA = "1";

    /**
     * 每个课程，每人每天只能算作一次，学习任意一个章节，都算是学了课程
     */
    @Override
    public Rsp saveRpCoursePnRecord(TrainRpCoursePnSaveReqBO reqBO) {
        log.info("保存学习人数记录：{}",reqBO);
        NbchatTrainRpStudyPn query = new NbchatTrainRpStudyPn();
        BeanUtils.copyProperties(reqBO,query);
        List<NbchatTrainRpStudyPn> nbchatTrainRpStudyPns = nbchatTrainRpStudyPnMapper.selectAll(query);

        if (CollectionUtils.isEmpty(nbchatTrainRpStudyPns)) {
            NbchatTrainRpStudyPn record = new NbchatTrainRpStudyPn();
            BeanUtils.copyProperties(reqBO,record);
            record.setDateTime(new Date());
            if (isStudied(reqBO.getCourseId(), reqBO.getUserId())) {
                record.setIsFinish(EntityValidType.NORMAL.getCode());
            }
            try {
                nbchatTrainRpStudyPnMapper.insertSelective(record);
            } catch (Exception e) {
                log.error("保存学习人数记录失败",e);
            }
        } else {
            if (isStudied(reqBO.getCourseId(), reqBO.getUserId())) {
                NbchatTrainRpStudyPn record = nbchatTrainRpStudyPns.get(0);
                record.setIsFinish(EntityValidType.NORMAL.getCode());
                nbchatTrainRpStudyPnMapper.update(record);
            }
        }
        return BaseRspUtils.createSuccessRsp(null);
    }

    /**
     * 判断是否学完课程
     * @param courseId
     * @param userId
     * @return
     */
    public boolean isStudied(String courseId,String userId){
        NbchatTrainRecord nbchatTrainRecord = nbchatTrainRecordMapper.selectByUserAndCourseId(userId, courseId);
        if (ObjectUtils.isEmpty(nbchatTrainRecord)) {
            return false;
        }
        if (EntityValidType.NORMAL.getCode().equals(nbchatTrainRecord.getTrainState())) {
            return true;
        }
        return false;
    }

    /**
     * 保存学习人次记录
     * @param @param ptSaveReqBO pt保存BO
     * @return @return {@link Rsp }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp saveRpCoursePtRecord(TrainRpCoursePtSaveReqBO ptSaveReqBO) {
        log.info("保存学习人次记录：{}", ptSaveReqBO);
        Long id = IdWorker.nextAutoId();
        String userId = ptSaveReqBO.getUserId();
        String courseId = ptSaveReqBO.getCourseId();
        String tenantCode = ptSaveReqBO.getTenantCode();
        String historyData = ptSaveReqBO.getHistoryData();
        Date dateDay = new Date();
        //判断是否为前端传入数据
        if (StringUtils.isNotBlank(historyData)&&historyData.equals(HISTORY_DATA)){
            dateDay = ptSaveReqBO.getDateDay();
        }
        NbchatTrainRpStudyPt nbchatTrainRpStudyPt = new NbchatTrainRpStudyPt();
        nbchatTrainRpStudyPt.setId(id);
        nbchatTrainRpStudyPt.setUserId(userId);
        nbchatTrainRpStudyPt.setCourseId(courseId);
        nbchatTrainRpStudyPt.setTenantCode(tenantCode);
        nbchatTrainRpStudyPt.setDateDay(dateDay);
        nbchatTrainRpStudyPt.setDateTime(new Date());
        nbchatTrainRpStudyPtMapper.insert(nbchatTrainRpStudyPt);
        return BaseRspUtils.createSuccessRsp("","保存成功");
    }
}
