{"openapi": "3.0.0", "info": {"title": "首页模板管理接口", "description": "首页模板管理相关的管理员接口，包括查询、新增、更新、删除和排序功能", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "本地开发服务器"}], "paths": {"/train/tdh/index/admin/templates": {"post": {"summary": "管理员查询首页模板列表", "description": "管理员查询首页模板列表，支持分页和条件筛选。需要系统管理员或租户管理员权限。", "tags": ["首页模板管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码", "example": 1}, "limit": {"type": "integer", "description": "每页数量", "example": 10}, "tplName": {"type": "string", "description": "模板名称"}, "tplType": {"type": "string", "description": "模板类型"}, "status": {"type": "string", "description": "上架状态"}, "isValid": {"type": "string", "description": "是否有效"}, "categoryCode": {"type": "string", "description": "分类编码"}}}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"rspCode": {"type": "string", "example": "0000"}, "rspDesc": {"type": "string", "example": "业务处理成功!"}, "rows": {"type": "array", "items": {"$ref": "#/components/schemas/TdhIndexTemplate"}}, "count": {"type": "integer", "example": 1}}}}}}}}}, "/train/tdh/index/admin/template/add": {"post": {"summary": "新增首页模板", "description": "新增首页模板，需要管理员权限。tplName和tplType为必填字段。", "tags": ["首页模板管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TdhIndexTemplateRequest"}, "example": {"tplName": "新PPT模板", "tplDesc": "这是一个新创建的PPT模板", "tplType": "ppt", "tplConfig": "{\"theme\": \"default\", \"layout\": \"standard\"}", "tplContent": "模板内容JSON数据", "categoryCode": "cat001", "tplSize": "16:9", "status": "0", "orderIndex": 1}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "example": {"rspCode": "0000", "rspDesc": "新增模板成功", "data": "123456789"}}}}}}}, "/train/tdh/index/admin/template/update": {"post": {"summary": "更新首页模板", "description": "更新首页模板，需要管理员权限。tplId为必填字段。", "tags": ["首页模板管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TdhIndexTemplateRequest"}, "example": {"tplId": "123456789", "tplName": "更新后的模板名称", "tplDesc": "更新后的模板描述", "tplType": "ppt", "status": "1", "orderIndex": 2}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/train/tdh/index/admin/template/delete": {"post": {"summary": "删除首页模板", "description": "删除首页模板（逻辑删除），需要管理员权限。", "tags": ["首页模板管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["tplId"], "properties": {"tplId": {"type": "string", "description": "模板ID"}}}, "example": {"tplId": "123456789"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/train/tdh/index/admin/template/sort": {"post": {"summary": "首页模板排序", "description": "首页模板排序，根据传入的模板ID列表顺序更新排序字段。需要管理员权限。", "tags": ["首页模板管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["tplIds"], "properties": {"tplIds": {"type": "array", "items": {"type": "string"}, "description": "模板ID列表，按排序顺序"}}}, "example": {"tplIds": ["123456789", "987654321", "456789123"]}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/train/tdh/index/admin/template/status": {"post": {"summary": "首页模板上下架", "description": "首页模板上下架，支持上架和下架操作。需要管理员权限。", "tags": ["首页模板管理"], "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["tplId", "status"], "properties": {"tplId": {"type": "string", "description": "模板ID"}, "status": {"type": "string", "enum": ["0", "1"], "description": "状态：0-下架，1-上架"}}}, "examples": {"上架": {"summary": "上架模板", "value": {"tplId": "123456789", "status": "1"}}, "下架": {"summary": "下架模板", "value": {"tplId": "123456789", "status": "0"}}}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}, "examples": {"上架成功": {"summary": "上架成功", "value": {"rspCode": "0000", "rspDesc": "模板上架成功", "data": "123456789"}}, "下架成功": {"summary": "下架成功", "value": {"rspCode": "0000", "rspDesc": "模板下架成功", "data": "123456789"}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "管理员认证token"}}, "schemas": {"TdhIndexTemplate": {"type": "object", "properties": {"tplId": {"type": "string", "description": "模板ID"}, "tenantCode": {"type": "string", "description": "租户编码"}, "tplName": {"type": "string", "description": "模板名称"}, "tplDesc": {"type": "string", "description": "模板描述"}, "tplType": {"type": "string", "description": "模板类型"}, "tplConfig": {"type": "string", "description": "模板配置"}, "tplContent": {"type": "string", "description": "模板内容"}, "categoryCode": {"type": "string", "description": "分类编码"}, "tplSize": {"type": "string", "description": "模板尺寸"}, "orderIndex": {"type": "integer", "description": "排序索引"}, "status": {"type": "string", "description": "上架状态：0-未上架，1-已上架"}, "isValid": {"type": "string", "description": "是否有效：0-无效，1-有效"}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}}}, "TdhIndexTemplateRequest": {"type": "object", "properties": {"tplId": {"type": "string", "description": "模板ID（更新时必填）"}, "tplName": {"type": "string", "description": "模板名称（新增时必填）"}, "tplDesc": {"type": "string", "description": "模板描述"}, "tplType": {"type": "string", "description": "模板类型（新增时必填）"}, "tplConfig": {"type": "string", "description": "模板配置"}, "tplContent": {"type": "string", "description": "模板内容"}, "categoryCode": {"type": "string", "description": "分类编码"}, "tplSize": {"type": "string", "description": "模板尺寸"}, "orderIndex": {"type": "integer", "description": "排序索引"}, "status": {"type": "string", "description": "上架状态：0-未上架，1-已上架"}}}, "ApiResponse": {"type": "object", "properties": {"rspCode": {"type": "string", "description": "响应码：0000-成功，9999-失败"}, "rspDesc": {"type": "string", "description": "响应描述"}, "data": {"description": "响应数据"}}}}}}