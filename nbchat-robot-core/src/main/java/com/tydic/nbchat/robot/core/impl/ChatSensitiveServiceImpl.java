package com.tydic.nbchat.robot.core.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.tydic.nicc.common.constants.RedisCacheKeyConstant;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.common.eums.sensitive.SensitiveLoadType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import com.tydic.nbchat.robot.api.bo.sensitive.*;
import com.tydic.nbchat.robot.api.ChatSensitiveService;
import com.tydic.nbchat.robot.mapper.ChatSensitiveConfMapper;
import com.tydic.nbchat.robot.mapper.po.*;
import com.tydic.nicc.tools.sensitiveWords.WordOptType;
import com.tydic.nicc.tools.sensitiveWords.WordType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Set;

/**
 * @Classname ChatSensitiveServiceImpl
 * @Description 敏感词管理
 * @Date 2021/12/22 5:50 下午
 * @Created by kangkang
 */

@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class ChatSensitiveServiceImpl implements ChatSensitiveService {


    @Resource
    private ChatSensitiveConfMapper chatSensitiveConfMapper;
    @Resource
    private RedisHelper redisHelper;

    @Override
    public RspList<ChatSensitiveConfBO> getSensitiveConfList(ChatSensitiveConfReqBO reqBO) {
        List<ChatSensitiveConfBO> sensitiveConfBOS = Lists.newArrayList();
        Page<ChatSensitiveConf> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        ChatSensitiveConfQueryCondition condition = new ChatSensitiveConfQueryCondition();
        BeanUtils.copyProperties(reqBO, condition);
        log.info("查询敏感词配置-开始: {}", condition);
        chatSensitiveConfMapper.selectConfigsByCondition(condition);
        NiccCommonUtil.copyList(page.getResult(), sensitiveConfBOS, ChatSensitiveConfBO.class);
        log.info("查询敏感词配置-结束: {} 条", sensitiveConfBOS.size());
        return BaseRspUtils.createSuccessRspList(sensitiveConfBOS, page.getTotal());
    }

    @Override
    public RspList<ChatSensitiveWordsConfBO> getSensitiveWordsConfList(ChatSensitiveWordsConfReqBO reqBO) {
        List<ChatSensitiveWordsConfBO> sensitiveConfBOS = Lists.newArrayList();
        Page<ChatSensitiveWordsConf> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        ChatSensitiveConfQueryCondition condition = new ChatSensitiveConfQueryCondition();
        BeanUtils.copyProperties(reqBO, condition);
        log.info("查询敏感词库-开始: {}", condition);
        chatSensitiveConfMapper.selectWordsConfByCondition(condition);
        NiccCommonUtil.copyList(page.getResult(), sensitiveConfBOS, ChatSensitiveWordsConfBO.class);
        log.info("查询敏感词库-结束: {} 条", sensitiveConfBOS.size());
        return BaseRspUtils.createSuccessRspList(sensitiveConfBOS, page.getTotal());
    }

    @Transactional(rollbackFor = Exception.class)
    @MethodParamVerifyEnable
    @Override
    public Rsp saveChatSensitiveWords(ChatSensitiveWordsSaveReqBO saveReqBO) {
        log.info("保存敏感词-开始:{}", saveReqBO);
        saveReqBO.setCrtTime(new Date());
        if (StringUtils.isAnyBlank(
                saveReqBO.getTenantCode(),
                saveReqBO.getWordType(),
                saveReqBO.getIsValid()) || StringUtils.isBlank(EntityValidType.getNameByCode(saveReqBO.getIsValid()))) {
            return BaseRspUtils.createErrorRsp("操作失败:参数异常!");
        }
        if (!saveReqBO.getWordSet().iterator().hasNext()) {
            return BaseRspUtils.createErrorRsp("操作失败:敏感词为空!");
        }

        WordType.valueOf(saveReqBO.getWordType().toUpperCase(Locale.ROOT));
        ChatSensitiveWordsSaveResult result = new ChatSensitiveWordsSaveResult();
        BeanUtils.copyProperties(saveReqBO, result);
        for (String wordText : saveReqBO.getWordSet()) {
            if (SensitiveLoadType.TABLE.getCode().equalsIgnoreCase(saveReqBO.getLoadType())) {
                ChatSensitiveWords word = chatSensitiveConfMapper.getOneWord(saveReqBO.getTenantCode(), wordText);
                if (word != null) {
                    //更新
                    ChatSensitiveWords sensitiveWords = new ChatSensitiveWords();
                    BeanUtils.copyProperties(saveReqBO, sensitiveWords);
                    sensitiveWords.setWordId(word.getWordId());
                    chatSensitiveConfMapper.updateWords(sensitiveWords);
                } else {
                    if (saveReqBO.getSensitiveId() == null) {
                        saveReqBO.setSensitiveId(0);
                    }
                    if (SensitiveLoadType.TABLE.getCode().equals(saveReqBO.getLoadType())) {
                        ChatSensitiveWords sensitiveWords = new ChatSensitiveWords();
                        BeanUtils.copyProperties(saveReqBO, sensitiveWords);
                        sensitiveWords.setWordText(wordText);
                        sensitiveWords.setWordId(IdWorker.nextAutoIdStr());
                        sensitiveWords.setWordType(saveReqBO.getWordType().toUpperCase(Locale.ROOT));
                        chatSensitiveConfMapper.insertWords(sensitiveWords);
                    }
                }
            } else if (SensitiveLoadType.REDIS.getCode().equalsIgnoreCase(saveReqBO.getLoadType())) {
                String cacheKey = RedisCacheKeyConstant.getImSensitiveWordsKey(saveReqBO.getTenantCode(), saveReqBO.getWordType());
                //保存到redis
                if (EntityValidType.DELETE.getCode().equals(saveReqBO.getIsValid())) {
                    redisHelper.setRemove(cacheKey, wordText);
                } else {
                    redisHelper.sSet(cacheKey, wordText);
                }
            } else {
                return BaseRspUtils.createErrorRsp("更新失败:操作类型异常!");
            }
        }
        result.setWordOptType(WordOptType.ADD.name());
        if (EntityValidType.DELETE.getCode().equals(saveReqBO.getIsValid())) {
            result.setWordOptType(WordOptType.REMOVE.name());
        }
        return BaseRspUtils.createSuccessRsp(result);
    }


    @MethodParamVerifyEnable
    @Override
    public RspList<ChatSensitiveWordsBO> getSensitiveWords(ChatSensitiveWordsQueryReqBO reqBO) {
        log.info("查询敏感词-开始:{}", reqBO);
        List<ChatSensitiveWordsBO> wordsList = Lists.newArrayList();
        if (SensitiveLoadType.TABLE.getCode().equals(reqBO.getLoadType())) {
            ChatSensitiveWordsQueryCondition condition = new ChatSensitiveWordsQueryCondition();
            BeanUtils.copyProperties(reqBO, condition);
            Page<ChatSensitiveWords> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
            chatSensitiveConfMapper.selectWordsListByCondition(condition);
            NiccCommonUtil.copyList(page.getResult(), wordsList, ChatSensitiveWordsBO.class);
            return BaseRspUtils.createSuccessRspList(wordsList, page.getTotal());
        } else if (SensitiveLoadType.REDIS.getCode().equals(reqBO.getLoadType())) {
            //保存到redis
            if (StringUtils.isBlank(reqBO.getWordType())) {
                String blackKey = RedisCacheKeyConstant.getImSensitiveWordsKey(reqBO.getTenantCode(), WordType.BLACK.name());
                Set<Object> blackList = redisHelper.getRedisTemplate().opsForZSet().reverseRangeByScore(blackKey, 0, System.currentTimeMillis());
                String whiteKey = RedisCacheKeyConstant.getImSensitiveWordsKey(reqBO.getTenantCode(), WordType.WHITE.name());
                Set<Object> whiteList = redisHelper.getRedisTemplate().opsForZSet().reverseRangeByScore(whiteKey, 0, System.currentTimeMillis());
                if (blackList != null) {
                    for (Object o : blackList) {
                        ChatSensitiveWordsBO wordsBO = new ChatSensitiveWordsBO();
                        wordsBO.setSensitiveId(reqBO.getSensitiveId());
                        wordsBO.setWordType(WordType.BLACK.name());
                        wordsBO.setWordText(String.valueOf(o));
                        wordsList.add(wordsBO);
                    }
                }
                if (whiteList != null) {
                    for (Object o : whiteList) {
                        ChatSensitiveWordsBO wordsBO = new ChatSensitiveWordsBO();
                        wordsBO.setSensitiveId(reqBO.getSensitiveId());
                        wordsBO.setWordType(WordType.WHITE.name());
                        wordsBO.setWordText(String.valueOf(o));
                        wordsList.add(wordsBO);
                    }
                }
            }
        }
        return BaseRspUtils.createSuccessRspList(wordsList, wordsList.size());
    }

}
