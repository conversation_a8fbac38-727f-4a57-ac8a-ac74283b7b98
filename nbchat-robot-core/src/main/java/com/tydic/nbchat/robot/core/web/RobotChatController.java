package com.tydic.nbchat.robot.core.web;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.NbChatRobotProcessApi;
import com.tydic.nbchat.robot.api.NbchatResearchMajorApi;
import com.tydic.nbchat.robot.api.RobotProcessCallback;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.bo.eums.ChatAppType;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMajorBO;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMajorQueryRequest;
import com.tydic.nbchat.robot.api.bo.research.ResearchPartResult;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.util.NbchatRobotMsgBuilder;
import com.tydic.nbchat.robot.core.util.RobotStreamChatUtil;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody;

import java.io.IOException;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/robot/")
public class RobotChatController {

    private final NbChatRobotProcessApi nbChatRobotProcessApi;
    private final NbchatResearchMajorApi nbchatResearchMajorApi;
    private final NbchatRobotConfigProperties nbchatRobotConfigProperties;


    public RobotChatController(
                               NbChatRobotProcessApi nbChatRobotProcessApi,
                               NbchatResearchMajorApi nbchatResearchMajorApi,
                               NbchatRobotConfigProperties nbchatRobotConfigProperties) {
        this.nbChatRobotProcessApi = nbChatRobotProcessApi;
        this.nbchatResearchMajorApi = nbchatResearchMajorApi;
        this.nbchatRobotConfigProperties = nbchatRobotConfigProperties;
    }

    @PostMapping(value = "/chat/research/parts")
    public RspList<ResearchPartResult> getResearchParts(@RequestBody RobotMessageRequest request) {
        List<ResearchPartResult> parts = nbchatResearchMajorApi.getResearchPartResult(request.getRequestId()).
                getData().getParts();
        return BaseRspUtils.createSuccessRspList(parts);
    }


    /**
     * 文档检索聊天-流返回
     *
     * @param request
     * @return
     */
    @Deprecated
    @PostMapping(value = "/chat/research/stream", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ResponseBody
    public StreamingResponseBody chatResearchStream(@RequestBody RobotMessageRequest request) {
        request.setAppType(ChatAppType.KB_RESEARCH.getCode());
        return checkAndStreamChat(request);
    }

    private StreamingResponseBody checkAndStreamChat(RobotMessageRequest request) {
        Rsp rsp = checkChatRequest(request);
        if (rsp.isSuccess()) {
            return RobotStreamChatUtil.streamChat(nbChatRobotProcessApi, request);
        } else {
            return outputStream -> {
                outputStream.write(JSONObject.toJSONBytes(rsp));
                outputStream.flush();
            };
        }
    }


    /**
     * 文档检索聊天
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/chat/research")
    @ResponseBody
    public Object chatResearch(@RequestBody RobotMessageRequest request) {
        request.setAppType(ChatAppType.KB_RESEARCH.getCode());
        Rsp rsp = checkChatRequest(request);
        if (!rsp.isSuccess()) {
            return rsp;
        }
        if (request.isStream()) {
            return RobotStreamChatUtil.streamChat(nbChatRobotProcessApi, request);
        } else {
            return nbChatRobotProcessApi.sendResearchMessage(request);
        }
    }


    /**
     * 聊天通用接口
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/chat/process", produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    @ResponseBody
    public StreamingResponseBody chatProcessBytes(@RequestBody RobotMessageRequest request) {
        //迪问聊天应用
        request.setAppType(ChatAppType.DIC_CHAT.getCode());
        return checkAndStreamChat(request);
    }


    @Deprecated
    @PostMapping(value = "/chat/event")
    public SseEmitter chatProcessEvent(@RequestBody RobotMessageRequest request) {
        SseEmitter emitter = new SseEmitter();
        nbChatRobotProcessApi.sendMessage(request, new RobotProcessCallback() {
            @Override
            public void onMessage(RobotMsgContext context) {
                try {
                    //emitter.send("", MediaType.APPLICATION_OCTET_STREAM_VALUE);
                    emitter.send(context);
                    if (log.isTraceEnabled()) {
                        log.trace("推送数据:{}", context.getText());
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }

            @Override
            public void onError(RobotMessageRequest request, Throwable e) {
                emitter.completeWithError(e);
            }
        });
        log.info("推送数据:结束");
        emitter.complete();
        return emitter;
    }


    /**
     * 校验请求参数
     *
     * @param request
     * @return
     */
    private Rsp checkChatRequest(RobotMessageRequest request) {
        if (StringUtils.isAnyBlank(request.getUserId(), request.getText())) {
            return BaseRspUtils.createErrorRsp("参数异常！");
        }
        if (StringUtils.isAllBlank(request.getSessionId(), request.getMajorId())) {
            return BaseRspUtils.createErrorRsp("参数异常！");
        }
        //计算sessionId
        if (StringUtils.isNotBlank(request.getMajorId()) && StringUtils.isBlank(request.getSessionId())) {
            request.setSessionId(NbchatRobotMsgBuilder.getMajorSession(request.getUserId(), request.getMajorId()));
        }
        choiceRobot(request);
        return BaseRspUtils.createSuccessRsp("");
    }


    /**
     * 选择机器人
     *
     * @param request
     */
    private void choiceRobot(RobotMessageRequest request) {
        if (StringUtils.isNotBlank(request.getMajorId())) {
            //加载设定的robotType
            FileResearchMajorQueryRequest queryRequest = new FileResearchMajorQueryRequest();
            queryRequest.setMajorId(request.getMajorId());
            queryRequest.setUserId(request.getUserId());
            Rsp<FileResearchMajorBO> rsp = nbchatResearchMajorApi.getResearchMajor(queryRequest);
            if (rsp.isSuccess()) {
                String robotType = rsp.getData().getRobotType();
                String robotName = RobotType.getNameByCode(robotType);
                if (StringUtils.isNotBlank(robotName)) {
                    //使用专属机器人预设默认机器人
                    request.setRobotType(robotType);
                    return;
                }
            }
        }
        //设定机器人
        /*String robotName = RobotType.getNameByCode(request.getRobotType());
        if(StringUtils.isBlank(robotName)){
            request.setRobotType(nbchatRobotConfigProperties.getRobotTypeDefault());
        }*/
        if (StringUtils.isBlank(request.getRobotType())) {
            request.setRobotType(nbchatRobotConfigProperties.getRobotTypeDefault());
        }
    }

}
