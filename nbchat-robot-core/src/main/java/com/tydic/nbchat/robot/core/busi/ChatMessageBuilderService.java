package com.tydic.nbchat.robot.core.busi;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.NbchatRobotToolsApi;
import com.tydic.nbchat.robot.api.bo.RequestOptions;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotRedisKeyConstant;
import com.tydic.nbchat.robot.api.bo.eums.ChatAppType;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.eums.SystemPromptCode;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.bo.research.FileResearchSearchPartResult;
import com.tydic.nbchat.robot.api.bo.research.FileResearchSearchRequest;
import com.tydic.nbchat.robot.api.bo.research.ResearchAppConfig;
import com.tydic.nbchat.robot.api.bo.research.ResearchPartResult;
import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppBO;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessage;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessageRole;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.KbResearchHelper;
import com.tydic.nbchat.robot.core.helper.SystemPresetPromptHelper;
import com.tydic.nbchat.robot.mapper.NbchatResearchMsgMapper;
import com.tydic.nbchat.robot.mapper.NbchatRobotSessionMsgMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchMsg;
import com.tydic.nbchat.robot.mapper.po.NbchatSessionMsg;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ChatMessageBuilderService {

    @Resource
    private NbchatRobotSessionMsgMapper nbchatRobotSessionMsgMapper;
    @Resource
    private NbchatResearchMsgMapper nbchatResearchMsgMapper;

    final private KbResearchHelper kbResearchHelper;
    final private SystemPresetPromptHelper systemPresetPromptHelper;
    final private NbchatRobotConfigProperties nbchatRobotConfigProperties;
    final private RedisHelper redisHelper;
    final private NbchatRobotToolsApi nbchatRobotToolsApi;

    public ChatMessageBuilderService(KbResearchHelper kbResearchHelper,
                                     SystemPresetPromptHelper systemPresetPromptHelper,
                                     NbchatRobotConfigProperties nbchatRobotConfigProperties,
                                     RedisHelper redisHelper,
                                     NbchatRobotToolsApi nbchatRobotToolsApi) {
        this.kbResearchHelper = kbResearchHelper;
        this.systemPresetPromptHelper = systemPresetPromptHelper;
        this.nbchatRobotConfigProperties = nbchatRobotConfigProperties;
        this.redisHelper = redisHelper;
        this.nbchatRobotToolsApi = nbchatRobotToolsApi;
    }

    /**
     * 构建迪问聊天消息
     *
     * @param request
     * @return
     */
    public ChatMessageBuilder buildMessage(RobotMessageRequest request) {
        request.setPresetId(systemPresetPromptHelper.matchRobotType(request.getPresetId(),"",request.getRobotType()));
        if (ChatAppType.DIC_CHAT.getCode().equals(request.getAppType())) {
            return dicChatMessage(request);
        } else {
            if (StringUtils.isNotBlank(request.getMajorId())) {
                return researchChatMessage(request, null);
            }
            if (StringUtils.isNotBlank(request.getAppId())) {
                ResearchAppBO appInfo = request.getAppInfo();
                //查询配置
                if (ChatAppType.KB_RESEARCH.getStrCode().equals(appInfo.getAppType())) {
                    JSONObject appConfigJson = JSONObject.parseObject(appInfo.getAppConfig());
                    ResearchAppConfig appConfig = appConfigJson.toJavaObject(ResearchAppConfig.class);
                    try {
                        //获取map对象
                        JSONObject modelConfig = appConfigJson.getJSONObject("modelConfig");
                        request.setModelConfig(modelConfig);
                    } catch (Exception e) {
                        log.error("解析模型配置异常: {}", appConfigJson);
                    }
                    return researchChatMessage(request, appConfig);
                }
            }
            return appChatMessage(request);
        }
    }


    private ChatMessageBuilder appChatMessage(RobotMessageRequest request) {
        final List<ChatMessage> messages = new ArrayList<>();
        final ChatMessage userMessage = new ChatMessage(ChatMessageRole.USER.value(), request.getText());
        messages.add(userMessage);
        return ChatMessageBuilder.builder().success(true).messages(messages).build();
    }

    private ChatMessageBuilder researchChatMessage(RobotMessageRequest request, ResearchAppConfig appConfig) {
        FileResearchSearchRequest searchRequest = FileResearchSearchRequest.builder().
                requestId(request.getRequestId()).
                text(request.getText()).
                appId(request.getAppId()).
                userId(request.getUserId()).
                tenantCode(request.getTenantCode()).
                majorConfig(request.getMajorConfig()).
                majorIds(request.getMajorIds()).
                majorId(request.getMajorId()).build();
        String systemRole = "";
        if (appConfig != null) {
            searchRequest.setRecallConfig(appConfig.getRecall());
            //系统提示词
            if (appConfig.checkPrompt()) {
                systemRole = appConfig.getPrompt().getSystem();
            }
        }
        //开启多轮会话改写
        if (request.getChatTurn() != null && request.getChatTurn() > 0) {
            if ("1".equals(request.getAppInfo().getQueryRewrite())) {
                List<ChatMessage> historyMsgs = this.buildHistory(request);
                String text = this.doRewrite(request.getRobotType(), request.getText(), historyMsgs);
                searchRequest.setText(text);
            }
        }
        log.info("文档检索消息处理-执行查询改写: {}", searchRequest);
        RspList<FileResearchSearchPartResult> rspList = kbResearchHelper.searchResult(searchRequest);
        Set<String> docNames = new LinkedHashSet<>();
        ChatMessageBuilder messageBuilder = null;
        if (rspList.isSuccess()) {
            List<ChatMessage> messages = new ArrayList<>();
            if (request.getChatTurn() != null && request.getChatTurn() > 0) {
                messages = this.buildHistory(request);
            } else {
                if (rspList.getCount() == 0) {
                    return ChatMessageBuilder.builder().success(false).unknownResearch(true).build();
                }
            }
            //拼接向量搜索结果
            StringBuilder embeddings = new StringBuilder();
            int i = 1;
            for (FileResearchSearchPartResult row : rspList.getRows()) {
                embeddings.append(i++).append(".").append(row.getContent()).append("\n");
                docNames.add(row.getFileName());
            }
            List<ResearchPartResult> partResults = Lists.newArrayList();
            NiccCommonUtil.copyList(rspList.getRows(), partResults, ResearchPartResult.class);
            //缓存结果-2分钟
            String key = RobotRedisKeyConstant.getResearchPartResultKey(request.getRequestId());
            redisHelper.set(key, JSONObject.toJSONString(partResults, false), TimeUnit.MINUTES.toSeconds(2));
            //系统角色设定
            if (StringUtils.isBlank(systemRole)) {
                systemRole = systemPresetPromptHelper.getSystemRole(SystemPromptCode.system_research.getCode(),
                        request.getText(),
                        embeddings.toString());
            }
            //当前提问
            String user = request.getText();
            if (rspList.getCount() > 0) {
                user = systemPresetPromptHelper.getUserRole(SystemPromptCode.system_research.getCode(),
                        request.getText(), embeddings.toString());
            }
            //用户提问
            messages.add(new ChatMessage(ChatMessageRole.USER.value(), user));
            //设置系统角色
            if (StringUtils.isNotBlank(systemRole)) {
                final ChatMessage systemMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), systemRole);
                //插入到第一个
                messages.add(0, systemMessage);
            }
            messageBuilder = ChatMessageBuilder.builder().success(true).messages(messages).docNames(docNames).build();
        } else {
            messageBuilder = ChatMessageBuilder.builder().success(false).unknownResearch(true).build();
        }
        if (log.isTraceEnabled()) {
            log.info("文档检索消息处理-请求明细[{},{},{}] | [{}|{}]", request.getRequestId(), request.getMajorId(), request.getRobotType(),
                    request.getText(), JSONObject.toJSONString(messageBuilder, false));
        }
        return messageBuilder;
    }


    public String doRewrite(String robotType, String currentMsg, List<ChatMessage> historyMsgs) {
        List<String> args = this.buildArgs(currentMsg, historyMsgs);
        if (robotType.contains("deepseek")) {
            //使用 doubao，deepseek思考时间太长
            robotType = RobotType.DOUBAO_AI.getCode();
        }
        Rsp<RobotToolsChatResponse> rsp = this.chatResult(robotType, SystemPromptCode.kb_q_rew, args);
        if (rsp.isSuccess()) {
            RobotToolsChatResponse chatResponse = rsp.getData();
            return chatResponse.getContent();
        } else {
            log.error("调用ai接口失败:{}", rsp);
            throw new RuntimeException("调用ai接口失败");
        }
    }

    public List<String> buildArgs(String currentMsg, List<ChatMessage> historyMsgs) {
        StringBuilder strB = new StringBuilder();
        for (ChatMessage msg : historyMsgs) {
            if (msg.getRole().equals(ChatMessageRole.USER.value())) {
                strB.append("提问：").append(msg.getContent()).append("\n");
            }
            if (msg.getRole().equals(ChatMessageRole.ASSISTANT.value())) {
                strB.append("回答：").append(msg.getContent()).append("\n");
            }
        }
        List<String> args = new ArrayList<>();
        args.add(strB.toString());
        args.add(currentMsg);
        return args;
    }

    public Rsp<RobotToolsChatResponse> chatResult(String robotType, SystemPromptCode promptCode, List<String> args) {
        RobotPromptMessageRequest msgRequest = new RobotPromptMessageRequest();
        msgRequest.setRobotType(robotType);
        msgRequest.setTrim(true);
        msgRequest.setPresetId(promptCode.getCode());
        msgRequest.setPresetPrompts(args);
        Rsp<RobotToolsChatResponse> chatResult = null;
        try {
            chatResult = nbchatRobotToolsApi.getChatResult(msgRequest);
        } catch (Exception e) {
            log.error("调用ai接口失败:", e);
            return BaseRspUtils.createErrorRsp("调用ai失败");
        }
        log.info("ai返回结果:{}", chatResult);
        return chatResult;
    }

    public List<ChatMessage> buildHistory(RobotMessageRequest request) {
        List<ChatMessage> messages = new ArrayList<>();
        List<NbchatResearchMsg> history = nbchatResearchMsgMapper.selectLastContents(request.getSessionId(), request.getChatTurn());
        //拼接历史对话
        //按时间dateTime顺序排
        history = history.stream().sorted(Comparator.comparing(NbchatResearchMsg::getDateTime)).collect(Collectors.toList());
        System.out.println(history);
        for (NbchatResearchMsg row : history) {
            messages.add(new ChatMessage(ChatMessageRole.USER.value(), row.getPrompt()));
            messages.add(new ChatMessage(ChatMessageRole.ASSISTANT.value(), row.getText()));
        }
        return messages;
    }

    private ChatMessageBuilder dicChatMessage(RobotMessageRequest request) {
        final List<ChatMessage> messages = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getRequestOptions()) && nbchatRobotConfigProperties.getChatTurn() > 0) {
            try {
                int chatTurn = nbchatRobotConfigProperties.getChatTurn();
                if (ObjectUtils.isNotEmpty(request.getChatTurn())) {
                    chatTurn = request.getChatTurn();
                }
                RequestOptions requestOptions = JSONObject.parseObject(request.getRequestOptions(), RequestOptions.class);
                List<NbchatSessionMsg> sessionMsgs = nbchatRobotSessionMsgMapper.selectLastContents(
                        request.getSessionId(),
                        requestOptions.getParentMessageId(), chatTurn);
                if (!sessionMsgs.isEmpty()) {
                    sessionMsgs = sessionMsgs.stream().sorted(Comparator.comparing(NbchatSessionMsg::getDateTime)).
                            collect(Collectors.toList());
                    for (NbchatSessionMsg sessionMsg : sessionMsgs) {
                        String prompt = sessionMsg.getPrompt();
                        final ChatMessage user = new ChatMessage(ChatMessageRole.USER.value(), prompt);
                        messages.add(user);
                        String text = sessionMsg.getText();
                        final ChatMessage assistant = new ChatMessage(ChatMessageRole.ASSISTANT.value(), text);
                        messages.add(assistant);
                    }
                }
            } catch (Exception e) {
                log.error("机器人消息处理-加载上次提问异常:{}", e, request);
            }
        }
        final ChatMessage userMessage = new ChatMessage(ChatMessageRole.USER.value(), request.getText());
        messages.add(userMessage);
        //设定gpt系统角色
        String robotType = request.getRobotType();
        if (StringUtils.isBlank(request.getPresetId()) &&
                StringUtils.isNotBlank(nbchatRobotConfigProperties.getRobotRole()) && (
                robotType.startsWith(RobotType.CHATGPT.getCode()) ||
                        robotType.startsWith(RobotType.ZP_AI.getCode()) ||
                        robotType.startsWith(RobotType.FASTCHAT.getCode())
        )
        ) {
            final ChatMessage systemMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), nbchatRobotConfigProperties.getRobotRole());
            //系统角色插到第一个
            messages.add(0, systemMessage);
        }
        return ChatMessageBuilder.builder().success(true).messages(messages).build();
    }

    public void handleResearchRequest(RobotMessageRequest request) {
        log.info("ResearchAppService researchApp");
    }

}
