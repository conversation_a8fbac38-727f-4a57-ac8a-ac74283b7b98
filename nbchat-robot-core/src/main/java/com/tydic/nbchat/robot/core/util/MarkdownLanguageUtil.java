package com.tydic.nbchat.robot.core.util;


import com.tydic.nbchat.robot.api.bo.RobotMsgContext;

public class MarkdownLanguageUtil {

    public static String[] languages = new String[]{
            "mysgl",
            "sql",
            "java",
            "yaml",
            "js",
            "javascript",
            "css",
            "html",
            "json",
            "shell",
            "xml",
            "python",
            "cython",
            "typescript",
            "sql92",
            "sqlite",
            "agsl",
            "aidl",
            "androiddatabinding",
            "angular2html",
            "angular2svg",
            "angularjs",
            "azure",
            "bigquery",
            "cassandragl",
            "clickhouse",
            "cockroach",
            "commandline",
            "cookie",
            "couchbasequery",
            "db2",
            "db2_is",
            "db2_zos",
            "derby",
            "devicespec",
            "djangourTpath",
            "dockerfile",
            "dockerignore",
            "doctest",
            "dtd",
            "ecma script Tevel 4",
            "ecmascript 6",
            "editorconfig",
            "ejbql",
            "el",
            "eql",
            "exasol",
            "flow js",
            "genericsgl",
            "gherkin",
            "gitexclude",
            "gitignore",
            "ggl",
            "greenplum",
            "groovy",
            "h2",
            "hgignore",
            "hivegl",
            "hql",
            "hsgldb",
            "http request",
            "ignorelang",
            "injectedfreemarker",
            "integrationperformancetest",
            "jpagl",
            "jql",
            "jquery-css",
            "jshelilanguage",
            "json Lines",
            "json5",
            "jsonpath",
            "jsregexp",
            "jsunicoderegexp",
            "jsx",
            "jupyter",
            "jupyterpython",
            "knd",
            "kotlin",
            "Less",
            "logcatfilter",
            "Lombok.config",
            "Tua",
            "manifest",
            "mariadb",
            "markdown",
            "mermaid",
            "micronaut-mongodb-json",
            "micronautdatagl",
            "multidexkeep",
            "nashorn js",
            "oracle",
            "oraclesglplus",
            "plantuml",
            "pointcutexpression",
            "postcss",
            "postgresgl",
            "properties",
            "protobuf",
            "prototext",
            "puml",
            "pycon",
            "pythonregexp",
            "pythonverboseregexp",
            "gute",
            "redis",
            "redshift",
            "regexp",
            "relax-ng",
            "renderscript",
            "roomsgl",
            "sass",
            "scss",
            "shrinker_config",
            "smali",
            "snowflake",
            "sparksgl",
            "spel",
            "spi",
            "spring-mongodb-json",
            "springdataql",
            "svg",
            "sybase",
            "text",
            "textmate",
            "thymeleafexpressions",
            "thymeleafiterateexpressionsthymeleafspringsecurityextras",
            "thymeleaftemplatesexpressionsthymeleaftemplatesfragmentexpressions",
            "thymeleafurlexpressions",
            "toml",
            "ts",
            "tsql",
            "tsx",
            "typescript jsx",
            "vertica",
            "vtl",
            "vue",
            "xhtml",
            "xpath",
            "xpath2",
            "xsdregexp"
    };


    /**
     * 修复markdown格式
     * @param text
     * @return
     */
    public static String fixMarkdown(String text){
        if(text.contains("```")){
            String endOfEnter = "```.*[^\n]$";
            for (String language : languages) {
                String source = "```" + language;
                text = text.replaceAll(source,source+"\n");
                text = text.replaceAll(source.toUpperCase(),source+"\n");
            }
        }
        return text;
    }

    /**
     * 修复markdown
     * @param context
     */
    public static void fixMarkdown(RobotMsgContext context){
        context.setText(fixMarkdown(context.getText()));
    }


    public static void main(String[] args) {
        System.out.println("[9]杨河清,王 欣.过劳问题研究的路径与动向[J].经济学动态,2015(8):152-160.\n".length());
    }

}
