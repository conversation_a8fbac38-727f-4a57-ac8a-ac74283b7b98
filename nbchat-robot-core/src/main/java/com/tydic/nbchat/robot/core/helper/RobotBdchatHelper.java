package com.tydic.nbchat.robot.core.helper;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.openai.completion.chat.*;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.api.AiRobotHelper;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.openai.service.OpenAiService;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.util.*;

@Slf4j
public class RobotBdchatHelper implements AiRobotHelper {
    private NbchatRobotConfigProperties configProperties;
    private List<RobotToken> tokens;

    private Map<String, OpenAiService> serviceTagMap = new HashMap<>();
    // openai 接口调用
    private Map<Integer, OpenAiService> serviceMap = new HashMap<>();


    public RobotBdchatHelper(NbchatRobotConfigProperties configProperties) {
        this(configProperties, new ArrayList<>());
    }

    public RobotBdchatHelper(NbchatRobotConfigProperties configProperties, List<RobotToken> tokens) {
        this.configProperties = configProperties;
        this.tokens = tokens;
        if (tokens != null && !tokens.isEmpty()) {
            reloadService(tokens);
        }
    }

    @Override
    public String robot() {
        return RobotType.BD_CHAT.getCode();
    }

    @Override
    public Flowable<ChatCompletionChunk> streamChat(AiRobotChatRequest request) {
        OpenAiService service = getService(request);
        return service.streamBaiduChatCompletion(buildChatRequest(request));
    }

    @Override
    public ChatCompletionResult chat(AiRobotChatRequest request) {
        OpenAiService service = getService(request);
        ChatCompletionResult result = service.createBaiduChatCompletion(buildChatRequest(request));
        convertMessage(result);
        return result;
    }


    @Override
    public ChatCompletionRequest buildChatRequest(AiRobotChatRequest request) {
        OpenAiService service = getService(request);
        ChatMessageBuilder messageBuilder = request.getMessageBuilder();
        List<ChatMessage> newMessages = Lists.newArrayList();
        String system = null;
        for (ChatMessage message : messageBuilder.getMessages()) {
            if (ChatMessageRole.USER.value().equals(message.getRole()) ||
                    ChatMessageRole.ASSISTANT.value().equals(message.getRole())) {
                //文心一言只支持这俩角色
                newMessages.add(message);
            }
            if (ChatMessageRole.SYSTEM.value().equals(message.getRole())){
                system = message.getStrContent();
            }
        }
        //文心一言要求这个参数长度为奇数: user assistant user ..
        if (!isOdd(newMessages.size())) {
            newMessages.remove(0);
        }
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder().
                model(service.getConfigProperties().getRobotModel()).system(system).
                messages(newMessages).build();
        log.info("请求机器人原始参数[{}|{}]:{}", robot(), service.getConfigProperties(), JSONObject.toJSONString(completionRequest));
        return completionRequest;
    }

    private boolean isOdd(int i) {
        return (i & 1) == 1;
    }


    @Override
    public void convertMessage(ChatCompletionChunk chunk) {
        //消息转换适配，文心一言没有choice对象
        if (chunk.getChoices() == null) {
            if (chunk.getIs_end() != null && chunk.getIs_end()) {
                chunk.setChoices(buildChoices("stop", chunk.getResult()));
            } else {
                chunk.setChoices(buildChoices(null, chunk.getResult()));
            }
        }
    }

    @Override
    public void convertMessage(ChatCompletionResult result) {
        //消息转换适配
        if (result.getChoices() == null) {
            if (result.getIs_end() != null && result.getIs_end()) {
                result.setChoices(buildChoices("stop", result.getResult()));
            } else {
                result.setChoices(buildChoices(null, result.getResult()));
            }
        }
    }

    private List<ChatCompletionChoice> buildChoices(String finished, String content) {
        List<ChatCompletionChoice> choices = new ArrayList<>();
        ChatCompletionChoice choice = new ChatCompletionChoice();
        choice.setFinishReason(finished);
        choice.setIndex(0);
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setRole(ChatMessageRole.ASSISTANT.value());
        chatMessage.setContent(content);
        choice.setMessage(chatMessage);
        choices.add(choice);
        return choices;
    }

    @Override
    public void reloadService(List<RobotToken> tokens) {
        this.tokens = tokens;
        serviceTagMap = initServiceTagMap(tokens, configProperties.getRobotReadTimeout());
        serviceMap = initServiceMap(tokens, configProperties.getRobotReadTimeout());
        log.info("初始化openAiServie[{}]-[tag]-完成:{}",robot(), toTagMapString(serviceTagMap));
        log.info("初始化openAiServie[{}]--完成:{}",robot(), toMapString(serviceMap));
    }

    @Override
    public Map<Integer, OpenAiService> getServiceMap() {
        return serviceMap;
    }

    @Override
    public Map<String, OpenAiService> getServiceTagMap() {
        return serviceTagMap;
    }

    @Override
    public void removeService(String token) {

    }

    @Override
    public OpenAiService getDefaultService() {
        return serviceMap.get(0);
    }

    @Override
    public List<RobotToken> getTokens() {
        return tokens;
    }
}
