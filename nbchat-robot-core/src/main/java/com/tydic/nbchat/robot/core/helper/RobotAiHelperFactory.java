package com.tydic.nbchat.robot.core.helper;

import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.bo.robot.RobotTokenQueryReqBO;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChunk;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.core.busi.RobotConfigBusiService;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.api.AiRobotHelper;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.openai.config.ServiceConfigProperties;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.RspList;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
public class RobotAiHelperFactory {

    private AiRobotHelper defaultHelper;
    private List<RobotToken> tokenList;
    private Map<String, AiRobotHelper> aiRobotHelperMap = new HashMap<>();
    private NbchatRobotConfigProperties nbchatRobotConfigProperties;
    private RobotConfigBusiService robotConfigBusiService;

    public RobotAiHelperFactory(NbchatRobotConfigProperties nbchatRobotConfigProperties) {
        this(nbchatRobotConfigProperties, new ArrayList<>());
    }

    public RobotAiHelperFactory(NbchatRobotConfigProperties configProperties,
                                RobotConfigBusiService robotConfigBusiService) {
        this.robotConfigBusiService = robotConfigBusiService;
        this.nbchatRobotConfigProperties = configProperties;
        RobotTokenQueryReqBO req = RobotTokenQueryReqBO.builder().
                envType(nbchatRobotConfigProperties.getEnvType()).
                        isValid(EntityValidType.NORMAL.getCode()).build();
        RspList<RobotToken>  tokenRspList = robotConfigBusiService.getRobotTokes(req);
        this.tokenList = tokenRspList.getRows();
        log.info("初始化 RobotAiHelperFactory: config = {}, tokens = {}",
                nbchatRobotConfigProperties,tokenRspList.getRows());
        initRobotHelpers(tokenList);
    }

    public RobotAiHelperFactory(NbchatRobotConfigProperties configProperties, List<RobotToken> tokenList) {
        this.tokenList = tokenList;
        this.nbchatRobotConfigProperties = configProperties;
        initRobotHelpers(tokenList);
    }

    /**
     * 刷新helper
     * @param robotType
     * @return
     */
    public boolean reloadHelper(String robotType){
        RobotTokenQueryReqBO req = RobotTokenQueryReqBO.builder().
                envType(nbchatRobotConfigProperties.getEnvType()).
                robotType(robotType).isValid(EntityValidType.NORMAL.getCode()).build();
        RspList<RobotToken>  tokenRspList = robotConfigBusiService.getRobotTokes(req);
        if(tokenRspList.isSuccess() && tokenRspList.getCount() > 0){
            aiRobotHelperMap.remove(robotType);
            AiRobotHelper helper = createHelper(robotType,tokenRspList.getRows());
            aiRobotHelperMap.put(robotType,helper);
            log.info("刷新 RobotAiHelper: {} -> {}", robotType, tokenRspList.getRows());
            return true;
        }
        return false;
    }

    public void initRobotHelpers(List<RobotToken> tokenList) {
        Map<String, List<RobotToken>> robotMap = tokenList.stream().collect(Collectors.groupingBy(RobotToken::getRobotType));
        if (robotMap.isEmpty()) {
            throw new IllegalArgumentException("机器人配置为空: 请配置nbchat_robot_token表!");
        }
        for (Map.Entry<String, List<RobotToken>> listEntry : robotMap.entrySet()) {
            this.aiRobotHelperMap.remove(listEntry.getKey());
            //加载ai helper
            log.info("加载 RobotAiHelper: {} -> {}", listEntry.getKey(), listEntry.getValue());
            Optional.ofNullable(createHelper(listEntry.getKey(), listEntry.getValue())).
                    ifPresent(aiRobotHelper -> this.aiRobotHelperMap.put(listEntry.getKey(), aiRobotHelper));
        }
        this.defaultHelper = aiRobotHelperMap.get(nbchatRobotConfigProperties.getRobotTypeDefault());
        if (this.defaultHelper == null) {
            log.warn("默认机器人配置读取失败: default = {}", nbchatRobotConfigProperties.getRobotTypeDefault());
        }
    }

    public void clearHelper(String robotType){
        aiRobotHelperMap.remove(robotType);
    }

    /**
     * 创建ai helper对象
     *
     * @param robotType
     * @param tokens
     * @return
     */
    public AiRobotHelper createHelper(String robotType, List<RobotToken> tokens) {
        if (robotType.startsWith(RobotType.CHATGPT.getCode())) {
            return new RobotChatgptHelper(this.nbchatRobotConfigProperties, tokens);
        } else if (robotType.startsWith(RobotType.FASTCHAT.getCode())) {
            return new RobotFastchatHelper(this.nbchatRobotConfigProperties, tokens);
        } else if (robotType.startsWith(RobotType.BD_CHAT.getCode())) {
            return new RobotBdchatHelper(this.nbchatRobotConfigProperties, tokens);
        } else if (robotType.startsWith(RobotType.ZP_AI.getCode())) {
            return new RobotZpAIchatHelper(this.nbchatRobotConfigProperties, tokens);
        } else if (robotType.startsWith(RobotType.DOUBAO_AI.getCode())) {
            return new RobotDoubaoAIchatHelper(this.nbchatRobotConfigProperties, tokens);
        } else if (robotType.startsWith(RobotType.UNICOM.getCode())) {
            return new RobotUnicomLlmHelper(this.nbchatRobotConfigProperties, tokens);
        } else {
            return null;
        }
    }


    /**
     * @param request
     * @return
     */
    public Flowable<ChatCompletionChunk> streamChat(AiRobotChatRequest request) {
        if (request.getSearchOnline() != null && request.getSearchOnline()) {
            /*if (StringUtils.isNotBlank(nbchatRobotConfigProperties.getRobotOnlineDefault())) {
                request.getMessageRequest().setRobotType(nbchatRobotConfigProperties.getRobotOnlineDefault());
            }*/
        }
        AiRobotHelper aiRobotHelper = getHelper(request.getMessageRequest().getRobotType());
        if (aiRobotHelper != null) {
            return aiRobotHelper.streamChat(request);
        } else {
            return defaultHelper.streamChat(request);
        }
    }

    /**
     * 聊天
     *
     * @param request
     * @return
     */
    public ChatCompletionResult chat(AiRobotChatRequest request) {
        if (request.getSearchOnline() != null && request.getSearchOnline()) {
            if (StringUtils.isNotBlank(nbchatRobotConfigProperties.getRobotOnlineDefault())) {
                request.getMessageRequest().setRobotType(nbchatRobotConfigProperties.getRobotOnlineDefault());
            }
        }
        AiRobotHelper aiRobotHelper = getHelper(request.getMessageRequest().getRobotType());
        if (aiRobotHelper != null) {
            return aiRobotHelper.chat(request);
        } else {
            return defaultHelper.chat(request);
        }
    }

    /**
     * 消息转换
     * @param request
     * @param chunk
     */
    public void convertChuckMessage(AiRobotChatRequest request,ChatCompletionChunk chunk) {
        AiRobotHelper aiRobotHelper = getHelper(request.getMessageRequest().getRobotType());
        if (aiRobotHelper != null) {
            aiRobotHelper.convertMessage(chunk);
        }
    }


    @Deprecated
    public void removeService(RobotMessageRequest request,String token){
        getHelper(request.getRobotType()).removeService(token);
    }

    /**
     * 获取配置信息
     * @param request
     * @return
     */
    @Deprecated
    public ServiceConfigProperties getServiceConfig(RobotMessageRequest request) {
        return getHelper(request.getRobotType()).getServiceConfig(request);
    }

    public AiRobotHelper getHelper() {
        return defaultHelper;
    }

    public AiRobotHelper getHelper(String robotType) {
        AtomicReference<AiRobotHelper> helper = new AtomicReference<>(defaultHelper);
        Optional.ofNullable(aiRobotHelperMap.get(robotType)).ifPresent(helper::set);
        return helper.get();
    }


    public List<RobotToken> getStartWithTokens(String robotType) {
        RobotTokenQueryReqBO req = RobotTokenQueryReqBO.builder().
                envType(nbchatRobotConfigProperties.getEnvType())
                .isValid(EntityValidType.NORMAL.getCode()).build();
        RspList<RobotToken> tokenRspList = robotConfigBusiService.getRobotTokes(req);
        this.tokenList = tokenRspList.getRows();
        List<RobotToken> tokens = tokenList.stream().filter(row ->
                row.getRobotType().startsWith(robotType)).collect(Collectors.toList());
        log.info("获取待刷新token: robotType = {}, count = {}", robotType, tokens.size());
        return tokens;
    }


    public Map<String, AiRobotHelper> getHelpers() {
        return aiRobotHelperMap;
    }

}
