package com.tydic.nbchat.robot.core.helper.api;

public interface AiTokenHelper {

    default String tokenCacheKey(Integer tokenId){
        return "nbchat-robot:token:" + tokenId;
    }

    /**
     * 机器人
     * @return
     */
    String robot();

    /**
     * 刷新token
     */
    void freshToken();

    /**
     * 指定robotType刷新
     * @param robotType
     */
    default void freshByRobotType(String robotType){}

    /**
     * 移除缓存key
     */
    default void removeCacheKey(){

    }

    default void removeCacheKey(String robotType){

    }

}
