package com.tydic.nbchat.robot.core.web;

import com.tydic.nbchat.robot.api.NbChatRobotManageApi;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.bo.robot.RobotTokenQueryReqBO;
import com.tydic.nbchat.robot.api.bo.robot.SaveRobotConfigReqBO;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.api.AiTokenHelper;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Slf4j
@RestController
@RequestMapping("/robot/config")
public class RobotConfigController {

    final private NbChatRobotManageApi nbChatRobotManageApi;
    final private RobotAiHelperFactory robotAiHelperFactory;
    final private List<AiTokenHelper> aiTokenHelpers;

    public RobotConfigController(NbChatRobotManageApi nbChatRobotManageApi,
                                 RobotAiHelperFactory robotAiHelperFactory,
                                 List<AiTokenHelper> aiTokenHelpers) {
        this.nbChatRobotManageApi = nbChatRobotManageApi;
        this.robotAiHelperFactory = robotAiHelperFactory;
        this.aiTokenHelpers = aiTokenHelpers;
    }

    @PostMapping(value = "/save")
    public Rsp saveRobotConfig(@RequestBody SaveRobotConfigReqBO req) {
        return nbChatRobotManageApi.saveRobotConfig(req);
    }

    @PostMapping(value = "/service/reload")
    public RspList reloadServiceInit(@RequestBody RobotTokenQueryReqBO req) {
        if (StringUtils.isAnyBlank(req.getEnvType())) {
            return BaseRspUtils.createErrorRspList("参数异常！");
        }
        req.setIsValid(EntityValidType.NORMAL.getCode());
        RspList<RobotToken> rspList = nbChatRobotManageApi.getRobotTokes(req);
        robotAiHelperFactory.initRobotHelpers(rspList.getRows());
        return rspList;
    }

    @GetMapping(value = "/helper/reload/{robotType}")
    public Rsp reloadServiceMap(@PathVariable("robotType") String robotType) {
        boolean res = robotAiHelperFactory.reloadHelper(robotType);
        freshToken(robotType);
        return BaseRspUtils.createSuccessRsp(res, robotType);
    }

    @GetMapping(value = "/token/flash/{robotType}")
    public Rsp freshToken(@PathVariable("robotType") String robotType) {
        for (AiTokenHelper aiTokenHelper : aiTokenHelpers) {
            if (robotType.startsWith(aiTokenHelper.robot())) {
                aiTokenHelper.removeCacheKey(robotType);
                aiTokenHelper.freshByRobotType(robotType);
                return BaseRspUtils.createSuccessRsp(robotType);
            }
        }
        return BaseRspUtils.createErrorRsp("参数异常");
    }

    @PostMapping(value = "/service/clear")
    public Rsp clearServiceMap(@RequestBody RobotTokenQueryReqBO req) {
        robotAiHelperFactory.clearHelper(req.getRobotType());
        return BaseRspUtils.createSuccessRsp(req.getTokenType());
    }

}
