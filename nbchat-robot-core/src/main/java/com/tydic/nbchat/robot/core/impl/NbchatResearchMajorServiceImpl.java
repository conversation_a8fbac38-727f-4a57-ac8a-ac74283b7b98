package com.tydic.nbchat.robot.core.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.robot.api.NbchatResearchMajorApi;
import com.tydic.nbchat.robot.api.bo.RobotRedisKeyConstant;
import com.tydic.nbchat.robot.api.bo.eums.DocSourceType;
import com.tydic.nbchat.robot.api.bo.eums.ResearchAnalysisState;
import com.tydic.nbchat.robot.api.bo.research.*;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.mapper.NbchatResearchFilesMapper;
import com.tydic.nbchat.robot.mapper.NbchatResearchMajorMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchFiles;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchFilesSelectCondition;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchMajor;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchMajorSelectCondition;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class NbchatResearchMajorServiceImpl implements NbchatResearchMajorApi {

    @Resource
    private NbchatResearchMajorMapper nbchatResearchMajorMapper;
    @Resource
    private NbchatResearchFilesMapper nbchatResearchFilesMapper;
    private final NbchatRobotConfigProperties nbchatRobotConfigProperties;
    private final RedisHelper redisHelper;

    public NbchatResearchMajorServiceImpl(
                                          NbchatRobotConfigProperties nbchatRobotConfigProperties,
                                          RedisHelper redisHelper) {
        this.nbchatRobotConfigProperties = nbchatRobotConfigProperties;
        this.redisHelper = redisHelper;
    }


    @Override
    public Rsp<ResearchPartResultRsp> getResearchPartResult(String requestId) {
        try {
            String key = RobotRedisKeyConstant.getResearchPartResultKey(requestId);
            String parts = (String) redisHelper.get(key);
            if(StringUtils.isNotBlank(parts)){
                List<ResearchPartResult> results = JSONObject.parseArray(parts,ResearchPartResult.class);
                ResearchPartResultRsp obj = ResearchPartResultRsp.builder().parts(results).
                        researchCitationPrefix(nbchatRobotConfigProperties.getKbResearch().getCitationPrefix()).
                        researchCitationEnable(nbchatRobotConfigProperties.getKbResearch().getCitationEnable()).build();
                return BaseRspUtils.createSuccessRsp(obj);
            }
        } catch (Exception e) {
            log.error("解析段落结果异常:",e);
        }
        return BaseRspUtils.createSuccessRsp(ResearchPartResultRsp.builder().parts(new ArrayList<>()).build());
    }

    @Override
    public RspList getResearchMajors(FileResearchMajorQueryRequest request) {
        log.info("文档检索-查询专属机器人:{}", JSON.toJSONString(request));
        if(StringUtils.isAnyBlank(request.getTenantCode(),request.getUserId())){
            return BaseRspUtils.createErrorRspList("参数异常!");
        }
        List<FileResearchMajorBO> majorBOS = Lists.newArrayList();
        NbchatResearchMajorSelectCondition condition = new NbchatResearchMajorSelectCondition();
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        condition.setUserId(request.getUserId());
        condition.setMajorSource(request.getMajorSource());
        condition.setMajorId(request.getMajorId());
        Page<NbchatResearchMajor> page = PageHelper.startPage(request.getPage(),request.getLimit());
        nbchatResearchMajorMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(page.getResult(),majorBOS,FileResearchMajorBO.class);
        return BaseRspUtils.createSuccessRspList(majorBOS);
    }

    @Override
    public Rsp getResearchMajor(FileResearchMajorQueryRequest request) {
        log.info("文档检索-查询专属机器人:{}", JSON.toJSONString(request));
        if(StringUtils.isAnyBlank(request.getUserId(),request.getMajorId())){
            return BaseRspUtils.createErrorRsp("参数异常!");
        }
        NbchatResearchMajor major = nbchatResearchMajorMapper.selectByPrimaryKey(request.getMajorId());
        if(major != null){
            FileResearchMajorBO majorBO = new FileResearchMajorBO();
            BeanUtils.copyProperties(major,majorBO);
            return BaseRspUtils.createSuccessRsp(majorBO);
        }
        return BaseRspUtils.createErrorRsp("文档不存在!");
    }

    @Override
    public Rsp getMajorAnalysisStatus(FileResearchMajorQueryRequest request) {
        log.info("文档检索-专属机器人状态:{}",request);
        if(StringUtils.isBlank(request.getMajorId())){
            return BaseRspUtils.createErrorRsp("参数异常!");
        }
        NbchatResearchFilesSelectCondition condition = new NbchatResearchFilesSelectCondition();
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        condition.setMajorId(request.getMajorId());
        List<NbchatResearchFiles> files = nbchatResearchFilesMapper.selectByCondition(condition);
        FileResearchMajorAnalysisStatus analysisStatus = FileResearchMajorAnalysisStatus.builder().
                majorId(request.getMajorId()).docTotalCount(files.size()).build();
        long parsedCount = files.stream().filter(obj-> ResearchAnalysisState.SUCCESS.getCode().equals(obj.getParseState())).count();
        analysisStatus.setDocParsedCount((int) parsedCount);
        return BaseRspUtils.createSuccessRsp(analysisStatus);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp saveMajor(FileResearchMajorSaveRequest request) {
        log.info("文档检索-保存专属机器人:{}",request);
        NbchatResearchMajor save = new NbchatResearchMajor();
        BeanUtils.copyProperties(request,save);
        save.setUpdateTime(new Date());
        if(StringUtils.isNotBlank(request.getMajorId())){
            NbchatResearchMajor major = nbchatResearchMajorMapper.selectByPrimaryKey(request.getMajorId());
            if(major == null){
                return BaseRspUtils.createErrorRsp("保存异常:该BOT不存在!");
            }
            if(EntityValidType.DELETE.getCode().equals(major.getIsValid())){
                return BaseRspUtils.createErrorRsp("该BOT已删除,请勿重复操作!");
            }
            if(!request.getUserId().equals(major.getUserId())){
                return BaseRspUtils.createErrorRsp("保存异常:非法操作!");
            }
            int update = nbchatResearchMajorMapper.updateSelective(save);
            return BaseRspUtils.createSuccessRsp(update,"更新成功");
        } else {
            //新增
            String majorId = NiccCommonUtil.createImUserId(true);
            save.setMajorId(majorId);
            save.setIsValid(EntityValidType.NORMAL.getCode());
            save.setIsShow(EntityValidType.DELETE.getCode());
            save.setCreateTime(new Date());
            save.setEmbedType(request.getEmbedType());
            nbchatResearchMajorMapper.insertSelective(save);
            NbchatResearchMajorRspBO rsp = new NbchatResearchMajorRspBO();
            BeanUtils.copyProperties(save,rsp);
            return BaseRspUtils.createSuccessRsp(rsp,"新增成功");
        }
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp deleteMajor(FileResearchMajorDeleteRequest request) {
        NbchatResearchMajor major = nbchatResearchMajorMapper.selectByPrimaryKey(request.getMajorId());
        if(major != null){
            if(DocSourceType.SYSTEM.getCode().equals(major.getMajorSource())){
                return BaseRspUtils.createErrorRsp("删除失败:系统内置无法删除!");
            }
            if(EntityValidType.DELETE.getCode().equals(major.getIsValid())){
                return BaseRspUtils.createErrorRsp("该BOT已删除,请勿重复操作!");
            }
            if(!request.getUserId().equals(major.getUserId())){
                return BaseRspUtils.createErrorRsp("删除失败:非法操作!");
            }
            int update = nbchatResearchMajorMapper.deleteByPrimaryKey(major.getMajorId());
            return BaseRspUtils.createSuccessRsp(update);
        }
        return BaseRspUtils.createErrorRsp("删除失败:该BOT不存在!");
    }
}
