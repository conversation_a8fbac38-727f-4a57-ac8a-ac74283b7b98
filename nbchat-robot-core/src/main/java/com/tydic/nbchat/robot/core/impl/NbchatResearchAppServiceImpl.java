package com.tydic.nbchat.robot.core.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.robot.api.NbchatResearchAppApi;
import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppBO;
import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppQueryRequest;
import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppSaveRequest;
import com.tydic.nbchat.robot.core.config.KbResearchConfigProperties;
import com.tydic.nbchat.robot.core.util.RandomStringGenerator;
import com.tydic.nbchat.robot.core.util.RobotStreamChatUtil;
import com.tydic.nbchat.robot.mapper.NbchatResearchAppMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchApp;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchAppSelectCondition;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class NbchatResearchAppServiceImpl implements NbchatResearchAppApi {

    @Resource
    private NbchatResearchAppMapper nbchatResearchAppMapper;
    @Autowired
    private KbResearchConfigProperties kbResearchConfigProperties;

    @MethodParamVerifyEnable
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp saveApp(ResearchAppSaveRequest request) {
        if (StringUtils.isBlank(request.getAppId())) {
            log.info("智能应用-保存:{}", request);
            String appId = RandomStringGenerator.createMaskId();
            request.setAppId(appId);
            NbchatResearchApp app = new NbchatResearchApp();
            app.setCreateTime(new Date());
            app.setUpdateTime(new Date());
            BeanUtils.copyProperties(request, app);
            if (request.getAppConfig() != null) {
                app.setAppConfig(request.getAppConfig().toJSONString());
            }
            app.setApiUrl(kbResearchConfigProperties.getPublishApi());
            nbchatResearchAppMapper.insertSelective(app);
            return BaseRspUtils.createSuccessRsp(appId);
        } else {
            log.info("智能应用-更新:{}", request);
            NbchatResearchApp app = new NbchatResearchApp();
            BeanUtils.copyProperties(request, app);
            app.setUpdateTime(new Date());
            if (EntityValidType.NORMAL.getCode().equals(request.getPublishState())) {
                String url = RobotStreamChatUtil.buildUrl(kbResearchConfigProperties.getPublishUrl(), request.getAppId());
                app.setGuestUrl(url);
                app.setApiUrl(kbResearchConfigProperties.getPublishApi());
            }
            if (request.getAppConfig() != null) {
                app.setAppConfig(request.getAppConfig().toJSONString());
            }
            int i = nbchatResearchAppMapper.updateByPrimaryKeySelective(app);
            if (i > 0) {
                return BaseRspUtils.createSuccessRsp(request.getAppId());
            }
            return BaseRspUtils.createErrorRsp("更新失败");
        }
    }

    @Override
    public Rsp deleteApp(String appId) {
        NbchatResearchApp app = new NbchatResearchApp();
        app.setAppId(appId);
        app.setIsValid(EntityValidType.DELETE.getCode());
        int i = nbchatResearchAppMapper.updateByPrimaryKeySelective(app);
        if (i > 0) {
            return BaseRspUtils.createSuccessRsp(appId);
        }
        return BaseRspUtils.createErrorRsp("删除失败");
    }

    @Override
    public RspList<ResearchAppBO> getAppsList(ResearchAppQueryRequest request) {
        log.info("智能应用-查询:{}", request);
        List<ResearchAppBO> list = Lists.newArrayList();
        NbchatResearchAppSelectCondition condition = new NbchatResearchAppSelectCondition();
        BeanUtils.copyProperties(request,condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        Page<NbchatResearchApp> page = PageHelper.startPage(request.getPage(),request.getLimit());
        nbchatResearchAppMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(page.getResult(),list,ResearchAppBO.class);
        return BaseRspUtils.createSuccessRspList(list,page.getTotal());
    }

    @Override
    public Rsp<ResearchAppBO> getApp(String appId) {
        ResearchAppBO appBO = new ResearchAppBO();
        NbchatResearchApp app = nbchatResearchAppMapper.selectByPrimaryKey(appId);
        if (app != null) {
            BeanUtils.copyProperties(app, appBO);
            return BaseRspUtils.createSuccessRsp(appBO);
        }
        return BaseRspUtils.createErrorRsp("应用不存在");
    }
}
