package com.tydic.nbchat.robot.core.event;

import com.tydic.nbchat.robot.api.event.ChatLogRecordEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ChatLogRecordEventListener {

    @EventListener
    @Async
    public void handleEvent(ChatLogRecordEvent event) {
        //记录处理的日志
        log.info("Received event: {}" , event);
    }
}
