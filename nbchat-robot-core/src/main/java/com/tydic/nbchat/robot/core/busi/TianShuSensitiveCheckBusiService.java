package com.tydic.nbchat.robot.core.busi;

import com.tydic.nbchat.robot.api.bo.RobotRedisKeyConstant;
import com.tydic.nbchat.user.api.UserAccountApi;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TianShuSensitiveCheckBusiService {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private UserAccountApi userAccountApi;
    //敏感词最大次数
    private static final int MAX_SENSITIVE_COUNT = 5;

    private final RedisHelper redisHelper;

    public TianShuSensitiveCheckBusiService(RedisHelper redisHelper) {
        this.redisHelper = redisHelper;
    }

    /**
     * 天枢模型敏感词次数加1
     * @param userId
     * @return
     */
    public long incrSensitiveCount(String userId) {
        try {
            String key = RobotRedisKeyConstant.getTsSensitiveHitPrefix(userId);
            long count = redisHelper.incr(key, 1);
            log.info("用户触发天枢模型敏感词: {}|{}", userId,count);
            if (count >= MAX_SENSITIVE_COUNT) {
                log.info("用户触发天枢模型敏感词超过最大次数: {}", userId);
                userAccountApi.accountDisable(userId,"您的账号由于触发敏感词已被禁用，请联系管理员进行处理！");
            }
            return count;
        } catch (Exception e) {
            log.error("用户触发天枢模型敏感词-处理异常: {}", userId, e);
        }
        return 0;
    }

}
