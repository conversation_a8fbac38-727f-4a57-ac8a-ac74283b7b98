package com.tydic.nbchat.robot.core.web;

import com.tydic.nbchat.robot.api.NbchatBubbleConfigApi;
import com.tydic.nbchat.robot.api.bo.bubble.NbchatBubbleConfigReqBO;
import com.tydic.nbchat.robot.api.bo.bubble.NbchatBubbleDeleteReqBO;
import com.tydic.nbchat.robot.api.bo.bubble.NbchatBubbleSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/robot/bubble")
public class RobotBubbleConfigController {

    final private NbchatBubbleConfigApi nbchatBubbleConfigApi;

    public RobotBubbleConfigController(NbchatBubbleConfigApi nbchatBubbleConfigApi) {
        this.nbchatBubbleConfigApi = nbchatBubbleConfigApi;
    }

    @PostMapping(value = "/list")
    public RspList getBubbleList(@RequestBody NbchatBubbleConfigReqBO request) {
        return nbchatBubbleConfigApi.getBubbleList(request);
    }

    @PostMapping(value = "/save")
    public Rsp saveBubble(@RequestBody NbchatBubbleSaveReqBO request) {
        return nbchatBubbleConfigApi.saveBubble(request);
    }

    @PostMapping(value = "/delete")
    public Rsp deleteBubble(@RequestBody NbchatBubbleDeleteReqBO request) {
        return nbchatBubbleConfigApi.deleteBubble(request);
    }

}
