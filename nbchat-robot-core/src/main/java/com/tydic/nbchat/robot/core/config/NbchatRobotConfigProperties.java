package com.tydic.nbchat.robot.core.config;

import com.tydic.nbchat.robot.api.bo.eums.EnvType;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-robot.config")
public class NbchatRobotConfigProperties {
    //配置环境 dev/prod，读取 nbchat_robot_token 表时使用
    private String envType = EnvType.DEV.getCode();
    //多轮对话- 0 为关闭
    private Integer chatTurn = 3;
    //智能体模型-支持联网搜索
    private String robotOnlineDefault = "doubao_online";
    //设定机器人角色
    private String robotRole = "你是AI小助手,名为'迪问'";
    //系统默认机器人类型
    private String robotTypeDefault = RobotType.ZP_AI.getCode();
    //机器人异常提示
    private String robotErrorNotice = "当前时段AI算力不足，您可以多尝试几次";
    //调用模型超时时长 s
    private Integer robotReadTimeout = 300;
    //敏感词拦截提示语
    private String sensitiveWaring = "该提问包含敏感词汇，请更换您的问题";
    //知识库检索配置
    private KbResearchConfigProperties kbResearch;
    //配置指定业务类型的机器人,定义map类型参数
    private Map<String,String> busiRobotMap;
    //匹配提示词id，使用指定的 robotType
    private Map<String,String> presetRobotMap;
}
