package com.tydic.nbchat.robot.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
@Component
@ConfigurationProperties(prefix = "nbchat-robot.config.yuewen-config")
public class NbchatYuewenConfigProperties {
    private Boolean tokenTimerEnable = false;// enable #是否开启刷新token
    private String clientId;// "粤问分配"
    private String clientSecret;// "粤问分配"
    private String tokenApi;// http://localhost/token/api #对接粤问的token配置
    private String robotTypes;// fastchat,fastchat2 #多个用,分隔
}

