package com.tydic.nbchat.robot.core.helper.api;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.eums.ChatAppType;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.openai.completion.chat.*;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.openai.config.ServiceConfigProperties;
import com.tydic.nbchat.robot.core.openai.service.OpenAiService;
import io.reactivex.Flowable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface AiRobotHelper {
    /**
     * 机器人
     *
     * @return
     */
    String robot();

    Logger logger = LoggerFactory.getLogger(AiRobotHelper.class);

    default ChatCompletionRequest buildChatRequest(AiRobotChatRequest request) {
        ChatMessageBuilder messageBuilder = request.getMessageBuilder();
        ServiceConfigProperties config = getServiceConfig(request);
        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder().
                searchOnline(request.getSearchOnline()).
                model(config.getRobotModel()).
                messages(messageBuilder.getSafeMessage(config.getMaxChars())).build();
        if (messageBuilder.getExceedChars() > 0) {
            logger.warn("请求机器人-消息超出限制:{}|{}", request, messageBuilder.getExceedChars());
        }
        if (ChatAppType.KB_RESEARCH.getCode().equals(request.getMessageRequest().getAppType()) ||
                ChatAppType.APP_CHAT.getCode().equals(request.getMessageRequest().getAppType())) {
            completionRequest.setTemperature(0.2);
        }
        try {
            Map<String, Object> modelParams = request.getMessageRequest().getModelConfig();
            if (modelParams != null && !modelParams.isEmpty()) {
                Double temperature = (Double) modelParams.get("temperature");
                if (temperature != null) {
                    completionRequest.setTemperature(temperature);
                }
                Integer maxTokens = (Integer) modelParams.get("maxTokens");
                if (maxTokens != null) {
                    completionRequest.setMaxTokens(maxTokens);
                }
                Double topP = (Double) modelParams.get("topP");
                if (topP != null) {
                    completionRequest.setTopP(topP);
                }
                String stop = (String) modelParams.get("stop");
                if (StringUtils.isNotBlank(stop)) {
                    completionRequest.setStop(Collections.singletonList(stop.trim()));
                }
            }
        } catch (Exception e) {
            logger.warn("请求机器人-构建参数异常:{}", request, e);
        }
        logger.info("请求机器人-原始参数[{}|{}]:{}", robot(), config.getTokenKey(),
                JSONObject.toJSONString(completionRequest));
        return completionRequest;
    }

    /**
     * 流式响应接口
     *
     * @param request
     * @return
     */
    default Flowable<ChatCompletionChunk> streamChat(AiRobotChatRequest request) {
        OpenAiService service = getService(request);
        Flowable<ChatCompletionChunk> chunkFlowable = service.streamChatCompletion(buildChatRequest(request));
        if (logger.isTraceEnabled()) {
            //打印结果
            chunkFlowable.subscribe(chunk -> {
                logger.trace("请求机器人-结果(streamChat): {}", JSONObject.toJSONString(chunk));
            });
        }
        return chunkFlowable;
    }

    /**
     * 聊天
     *
     * @param request
     * @return
     */
    default ChatCompletionResult chat(AiRobotChatRequest request) {
        OpenAiService service = getService(request);
        ChatCompletionResult result = service.createChatCompletion(buildChatRequest(request));
        if (logger.isTraceEnabled()) {
            logger.trace("请求机器人-结果(chat): {}", JSONObject.toJSONString(result));
        }
        return result;
    }


    default OpenAiService getService(AiRobotChatRequest request) {
        OpenAiService service;
        if (StringUtils.isNotBlank(request.getRobotTag())) {
            service = getTagService(request.getRobotTag());
        } else {
            try {
                service = getService(request.getMessageRequest().getUserId());
            } catch (Exception e) {
                logger.error("请求机器人-获取service异常:{}", request, e);
                service = getDefaultService();
            }
        }
        return service;
    }

    default ServiceConfigProperties getServiceConfig(AiRobotChatRequest request) {
        OpenAiService service = getService(request);
        return service.getConfigProperties();
    }


    default void convertMessage(ChatCompletionChunk completionChunk) {
        //消息转换适配
    }

    default void convertMessage(ChatCompletionResult result) {
        //消息转换适配
    }


    /**
     * 获取token
     *
     * @return
     */
    List<RobotToken> getTokens();

    /**
     * 刷新服务
     *
     * @param tokens
     */
    void reloadService(List<RobotToken> tokens);

    /**
     * 构建服务配置
     *
     * @param token
     * @return
     */
    default ServiceConfigProperties buildServiceConfig(RobotToken token) {
        if (StringUtils.isAnyBlank(token.getTokenKey(), token.getApiUrl())) {
            throw new RuntimeException("RobotToken参数异常!");
        }
        ServiceConfigProperties properties = new ServiceConfigProperties();
        properties.setTokenId(String.valueOf(token.getTokenId()));
        properties.setTokenName(token.getTokenName());
        properties.setTokenKey(token.getTokenKey());
        properties.setApiUrl(token.getApiUrl());
        properties.setRobotType(token.getRobotType());
        properties.setRobotModel(token.getRobotModel());
        properties.setTag(token.getTag());
        properties.setMaxTokens(token.getMaxTokens());
        properties.setRequestMaxTokens(token.getRequestMaxTokens());
        properties.setMaxChars(token.getMaxChars());
        properties.setConfig(token.getConfig());
        return properties;
    }

    default ServiceConfigProperties buildServiceConfig(RobotToken token, int readTimeOut) {
        ServiceConfigProperties properties = buildServiceConfig(token);
        properties.setReadTimeout(readTimeOut);
        return properties;
    }

    default Map<Integer, OpenAiService> initServiceMap(List<RobotToken> tokens) {
        return initServiceMap(tokens, 180);
    }

    /**
     * 刷新方法
     *
     * @param tokens
     */
    default Map<Integer, OpenAiService> initServiceMap(List<RobotToken> tokens, int readTimeOut) {
        // openai 接口调用
        Map<Integer, OpenAiService> serviceMap = new HashMap<>();
        int openAiIndex = 0;
        for (RobotToken token : tokens) {
            if (StringUtils.isNotBlank(token.getApiUrl()) && StringUtils.isBlank(token.getTag())) {
                OpenAiService service = new OpenAiService(buildServiceConfig(token, readTimeOut));
                serviceMap.put(openAiIndex++, service);
                if (logger.isTraceEnabled()) {
                    logger.trace("初始化openService-initServiceTagMap():index = {},config = {}",
                            openAiIndex, service.getConfigProperties());
                }
            }
        }
        if (openAiIndex == 0) {
            logger.error("请检查配置表：nbchat_robot_token，tag标记的配置分多条配置!");
        }
        return serviceMap;
    }


    default Map<String, OpenAiService> initServiceTagMap(List<RobotToken> tokens) {
        return initServiceTagMap(tokens, 180);
    }

    /**
     * 封装map
     *
     * @param tokens
     * @return
     */
    default Map<String, OpenAiService> initServiceTagMap(List<RobotToken> tokens, int readTimeOut) {
        Map<String, OpenAiService> serviceTagMap = new HashMap<>();
        for (RobotToken token : tokens) {
            if (StringUtils.isNotBlank(token.getTag())) {
                OpenAiService service = new OpenAiService(buildServiceConfig(token, readTimeOut));
                serviceTagMap.put(token.getTag().toUpperCase(), service);
                if (logger.isTraceEnabled()) {
                    logger.trace("初始化openService-initServiceTagMap():tag = {},config = {}",
                            token.getTag().toUpperCase(), service.getConfigProperties());
                }
            }
        }
        return serviceTagMap;
    }

    /**
     * 获取map
     *
     * @return
     */
    Map<Integer, OpenAiService> getServiceMap();

    /**
     * 获取map
     *
     * @return
     */
    Map<String, OpenAiService> getServiceTagMap();

    /**
     * 移除服务
     *
     * @param token
     */
    void removeService(String token);


    default int getTokenIndex(String userId) {
        // 将用户ID哈希成整数
        if (StringUtils.isBlank(userId)) {
            return 0;
        }
        int hashCode = userId.hashCode();
        // 对结果取模，得到0到5之间的值
        if (getServiceMap() != null && !getServiceMap().isEmpty()) {
            // 对结果取模，得到0到5之间的值
            return Math.abs(hashCode % getServiceMap().size());
        }
        return -1;
    }


    /**
     * 获取默认服务
     *
     * @return
     */
    OpenAiService getDefaultService();

    /**
     * 获取service
     *
     * @return
     */
    default OpenAiService getService(String userId) {
        int index = getTokenIndex(userId);
        if (logger.isTraceEnabled()) {
            logger.trace("请求机器人-getService:userId = {},index = {}", userId, index);
        }
        if (index < 0 || getServiceMap().size() == 1) {
            return getDefaultService();
        }
        OpenAiService openAiService = getServiceMap().get(index);
        if (openAiService == null) {
            openAiService = getDefaultService();
        }
        return openAiService;
    }


    /**
     * 获取tagService
     *
     * @param tag
     * @return
     */
    default OpenAiService getTagService(String tag) {
        OpenAiService service = getServiceTagMap().get(tag.toUpperCase());
        if (service == null) {
            service = getDefaultService();
        }
        if (logger.isTraceEnabled()) {
            logger.trace("请求机器人-getTagService():tag = {},config = {}", tag, service.getConfigProperties());
        }
        return service;
    }


    default ServiceConfigProperties getServiceConfig(RobotMessageRequest request) {
        OpenAiService service = null;
        ServiceConfigProperties properties = null;
        if (StringUtils.isNotBlank(request.getRobotTag())) {
            service = getTagService(request.getRobotTag().toUpperCase());
        }
        if (service == null) {
            service = getService(request.getUserId());
        }
        if (service != null) {
            properties = service.getConfigProperties();
        } else {
            logger.error("获取Robot配置异常:{}", request);
        }
        return properties;
    }


    default String toMapString(Map<Integer, OpenAiService> serviceTagMap) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<Integer, OpenAiService> entry : serviceTagMap.entrySet()) {
            sb.append("[").append(entry.getKey()).append("_").append(entry.getValue().
                    getConfigProperties().getTokenName()).append("]").append(",");
        }
        return sb.toString();
    }

    default String toTagMapString(Map<String, OpenAiService> serviceTagMap) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, OpenAiService> entry : serviceTagMap.entrySet()) {
            sb.append("[").append(entry.getKey()).append("_").append(entry.getValue().
                    getConfigProperties().getTokenName()).append("]").append(",");
        }
        return sb.toString();
    }

}
