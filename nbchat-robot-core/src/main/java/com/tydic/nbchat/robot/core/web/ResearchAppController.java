package com.tydic.nbchat.robot.core.web;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.NbChatRobotProcessApi;
import com.tydic.nbchat.robot.api.NbchatResearchAppApi;
import com.tydic.nbchat.robot.api.NbchatResearchAppMsgApi;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.eums.ChatAppType;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMsgQueryRequest;
import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppBO;
import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppQueryRequest;
import com.tydic.nbchat.robot.api.bo.research.app.ResearchAppSaveRequest;
import com.tydic.nbchat.robot.core.util.RobotStreamChatUtil;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController

@RequestMapping("/robot/research/app")
public class ResearchAppController {
    private final NbchatResearchAppMsgApi nbchatResearchAppMsgApi;
    private final NbchatResearchAppApi nbchatResearchAppApi;
    private final NbChatRobotProcessApi nbChatRobotProcessApi;

    public ResearchAppController(NbchatResearchAppMsgApi nbchatResearchAppMsgApi,
                                 NbchatResearchAppApi nbchatResearchAppApi,
                                 NbChatRobotProcessApi nbChatRobotProcessApi) {
        this.nbchatResearchAppMsgApi = nbchatResearchAppMsgApi;
        this.nbchatResearchAppApi = nbchatResearchAppApi;
        this.nbChatRobotProcessApi = nbChatRobotProcessApi;
    }

    @PostMapping(value = "/message")
    public RspList getMessage(@RequestBody FileResearchMsgQueryRequest request) {
        return nbchatResearchAppMsgApi.getResearchMessages(request);
    }

    @PostMapping(value = "/message/clean")
    public Rsp cleanMessage(@RequestBody FileResearchMsgQueryRequest request) {
        return nbchatResearchAppMsgApi.cleanMessage(request);
    }

    @PostMapping("/save")
    public Rsp saveApp(@RequestBody ResearchAppSaveRequest request) {
        return nbchatResearchAppApi.saveApp(request);
    }

    @PostMapping("/list")
    public RspList<ResearchAppBO> appList(@RequestBody ResearchAppQueryRequest request) {
        return nbchatResearchAppApi.getAppsList(request);
    }

    @PostMapping("/info")
    public Rsp<ResearchAppBO> appInfo(@RequestBody ResearchAppQueryRequest request) {
        return nbchatResearchAppApi.getApp(request.getAppId());
    }

    @PostMapping("/delete")
    public Rsp appDelete(@RequestBody ResearchAppQueryRequest request) {
        return nbchatResearchAppApi.deleteApp(request.getAppId());
    }


    /**
     * 应用聊天
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/chat")
    @ResponseBody
    public Object chatApp(@RequestBody RobotMessageRequest request) {
        request.setAppType(ChatAppType.APP_CHAT.getCode());
        if (StringUtils.isBlank(request.getSessionId())) {
            if (StringUtils.isNotBlank(request.getAppId())) {
                request.setSessionId(request.getAppId());
            } else if (StringUtils.isNotBlank(request.getMajorId())) {
                request.setSessionId(request.getMajorId());
            } else {
                request.setSessionId(IdWorker.nextAutoIdStr());
            }
        }
        if (StringUtils.isBlank(request.getAppId())) {
            return BaseRspUtils.createErrorRsp("应用ID不得为空！");
        }
        if (StringUtils.isBlank(request.getText())) {
            return BaseRspUtils.createErrorRsp("请输入提问内容！");
        }
        if (request.getAppInfo() == null) {
            Rsp<ResearchAppBO> appRsp = nbchatResearchAppApi.getApp(request.getAppId());
            if (!appRsp.isSuccess()) {
                return BaseRspUtils.createErrorRsp("应用不存在！");
            }
            ResearchAppBO appBO = appRsp.getData();
            if (!EntityValidType.NORMAL.getCode().equals(appBO.getIsValid())) {
                return BaseRspUtils.createErrorRsp("应用已删除！");
            }
            if (!EntityValidType.NORMAL.getCode().equals(appBO.getAppStatus())) {
                return BaseRspUtils.createErrorRsp("应用已停用,无法调用！");
            }
            request.setAppInfo(appBO);
            request.setRobotType(appBO.getRobotType());
        } else {
            //调试模式，传入自定义的参数
            if (!JSONObject.isValid(request.getAppInfo().getAppConfig())) {
                return BaseRspUtils.createErrorRsp("应用配置异常！");
            }
            if (StringUtils.isBlank(request.getAppInfo().getRobotType())) {
                return BaseRspUtils.createErrorRsp("机器人类型异常！");
            }
            if (StringUtils.isBlank(request.getAppInfo().getAppType())) {
                return BaseRspUtils.createErrorRsp("应用类型错误！");
            }
        }
        if (request.getChatTurn() == null) {
            request.setChatTurn(request.getAppInfo().getChatTurn());
        }
        if (StringUtils.isBlank(request.getRobotType())) {
            request.setRobotType(request.getAppInfo().getRobotType());
        }
        if (request.isStream()) {
            return RobotStreamChatUtil.streamChat(nbChatRobotProcessApi, request);
        } else {
            return nbChatRobotProcessApi.sendResearchMessage(request);
        }
    }

}
