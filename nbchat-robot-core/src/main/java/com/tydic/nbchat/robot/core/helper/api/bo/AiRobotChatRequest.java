package com.tydic.nbchat.robot.core.helper.api.bo;

import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiRobotChatRequest implements Serializable {

    //开启联网搜索
    private Boolean searchOnline;
    private String robotTag;
    private RobotMessageRequest messageRequest;
    private ChatMessageBuilder messageBuilder;


    public AiRobotChatRequest(String robotTag, RobotMessageRequest messageRequest, ChatMessageBuilder messageBuilder) {
        this.robotTag = robotTag;
        this.messageRequest = messageRequest;
        this.messageBuilder = messageBuilder;
    }

}
