package com.tydic.nbchat.robot.core.web;

import com.tydic.nbchat.robot.api.NbchatPromptApi;
import com.tydic.nbchat.robot.api.bo.robot.NbchatPromptReqBO;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/robot/prompt")
public class RobotPromptController {

    private final NbchatPromptApi nbchatPromptApi;

    @PostMapping("/query")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public RspList queryPromptList(@RequestBody NbchatPromptReqBO reqBO) {
        return nbchatPromptApi.queryPromptList(reqBO);
    }
    @PostMapping("/save")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp save(@Validated @RequestBody NbchatPromptReqBO reqBO){
        return nbchatPromptApi.save(reqBO);
    }
}
