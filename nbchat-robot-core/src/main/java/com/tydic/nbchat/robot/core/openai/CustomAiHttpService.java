package com.tydic.nbchat.robot.core.openai;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionRequest;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessage;
import com.tydic.nbchat.robot.core.openai.config.ServiceConfigProperties;
import com.tydic.nbchat.robot.core.openai.interceptor.AuthenticationInterceptor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;

import java.time.Duration;
import java.util.Collections;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 自定义调用ai服务的工具，提供2个方法，直接调用接口-json返回/流式获取返回
 */
@Slf4j
public class CustomAiHttpService {

    private static final int DEFAULT_CONNECT_TIMEOUT = 2;
    @Getter
    private ServiceConfigProperties configProperties;
    private OkHttpClient client;

    public CustomAiHttpService(final ServiceConfigProperties configProperties) {
        this.configProperties = configProperties;
        this.client = createDefaultClient();
    }


    public ChatCompletionResult createChatCompletion(ChatCompletionRequest request) {
        return chatCustomCompletion(request,ChatCompletionResult.class);
    }


    public <T> T chatCustomCompletion(ChatCompletionRequest request,Class<? extends T> objectClass) {
        request.setStream(false);
        //client.newCall()
        RequestBody body = RequestBody.create(JSONObject.toJSONBytes(request), MediaType.get("application/json"));
        Request callRequest = new Request.Builder().
                url(this.configProperties.getApiUrl()).
                post(body).build();
        try (Response response = client.newCall(callRequest).execute()) {
            if (response.isSuccessful()) {
                log.info("请求机器人-调用自定义模型成功: {}, {}, {}", configProperties.getApiUrl() ,request, response.body());
                if (response.body() != null) {
                    return JSONObject.parseObject(response.body().string(), objectClass);
                }
            }
        } catch (Exception e) {
            log.warn("请求机器人-调用自定义模型异常: {}, {}, {}", configProperties.getApiUrl() ,request, e.getMessage());
           throw new RuntimeException(e);
        }
        return null;
    }


    public OkHttpClient createDefaultClient() {
        Objects.requireNonNull(this.configProperties.getApiUrl(), "CustomAiHttpService apiUrl required");
        OkHttpClient.Builder builder = new OkHttpClient.Builder();
        final ConnectionPool pool = new ConnectionPool(64, 10, TimeUnit.SECONDS);
        builder.connectionPool(pool).
                readTimeout(Duration.ofSeconds(this.configProperties.getReadTimeout())).
                retryOnConnectionFailure(true).
                connectTimeout(DEFAULT_CONNECT_TIMEOUT,TimeUnit.SECONDS);
        if (StringUtils.isNotBlank(this.configProperties.getTokenKey())) {
            Interceptor authInterceptor = new AuthenticationInterceptor(this.configProperties.getTokenKey());
            builder.addInterceptor(authInterceptor);
        }
        return builder.build();
    }

    public static void main(String[] args) {
        ServiceConfigProperties conf = new ServiceConfigProperties();
        conf.setApiUrl("http://localhost:8080/kb/kf-llm/chat");
        CustomAiHttpService service = new CustomAiHttpService(conf);
        ChatCompletionRequest request = new ChatCompletionRequest();
        request.setStream(false);
        request.setMessages(Collections.singletonList(new ChatMessage("user","你好")));
        request.setModel("gpt-3.5-turbo");
        JSONObject result = service.chatCustomCompletion(request,JSONObject.class);
        System.out.println(result);
    }

}
