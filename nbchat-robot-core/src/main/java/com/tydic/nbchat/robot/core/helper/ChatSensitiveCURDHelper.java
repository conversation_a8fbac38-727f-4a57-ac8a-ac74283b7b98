package com.tydic.nbchat.robot.core.helper;

import com.tydic.nbchat.robot.api.bo.sensitive.SensitiveUpdateContext;
import com.tydic.nicc.common.eums.im.SensitiveOpType;
import com.tydic.nicc.mq.starter.api.KKMqProducerHelper;
import com.tydic.nicc.tools.sensitiveWords.WordType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Classname ChatSensitiveCURDHelper
 * @Description 敏感词统一操作
 * @Date 2022/2/9 7:03 下午
 * @Created by kangkang
 */
@Slf4j
@Component
public class ChatSensitiveCURDHelper {

    @Resource
    private KKMqProducerHelper kkMqProducerHelper;

    public void init(){
        SensitiveUpdateContext context = SensitiveUpdateContext.builder().opType(SensitiveOpType.INIT).build();
        log.info("ChatSensitiveCURDHelper--初始化敏感词:{}",context);
        //kkMqProducerHelper.sendMsg(NbchatTopicsConstants.NBCHAT_ROBOT_SENSITIVE_UPDATE_TOPIC,context);
    }


    /**
     * 同步新增
     * @param tenantCode
     * @param words
     * @param wordType
     */
    public void syncAdd(String tenantCode,Iterable<String> words,WordType wordType){
        update(tenantCode,words,SensitiveOpType.UPDATE_WORDS,wordType);
    }

    /**
     * 同步移除
     * @param tenantCode
     * @param words
     * @param wordType
     */
    public void syncRemove(String tenantCode,Iterable<String> words,WordType wordType){
        update(tenantCode,words,SensitiveOpType.DELETE_WORDS,wordType);
    }

    public void update(String tenantCode,Iterable<String> words,SensitiveOpType opType,WordType wordType){
        SensitiveUpdateContext context = SensitiveUpdateContext.builder().
                tenantCode(tenantCode).
                opType(opType).build();
        if (WordType.BLACK.equals(wordType)){
            context.setBlackWords(words);
        } else if (WordType.BLACK_REG.equals(wordType)){
            context.setBlackRegWords(words);
        } else {
            context.setWhiteWords(words);
        }
        log.info("ChatSensitiveCURDHelper--操作敏感词:{}",context);
        //kkMqProducerHelper.sendMsg(NbchatTopicsConstants.NBCHAT_ROBOT_SENSITIVE_UPDATE_TOPIC,context);
    }

}
