package com.tydic.nbchat.robot.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 新版知识库配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "nbchat-robot.config.kb-research")
public class KbResearchConfigProperties {
    //知识库召回接口地址
    private String researchApi;
    //显示引用文献
    private Boolean citationEnable = false;
    //引用文献前缀
    private String citationPrefix = "\n 该回答来自文档:\n";
    //未匹配到检索结果提示
    private String unknownNotice = "未能确定您提出的问题涉及的具体上下文和背景，请您提供更多信息以便我们能够更好地为您服务。谢谢！";
    private String publishApi;
    private String publishUrl;
}
