package com.tydic.nbchat.robot.core.timer;

import com.tydic.nbchat.robot.core.helper.api.AiTokenHelper;
import com.tydic.nicc.dc.boot.starter.entity.RedisLockEntity;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@EnableScheduling
@Slf4j
public class AiRobotTokenFlashTimer {

    private final List<AiTokenHelper> aiTokenHelpers;
    private final static String NBCHAT_ROBOT_TOKEN_FLASH_LOCK_KEY = "NBCHAT_ROBOT_TOKEN_FLASH_LOCK";
    private final RedisHelper redisHelper;

    public AiRobotTokenFlashTimer(List<AiTokenHelper> aiTokenHelpers, RedisHelper redisHelper) {
        this.aiTokenHelpers = aiTokenHelpers;
        this.redisHelper = redisHelper;
    }

    @Scheduled(fixedRate = 60 * 1000)
    public void run(){
        //加锁
        RedisLockEntity redisLockEntity = RedisLockEntity.builder().
                lockKey(NBCHAT_ROBOT_TOKEN_FLASH_LOCK_KEY).
                requestId(IdWorker.nextAutoIdStr()).build();
        boolean locked = redisHelper.lock(redisLockEntity);
        try {
            log.info("Token刷新任务,开始执行:{}", redisLockEntity);
            if (locked) {
                for (AiTokenHelper aiTokenHelper : aiTokenHelpers) {
                    try {
                        aiTokenHelper.freshToken();
                    } catch (Exception e) {
                        log.error("Token刷新任务,执行异常:{}", redisLockEntity, e);
                    }
                }
            }
            log.info("Token刷新任务,执行完成:{}", redisLockEntity);
        } catch (Exception e) {
            log.error("Token刷新任务,执行异常:{}", redisLockEntity,e);
        } finally {
            if(locked){
                redisHelper.unlockLua(redisLockEntity);
            }
        }
    }

    /**
     * 刷新指定类型机器人token
     * @param robotType
     * @return
     */
    public boolean run(String robotType){
        for (AiTokenHelper aiTokenHelper : aiTokenHelpers) {
            if (aiTokenHelper.robot().equals(robotType)) {
                aiTokenHelper.freshToken();
                return true;
            }
        }
        return false;
    }

}
