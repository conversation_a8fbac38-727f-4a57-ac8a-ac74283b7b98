package com.tydic.nbchat.robot.core.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-robot.config.zhipu-config")
public class NbchatZpRobotConfigProperties {
    private String appId = "zhipu";
    private String accessKey;
    private String secretKey;
    private String api = "";
    // 自定义过期时间 30天
    private Integer expiresIn = 604800;
    private Boolean tokenTimerEnable = true;

    public boolean isValid(){
        return StringUtils.isNoneBlank(accessKey);
    }
}
