package com.tydic.nbchat.robot.core.busi;

import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.bo.robot.RobotTokenQueryReqBO;
import com.tydic.nbchat.robot.mapper.NbchatRobotTokenMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatRobotToken;
import com.tydic.nbchat.robot.mapper.po.NbchatRobotTokenSelectCondition;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class RobotConfigBusiService {
    @Resource
    private NbchatRobotTokenMapper nbchatRobotTokenMapper;


    public RspList<RobotToken> getRobotTokes(RobotTokenQueryReqBO reqBO) {
        log.debug("查询token列表: {}", reqBO);
        List<RobotToken> tokens = Lists.newArrayList();
        NbchatRobotTokenSelectCondition condition = new NbchatRobotTokenSelectCondition();
        BeanUtils.copyProperties(reqBO, condition);
        //Page<NbchatRobotToken> page = PageHelper.startPage(reqBO.getPage(),reqBO.getLimit());
        List<NbchatRobotToken> tokenList = nbchatRobotTokenMapper.selectByCondition(condition);
        log.debug("查询token列表-结果: {}|{}", condition, tokenList);
        NiccCommonUtil.copyList(tokenList, tokens, RobotToken.class);
        return BaseRspUtils.createSuccessRspList(tokens);
    }

    public Rsp updateTokenKey(Integer tokenId, String tokenKey) {
        NbchatRobotToken token = new NbchatRobotToken();
        token.setTokenId(tokenId);
        token.setTokenKey(tokenKey);
        int i = nbchatRobotTokenMapper.updateTokenKey(token);
        if (i > 0) {
            return BaseRspUtils.createSuccessRsp(i);
        }
        return BaseRspUtils.createErrorRsp("更新失败");
    }

}
