package com.tydic.nbchat.robot.core.busi.token;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.bo.robot.RobotTokenConfig;
import com.tydic.nbchat.robot.core.busi.RobotConfigBusiService;
import com.tydic.nbchat.robot.core.config.NbchatDoubaoRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.api.AiTokenHelper;
import com.tydic.nbchat.robot.core.util.RobotTokenConfigParseUtil;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@Slf4j
public class DoubaoAIRobotTokenHelper implements AiTokenHelper {

    private final NbchatDoubaoRobotConfigProperties nbchatDoubaoRobotConfigProperties;
    private final RobotConfigBusiService robotConfigBusiService;
    private final RobotAiHelperFactory robotAiHelperFactory;
    private final RedisHelper redisHelper;

    public DoubaoAIRobotTokenHelper(NbchatDoubaoRobotConfigProperties nbchatDoubaoRobotConfigProperties,
                                    RobotConfigBusiService robotConfigBusiService,
                                    RobotAiHelperFactory robotAiHelperFactory,
                                    RedisHelper redisHelper) {
        this.nbchatDoubaoRobotConfigProperties = nbchatDoubaoRobotConfigProperties;
        this.robotConfigBusiService = robotConfigBusiService;
        this.robotAiHelperFactory = robotAiHelperFactory;
        this.redisHelper = redisHelper;
    }

    @Override
    public String robot() {
        return RobotType.DOUBAO_AI.getCode();
    }

    @Override
    public void freshByRobotType(String robotType) {
        if (!nbchatDoubaoRobotConfigProperties.getTokenTimerEnable()) {
            return;
        }
        List<RobotToken> tokens = robotAiHelperFactory.getStartWithTokens(robotType);
        for (RobotToken row : tokens) {
            String key = tokenCacheKey(row.getTokenId());
            boolean exp = redisHelper.hasKey(key);
            if (!exp) {
                Optional<RobotTokenConfig> config = RobotTokenConfigParseUtil.parse(row.getConfig());
                if (config.isPresent()) {
                    String access_token = generateToken(
                            config.get().getAk(),
                            config.get().getSk(),
                            config.get().getResourceIds());
                    log.info("刷新robot-token[{}]-获取新token: config = {},new_token = {}", row.getRobotType(),
                            config.get(), access_token);
                    if ( StringUtils.isBlank(access_token) && nbchatDoubaoRobotConfigProperties.isValid() ) {
                        access_token = generateToken(nbchatDoubaoRobotConfigProperties.getAccessKey(),
                                nbchatDoubaoRobotConfigProperties.getSecretKey(),
                                nbchatDoubaoRobotConfigProperties.getResourceIds());
                    }
                    if (StringUtils.isNotBlank(access_token)) {
                        Rsp rsp = robotConfigBusiService.updateTokenKey(row.getTokenId(), access_token);
                        //重载
                        boolean flash = robotAiHelperFactory.reloadHelper(row.getRobotType());
                        log.info("刷新robot-token[{}]-重载服务:{}|{}", row.getRobotType(), rsp, flash);
                        //提前10分钟失效
                        redisHelper.set(key, access_token, 25*24*3600);
                    }
                }
            }
        }
    }

    @Override
    public void freshToken() {
        freshByRobotType(robot());
    }

    /**
     * 刷新token
     * @param ak
     * @param sk
     * @param resourceIds
     * @return
     */
    public String generateToken(String ak, String sk, List<String> resourceIds) {
        String result = "";
        try {
            String endpoint = "open.volcengineapi.com";
            String path = "/";
            String service = "ark";
            String region = "cn-beijing";
            String schema = "https";
            VolcSign sign = new VolcSign(region, service, schema, endpoint, path, ak, sk);
            String action = "GetApiKey";
            String version = "2024-01-01";
            Date date = new Date();
            HashMap<String, String> queryMap = new HashMap<>();
            JSONObject object = new JSONObject();
            object.put("DurationSeconds", 30 * 24 * 3600);
            object.put("ResourceType", "endpoint");
            object.put("ResourceIds", resourceIds);
            byte[] body = object.toJSONString().getBytes();
            result = sign.doRequest("POST", queryMap, body, date, action, version);
            /**
             * {"ResponseMetadata":{"RequestId":"2024051615130416ECC75DCD5C0AE7B8B7","Action":"GetApiKey","Version":"2024-01-01","Service":"ark","Region":"cn-beijing"},
             * "Result":{"ApiKey":"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9","ExpiredTime":1718435584}}
             */
            //获取结果中的apiKey
            JSONObject resultJson = JSONObject.parseObject(result);
            JSONObject resultObj = resultJson.getJSONObject("Result");
            return resultObj.getString("ApiKey");
        } catch (Exception e) {
            log.error("刷新robot-token[{}]-异常: result = {}", robot(), result, e);
        }
        return "";
    }

    @Override
    public void removeCacheKey() {
        removeCacheKey(robot());
    }

    @Override
    public void removeCacheKey(String robotType) {
        List<RobotToken> tokens = robotAiHelperFactory.getStartWithTokens(robotType);
        for (RobotToken row : tokens) {
            String key = tokenCacheKey(row.getTokenId());
            log.info("刷新robot-token[{}]-移除tokenKey:{}", robot(), key);
            redisHelper.del(key);
        }
    }

}
