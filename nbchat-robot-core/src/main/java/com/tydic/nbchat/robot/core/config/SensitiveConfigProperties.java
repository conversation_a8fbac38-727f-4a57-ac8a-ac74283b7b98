package com.tydic.nbchat.robot.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-robot.config.sensitive-filter")
public class SensitiveConfigProperties {

    private Boolean enable =  false; // 是否开启
    private String filterType = ""; // 拦截类型
    private Boolean loadDefault = true; // 是否加载系统默认词库
    private String blacklist = "/blacklist.txt"; // 默认黑名单，不填写加载系统内置 blacklist.txt
    private String blackReglist = "/blackReglist.txt"; //默认正则匹配黑名单，不填写加载系统内置 blackReglist.txt
    private String whitelist = "/whitelist.txt"; // 默认白名单，不填写加载系统内置 whitelist.txt

}
