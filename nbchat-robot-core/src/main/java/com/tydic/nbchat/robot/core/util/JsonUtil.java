package com.tydic.nbchat.robot.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class JsonUtil {

    public static final String EMPTY = "";
    /**
     * 替换所有空格，留下一个
     */
    private static final String REPLACE_BLANK_ENTER = "\\s{2,}|\t|\r|\n|```json|```";
    private static final String REPLACE_HTML_BLANK = "&amp;nbsp|&amp|&ldquo|&iuml|&frac14|&rdquo|&ldquo|&bull";
    private static final Pattern REPLACE_P = Pattern.compile(REPLACE_BLANK_ENTER);

    private static final Pattern REPLACE_H = Pattern.compile(REPLACE_HTML_BLANK);



    public static String replaceAllBlankAndHtml(String str) {
        return replaceAllBlank(replaceHtml(str));
    }

    /**
     * 使用正则表达式删除字符串中的空格、回车、换行符、制表符
     * @param str
     * @return
     */
    public static String replaceAllBlank(String str) {
        String dest = "";
        if (StringUtils.isNotBlank(str)) {
            Matcher m = REPLACE_P.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }

    public static String replaceHtml(String str) {
        String dest = "";
        if (StringUtils.isNotBlank(str)) {
            Matcher m = REPLACE_H.matcher(str);
            dest = m.replaceAll("");
        }
        return dest;
    }


    /**
     * 去除字符串中的空格、回车、换行符、制表符
     *     \n 回车(\u000a)
     *     \t 水平制表符(\u0009)
     *     \s 空格(\u0008)
     *     \r 换行(\u000d)
     * @param source
     * @return
     */
    public static String replaceBlank(String source) {
        String ret = EMPTY;
        if (StringUtils.isNotBlank(source)) {
            ret = source.replaceAll(StringUtils.LF, EMPTY)
                    .replaceAll("\\s{2,}",  EMPTY)
                    .replaceAll("\\t", EMPTY)
                    .replaceAll(StringUtils.CR,  EMPTY);
        }
        return ret;
    }

    /**
     * 使用fastjson JSONObject格式化输出JSON字符串
     * @param source
     * @return
     */
    public static String formatJson(String source) {
        JSONObject object = JSONObject.parseObject(source);
        String pretty = JSON.toJSONString(object, SerializerFeature.PrettyFormat,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteDateUseDateFormat);
        return pretty;
    }
    public static String formatJsonOneRow(String source) {
        JSONObject object = JSONObject.parseObject(source);
        String pretty = JSON.toJSONString(object, SerializerFeature.PrettyFormat,
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteDateUseDateFormat);
        return pretty;
    }
    public static void main(String[] args) {
        String s = "```json[{\"question\": \"国内指的是哪些地区？\",\"difficulty\": \"1\",\"answers\": [\"0\"],\"items\": [\"中国内地，不含台、港、澳地区\",\"中国内地和台湾地区\",\"中国内地和香港、澳门地区\",\"中国内地和台湾、香港、澳门地区\"],\"explanation\": \"国内指中国内地,不含台、港、澳地区。\"},{\"question\": \"首月资费说明中,哪种类型的套餐会使套餐所含语音和流量内容减半？\",\"difficulty\": \"2\",\"answers\": [\"1\"],\"items\": [\"全月套餐\",\"半月套餐\",\"按量计费\",\"定向流量\"],\"explanation\": \"半月套餐指申请套餐即时生效,用户入网当月即按照用户所洗套餐月费的半价收取,主套餐所含语音、流量内容减半。\"}]```";
        System.out.println(replaceAllBlank(s));
    }
}
