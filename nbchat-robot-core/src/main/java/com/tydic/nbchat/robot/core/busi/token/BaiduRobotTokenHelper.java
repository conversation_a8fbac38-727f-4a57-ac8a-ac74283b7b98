package com.tydic.nbchat.robot.core.busi.token;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.bo.robot.RobotTokenConfig;
import com.tydic.nbchat.robot.core.busi.RobotConfigBusiService;
import com.tydic.nbchat.robot.core.config.NbchatBdRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.api.AiTokenHelper;
import com.tydic.nbchat.robot.core.util.RobotTokenConfigParseUtil;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.framework.utils.HttpClientHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class BaiduRobotTokenHelper implements AiTokenHelper {

    private final NbchatBdRobotConfigProperties nbchatBdRobotConfigProperties;
    private final RobotConfigBusiService robotConfigBusiService;
    private final RobotAiHelperFactory robotAiHelperFactory;
    private final RedisHelper redisHelper;

    public BaiduRobotTokenHelper(NbchatBdRobotConfigProperties nbchatBdRobotConfigProperties,
                                 RobotConfigBusiService robotConfigBusiService,
                                 RobotAiHelperFactory robotAiHelperFactory,
                                 RedisHelper redisHelper) {
        this.nbchatBdRobotConfigProperties = nbchatBdRobotConfigProperties;
        this.robotConfigBusiService = robotConfigBusiService;
        this.robotAiHelperFactory = robotAiHelperFactory;
        this.redisHelper = redisHelper;
    }

    @Override
    public String robot() {
        return RobotType.BD_CHAT.getCode();
    }

    @Override
    public void freshByRobotType(String robotType) {
        if (!nbchatBdRobotConfigProperties.getTokenTimerEnable()) {
            return;
        }
        List<RobotToken> tokens = robotAiHelperFactory.getStartWithTokens(robotType);
        int expTime = nbchatBdRobotConfigProperties.getExpiresIn() - 3600;
        for (RobotToken row : tokens) {
            Optional<RobotTokenConfig> config = RobotTokenConfigParseUtil.parse(row.getConfig());
            String api = "";
            if (config.isPresent()) {
                api = UriComponentsBuilder.fromUriString(nbchatBdRobotConfigProperties.getApi()).
                        build(config.get().getAk(),
                                config.get().getSk()).toString();
                expTime = config.get().getCacheTime();
            } else {
                if(!nbchatBdRobotConfigProperties.isValid()){
                    log.warn("刷新robot-token[{}]-参数异常:{}",robot(),nbchatBdRobotConfigProperties);
                    return;
                }
                api = UriComponentsBuilder.fromUriString(nbchatBdRobotConfigProperties.getApi()).
                        build(nbchatBdRobotConfigProperties.getAccessKey(),
                                nbchatBdRobotConfigProperties.getSecretKey()).toString();
            }
            String key = tokenCacheKey(row.getTokenId());
            boolean exp = redisHelper.hasKey(key);
            if (!exp) {
                //重新获取
                String res = HttpClientHelper.doGet(api, new HashMap<>());
                log.info("刷新robot-token[{}]-获取新token:{}|{}", row.getRobotType(), api, res);
                if (JSONValidator.from(res).validate()) {
                    String access_token = JSONObject.parseObject(res).getString("access_token");
                    Rsp rsp = robotConfigBusiService.updateTokenKey(row.getTokenId(), access_token);
                    //重载
                    boolean flash = robotAiHelperFactory.reloadHelper(row.getRobotType());
                    log.info("刷新robot-token[{}]-重载服务:{}|{}", row.getRobotType(), rsp, flash);
                    //提前1小时失效
                    redisHelper.set(key, access_token, expTime);
                }
            }
        }
    }

    @Override
    public void freshToken() {
        freshByRobotType(robot());
    }


    @Override
    public void removeCacheKey() {
        removeCacheKey(robot());
    }

    @Override
    public void removeCacheKey(String robotType) {
        List<RobotToken> tokens = robotAiHelperFactory.getStartWithTokens(robotType);
        for (RobotToken row : tokens) {
            String key = tokenCacheKey(row.getTokenId());
            log.info("刷新robot-token[{}]-移除tokenKey:{}", robot(), key);
            redisHelper.del(key);
        }
    }
}
