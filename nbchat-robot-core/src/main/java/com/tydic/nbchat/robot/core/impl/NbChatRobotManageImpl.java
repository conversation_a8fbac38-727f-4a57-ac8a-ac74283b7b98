package com.tydic.nbchat.robot.core.impl;

import com.tydic.nbchat.robot.api.NbChatRobotManageApi;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.bo.robot.RobotTokenQueryReqBO;
import com.tydic.nbchat.robot.api.bo.robot.SaveRobotConfigReqBO;
import com.tydic.nbchat.robot.core.busi.RobotConfigBusiService;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.api.AiTokenHelper;
import com.tydic.nbchat.robot.mapper.NbchatRobotTokenMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatRobotToken;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class NbChatRobotManageImpl implements NbChatRobotManageApi {

    @Resource
    private NbchatRobotTokenMapper nbchatRobotTokenMapper;
    final private RobotAiHelperFactory robotAiHelperFactory;
    final private List<AiTokenHelper> aiTokenHelpers;
    final private RobotConfigBusiService robotConfigBusiService;

    public NbChatRobotManageImpl(RobotAiHelperFactory robotAiHelperFactory,
                                 List<AiTokenHelper> aiTokenHelpers,
                                 RobotConfigBusiService robotConfigBusiService) {
        this.robotAiHelperFactory = robotAiHelperFactory;
        this.aiTokenHelpers = aiTokenHelpers;
        this.robotConfigBusiService = robotConfigBusiService;
    }

    @Override
    public RspList<RobotToken> getRobotTokes(RobotTokenQueryReqBO reqBO) {
        return robotConfigBusiService.getRobotTokes(reqBO);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp saveRobotConfig(SaveRobotConfigReqBO reqBO) {
        log.info("新增模型配置:{}",reqBO);
        Integer tokenId = reqBO.getTokenId();
        int i;
        if (reqBO.getTokenId() != null) {
            NbchatRobotToken token = nbchatRobotTokenMapper.selectByPrimaryKey(tokenId);
            if (token == null) {
                return BaseRspUtils.createErrorRsp("参数异常: 配置不存在");
            }
            //更新
            token.setCreateAt(new Date());
            BeanUtils.copyProperties(reqBO,token);
            i = nbchatRobotTokenMapper.updateSelective(token);
            if (i > 0) {
                robotAiHelperFactory.reloadHelper(reqBO.getRobotType());
                flashToken(token.getRobotType());
            }
        } else {
            if (StringUtils.isAnyBlank(reqBO.getApiUrl(),reqBO.getRobotType(),reqBO.getRobotModel())) {
                return BaseRspUtils.createErrorRsp("参数异常");
            }
            //插入
            NbchatRobotToken token = new NbchatRobotToken();
            BeanUtils.copyProperties(reqBO,token);
            token.setCreateAt(new Date());
            i = nbchatRobotTokenMapper.insertSelective(token);
            tokenId = token.getTokenId();
            if (i > 0) {
                robotAiHelperFactory.reloadHelper(reqBO.getRobotType());
                List<String> list = flashToken(reqBO.getRobotType());
                log.info("新增模型配置-刷新token:{}",list);
            }
        }
        return BaseRspUtils.createSuccessRsp(getRobotToken(tokenId));
    }


    private RobotToken getRobotToken(Integer tokenId) {
        NbchatRobotToken token = nbchatRobotTokenMapper.selectByPrimaryKey(tokenId);
        if (token != null) {
            RobotToken robotToken = new RobotToken();
            BeanUtils.copyProperties(token,robotToken);
            return robotToken;
        }
        return null;
    }

    private List<String> flashToken(String robotType){
        List<String> list = new ArrayList<>();
        for (AiTokenHelper aiTokenHelper : aiTokenHelpers) {
            if(robotType.startsWith(aiTokenHelper.robot())){
                aiTokenHelper.removeCacheKey(robotType);
                aiTokenHelper.freshByRobotType(robotType);
                list.add(robotType);
            }
        }
        return list;
    }

}
