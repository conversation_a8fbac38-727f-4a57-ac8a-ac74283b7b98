package com.tydic.nbchat.robot.core.busi.token;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.core.busi.RobotConfigBusiService;
import com.tydic.nbchat.robot.core.config.NbchatYuewenConfigProperties;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.api.AiTokenHelper;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.framework.utils.HttpClientHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Base64;
import java.util.HashMap;
import java.util.List;

@Service
@Slf4j
public class NbchatYuewenRobotTokenHelper implements AiTokenHelper {

    private final NbchatYuewenConfigProperties nbchatYuewenConfigProperties;
    private final RobotConfigBusiService robotConfigBusiService;
    private final RobotAiHelperFactory robotAiHelperFactory;
    private final RedisHelper redisHelper;

    public NbchatYuewenRobotTokenHelper(NbchatYuewenConfigProperties nbchatYuewenConfigProperties,
                                        RobotConfigBusiService robotConfigBusiService,
                                        RobotAiHelperFactory robotAiHelperFactory, RedisHelper redisHelper) {
        this.nbchatYuewenConfigProperties = nbchatYuewenConfigProperties;
        this.robotConfigBusiService = robotConfigBusiService;
        this.robotAiHelperFactory = robotAiHelperFactory;
        this.redisHelper = redisHelper;
    }

    @Override
    public String robot() {
        return RobotType.FASTCHAT.getCode();
    }

    @Override
    public void freshToken() {
        if (!nbchatYuewenConfigProperties.getTokenTimerEnable()) {
            return;
        }
        String clientId = nbchatYuewenConfigProperties.getClientId();
        String clientSecret = nbchatYuewenConfigProperties.getClientSecret();
        String robotTypes = nbchatYuewenConfigProperties.getRobotTypes();
        List<RobotToken> tokens = robotAiHelperFactory.getHelper(robot()).getTokens();
        String api = UriComponentsBuilder.fromUriString(nbchatYuewenConfigProperties.getTokenApi()).
                build(nbchatYuewenConfigProperties.getClientId(),
                        nbchatYuewenConfigProperties.getClientSecret()).toString();
        for (RobotToken row : tokens) {
            if (!robotTypes.contains(row.getRobotType())) {
                continue;
            }
            String key = tokenCacheKey(row.getTokenId());
            boolean exp = redisHelper.hasKey(key);
            if (!exp) {
                //重新获取
                String basic = Base64.getEncoder().encodeToString((clientId + clientSecret).getBytes());
                HashMap<String, String> headers = new HashMap<>();
                headers.put("Content-Type", ContentType.APPLICATION_JSON.getMimeType());
                headers.put("Authorization", basic);
                String res = HttpClientHelper.doPost(api, headers, new Object());
                log.info("刷新robot-token[{}]-获取新token:{}|{}", robot(), api, res);
                if (JSONValidator.from(res).validate()) {
                    JSONObject respData = JSONObject.parseObject(res);
                    if (!respData.getString("code").equals("0")) {
                        log.error("刷新robot-token[{}]-获取新token失败:{}|{}", robot(), api, res);
                        continue;
                    }
                    String access_token = respData.getJSONObject("data").getString("access_token");
                    Rsp rsp = robotConfigBusiService.updateTokenKey(row.getTokenId(), access_token);
                    //重载
                    boolean flash = robotAiHelperFactory.reloadHelper(robot());
                    log.info("刷新robot-token[{}]-重载服务:{}|{}", robot(), rsp, flash);
                    //设置有效期
                    Long expires_in = JSONObject.parseObject(res).getJSONObject("data").getLong("expires_in");
                    redisHelper.set(key, access_token, expires_in - 600);
                }
            }
        }

    }

    public static void main(String[] args) {
        String clientId = "ai-d-human";
        String clientSecret = "dh2GbqTm!bkD";
        String api = "http://10.242.39.153:8198/maas-admin/uaa/oauth2/token?grant_type=client_credentials";
        String basic = Base64.getEncoder().encodeToString((clientId + clientSecret).getBytes());
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", ContentType.APPLICATION_JSON.getMimeType());
        headers.put("Authorization", basic);
        String res = HttpClientHelper.doPost(api, headers, new Object());
        JSONObject respData = JSONObject.parseObject(res);
        System.out.println(res);
    }
}
