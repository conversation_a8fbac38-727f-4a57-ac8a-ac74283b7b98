package com.tydic.nbchat.robot.core.web;

import com.tydic.nbchat.robot.core.timer.AiRobotTokenFlashTimer;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/robot/token")
public class RobotTokenFlashController {

    @Autowired
    private AiRobotTokenFlashTimer aiRobotTokenFlashTimer;

    @GetMapping("/flash")
    public Rsp flash() {
        aiRobotTokenFlashTimer.run();
        log.info("flash token success");
        return BaseRspUtils.createSuccessRsp("success");
    }

    @GetMapping("/flash/{robotType}")
    public Rsp flashRobot(@PathVariable("robotType") String robotType) {
        boolean flag = aiRobotTokenFlashTimer.run(robotType);
        log.info("flash token success:{}",robotType);
        return BaseRspUtils.createSuccessRsp(flag,robotType);
    }

}
