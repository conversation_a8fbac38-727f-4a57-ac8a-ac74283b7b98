package com.tydic.nbchat.robot.core.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.robot.api.NbchatResearchAppMsgApi;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMsgBO;
import com.tydic.nbchat.robot.api.bo.research.FileResearchMsgQueryRequest;
import com.tydic.nbchat.robot.mapper.NbchatResearchMsgMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchMsg;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchMsgSelectCondition;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class NbchatResearchAppMsgServiceImpl implements NbchatResearchAppMsgApi {

    @Resource
    private NbchatResearchMsgMapper nbchatResearchMsgMapper;

    @MethodParamVerifyEnable
    @Override
    public RspList getResearchMessages(FileResearchMsgQueryRequest request) {
        log.info("文档检索-查询会话消息:{}", JSON.toJSONString(request));
        if (StringUtils.isAllBlank(request.getRequestId(),request.getAppId(),request.getSessionId())) {
            return BaseRspUtils.createErrorRspList("参数异常!");
        }
        List<FileResearchMsgBO> msgBOList = Lists.newArrayList();
        NbchatResearchMsgSelectCondition condition = new NbchatResearchMsgSelectCondition();
        BeanUtils.copyProperties(request, condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        Page<NbchatResearchMsg> page = PageHelper.startPage(request.getPage(), request.getLimit());
        nbchatResearchMsgMapper.selectByCondition(condition);
        NiccCommonUtil.copyList(page.getResult(), msgBOList, FileResearchMsgBO.class);
        return BaseRspUtils.createSuccessRspList(msgBOList);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp cleanMessage(FileResearchMsgQueryRequest request) {
        log.info("文档检索-清空会话:{}", JSON.toJSONString(request));
        if (StringUtils.isBlank(request.getSessionId())) {
            return BaseRspUtils.createErrorRsp("参数异常!");
        }
        int i = nbchatResearchMsgMapper.deleteBySessionId(request.getUserId(), request.getSessionId());
        return BaseRspUtils.createSuccessRsp(i);
    }

    @Override
    public Rsp deleteMessage(FileResearchMsgQueryRequest request) {
        log.info("文档检索-删除消息:{}", JSON.toJSONString(request));
        NbchatResearchMsg update = new NbchatResearchMsg();
        update.setRequestId(request.getRequestId());
        update.setUserId(request.getUserId());
        update.setIsValid(EntityValidType.DELETE.getCode());
        int i = nbchatResearchMsgMapper.updateSelective(update);
        if (i > 0) {
            return BaseRspUtils.createSuccessRsp(i, "操作成功！");
        }
        return BaseRspUtils.createErrorRsp("操作失败:消息不存在或已删除");
    }
}
