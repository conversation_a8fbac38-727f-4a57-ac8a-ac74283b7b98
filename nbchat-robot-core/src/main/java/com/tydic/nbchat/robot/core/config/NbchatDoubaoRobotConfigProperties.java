package com.tydic.nbchat.robot.core.config;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-robot.config.doubao-config")
public class NbchatDoubaoRobotConfigProperties {
    private String accessKey;
    private String secretKey;
    private Boolean tokenTimerEnable = true;
    private List<String> resourceIds;
    public boolean isValid(){
        return StringUtils.isNoneBlank(accessKey, secretKey) && resourceIds != null && !resourceIds.isEmpty();
    }
}
