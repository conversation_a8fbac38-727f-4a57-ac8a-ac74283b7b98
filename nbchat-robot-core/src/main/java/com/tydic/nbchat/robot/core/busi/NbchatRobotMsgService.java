package com.tydic.nbchat.robot.core.busi;

import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.bo.RobotRedisKeyConstant;
import com.tydic.nbchat.robot.api.bo.eums.ChatAppType;
import com.tydic.nbchat.robot.mapper.NbchatResearchMsgMapper;
import com.tydic.nbchat.robot.mapper.NbchatRobotSessionMsgMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatResearchMsg;
import com.tydic.nbchat.robot.mapper.po.NbchatSessionMsg;
import com.tydic.nicc.common.eums.user.UserTypeField;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class NbchatRobotMsgService {

    @Resource
    private NbchatRobotSessionMsgMapper nbchatRobotSessionMsgMapper;
    @Resource
    private NbchatResearchMsgMapper nbchatResearchMsgMapper;

    private final RedisHelper redisHelper;

    public NbchatRobotMsgService(RedisHelper redisHelper) {
        this.redisHelper = redisHelper;
    }

    public void saveRequestMessage(RobotMessageRequest request) {
        try {
            if (request.isAppTest()) {
                return;
            }
            log.info("机器人消息处理[{}]-保存请求消息:{}", request.getRequestId(), request);
            if (StringUtils.isNotBlank(request.getReload())) {
                log.info("机器人消息处理[{}]-保存请求消息-刷新消息", request.getRequestId());
                return;
            }
            if (ChatAppType.DIC_CHAT.getCode().equals(request.getAppType())) {
                NbchatSessionMsg msg = new NbchatSessionMsg();
                msg.setUserId(request.getUserId());
                msg.setTenantCode(request.getTenantCode());
                msg.setSessionId(request.getSessionId());
                msg.setRequestId(request.getRequestId());
                msg.setDateTime(new Date());
                msg.setText(request.getText());
                msg.setRequestOptions(request.getRequestOptions());
                msg.setUserType(UserTypeField.USER.getCode());
                msg.setRobotType(request.getRobotType());
                nbchatRobotSessionMsgMapper.insertSelective(msg);
            } else {
                NbchatResearchMsg msg = buildResearchMsg(request);
                msg.setRequestId(request.getRequestId());
                msg.setText(request.getText());
                msg.setRequestOptions(request.getRequestOptions());
                msg.setUserType(UserTypeField.USER.getCode());
                msg.setRobotType(request.getRobotType());
                nbchatResearchMsgMapper.insertSelective(msg);
            }
        } catch (Exception e) {
            log.error("机器人消息处理[{}]-保存请求消息-异常:", request.getRequestId(), e);
        }
    }

    private String getParts(String requestId) {
        //缓存结果-2分钟
        try {
            String key = RobotRedisKeyConstant.getResearchPartResultKey(requestId);
            return (String) redisHelper.get(key);
        } catch (Exception e) {
            log.error("保存请求消息-解析段落结果异常:", e);
        }
        return "";
    }


    public void saveResponseMessage(RobotMessageRequest request, RobotMsgContext context) {
        try {
            if (request.isAppTest()) {
                return;
            }
            log.info("机器人消息处理[{}]-保存响应消息:{}|{}", request.getRequestId(), request, context);
            if (ChatAppType.DIC_CHAT.getCode().equals(request.getAppType())) {
                NbchatSessionMsg msg = buildSessionMsg(request, context);
                if (StringUtils.isNotBlank(request.getReload())) {
                    log.info("机器人消息处理[{}]-保存响应消息-刷新消息", request.getRequestId());
                    NbchatSessionMsg loadMsg = nbchatRobotSessionMsgMapper.selectByRequestId(request.getRequestId());
                    if (loadMsg != null) {
                        nbchatRobotSessionMsgMapper.updateByRequestId(msg);
                    } else {
                        nbchatRobotSessionMsgMapper.insertSelective(msg);
                    }
                } else {
                    //新回复
                    nbchatRobotSessionMsgMapper.insertSelective(msg);
                }
            } else {
                NbchatResearchMsg msg = buildResearchMsg(request, context);
                //保存文档段落检索结果
                msg.setDocParts(getParts(msg.getPromptRequestId()));
                if (StringUtils.isNotBlank(request.getReload())) {
                    log.info("机器人消息处理[{}]-保存响应消息-刷新消息", request.getRequestId());
                    NbchatResearchMsg loadMsg = nbchatResearchMsgMapper.selectByRequestId(request.getRequestId());
                    if (loadMsg != null) {
                        nbchatResearchMsgMapper.updateByRequestId(msg);
                    } else {
                        nbchatResearchMsgMapper.insertSelective(msg);
                    }
                } else {
                    //新回复
                    nbchatResearchMsgMapper.insertSelective(msg);
                }
            }
        } catch (Exception e) {
            log.error("机器人消息处理[{}]-保存响应消息-异常:", request.getRequestId(), e);
        }
    }


    private NbchatSessionMsg buildSessionMsg(RobotMessageRequest request, RobotMsgContext context) {
        NbchatSessionMsg msg = new NbchatSessionMsg();
        msg.setRequestId(context.getId());
        msg.setTenantCode(request.getTenantCode());
        msg.setUserId(request.getUserId());
        msg.setSessionId(request.getSessionId());
        msg.setDateTime(new Date());
        msg.setText(context.getText());
        msg.setPrompt(request.getText());
        msg.setReasoning(context.getReasoning());
        msg.setPromptRequestId(request.getRequestId());
        msg.setRequestOptions(request.getRequestOptions());
        msg.setConversationOptions(request.getConversationOptions());
        msg.setUserType(UserTypeField.SYSTEM.getCode());
        msg.setRobotType(request.getRobotType());
        return msg;
    }

    private NbchatResearchMsg buildResearchMsg(RobotMessageRequest request, RobotMsgContext context) {
        NbchatResearchMsg msg = buildResearchMsg(request);
        msg.setRequestId(context.getId());
        msg.setText(context.getText());
        msg.setPrompt(request.getText());
        msg.setPromptRequestId(request.getRequestId());
        msg.setRequestOptions(request.getRequestOptions());
        msg.setConversationOptions(request.getConversationOptions());
        msg.setUserType(UserTypeField.SYSTEM.getCode());
        msg.setGuestId(request.getGuestId());
        return msg;
    }


    private NbchatResearchMsg buildResearchMsg(RobotMessageRequest request) {
        NbchatResearchMsg msg = new NbchatResearchMsg();
        msg.setTenantCode(request.getTenantCode());
        msg.setMajorId(request.getMajorId());
        msg.setUserId(request.getUserId());
        msg.setAppId(request.getAppId());
        msg.setGuestId(request.getGuestId());
        if (StringUtils.isBlank(request.getSessionId())) {
            if (StringUtils.isNotBlank(request.getAppId())) {
                msg.setSessionId(request.getAppId());
            } else if (StringUtils.isNotBlank(request.getMajorId())) {
                msg.setSessionId(request.getMajorId());
            } else {
                msg.setSessionId("");
            }
        } else {
            msg.setSessionId(request.getSessionId());
        }
        msg.setDateTime(new Date());
        msg.setRobotType(request.getRobotType());
        return msg;
    }

}
