package com.tydic.nbchat.robot.core.impl;

import com.tydic.nbchat.robot.api.NbChatRobotProcessApi;
import com.tydic.nbchat.robot.api.RobotProcessCallback;
import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.core.busi.ChatMessageBuilderService;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.RobotAiHelperFactory;
import com.tydic.nbchat.robot.core.helper.SystemPresetPromptHelper;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.listener.NbchatMsgListener;
import com.tydic.nbchat.robot.core.util.NbchatRobotMsgBuilder;
import com.tydic.nbchat.robot.core.util.exception.SensitiveIncludeException;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class NbChatRobotProcessServiceImpl implements NbChatRobotProcessApi {

    final private ChatMessageBuilderService chatMessageBuilderService;
    final private RobotAiHelperFactory robotAiHelperFactory;
    final private NbchatMsgListener nbchatMsgListener;
    final private SystemPresetPromptHelper systemPresetPromptHelper;
    final private NbchatRobotConfigProperties nbchatRobotConfigProperties;


    public NbChatRobotProcessServiceImpl(ChatMessageBuilderService chatMessageBuilderService,
                                         RobotAiHelperFactory robotAiHelperFactory,
                                         NbchatRobotConfigProperties nbchatRobotConfigProperties,
                                         NbchatMsgListener nbchatMsgListener,
                                         SystemPresetPromptHelper systemPresetPromptHelper
    ) {
        this.chatMessageBuilderService = chatMessageBuilderService;
        this.robotAiHelperFactory = robotAiHelperFactory;
        this.nbchatRobotConfigProperties = nbchatRobotConfigProperties;
        this.nbchatMsgListener = nbchatMsgListener;
        this.systemPresetPromptHelper = systemPresetPromptHelper;
    }


    @Override
    public void sendMessage(RobotMessageRequest request, RobotProcessCallback callback) {
        log.info("机器人消息处理:{}", request);
        if (StringUtils.isAllBlank(request.getRequestId(), request.getReload())) {
            request.setRequestId(NbchatRobotMsgBuilder.createRequestId());
        }
        try {
            nbchatMsgListener.onRequest(request);
        } catch (SensitiveIncludeException e) {
            //敏感词警告
            log.warn("机器人消息处理-请求异常[触发敏感词]:{}", e.getMessage());
            RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildSensitiveWaring(request, nbchatRobotConfigProperties);
            callback.onMessage(warnContext);
            return;
        }

        //兼容处理生成试题专业版
        if (StringUtils.isNotEmpty(request.getPresetId()) && CollectionUtils.isNotEmpty(request.getPresetPrompts())) {
            request.setConversationOptions(request.getText());
            String text = systemPresetPromptHelper.buildPromptMessage(request.getPresetId(), request.getPresetPrompts());
            //重写内容
            request.setText(text);
        }

        ChatMessageBuilder messageBuilder = chatMessageBuilderService.buildMessage(request);

        if (messageBuilder.isUnknownResearch()) {
            //未识别
            RobotMsgContext unknow = NbchatRobotMsgBuilder.buildResearchUnknow(request, nbchatRobotConfigProperties);
            callback.onMessage(unknow);
            return;
        }

        if (!messageBuilder.isSuccess()) {
            RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildContext(request.getRequestId(), "知识检索异常，请稍后再试");
            callback.onMessage(warnContext);
            return;
        }

        AiRobotChatRequest aiRobotChatRequest = AiRobotChatRequest.builder().
                robotTag(request.getRobotTag()).
                searchOnline(request.getSearchOnline()).
                messageRequest(request).
                messageBuilder(messageBuilder).build();
        try {
            robotAiHelperFactory.streamChat(aiRobotChatRequest).doOnError(e -> {
                //处理异常事件
                log.error("机器人消息处理-流式返回异常:{}", request, e);
                //RobotMsgContext errContext = nbchatMsgListener.onError(request, e);
                //callback.onMessage(errContext);
            }).blockingForEach(obj -> {
                //消息转换
                robotAiHelperFactory.convertChuckMessage(aiRobotChatRequest, obj);
                RobotMsgContext context = nbchatMsgListener.onResponse(request, obj);
                Optional.ofNullable(context).ifPresent(callback::onMessage);
            });
        }  catch (NullPointerException e) {
            log.error("机器人消息处理-接口调用异常:{}", request, e);
            RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildContext(request.getRequestId(),
                    "输入长度超过该模型限制，请减少输入内容或切换机器人在试试吧");
            callback.onMessage(warnContext);
        } catch (Exception e) {
            log.error("机器人消息处理-接口调用异常:{}", request, e);
            //处理异常事件
            RobotMsgContext errContext = nbchatMsgListener.onError(request, e);
            callback.onMessage(errContext);
        }
    }


    @Override
    public Rsp sendResearchMessage(RobotMessageRequest request) {

        log.info("文档检索消息处理:{}", request);
        if (StringUtils.isAllBlank(request.getRequestId(), request.getReload())) {
            request.setRequestId(NbchatRobotMsgBuilder.createRequestId());
        }
        try {
            nbchatMsgListener.onRequest(request);
        } catch (SensitiveIncludeException e) {
            //敏感词警告
            log.warn("文档检索消息处理-异常:{}", e.getMessage());
            RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildSensitiveWaring(request, nbchatRobotConfigProperties);
            return BaseRspUtils.createSuccessRsp(warnContext);
        }

        //查询文档检索结果 - 携带给模型
        ChatMessageBuilder messageBuilder = chatMessageBuilderService.buildMessage(request);
        if (messageBuilder.isUnknownResearch()) {
            //未识别问题
            RobotMsgContext unknowContext = NbchatRobotMsgBuilder.buildResearchUnknow(request, nbchatRobotConfigProperties);
            return BaseRspUtils.createSuccessRsp(unknowContext);
        }
        if (!messageBuilder.isSuccess()) {
            RobotMsgContext warnContext = NbchatRobotMsgBuilder.buildContext(request.getRequestId(), "知识检索异常，请稍后再试");
            return BaseRspUtils.createSuccessRsp(warnContext);
        }
        AiRobotChatRequest aiRobotChatRequest = AiRobotChatRequest.builder().
                searchOnline(request.getSearchOnline()).robotTag(request.getRobotTag()).
                messageRequest(request).messageBuilder(messageBuilder).build();
        try {
            ChatCompletionResult result = robotAiHelperFactory.chat(aiRobotChatRequest);
            RobotMsgContext robotMsgContext = nbchatMsgListener.onResponse(request, result);
            return BaseRspUtils.createSuccessRsp(robotMsgContext);
        } catch (Exception e) {
            String err = e.getMessage();
            if (StringUtils.isNotBlank(err) && err.contains(NbchatRobotMsgBuilder.BILLING_CHECK_ERR)) {
                log.error("文档检索消息处理-异常[账户余额不足]:{}|{}", request, err);
                //helper.removeService(config.getTokenKey());
            } else {
                log.error("文档检索消息处理-异常:{}|{}", request, err);
            }
            nbchatMsgListener.onError(request, e);
        }
        return BaseRspUtils.createErrorRsp("消息处理异常!");
    }


}
