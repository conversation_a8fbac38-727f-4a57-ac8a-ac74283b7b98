package com.tydic.nbchat.robot.core.helper;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.AiAppRequest;
import com.tydic.nbchat.robot.api.bo.RequestOptions;
import com.tydic.nbchat.robot.api.bo.msg.ChatMessageBuilder;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessage;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessageRole;
import com.tydic.nbchat.robot.core.helper.api.AppHelper;
import com.tydic.nbchat.robot.mapper.AppConfigMapper;
import com.tydic.nbchat.robot.mapper.NbchatPresetPromptMapper;
import com.tydic.nbchat.robot.mapper.NbchatRobotSessionMsgMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatSessionMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class KPIAppHelper implements AppHelper {

    @Resource
    AppConfigMapper appConfigMapper;
    @Resource
    NbchatRobotSessionMsgMapper nbchatRobotSessionMsgMapper;
    @Resource
    SystemPresetPromptHelper systemPresetPromptHelper;



    @Override
    public String type() {
        return "KPI";
    }

    @Override
    public ChatMessageBuilder buildMessage(RobotPromptMessageRequest request) {
        return this.parseChatMessage(request);
    }

    private ChatMessageBuilder parseChatMessage(RobotPromptMessageRequest request){
        List<ChatMessage> messages = new ArrayList<>();
        if (StringUtils.isNotBlank(request.getRequestOptions())) {
            try {
                int assistantLimit = 999;
                RequestOptions requestOptions = JSONObject.parseObject(request.getRequestOptions(), RequestOptions.class);
                List<NbchatSessionMsg> sessionMsgs = nbchatRobotSessionMsgMapper.selectMsgs(
                        request.getSessionId(),
                        requestOptions.getParentMessageId(),
                        assistantLimit);
                if (CollectionUtils.isNotEmpty(sessionMsgs)) {
                    sessionMsgs = sessionMsgs.stream().sorted(Comparator.comparing(NbchatSessionMsg::getDateTime)).collect(Collectors.toList());
                    for (NbchatSessionMsg sessionMsg : sessionMsgs) {
                        ChatMessage msg;
                        if (sessionMsg.getUserType().equals("1")) { //1用户 2机器人
                            msg = new ChatMessage(ChatMessageRole.USER.value(), sessionMsg.getText());
                        } else {
                            msg = new ChatMessage(ChatMessageRole.ASSISTANT.value(), sessionMsg.getText());
                        }
                        messages.add(msg);
                    }
                }
            } catch (Exception e) {
                log.error("机器人消息处理-加载上次提问异常:{}", request,e);
            }
        }
        ChatMessage userMessage = new ChatMessage(ChatMessageRole.USER.value(), request.getText());
        messages.add(userMessage);

        //构建系统消息，设定ai-app系统角色
        String systemMsg = this.buildSystemMsg(request);
        if (StringUtils.isNotEmpty(systemMsg)) {
            ChatMessage systemMessage = new ChatMessage(ChatMessageRole.SYSTEM.value(), systemMsg);
            //系统角色插到第一个
            messages.add(0, systemMessage);
        }
        return ChatMessageBuilder.builder().success(true).messages(messages).build();
    }

    /**
     * {
     *     "examName": "考试名称",
     *     "examScene": "场景描述",
     *     "coreIssues": "我是核心问题",
     *     "openingRemarks": "我是开场白",
     *     "referenceAnswer": "核心问题答案",
     *     "examQuestionsNum": 5,
     *     "examScoringDimensionsStr": "沟通表达（满分10分）\n人格魅力（满分10分）\n人气值（满分10分）\n"
     * }
     */

    public String buildSystemMsg(RobotPromptMessageRequest request) {
        String appId = request.getAppId();
        String config = appConfigMapper.queryById(appId);
        if (StringUtils.isEmpty(config)) {
            return config;
        }
        List<String> args = new ArrayList<>();
        if (JSONObject.isValid(config)) {
            JSONObject jsonObject = JSONObject.parseObject(config);
            args.add(jsonObject.getString("examScene"));//场景描述
            args.add(jsonObject.getString("examQuestionsNum"));//题目数量
            args.add(jsonObject.getString("examScoringDimensionsStr"));//评分维度
            args.add(jsonObject.getString("coreIssues"));//核心问题
            args.add(jsonObject.getString("referenceAnswer"));//答案参考
            args.add(jsonObject.getString("examScoringResultStr"));//输出格式
        }
        return systemPresetPromptHelper.buildPromptMessage(request.getPresetId(), args);
    }

}
