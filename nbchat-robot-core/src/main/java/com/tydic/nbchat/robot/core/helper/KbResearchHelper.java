package com.tydic.nbchat.robot.core.helper;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.research.FileResearchSearchPartResult;
import com.tydic.nbchat.robot.api.bo.research.FileResearchSearchRequest;
import com.tydic.nbchat.robot.core.config.KbResearchConfigProperties;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.HttpClientHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Slf4j
@Component
public class KbResearchHelper {


    private final KbResearchConfigProperties researchKbConfigProperties;

    public KbResearchHelper(KbResearchConfigProperties researchKbConfigProperties) {
        this.researchKbConfigProperties = researchKbConfigProperties;
    }

    public RspList<FileResearchSearchPartResult> searchResult(FileResearchSearchRequest searchRequest) {
        /***
         * {
         *     "userId": "1",
         *     "tenantCode": "00000000",
         *     "majorId": "1764936563248205824",
         *     "text":"该员工对产品改进做了哪些优化？",
         *     "majorConfig":{
         *         "searchType":"both",
         *         "rerank": true
         *     }
         * }
         */
        try {
            List<FileResearchSearchPartResult> partResults = Lists.newArrayList();
            log.info("新版知识库检索-请求:{}", searchRequest);
            String postResult = HttpClientHelper.doPost(researchKbConfigProperties.getResearchApi(), new HashMap<>(),
                    searchRequest, 30000);
            if (StringUtils.isNotBlank(postResult)) {
                JSONObject obj = JSONObject.parseObject(postResult);
                if (obj.containsKey("rows")) {
                    partResults = obj.getJSONArray("rows").toJavaList(FileResearchSearchPartResult.class);
                }
            }
            log.info("新版知识库检索-结果:{}", postResult);
            return BaseRspUtils.createSuccessRspList(partResults, partResults.size());
        } catch (Exception e) {
            log.error("新版知识库检索-异常:{}", e.getMessage(), e);
            return BaseRspUtils.createErrorRspList("检索失败!");
        }
    }
}
