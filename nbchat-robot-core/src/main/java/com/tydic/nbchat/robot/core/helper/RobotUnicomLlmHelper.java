package com.tydic.nbchat.robot.core.helper;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.openai.Usage;
import com.tydic.nbchat.robot.api.openai.completion.chat.*;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.api.AiRobotHelper;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.openai.CustomAiHttpService;
import com.tydic.nbchat.robot.core.openai.config.ServiceConfigProperties;
import com.tydic.nbchat.robot.core.openai.service.OpenAiService;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.*;

@Slf4j
public class RobotUnicomLlmHelper implements AiRobotHelper {
    private final NbchatRobotConfigProperties configProperties;
    private List<RobotToken> tokens;
    private String robot;

    private final Map<String, CustomAiHttpService> serviceTagMap = new HashMap<>();
    // openai 接口调用
    private final Map<Integer, CustomAiHttpService> serviceMap = new HashMap<>();


    public RobotUnicomLlmHelper(NbchatRobotConfigProperties configProperties) {
        this(configProperties, new ArrayList<>());
        this.robot = RobotType.UNICOM.getCode();
    }

    public RobotUnicomLlmHelper(NbchatRobotConfigProperties configProperties, List<RobotToken> tokens) {
        this.configProperties = configProperties;
        this.tokens = tokens;
        if (tokens != null && !tokens.isEmpty()) {
            this.robot = tokens.get(0).getRobotType();
            reloadService(tokens);
        }
    }

    @Override
    public String robot() {
        return robot;
    }

    @Override
    public List<RobotToken> getTokens() {
        return tokens;
    }

    @Override
    public void reloadService(List<RobotToken> tokens) {
        this.tokens = tokens;
        int openAiIndex = 0;
        for (RobotToken token : tokens) {
            if (token.getRobotType().startsWith(robot())) {
                serviceMap.put(openAiIndex++, new CustomAiHttpService(buildServiceConfig(token, configProperties.getRobotReadTimeout())));
                if (StringUtils.isNotBlank(token.getTag())) {
                    serviceTagMap.put(token.getTag(), new CustomAiHttpService(buildServiceConfig(token, configProperties.getRobotReadTimeout())));
                }
            }
        }
        log.info("初始化openAiServie[{}]-[tag]-完成:{}", robot(), serviceTagMap);
        log.info("初始化openAiServie[{}]--完成:{}", robot(), serviceMap);
    }


    @Override
    public Flowable<ChatCompletionChunk> streamChat(AiRobotChatRequest request) {
        ChatCompletionResult result = chat(request);
        //模拟流式返回
        ChatCompletionChunk chunk = new ChatCompletionChunk();
        BeanUtils.copyProperties(result, chunk);
        return Flowable.just(chunk);
    }

    /**
     * 客服大模型接口（注意：仅用于测试，不可用于线上真实环境！！）
     * 1、 URL：
     * http://10.188.48.146:8088/naturalLanguageProcessing/kf-llm/chat (Postman测试需要开VPN)
     * 2、调用示例：
     * {
     * "messages":[
     * {"role":"user","content":"你好"},
     * {"role":"assistant","content":"您好，请问有什么可以帮助您"},
     * {"role":"user","content":"给我写首诗"}
     * ]
     * }
     * <p>
     * 3、输出结构
     * {
     * "CODE": "0000",
     * "MSG": "成功",
     * "result": " 好的，请问您想要什么主题的诗歌呢？\n"
     * }
     *
     * @param request
     * @return
     */
    @Override
    public ChatCompletionResult chat(AiRobotChatRequest request) {
        ChatCompletionRequest completionRequest = buildChatRequest(request);
        JSONObject result = this.serviceMap.get(0).chatCustomCompletion(completionRequest, JSONObject.class);
        if (result != null && result.containsKey("result")) {
            return buildResult(result.getString("result"));
        } else {
            return buildResult(null);
        }
    }


    private ChatCompletionResult buildResult(String content) {
        if (StringUtils.isBlank(content)) {
            content = this.configProperties.getRobotErrorNotice();
        }
        //将消息转换为 ChatCompletionResult
        ChatCompletionResult chatCompletionResult = new ChatCompletionResult();
        ChatMessage message = new ChatMessage(ChatMessageRole.ASSISTANT.value(), content);
        List<ChatCompletionChoice> choices = new ArrayList<>();
        choices.add(new ChatCompletionChoice(0, message, "stop"));
        chatCompletionResult.setId(IdWorker.nextAutoIdStr());
        chatCompletionResult.setCreated(System.currentTimeMillis());
        chatCompletionResult.setUsage(new Usage(0, content.length(), content.length()));
        chatCompletionResult.setModel(robot());
        chatCompletionResult.setChoices(choices);
        return chatCompletionResult;
    }

    @Override
    public ServiceConfigProperties getServiceConfig(AiRobotChatRequest request) {
        return this.serviceMap.get(0).getConfigProperties();
    }

    @Override
    public Map<Integer, OpenAiService> getServiceMap() {
        return new HashMap<>();
    }

    @Override
    public Map<String, OpenAiService> getServiceTagMap() {
        return new HashMap<>();
    }

    @Override
    public void removeService(String token) {

    }

    @Override
    public OpenAiService getDefaultService() {
        return null;
    }
}
