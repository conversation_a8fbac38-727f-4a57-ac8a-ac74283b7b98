package com.tydic.nbchat.robot.core.openai.exception;


public class OpenAiHttpException extends RuntimeException {
    private String errorBody;
    private int statusCode;

    public OpenAiHttpException(Exception parent,String errorBody,int statusCode) {
        super(errorBody, parent);
        this.errorBody = errorBody;
        this.statusCode = statusCode;
    }

    public String getErrorBody() {
        return errorBody;
    }

    public void setErrorBody(String errorBody) {
        this.errorBody = errorBody;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }
}
