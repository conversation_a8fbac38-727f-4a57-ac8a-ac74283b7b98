package com.tydic.nbchat.robot.core.web;

import com.tydic.nbchat.robot.api.NbchatResearchShareApi;
import com.tydic.nbchat.robot.api.bo.share.RobotShareQueryReqBO;
import com.tydic.nbchat.robot.api.bo.share.RobotShareSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/robot/research")
public class RobotResearchShareController {

    private final NbchatResearchShareApi nbchatResearchShareApi;

    public RobotResearchShareController(NbchatResearchShareApi nbchatResearchShareApi) {
        this.nbchatResearchShareApi = nbchatResearchShareApi;
    }

    @PostMapping(value = "/share/create")
    public Rsp createShare(@RequestBody RobotShareSaveReqBO request) {
        return nbchatResearchShareApi.createShare(request);
    }

    @PostMapping(value = "/share/get")
    public Rsp getShare(@RequestBody RobotShareQueryReqBO request) {
        return nbchatResearchShareApi.getShare(request);
    }

    @PostMapping(value = "/share/check")
    public Rsp checkShare(@RequestBody RobotShareQueryReqBO request) {
        return nbchatResearchShareApi.checkShare(request);
    }

}
