package com.tydic.nbchat.robot.core.openai.config;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

public class ServiceConfigProperties implements Serializable {
    private String apiUrl = "https://api.openai.com/";
    private String tokenId;
    private String tokenName;
    private String tokenKey;
    private String tag;
    private String robotType = "chatgpt";
    private String robotModel = "gpt-3.5-turbo";
    private Integer maxTokens = 3800;
    private Integer requestMaxTokens = 2000;
    private Integer maxChars = 2000;
    private String envType;
    //连接超时时间
    private Integer readTimeout = 600;
    //额外配置信息
    private JSONObject config = new JSONObject();

    public JSONObject getConfig() {
        return config;
    }

    public void setConfig(String config) {
        if (StringUtils.isNotBlank(config)) {
            try {
                this.config = JSONObject.parseObject(config);
            } catch (Exception e) {
                System.out.println("set config error: "+e.getMessage());
            }
        }
    }

    public String getConfigValue(String key) {
        return config.getString(key);
    }

    public boolean checkConfigValue(String key,String value){
        try {
            if(config.containsKey(key)){
                return config.getString(key).equals(value);
            }
        } catch (Exception e) {
            System.out.println("check config error: "+e.getMessage());
        }
        return false;
    }

    public Integer getReadTimeout() {
        return readTimeout;
    }

    public void setReadTimeout(Integer readTimeout) {
        this.readTimeout = readTimeout;
    }

    public ServiceConfigProperties(String tokenKey) {
        this.tokenKey = tokenKey;
    }

    public ServiceConfigProperties() {
    }

    public ServiceConfigProperties(String apiUrl, String tokenKey) {
        this.apiUrl = apiUrl;
        this.tokenKey = tokenKey;
    }

    public ServiceConfigProperties(String apiUrl, String tokenKey, String robotType, String robotModel) {
        this.apiUrl = apiUrl;
        this.tokenKey = tokenKey;
        this.robotType = robotType;
        this.robotModel = robotModel;
    }

    public String getApiUrl() {
        return apiUrl;
    }

    public void setApiUrl(String apiUrl) {
        this.apiUrl = apiUrl;
    }

    public String getTokenId() {
        return tokenId;
    }

    public void setTokenId(String tokenId) {
        this.tokenId = tokenId;
    }

    public String getTokenName() {
        return tokenName;
    }

    public void setTokenName(String tokenName) {
        this.tokenName = tokenName;
    }

    public String getTokenKey() {
        return tokenKey;
    }

    public void setTokenKey(String tokenKey) {
        this.tokenKey = tokenKey;
    }

    public String getRobotType() {
        return robotType;
    }

    public void setRobotType(String robotType) {
        this.robotType = robotType;
    }

    public String getRobotModel() {
        return robotModel;
    }

    public void setRobotModel(String robotModel) {
        this.robotModel = robotModel;
    }

    public Integer getMaxTokens() {
        return maxTokens;
    }

    public void setMaxTokens(Integer maxTokens) {
        this.maxTokens = maxTokens;
    }

    public Integer getRequestMaxTokens() {
        return requestMaxTokens;
    }

    public void setRequestMaxTokens(Integer requestMaxTokens) {
        this.requestMaxTokens = requestMaxTokens;
    }

    public String getEnvType() {
        return envType;
    }

    public void setEnvType(String envType) {
        this.envType = envType;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Override
    public String toString() {
        return "ServiceConfigProperties{" +
                "apiUrl='" + apiUrl + '\'' +
                ", tokenId='" + tokenId + '\'' +
                ", tokenName='" + tokenName + '\'' +
                ", tokenKey='" + tokenKey + '\'' +
                ", tag='" + tag + '\'' +
                ", robotType='" + robotType + '\'' +
                ", robotModel='" + robotModel + '\'' +
                ", maxTokens=" + maxTokens +
                ", requestMaxTokens=" + requestMaxTokens +
                ", envType='" + envType + '\'' +
                '}';
    }

    public Integer getMaxChars() {
        return maxChars;
    }

    public void setMaxChars(Integer maxChars) {
        this.maxChars = maxChars;
    }
}
