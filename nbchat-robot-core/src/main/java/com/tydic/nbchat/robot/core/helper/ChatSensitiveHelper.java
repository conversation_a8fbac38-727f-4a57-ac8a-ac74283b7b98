package com.tydic.nbchat.robot.core.helper;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.core.config.ChatSensitiveContext;
import com.tydic.nbchat.robot.core.config.SensitiveConfigProperties;
import com.tydic.nicc.common.constants.RedisCacheKeyConstant;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.common.eums.im.MsgFormType;
import com.tydic.nicc.common.eums.im.MsgStatus;
import com.tydic.nicc.common.eums.sensitive.SensitiveEncryptType;
import com.tydic.nicc.common.eums.sensitive.SensitiveFilterState;
import com.tydic.nicc.common.eums.sensitive.SensitiveLoadType;
import com.tydic.nicc.common.msg.ImMessage;
import com.tydic.nicc.common.msg.ImUserMessageBody;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.FileUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import com.tydic.nbchat.robot.api.bo.sensitive.*;
import com.tydic.nbchat.robot.api.ChatSensitiveService;
import com.tydic.nbchat.robot.mapper.po.ChatSensitiveOriginalMsg;
import com.tydic.nicc.tools.sensitiveWords.WordContext;
import com.tydic.nicc.tools.sensitiveWords.WordOptType;
import com.tydic.nicc.tools.sensitiveWords.WordType;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

/**
 * @Classname ChatSensitiveHelper
 * @Description ChatSensitiveHelper
 * @Date 2021/12/22 5:53 下午
 * @Created by kangkang
 */
@Slf4j
@Component
public class ChatSensitiveHelper {

    @Resource
    private RedisHelper redisHelper;
    private final ChatSensitiveService chatSensitiveService;
    private final SensitiveConfigProperties sensitiveConfigProperties;
    private final ChatSensitiveCURDHelper chatSensitiveCURDHelper;
    /**
     * 保存租户级别的敏感词上下文
     */
    public static Map<String, ChatSensitiveContext> CHAT_SENSITIVE_TENANT_MAP = new HashMap<>();

    public ChatSensitiveHelper(ChatSensitiveService chatSensitiveService,
                               SensitiveConfigProperties sensitiveConfigProperties,
                               ChatSensitiveCURDHelper chatSensitiveCURDHelper) {
        this.chatSensitiveService = chatSensitiveService;
        this.sensitiveConfigProperties = sensitiveConfigProperties;
        this.chatSensitiveCURDHelper = chatSensitiveCURDHelper;
    }


    public boolean include(String tenantCode, String input) {
        return include(tenantCode, input, 0);
    }

    public String replace(String tenantCode, String input) {
        return replace(tenantCode, input, 0);
    }

    public List<String> wordList(String tenantCode, String input) {
        return wordList(tenantCode, input, 0);
    }

    public int count(String tenantCode, String input) {
        return count(tenantCode, input, 0);
    }

    public List<String> wordList(String tenantCode, String input, int skip) {
        ChatSensitiveContext chatSensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(tenantCode);
        if (chatSensitiveContext != null) {
            return chatSensitiveContext.getWordFilter().wordList(input, skip);
        }
        return new ArrayList<>();
    }

    public Map getWordMap(String tenantCode) {
        ChatSensitiveContext chatSensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(tenantCode);
        Map<String, Set> map = new HashMap();
        if (chatSensitiveContext != null) {
            Set<String> blackSet = chatSensitiveContext.getWordContext().getWordSet(WordType.BLACK);
            Set<String> blackRegSet = chatSensitiveContext.getWordContext().getWordSet(WordType.BLACK_REG);
            Set<String> whiteSet = chatSensitiveContext.getWordContext().getWordSet(WordType.WHITE);
            map.put(WordType.BLACK.name(), blackSet);
            map.put(WordType.BLACK_REG.name(), blackRegSet);
            map.put(WordType.WHITE.name(), whiteSet);
        }
        return map;
    }

    public Map getWordMap(String tenantCode, String wordType) {
        Map<String, Set<String>> map = getWordMap(tenantCode);
        if (StringUtils.isNotBlank(wordType)) {
            Map newMap = new HashMap<>();
            newMap.put(wordType.toUpperCase(Locale.ROOT), map.get(wordType.toUpperCase(Locale.ROOT)));
            return newMap;
        }
        return map;
    }

    public Map getWordMap(String tenantCode, String wordType, String wordKey) {
        Map<String, Set<String>> map = getWordMap(tenantCode, wordType);
        if (StringUtils.isNotBlank(wordKey) && map != null) {
            for (Map.Entry<String, Set<String>> setEntry : map.entrySet()) {
                Set<String> wordSet = setEntry.getValue();
                if (wordSet != null) {
                    Set<String> newSet = new LinkedHashSet<>();
                    for (String s : wordSet) {
                        if (s.contains(wordKey)) {
                            newSet.add(s);
                        }
                    }
                    setEntry.setValue(newSet);
                }
            }
        }
        return map;
    }


    public int count(String tenantCode, String input, int skip) {
        if (sensitiveConfigProperties.getEnable()) {
            ChatSensitiveContext chatSensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(tenantCode);
            if (chatSensitiveContext != null) {
                return chatSensitiveContext.getWordFilter().wordCount(input, skip);
            }
        }
        return 0;
    }

    @Synchronized
    public String replace(String tenantCode, String input, int skip) {
        if(StringUtils.isBlank(input)){
            return input;
        }
        if (sensitiveConfigProperties.getEnable()) {
            ChatSensitiveContext chatSensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(tenantCode);
            if (chatSensitiveContext != null) {
                return chatSensitiveContext.getWordFilter().replace(input, skip, '★');
            }
        }
        return input;
    }

    @Synchronized
    public boolean include(String tenantCode, String input, int skip) {
        if (sensitiveConfigProperties.getEnable()) {
            ChatSensitiveContext chatSensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(tenantCode);
            if (chatSensitiveContext != null) {
                return chatSensitiveContext.getWordFilter().include(input, skip);
            }
        }
        return false;
    }

    /**
     * 初始化敏感词配置
     */
    public void initSensitiveContext() {
        ChatSensitiveConfReqBO reqBO = new ChatSensitiveConfReqBO();
        reqBO.setFilterState(SensitiveFilterState.ENABLE.getCode());
        RspList<ChatSensitiveConfBO> configRspList = chatSensitiveService.getSensitiveConfList(reqBO);
        log.info("敏感词初始化-读取配置信息:{}", configRspList.getRows());
        if (configRspList.isSuccess()) {
            for (ChatSensitiveConfBO sensitiveConf : configRspList.getRows()) {
                ChatSensitiveContext sensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(sensitiveConf.getTenantCode());
                if (sensitiveContext != null) {
                    //重新加载前先清空内存里的数据
                    sensitiveContext.getWordContext().getWordSet(WordType.BLACK).clear();
                    sensitiveContext.getWordContext().getWordSet(WordType.BLACK_REG).clear();
                    sensitiveContext.getWordContext().getWordSet(WordType.WHITE).clear();
                    sensitiveContext.getWordContext().getWordMap().clear();
                }
                addOrUpdateSensitive(sensitiveConf);
            }
        }
    }


    /**
     * 移除敏感词配置
     *
     * @param sensitiveConf
     */
    public void removeSensitive(ChatSensitiveConfBO sensitiveConf) {
        if (sensitiveConf == null) {
            log.warn("移除敏感词配置-异常:配置不得为空！");
            return;
        }
        CHAT_SENSITIVE_TENANT_MAP.remove(sensitiveConf.getTenantCode());
    }


    /**
     * 新增敏感词配置
     *
     * @param sensitiveConf
     */
    public void addOrUpdateSensitive(ChatSensitiveConfBO sensitiveConf) {
        if (sensitiveConf == null) {
            log.warn("敏感词初始化-异常:配置不得为空！");
            return;
        }
        ChatSensitiveWordsConfReqBO wordsReqBO = new ChatSensitiveWordsConfReqBO();
        wordsReqBO.setLimit(NiccCommonUtil.page_un_limit);
        wordsReqBO.setTenantCode(sensitiveConf.getTenantCode());
        wordsReqBO.setSensitiveState(SensitiveFilterState.ENABLE.getCode());
        RspList<ChatSensitiveWordsConfBO> sensitiveList = chatSensitiveService.getSensitiveWordsConfList(wordsReqBO);
        ChatSensitiveContext sensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(sensitiveConf.getTenantCode());
        if (sensitiveContext == null) {
            WordContext wordContext = new WordContext(
                    sensitiveConfigProperties.getLoadDefault(),
                    sensitiveConfigProperties.getBlacklist(),
                    sensitiveConfigProperties.getBlackReglist(),
                    sensitiveConfigProperties.getWhitelist());
            log.info("敏感词初始化-读取词库配置:{} 条", sensitiveList.getCount());
            for (ChatSensitiveWordsConfBO chatSensitive : sensitiveList.getRows()) {
                //加载配置
                loadWords(wordContext, chatSensitive);
            }
            sensitiveContext = new ChatSensitiveContext(sensitiveConf, wordContext);
            CHAT_SENSITIVE_TENANT_MAP.put(sensitiveConf.getTenantCode(), sensitiveContext);
        } else {
            //已存在则更新
            updateSensitiveConfAndWords(sensitiveConf, sensitiveList.getRows());
        }
    }


    /**
     * 更新配置和词库
     *
     * @param sensitiveConf
     * @param wordsBOList
     */
    public void updateSensitiveConfAndWords(ChatSensitiveConfBO sensitiveConf, List<ChatSensitiveWordsConfBO> wordsBOList) {
        ChatSensitiveContext sensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(sensitiveConf.getTenantCode());
        if (sensitiveContext != null) {
            sensitiveContext.setSensitiveConf(sensitiveConf);
            for (ChatSensitiveWordsConfBO chatSensitive : wordsBOList) {
                //加载配置
                if (sensitiveConf.getTenantCode().equals(chatSensitive.getTenantCode())) {
                    loadWords(sensitiveContext.getWordContext(), chatSensitive);
                }
            }
        }
    }

    /**
     * 加载词库
     *
     * @param tenantCode
     * @param wordSet
     * @param wordType
     */
    public void addWords(String tenantCode, Iterable<String> wordSet, WordType wordType) {
        if (wordSet != null) {
            ChatSensitiveContext sensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(tenantCode);
            if (sensitiveContext != null) {
                sensitiveContext.getWordContext().addWord(wordSet, wordType);
            }
        }
    }

    /**
     * 移除敏感词
     *
     * @param tenantCode
     * @param wordSet
     * @param wordType
     */
    public void removeWords(String tenantCode, Iterable<String> wordSet, WordType wordType) {
        if (wordSet != null) {
            ChatSensitiveContext sensitiveContext = CHAT_SENSITIVE_TENANT_MAP.get(tenantCode);
            if (sensitiveContext != null) {
                sensitiveContext.getWordContext().removeWord(wordSet, wordType);
            }
        }
    }


    @MethodParamVerifyEnable
    public Rsp flashWords(ChatSensitiveWordsFlashReqBO updateReqBO) {
        return flashWords(updateReqBO, WordType.BLACK);
    }

    /**
     * 刷新敏感词
     * @param updateReqBO
     * @param wordType
     * @return
     */
    public Rsp flashWords(ChatSensitiveWordsFlashReqBO updateReqBO, WordType wordType) {
        if (!sensitiveConfigProperties.getEnable()) {
            return BaseRspUtils.createErrorRsp("操作失败:敏感词开关已关闭!");
        }
        WordOptType optType = WordOptType.valueOf(updateReqBO.getWordOptType().toUpperCase(Locale.ROOT));
        if (WordOptType.ADD.equals(optType)) {
            chatSensitiveCURDHelper.syncAdd(updateReqBO.getTenantCode(),updateReqBO.getWordSet(),wordType);
            //addWords(updateReqBO.getTenantCode(), updateReqBO.getWordSet(), wordType);
        } else if (WordOptType.REMOVE.equals(optType)) {
            chatSensitiveCURDHelper.syncRemove(updateReqBO.getTenantCode(),updateReqBO.getWordSet(),wordType);
            //removeWords(updateReqBO.getTenantCode(), updateReqBO.getWordSet(), wordType);
        } else {
            return BaseRspUtils.createErrorRsp("操作失败:参数异常!");
        }
        return BaseRspUtils.createSuccessRsp(updateReqBO.getWordSet());
    }


    /**
     * 加载词库
     *
     * @param wordContext
     * @param chatSensitive
     */
    private void loadWords(WordContext wordContext, ChatSensitiveWordsConfBO chatSensitive) {
        Set<String> wordsSet = new HashSet<>();
        try {
            log.info("敏感词初始化-开始加载词库:{}", chatSensitive);
            if (SensitiveLoadType.URL.getCode().equals(chatSensitive.getLoadType())) {
                //从网络加载
                for (String s : chatSensitive.getFileList()) {
                    Set<String> tmpSet = FileUtils.readWordFromUrl(s, chatSensitive.getEncrypt(), chatSensitive.getSplit());
                    wordsSet.addAll(tmpSet);
                    log.info("敏感词初始化-从远程地址加载:{},{}", tmpSet, s);
                }
            }
            if (SensitiveLoadType.LOCAL.getCode().equals(chatSensitive.getLoadType())) {
                //从本地加载
                for (String filePath : chatSensitive.getFileList()) {
                    Set<String> tmpSet = wordContext.readWordFile(new File(filePath));
                    if (SensitiveEncryptType.BASE64.matchCode(chatSensitive.getEncrypt())) {
                        for (String word : tmpSet) {
                            //解密
                            word = NiccCommonUtil.decodeStr(word).trim();
                            wordsSet.add(word);
                        }
                    } else {
                        wordsSet.addAll(tmpSet);
                    }
                    log.info("敏感词初始化-从本地文件加载:{},{}", wordsSet, filePath);
                }
            }
            if (SensitiveLoadType.TABLE.getCode().equals(chatSensitive.getLoadType())) {
                //从数据库加载
                ChatSensitiveWordsQueryReqBO reqBO = new ChatSensitiveWordsQueryReqBO();
                reqBO.setTenantCode(chatSensitive.getTenantCode());
                reqBO.setSensitiveId(chatSensitive.getSensitiveId());
                reqBO.setWordType(chatSensitive.getWordType());
                reqBO.setLoadType(chatSensitive.getLoadType());
                reqBO.setIsValid(EntityValidType.NORMAL.getCode());
                reqBO.setLimit(NiccCommonUtil.page_un_limit);
                RspList<ChatSensitiveWordsBO> wordsBORspList = chatSensitiveService.getSensitiveWords(reqBO);
                for (ChatSensitiveWordsBO row : wordsBORspList.getRows()) {
                    wordsSet.add(row.getWordText());
                }
                log.info("敏感词初始化-从数据库加载:{}", wordsSet);
            }
            if(SensitiveLoadType.REDIS.getCode().equals(chatSensitive.getLoadType())){
                String cacheKey = RedisCacheKeyConstant.getImSensitiveWordsKey(chatSensitive.getTenantCode(),chatSensitive.getWordType());
                Set<Object> wordSet = redisHelper.sGet(cacheKey);
                log.info("敏感词初始化-从REDIS加载:{}", wordSet);
                if(wordSet != null){
                    for (Object o : wordSet) {
                        wordsSet.add(String.valueOf(o));
                    }
                }
            }
            if (wordsSet != null && !wordsSet.isEmpty()) {
                log.info("敏感词初始化-读取到词库:{}-{}", WordType.valueOf(chatSensitive.getWordType()), wordsSet);
                wordContext.addWord(wordsSet, WordType.valueOf(chatSensitive.getWordType()));
            }
        } catch (Exception e) {
            log.error("敏感词初始化-异常:chatSensitive = {}", chatSensitive, e);
        }
    }


    public void saveOriginalMessage(ImMessage message, String newMsgId, String originContent, String chatKey) {
        ChatSensitiveOriginalMsg record = new ChatSensitiveOriginalMsg();
        record.setChatKey(chatKey);
        record.setTenantCode(message.getTenantCode());
        record.setChatType(message.getChatType());
        record.setChatKey(chatKey);
        record.setMsgTime(new Date(message.getMsgTime()));
        record.setMsgId(newMsgId);
        record.setFromNo(message.getFromNo());
        record.setToNo(message.getToNo());
        record.setCreateTime(new Date());
        record.setSessionId(message.getSessionId());
        record.setMsgType(message.getMsgType());
        record.setMsgStatus(MsgStatus.RECEIVED.getShortCode());
        String content = "";
        String msgForm = "";
        try {
            ImUserMessageBody body = (ImUserMessageBody) message.getMsgBody();
            content = body.getMsgContent();
            if (StringUtils.isEmpty(body.getMsgForm())) {
                msgForm = MsgFormType.TEXT.getCode();
            } else {
                msgForm = body.getMsgForm();
            }
            record.setMsgForm(msgForm);
            record.setMsgContent(originContent);
            record.setMsgFilter(content);
            log.info("保存敏感词过滤原始消息:{}", JSONObject.toJSON(record));
            //chatSensitiveOriginalMsgMapper.insertSelective(record);
        } catch (Exception e) {
            log.error("保存原始消息异常:", e);
        }
    }

}
