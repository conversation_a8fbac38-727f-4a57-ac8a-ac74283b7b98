package com.tydic.nbchat.robot.core.util;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.robot.api.bo.robot.RobotTokenConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@Slf4j
public class RobotTokenConfigParseUtil {

    public static Optional<RobotTokenConfig> parse(String config) {
        if (StringUtils.isBlank(config)) {
            return Optional.empty();
        }
        try {
            return Optional.ofNullable(JSONObject.parseObject(config, RobotTokenConfig.class));
        } catch (Exception e) {
            log.error("解析robot-token配置异常:{}", config, e);
        }
        return Optional.empty();
    }

}
