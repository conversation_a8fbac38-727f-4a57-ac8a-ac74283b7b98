package com.tydic.nbchat.robot.core.openai.service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Objects;

import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;

import com.tydic.nbchat.robot.core.openai.exception.OpenAiHttpException;
import com.tydic.nbchat.robot.core.openai.exception.TianShuErrorBody;
import com.tydic.nbchat.robot.core.openai.exception.TianShuException;
import io.reactivex.FlowableEmitter;

import lombok.extern.slf4j.Slf4j;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.HttpException;
import retrofit2.Response;

/**
 * Callback to parse Server Sent Events (SSE) from raw InputStream and
 * emit the events with io.reactivex.FlowableEmitter to allow streaming of
 * SSE.
 */
@Slf4j
public class ResponseBodyCallback implements Callback<ResponseBody> {

    public static ObjectMapper defaultObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        return mapper;
    }

    private FlowableEmitter<SSE> emitter;
    private boolean emitDone = false;

    public ResponseBodyCallback(FlowableEmitter<SSE> emitter, boolean emitDone) {
        this.emitter = emitter;
        this.emitDone = emitDone;
    }

    @Override
    public void onResponse(@NotNull Call<ResponseBody> call, @NotNull Response<ResponseBody> response) {

        try {
            if (!response.isSuccessful()) {
                HttpException e = new HttpException(response);
                try (ResponseBody responseBody = Objects.requireNonNull(e.response()).errorBody()) {
                    if (responseBody != null) {
                        String errorBody = responseBody.string();
                        try {
                            TianShuErrorBody tianShuErr = JSONObject.parseObject(errorBody, TianShuErrorBody.class);
                            if (tianShuErr.getError() != null) {
                                throw new TianShuException(e, errorBody, e.code());
                            }
                        } catch (JSONException ignored) {}
                        log.error("OpenAI http stream call error: {}", errorBody,e);
                        throw new OpenAiHttpException(e,errorBody,e.code());
                    }
                    throw e;
                }
            }

            try (ResponseBody responseBody = response.body()) {
                if (responseBody != null) {
                    InputStream in = responseBody.byteStream();
                    BufferedReader reader = new BufferedReader(new InputStreamReader(in));
                    String line;
                    SSE sse = null;
                    JSONObject tmpData = new JSONObject();
                    StringBuilder bufferData = new StringBuilder();
                    while ((line = reader.readLine()) != null) {
                        //兼容智普AI -- start
                        if (StringUtils.isNotBlank(line) && line.trim().equals("event:new_message")) {
                            continue;
                        }
                        if (line.startsWith("id:")) {
                            tmpData.put("id", line.substring(3).trim());
                            continue;
                        }
                        if (line.startsWith("event:")) {
                            tmpData.put("event", line.substring(6).trim());
                            continue;
                        }
                        if (line.startsWith("meta:")) {
                            String meta = line.substring(5).trim();
                            tmpData.put("meta", JSONObject.parseObject(meta).getJSONObject("usage"));
                            continue;
                        }
                        //兼容智普AI -- end
                        if (line.startsWith("data:")) {
                            if (tmpData.isEmpty()){
                                String data = line.substring(5).trim();
                                sse = new SSE(data);
                            } else {
                                //兼容智普AI
                                String data = line.substring(5);
                                if (StringUtils.isEmpty(data)){
                                    bufferData.append("\n");
                                } else {
                                    bufferData.append(data);
                                }
                            }
                        } else if (StringUtils.isBlank(line) && !tmpData.isEmpty()) {
                            tmpData.put("data", bufferData.toString());
                            sse = new SSE(tmpData.toJSONString());
                            tmpData.clear();
                            bufferData = new StringBuilder();
                            emitter.onNext(sse);
                        } else if (line.isEmpty() && sse != null) {
                            if (sse.isDone()) {
                                if (emitDone) {
                                    emitter.onNext(sse);
                                }
                                break;
                            }
                            emitter.onNext(sse);
                            sse = null;
                        } else {
                            log.error("Invalid sse format:: {}", line);
                            //throw new SSEFormatException("Invalid sse format: " + line);
                        }
                    }
                    emitter.onComplete();
                } else {
                    emitter.onError(new IOException("Empty response body"));
                    }
            }
        } catch (Throwable t) {
            onFailure(call, t);
        }
    }

    @Override
    public void onFailure(Call<ResponseBody> call, Throwable t) {
        emitter.onError(t);
    }

}