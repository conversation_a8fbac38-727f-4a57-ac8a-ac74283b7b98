package com.tydic.nbchat.robot.core.config;

import com.tydic.nbchat.robot.core.helper.ChatSensitiveHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@Order(1)
public class InitRunner implements ApplicationRunner {

    private final ChatSensitiveHelper chatSensitiveHelper;

    public InitRunner(ChatSensitiveHelper chatSensitiveHelper) {
        this.chatSensitiveHelper = chatSensitiveHelper;
    }

    @Override
    public void run(ApplicationArguments args) {
        try {
            chatSensitiveHelper.initSensitiveContext();
        } catch (Exception e) {
            log.error("敏感词初始化-异常:", e);
        }
    }
}
