package com.tydic.nbchat.robot.core.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.robot.api.NbchatPromptApi;
import com.tydic.nbchat.robot.api.bo.robot.NbchatPromptReqBO;
import com.tydic.nbchat.robot.api.bo.robot.NbchatPromptRspBO;
import com.tydic.nbchat.robot.mapper.NbchatPresetPromptMapper;
import com.tydic.nbchat.robot.mapper.po.NbchatPresetPrompt;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@AllArgsConstructor
public class NbchatPromptServiceImpl implements NbchatPromptApi {

    private final NbchatPresetPromptMapper nbchatPresetPromptMapper;

    @Override
    public RspList queryPromptList(NbchatPromptReqBO reqBO) {
        log.info("查询提示语列表-开始:{}", reqBO);
        NbchatPresetPrompt nbchatPresetPrompt = new NbchatPresetPrompt();
        BeanUtils.copyProperties(reqBO, nbchatPresetPrompt);
        nbchatPresetPrompt.setTenantCode(null);
        nbchatPresetPrompt.setIsValid(EntityValidType.NORMAL.getCode());
        Page<NbchatPresetPrompt> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        nbchatPresetPromptMapper.selectAll(nbchatPresetPrompt);
        if (page.getTotal() > 0) {
            log.info("查询提示语列表-查到数据:{}", page.getTotal());
            List<NbchatPresetPrompt> result = page.getResult();
            List<NbchatPromptRspBO> res = new ArrayList<>();
            NiccCommonUtil.copyList(result, res, NbchatPromptRspBO.class);
            return BaseRspUtils.createSuccessRspList(res, page.getTotal());
        }
        log.info("查询提示语列表-未查到数据");
        return BaseRspUtils.createSuccessRspList(new ArrayList<NbchatPromptRspBO>(), 0L);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp save(NbchatPromptReqBO reqBO) {
        log.info("保存提示词 - 开始: {}", reqBO);

        if (StringUtils.isBlank(reqBO.getPresetName())) {
            return BaseRspUtils.createErrorRsp("提示词名称不能为空");
        }

        NbchatPresetPrompt existing = null;
        if (StringUtils.isNotBlank(reqBO.getPresetId())) {
            existing = nbchatPresetPromptMapper.queryById(reqBO.getPresetId());
        }

        return (existing == null) ? insertPrompt(reqBO) : updatePrompt(reqBO, existing);
    }

    private Rsp insertPrompt(NbchatPromptReqBO reqBO) {
        if (StringUtils.isBlank(reqBO.getPresetId())) {
            reqBO.setPresetId(IdWorker.nextAutoIdStr());
        }
        NbchatPresetPrompt prompt = buildPromptEntity(reqBO, new NbchatPresetPrompt(), 0);

        List<NbchatPresetPrompt> historyList = new ArrayList<>();
        historyList.add(prompt);
        prompt.setHistoryVersion(JSONObject.toJSONString(historyList));

        nbchatPresetPromptMapper.insertSelective(prompt);
        log.info("保存提示语 - 结束（插入逻辑）");
        return BaseRspUtils.createSuccessRsp(prompt.getPresetId(), "保存成功");
    }

    private Rsp updatePrompt(NbchatPromptReqBO reqBO, NbchatPresetPrompt existing) {

        NbchatPresetPrompt prompt = buildPromptEntity(reqBO, existing, existing.getVersion() + 1);

        List<NbchatPresetPrompt> historyList = buildHistory(prompt.getHistoryVersion(), prompt);
        prompt.setHistoryVersion(JSONObject.toJSONString(historyList));

        nbchatPresetPromptMapper.update(prompt);
        NbchatPresetPrompt result = nbchatPresetPromptMapper.queryById(reqBO.getPresetId());
        log.info("保存提示词 - 结束（更新逻辑）");
        return BaseRspUtils.createSuccessRsp(result, "更新成功");
    }

    private NbchatPresetPrompt buildPromptEntity(NbchatPromptReqBO reqBO, NbchatPresetPrompt target, int version) {
        BeanUtils.copyProperties(reqBO, target);
        target.setCreateTime(new Date());
        target.setIsValid(EntityValidType.NORMAL.getCode());
        target.setVersion(version);
        return target;
    }

    private List<NbchatPresetPrompt> buildHistory(String existingJson, NbchatPresetPrompt current) {
        current.setHistoryVersion(null);
        List<NbchatPresetPrompt> list;
        if (StringUtils.isNotBlank(existingJson)) {
            list = JSONArray.parseArray(existingJson, NbchatPresetPrompt.class);
        } else {
            list = new ArrayList<>();
        }
        if (list.size() > 4) {
            list = list.subList(list.size() - 4, list.size());
        }
        list.add(current);
        return list;
    }
}

