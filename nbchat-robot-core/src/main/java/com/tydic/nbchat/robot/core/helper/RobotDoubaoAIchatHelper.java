package com.tydic.nbchat.robot.core.helper;

import com.tydic.nbchat.robot.api.bo.eums.RobotType;
import com.tydic.nbchat.robot.api.bo.robot.RobotToken;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChunk;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nbchat.robot.core.helper.api.AiRobotHelper;
import com.tydic.nbchat.robot.core.helper.api.bo.AiRobotChatRequest;
import com.tydic.nbchat.robot.core.openai.service.OpenAiService;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class RobotDoubaoAIchatHelper implements AiRobotHelper {
    private NbchatRobotConfigProperties configProperties;
    private List<RobotToken> tokens;

    private Map<String, OpenAiService> serviceTagMap = new HashMap<>();
    // openai 接口调用
    private Map<Integer, OpenAiService> serviceMap = new HashMap<>();


    public RobotDoubaoAIchatHelper(NbchatRobotConfigProperties configProperties) {
        this(configProperties, new ArrayList<>());
    }

    public RobotDoubaoAIchatHelper(NbchatRobotConfigProperties configProperties, List<RobotToken> tokens) {
        this.configProperties = configProperties;
        this.tokens = tokens;
        if (tokens != null && !tokens.isEmpty()) {
            reloadService(tokens);
        }
    }

    @Override
    public String robot() {
        return RobotType.DOUBAO_AI.getCode();
    }

    @Override
    public Flowable<ChatCompletionChunk> streamChat(AiRobotChatRequest request) {
        OpenAiService service = getService(request);
        return service.streamDoubaoChatCompletion(buildChatRequest(request));
    }


    @Override
    public ChatCompletionResult chat(AiRobotChatRequest request) {
        OpenAiService service = getService(request);
        return service.createDoubaoChatCompletion(buildChatRequest(request));
    }


    @Override
    public void reloadService(List<RobotToken> tokens) {
        this.tokens = tokens;
        serviceTagMap = initServiceTagMap(tokens,configProperties.getRobotReadTimeout());
        serviceMap = initServiceMap(tokens,configProperties.getRobotReadTimeout());
        log.info("初始化openAiServie[{}]-[tag]-完成:{}",robot(), toTagMapString(serviceTagMap));
        log.info("初始化openAiServie[{}]--完成:{}",robot(), toMapString(serviceMap));
    }

    @Override
    public Map<Integer, OpenAiService> getServiceMap() {
        return serviceMap;
    }

    @Override
    public Map<String, OpenAiService> getServiceTagMap() {
        return serviceTagMap;
    }

    @Override
    public void removeService(String token) {

    }

    @Override
    public OpenAiService getDefaultService() {
        return serviceMap.get(0);
    }

    @Override
    public List<RobotToken> getTokens() {
        return tokens;
    }


}
