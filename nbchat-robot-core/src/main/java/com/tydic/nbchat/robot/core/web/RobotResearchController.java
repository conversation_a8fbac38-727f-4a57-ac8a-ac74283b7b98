package com.tydic.nbchat.robot.core.web;

import com.tydic.nbchat.robot.api.bo.research.FileResearchDeleteRequest;
import com.tydic.nbchat.robot.api.bo.research.FileResearchRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;


@Deprecated
@Slf4j
@RestController
@RequestMapping("/robot/research")
public class RobotResearchController {


    @PostMapping("/files/update")
    public Rsp updateFile(@RequestBody FileResearchRequest reqBo) {
        return BaseRspUtils.createSuccessRsp("");
    }

    @PostMapping("/files/delete")
    public Rsp deleteFile(@RequestBody FileResearchDeleteRequest reqBo) {
        return BaseRspUtils.createSuccessRsp("");
    }

    @Deprecated
    @PostMapping("/files/page")
    public RspList getResearchFilesPage(@RequestBody FileResearchRequest reqBo) {
        return BaseRspUtils.createSuccessRspList(new ArrayList<>());
    }

    @PostMapping("/files")
    public RspList getResearchFiles(@RequestBody FileResearchRequest reqBo) {
        return BaseRspUtils.createSuccessRspList(new ArrayList<>());
    }

    @Deprecated
    @PostMapping("/files/qryFile")
    public Rsp qryFile(@RequestBody FileResearchRequest reqBo) {
        return BaseRspUtils.createSuccessRsp("");
    }

    @PostMapping("/file")
    public Rsp getResearchFile(@RequestBody FileResearchRequest reqBo) {
        return BaseRspUtils.createSuccessRsp("");
    }

}
