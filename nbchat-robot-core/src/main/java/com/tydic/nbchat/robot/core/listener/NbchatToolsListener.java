package com.tydic.nbchat.robot.core.listener;

import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotPromptMessageRequest;
import com.tydic.nbchat.robot.api.bo.tools.RobotToolsChatResponse;


public interface NbchatToolsListener {

    /**
     * 用户请求
     * @param request
     * @return
     */
    void onRequest(RobotPromptMessageRequest promptMessageRequest, RobotMessageRequest request);

    /**
     * 收到消息
     * @param response
     * @return
     */
    void onResponse(RobotPromptMessageRequest promptMessageRequest,RobotToolsChatResponse response);

    /**
     * 异常
     * @param promptMessageRequest
     * @param reason
     * @param e
     */
    RobotToolsChatResponse onError(RobotPromptMessageRequest promptMessageRequest,String reason,Exception e);
}
