package com.tydic.nbchat.robot.core.web;

import com.tydic.nbchat.robot.api.ChatSensitiveService;
import com.tydic.nbchat.robot.api.bo.sensitive.*;
import com.tydic.nbchat.robot.core.helper.ChatSensitiveCURDHelper;
import com.tydic.nbchat.robot.core.helper.ChatSensitiveHelper;
import com.tydic.nicc.common.eums.sensitive.SensitiveCheckType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.tools.sensitiveWords.WordType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * @Classname ImDataCollectController
 * @Description 数据收集
 * @Date 2021/7/9 4:48 下午
 * @Created by kangkang
 */
@Slf4j
@RestController
@RequestMapping("/robot/sensitive")
public class RobotChatSensitiveController {

    @Resource
    private ChatSensitiveHelper chatSensitiveHelper;
    @Resource
    private ChatSensitiveCURDHelper chatSensitiveCURDHelper;
    //@Resource
    //private ImSensitiveOriginalMsgService originalMsgService;
    @Resource
    private ChatSensitiveService chatSensitiveService;

    /**
     * 手动初始化敏感词
     * @return
     */
    @GetMapping("/init")
    public Rsp init(){
        chatSensitiveCURDHelper.init();
        return BaseRspUtils.createSuccessRsp("ok");
    }

    /**
     * 校验敏感词
     * @param checkReqBO
     * @return
     */
    @MethodParamVerifyEnable
    @PostMapping("/check")
    public Rsp replace(@RequestBody ChatSensitiveCheckReqBO checkReqBO){
        log.info("敏感词测试:{}",checkReqBO);
        int skip = checkReqBO.getSkip();
        if(SensitiveCheckType.replace.getCode().equals(checkReqBO.getCheckType())){
            String replace = chatSensitiveHelper.replace(checkReqBO.getTenantCode(),checkReqBO.getWords(),skip);
            return BaseRspUtils.createSuccessRsp(replace);
        } else if (SensitiveCheckType.detail.getCode().equals(checkReqBO.getCheckType())) {
            String include = chatSensitiveHelper.replace(checkReqBO.getTenantCode(),checkReqBO.getWords(),skip);
            List<String> words = chatSensitiveHelper.wordList(checkReqBO.getTenantCode(),checkReqBO.getWords());
            return BaseRspUtils.createSuccessRsp(new ChatSensitiveCheckResult(include,words));
        } else if (SensitiveCheckType.include.getCode().equals(checkReqBO.getCheckType())){
            String replace = chatSensitiveHelper.replace(checkReqBO.getTenantCode(),checkReqBO.getWords(),skip);
            return BaseRspUtils.createSuccessRsp(replace);
        } else {
            return BaseRspUtils.createErrorRsp("输入参数错误!");
        }
    }

    /**
     * 获取敏感词字典
     * @param req
     * @return
     */
    @MethodParamVerifyEnable
    @PostMapping("/words")
    public Rsp sensitives(@RequestBody ChatSensitiveWordsQueryReqBO req){
        Map map = chatSensitiveHelper.getWordMap(req.getTenantCode(),req.getWordType(),req.getWordKey());
        return BaseRspUtils.createSuccessRsp(map);
    }


    @PostMapping("/message")
    public RspList messages(@RequestBody ChatSensitiveOrignMsgReqBO reqBO){
        //return originalMsgService.getSensitiveOriginalMessages(reqBO);
        return BaseRspUtils.createSuccessRspList(new ArrayList<>());
    }


    /**
     * 更新字典-实时生效
     * @param updateReqBO
     * @return
     */
    @PostMapping("/words/flash")
    public Rsp wordsUpdate(@RequestBody ChatSensitiveWordsFlashReqBO updateReqBO){
        return chatSensitiveHelper.flashWords(updateReqBO);
    }

    @PostMapping("/words/save")
    public Rsp wordsSave(@RequestBody ChatSensitiveWordsSaveReqBO saveReqBO){
        try {
            Rsp<ChatSensitiveWordsSaveResult> saveRsp = chatSensitiveService.saveChatSensitiveWords(saveReqBO);
            if(saveRsp.isSuccess()){
                WordType wordType = WordType.valueOf(saveRsp.getData().getWordType().toUpperCase(Locale.ROOT));
                ChatSensitiveWordsFlashReqBO flashReqBO = new ChatSensitiveWordsFlashReqBO();
                flashReqBO.setWordSet(saveReqBO.getWordSet());
                flashReqBO.setTenantCode(saveReqBO.getTenantCode());
                flashReqBO.setWordOptType(saveRsp.getData().getWordOptType());
                chatSensitiveHelper.flashWords(flashReqBO,wordType);
            }
            return saveRsp;
        } catch (Exception e) {
            log.error("保存敏感词-异常:",e);
            return BaseRspUtils.createErrorRsp("保存敏感词异常,请检查参数");
        }
    }


    @PostMapping("/words/list")
    public RspList wordsSave(@RequestBody ChatSensitiveWordsQueryReqBO queryReqBO){
        return chatSensitiveService.getSensitiveWords(queryReqBO);
    }

}
