package com.tydic.nbchat.robot.core.util;

import com.tydic.nbchat.robot.api.bo.RobotMessageRequest;
import com.tydic.nbchat.robot.api.bo.RobotMsgContext;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionChunk;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatCompletionResult;
import com.tydic.nbchat.robot.api.openai.completion.chat.ChatMessageRole;
import com.tydic.nbchat.robot.core.config.NbchatRobotConfigProperties;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NbchatRobotMsgBuilder {

    /**
     * 余额用尽错误消息
     */
    public final static String BILLING_CHECK_ERR = "You exceeded your current quota";

    public static String createRequestId() {
        return "chatreq-" + NiccCommonUtil.createImUserId(true);
    }

    public static String createResponseId(){
        return "chatrsp-" + NiccCommonUtil.createImUserId(true);
    }

    public final static byte[] enter = "\n".getBytes();

    public static byte[] mergeEnter(byte[] a) {
        return merge(a,enter);
    }

    /**
     * 合并字节数组
     * @param a
     * @param b
     * @return
     */
    public static byte[] merge(byte[] a, byte[] b) {
        byte[] result = new byte[a.length + b.length];
        System.arraycopy(a, 0, result, 0, a.length);
        System.arraycopy(b, 0, result, a.length, b.length);
        return result;
    }

    public static RobotMsgContext buildMessage(String text, ChatCompletionChunk chunk) {
        RobotMsgContext context = new RobotMsgContext();
        context.setId(createResponseId());
        context.setRole(chunk.getChoices().get(0).getMessage().getRole());
        context.setText(text);
        context.setDelta(chunk.getChoices().get(0).getMessage().getStrContent());
        context.setDetail(chunk);
        return context;
    }

    public static RobotMsgContext buildMessage(ChatCompletionResult result) {
        RobotMsgContext context = new RobotMsgContext();
        context.setId(createResponseId());
        context.setRole(result.getChoices().get(0).getMessage().getRole());
        context.setText(result.getChoices().get(0).getMessage().getStrContent());
        return context;
    }

    public static String getMajorSession(String userId, String majorId) {
        String sessionId = userId + majorId;
        return NiccCommonUtil.stringToMD5(sessionId);
    }


    /**
     * 异常提示信息
     *
     * @param request
     * @return
     */
    public static RobotMsgContext buildErrorContext(RobotMessageRequest request, NbchatRobotConfigProperties configProperties) {
        RobotMsgContext msgContext = new RobotMsgContext();
        msgContext.setRequestId(request.getRequestId());
        msgContext.setRole(ChatMessageRole.ASSISTANT.value());
        msgContext.setText(configProperties.getRobotErrorNotice());
        return msgContext;
    }

    public static RobotMsgContext buildContext(String requestId, String notice) {
        RobotMsgContext msgContext = new RobotMsgContext();
        msgContext.setRequestId(requestId);
        msgContext.setRole(ChatMessageRole.ASSISTANT.value());
        msgContext.setText(notice);
        return msgContext;
    }


    /**
     * @param request
     * @return
     */
    public static RobotMsgContext buildResearchUnknow(RobotMessageRequest request, NbchatRobotConfigProperties configProperties) {
        RobotMsgContext msgContext = new RobotMsgContext();
        msgContext.setRequestId(request.getRequestId());
        msgContext.setRole(ChatMessageRole.ASSISTANT.value());
        msgContext.setText(configProperties.getKbResearch().getUnknownNotice());
        return msgContext;
    }


    /**
     * 敏感词警告
     *
     * @param request
     * @return
     */
    public static RobotMsgContext buildSensitiveWaring(RobotMessageRequest request, NbchatRobotConfigProperties configProperties) {
        RobotMsgContext msgContext = new RobotMsgContext();
        msgContext.setRequestId(request.getRequestId());
        msgContext.setRole(ChatMessageRole.ASSISTANT.value());
        msgContext.setText(configProperties.getSensitiveWaring());
        return msgContext;
    }
}
