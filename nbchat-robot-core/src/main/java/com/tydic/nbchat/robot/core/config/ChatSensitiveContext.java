package com.tydic.nbchat.robot.core.config;

import com.tydic.nbchat.robot.api.bo.sensitive.ChatSensitiveConfBO;
import com.tydic.nicc.tools.sensitiveWords.WordContext;
import com.tydic.nicc.tools.sensitiveWords.WordFilter;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * @Classname ChatSensitiveContext
 * @Description 敏感词上下文
 * @Date 2021/12/22 6:38 下午
 * @Created by kangkang
 */
@Data
public class ChatSensitiveContext {

    private ChatSensitiveConfBO sensitiveConf;
    private WordContext wordContext;
    private WordFilter wordFilter;

    public ChatSensitiveContext(ChatSensitiveConfBO sensitiveConf, WordContext wordContext){
        this.sensitiveConf = sensitiveConf;
        this.wordContext = wordContext;
        this.wordFilter = new WordFilter(wordContext);
    }


    public boolean matchSkillGid(String inputSkill){
        if(StringUtils.isNotEmpty(sensitiveConf.getSkillGids())){
            return sensitiveConf.getSkillGids().contains(inputSkill);
        }
        return true;
    }

    public boolean matchChannel(String inputChannel){
        if(StringUtils.isNotEmpty(sensitiveConf.getSkillGids())){
            return sensitiveConf.getChannels().contains(inputChannel);
        }
        return true;
    }

}
