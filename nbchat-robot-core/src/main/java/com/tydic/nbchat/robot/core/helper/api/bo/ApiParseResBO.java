package com.tydic.nbchat.robot.core.helper.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class ApiParseResBO implements Serializable {
    private String trans_id;
    private Integer code;
    private Content data;
    private String error;

    @Data
    public class Content {
        private String pk_name;
        private String file_path;
        private List<Paragraph> pdf_data;
    }

    @Data
    public class Paragraph {
        private ParagraphInfo paragraph;
    }

    @Data
    public class ParagraphInfo {
        private String sentence;
        private Integer page_number;
    }
}
