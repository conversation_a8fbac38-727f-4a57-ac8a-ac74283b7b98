<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatExamQuestionMapper" >
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.NbchatExamQuestion" >
    <id column="question_id" property="questionId" jdbcType="VARCHAR" />
    <result column="course_id" property="courseId" jdbcType="VARCHAR" />
    <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR" />
    <result column="question_name" property="questionName" jdbcType="VARCHAR" />
    <result column="question_type" property="questionType" jdbcType="CHAR" />
    <result column="part_id" property="partId" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="is_valid" property="isValid" jdbcType="CHAR" />
    <result column="explan" property="explan" jdbcType="VARCHAR" />
    <result column="difficulty" property="difficulty" jdbcType="CHAR" />
    <result column="question_type" property="questionType" jdbcType="CHAR" />
    <result column="knowledges" property="knowledges" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    question_id, course_id, tenant_code, question_name, question_type, part_id, create_time, 
    is_valid, explan,difficulty,question_type,knowledges
  </sql>

  <select id="newTestPaper" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamNewTestPaper" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM nbchat_exam_question
    where tenant_code = #{tenantCode}
    and is_valid = 1
    <if test="courseId != null" >
      and course_id = #{courseId}
    </if>
    <if test="questionType != null" >
      and question_type = #{questionType}
    </if>
    <if test='createType != null and createType == "1" '>
      ORDER BY RAND()
    </if>
    <if test='createType != null and createType == "2" '>
      ORDER BY create_time
    </if>
        LIMIT #{questions}
  </select>

  <select id="checkNewTestPaper" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamNewTestPaper" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM nbchat_exam_question
    where tenant_code = #{tenantCode}
      <!-- 临时使用删除状态 -->
    and is_valid = 0
    <if test="courseId != null" >
      and course_id = #{courseId}
    </if>
    <if test="questionType != null" >
      and question_type = #{questionType}
    </if>
    <if test='createType != null and createType == "1" '>
      ORDER BY RAND()
    </if>
    <if test='createType != null and createType == "2" '>
      ORDER BY create_time
    </if>
    LIMIT #{questions}
  </select>

  <select id="queryQuestions" resultMap="BaseResultMap">
    select
        <include refid="Base_Column_List" />
    FROM nbchat_exam_question
    where question_id in
    <foreach collection="questionIds" separator="," index="index" item="id" open="(" close=")">
      #{id}
    </foreach>
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from nbchat_exam_question
    where question_id = #{questionId,jdbcType=VARCHAR}
  </select>
  <select id="selectByCourseId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from nbchat_exam_question
    where course_id = #{courseId} and is_valid = '1'
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from nbchat_exam_question
    where question_id = #{questionId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamQuestion" >
    insert into nbchat_exam_question (question_id, course_id, tenant_code, 
      question_name, question_type, part_id, 
      create_time, is_valid, explan
      )
    values (#{questionId,jdbcType=VARCHAR}, #{courseId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, 
      #{questionName,jdbcType=VARCHAR}, #{questionType,jdbcType=CHAR}, #{partId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{isValid,jdbcType=CHAR}, #{explan,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamQuestion" >
    insert into nbchat_exam_question
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="questionId != null" >
        question_id,
      </if>
      <if test="courseId != null" >
        course_id,
      </if>
      <if test="tenantCode != null" >
        tenant_code,
      </if>
      <if test="questionName != null" >
        question_name,
      </if>
      <if test="questionType != null" >
        question_type,
      </if>
      <if test="partId != null" >
        part_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="isValid != null" >
        is_valid,
      </if>
      <if test="explan != null" >
        explan,
      </if>
      <if test="difficulty != null" >
        difficulty,
      </if>
      <if test="knowledges != null" >
        knowledges,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="questionId != null" >
        #{questionId,jdbcType=VARCHAR},
      </if>
      <if test="courseId != null" >
        #{courseId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="questionName != null" >
        #{questionName,jdbcType=VARCHAR},
      </if>
      <if test="questionType != null" >
        #{questionType,jdbcType=CHAR},
      </if>
      <if test="partId != null" >
        #{partId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isValid != null" >
        #{isValid,jdbcType=CHAR},
      </if>
      <if test="explan != null" >
        #{explan,jdbcType=VARCHAR},
      </if>
      <if test="difficulty != null" >
        #{difficulty,jdbcType=CHAR},
      </if>
      <if test="knowledges != null" >
        #{knowledges,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamQuestion" >
    update nbchat_exam_question
    <set >
      <if test="courseId != null" >
        course_id = #{courseId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null" >
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="questionName != null" >
        question_name = #{questionName,jdbcType=VARCHAR},
      </if>
      <if test="questionType != null" >
        question_type = #{questionType,jdbcType=CHAR},
      </if>
      <if test="partId != null" >
        part_id = #{partId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isValid != null" >
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
      <if test="explan != null" >
        explan = #{explan,jdbcType=VARCHAR},
      </if>
    </set>
    where question_id = #{questionId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.train.mapper.po.NbchatExamQuestion" >
    update nbchat_exam_question
    set course_id = #{courseId,jdbcType=VARCHAR},
      tenant_code = #{tenantCode,jdbcType=VARCHAR},
      question_name = #{questionName,jdbcType=VARCHAR},
      question_type = #{questionType,jdbcType=CHAR},
      part_id = #{partId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      is_valid = #{isValid,jdbcType=CHAR},
      explan = #{explan,jdbcType=VARCHAR}
    where question_id = #{questionId,jdbcType=VARCHAR}
  </update>
</mapper>