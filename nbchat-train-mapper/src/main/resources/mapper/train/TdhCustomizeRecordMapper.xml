<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhCustomizeRecordMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord">
    <!--@mbg.generated-->
    <!--@Table tdh_customize_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_status" jdbcType="CHAR" property="orderStatus" />
    <result column="origin_price" jdbcType="INTEGER" property="originPrice" />
    <result column="order_price" jdbcType="INTEGER" property="orderPrice" />
    <result column="pay_price" jdbcType="INTEGER" property="payPrice" />
    <result column="customize_type" jdbcType="CHAR" property="customizeType" />
    <result column="customize_status" jdbcType="CHAR" property="customizeStatus" />
    <result column="identity_url" jdbcType="VARCHAR" property="identityUrl" />
    <result column="voice_url" jdbcType="VARCHAR" property="voiceUrl" />
    <result column="voice_name" jdbcType="VARCHAR" property="voiceName" />
    <result column="voice_demo" jdbcType="VARCHAR" property="voiceDemo" />
    <result column="volc_id" jdbcType="VARCHAR" property="volcId" />
    <result column="tdh_img" jdbcType="VARCHAR" property="tdhImg" />
    <result column="tdh_id" jdbcType="VARCHAR" property="tdhId" />
    <result column="tdh_name" jdbcType="VARCHAR" property="tdhName" />
    <result column="tdh_demo" jdbcType="VARCHAR" property="tdhDemo" />
    <result column="gender" jdbcType="CHAR" property="gender" />
    <result column="pose_type" jdbcType="VARCHAR" property="poseType" />
    <result column="video_url" jdbcType="VARCHAR" property="videoUrl" />
    <result column="communication" jdbcType="VARCHAR" property="communication" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
    <result column="sku_id" jdbcType="VARCHAR" property="skuId" />
    <result column="origin_image" jdbcType="VARCHAR" property="originImage" />
    <result property="progressing" column="progressing" jdbcType="VARCHAR"/>
    <result property="extInfo" column="ext_info" jdbcType="VARCHAR"/>
  </resultMap>
  <resultMap id="listResultMap" type="com.tydic.nbchat.train.mapper.po.TdhCustomizeRecordCondition">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="user_id" jdbcType="VARCHAR" property="userId"/>
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
    <result column="tenant_name" jdbcType="VARCHAR" property="companyName"/>
    <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
    <result column="order_status" jdbcType="CHAR" property="orderStatus"/>
    <result column="origin_price" jdbcType="INTEGER" property="originPrice"/>
    <result column="order_price" jdbcType="INTEGER" property="orderPrice"/>
    <result column="pay_price" jdbcType="INTEGER" property="payPrice"/>
    <result column="customize_type" jdbcType="CHAR" property="customizeType"/>
    <result column="customize_status" jdbcType="CHAR" property="customizeStatus"/>
    <result column="identity_url" jdbcType="VARCHAR" property="identityUrl"/>
    <result column="voice_url" jdbcType="VARCHAR" property="voiceUrl"/>
    <result column="voice_name" jdbcType="VARCHAR" property="voiceName"/>
    <result column="voice_demo" jdbcType="VARCHAR" property="voiceDemo"/>
    <result column="volc_id" jdbcType="VARCHAR" property="volcId"/>
    <result column="tdh_img" jdbcType="VARCHAR" property="tdhImg"/>
    <result column="tdh_id" jdbcType="VARCHAR" property="tdhId"/>
    <result column="tdh_name" jdbcType="VARCHAR" property="tdhName"/>
    <result column="tdh_demo" jdbcType="VARCHAR" property="tdhDemo"/>
    <result column="gender" jdbcType="CHAR" property="gender"/>
    <result column="pose_type" jdbcType="VARCHAR" property="poseType"/>
    <result column="video_url" jdbcType="VARCHAR" property="videoUrl"/>
    <result column="communication" jdbcType="VARCHAR" property="communication"/>
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    <result column="is_valid" jdbcType="CHAR" property="isValid"/>
    <result column="sku_id" jdbcType="VARCHAR" property="skuId"/>
    <result column="origin_image" jdbcType="VARCHAR" property="originImage"/>
    <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
    <result column="first_view_time" jdbcType="TIMESTAMP" property="firstViewTime"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, user_id, tenant_code, order_no, order_status, origin_price, order_price, pay_price,
    customize_type, customize_status, identity_url, voice_url, voice_name, voice_demo,
    volc_id, tdh_img, tdh_id, tdh_name, tdh_demo, gender, pose_type, video_url, communication,
    start_time, end_time, create_time, update_time, update_user, is_valid, sku_id, origin_image,progressing,ext_info,first_view_time
  </sql>


  <update id="updateOrderStatus" >
    update tdh_customize_record
    set order_status = #{orderStatus},
        customize_status = #{customizeStatus},
        start_time = #{startTime},
        end_time = #{endTime}
      <if test="extInfo != null and extInfo != ''">
        ,ext_info = #{extInfo,jdbcType=VARCHAR}
      </if>
    where order_no = #{orderNo}
  </update>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from tdh_customize_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from tdh_customize_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tdh_customize_record (user_id, tenant_code, order_no,
      order_status, origin_price, order_price,
      pay_price, customize_type, customize_status,
      identity_url, voice_url, voice_name,
      voice_demo, volc_id, tdh_img,
      tdh_id, tdh_name, tdh_demo,
      gender, pose_type, video_url,
      communication, start_time, end_time,
      create_time, update_time, update_user,
      is_valid, sku_id, origin_image
      )
    values (#{userId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR},
      #{orderStatus,jdbcType=CHAR}, #{originPrice,jdbcType=INTEGER}, #{orderPrice,jdbcType=INTEGER},
      #{payPrice,jdbcType=INTEGER}, #{customizeType,jdbcType=CHAR}, #{customizeStatus,jdbcType=CHAR},
      #{identityUrl,jdbcType=VARCHAR}, #{voiceUrl,jdbcType=VARCHAR}, #{voiceName,jdbcType=VARCHAR},
      #{voiceDemo,jdbcType=VARCHAR}, #{volcId,jdbcType=VARCHAR}, #{tdhImg,jdbcType=VARCHAR},
      #{tdhId,jdbcType=VARCHAR}, #{tdhName,jdbcType=VARCHAR}, #{tdhDemo,jdbcType=VARCHAR},
      #{gender,jdbcType=CHAR}, #{poseType,jdbcType=VARCHAR}, #{videoUrl,jdbcType=VARCHAR},
      #{communication,jdbcType=VARCHAR}, #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUser,jdbcType=VARCHAR},
      #{isValid,jdbcType=CHAR}, #{skuId,jdbcType=VARCHAR}, #{originImage,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tdh_customize_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="originPrice != null">
        origin_price,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="payPrice != null">
        pay_price,
      </if>
      <if test="customizeType != null">
        customize_type,
      </if>
      <if test="customizeStatus != null">
        customize_status,
      </if>
      <if test="identityUrl != null">
        identity_url,
      </if>
      <if test="voiceUrl != null">
        voice_url,
      </if>
      <if test="voiceName != null">
        voice_name,
      </if>
      <if test="voiceDemo != null">
        voice_demo,
      </if>
      <if test="volcId != null">
        volc_id,
      </if>
      <if test="tdhImg != null">
        tdh_img,
      </if>
      <if test="tdhId != null">
        tdh_id,
      </if>
      <if test="tdhName != null">
        tdh_name,
      </if>
      <if test="tdhDemo != null">
        tdh_demo,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="poseType != null">
        pose_type,
      </if>
      <if test="videoUrl != null">
        video_url,
      </if>
      <if test="communication != null">
        communication,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="originImage != null">
        origin_image,
      </if>
      <if test="isCutout != null">
        is_cutout,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=CHAR},
      </if>
      <if test="originPrice != null">
        #{originPrice,jdbcType=INTEGER},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=INTEGER},
      </if>
      <if test="payPrice != null">
        #{payPrice,jdbcType=INTEGER},
      </if>
      <if test="customizeType != null">
        #{customizeType,jdbcType=CHAR},
      </if>
      <if test="customizeStatus != null">
        #{customizeStatus,jdbcType=CHAR},
      </if>
      <if test="identityUrl != null">
        #{identityUrl,jdbcType=VARCHAR},
      </if>
      <if test="voiceUrl != null">
        #{voiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="voiceName != null">
        #{voiceName,jdbcType=VARCHAR},
      </if>
      <if test="voiceDemo != null">
        #{voiceDemo,jdbcType=VARCHAR},
      </if>
      <if test="volcId != null">
        #{volcId,jdbcType=VARCHAR},
      </if>
      <if test="tdhImg != null">
        #{tdhImg,jdbcType=VARCHAR},
      </if>
      <if test="tdhId != null">
        #{tdhId,jdbcType=VARCHAR},
      </if>
      <if test="tdhName != null">
        #{tdhName,jdbcType=VARCHAR},
      </if>
      <if test="tdhDemo != null">
        #{tdhDemo,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=CHAR},
      </if>
      <if test="poseType != null">
        #{poseType,jdbcType=VARCHAR},
      </if>
      <if test="videoUrl != null">
        #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="communication != null">
        #{communication,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=CHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="originImage != null">
        #{originImage,jdbcType=VARCHAR},
      </if>
      <if test="isCutout != null">
        #{isCutout,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord">
    <!--@mbg.generated-->
    update tdh_customize_record
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=CHAR},
      </if>
      <if test="originPrice != null">
        origin_price = #{originPrice,jdbcType=INTEGER},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=INTEGER},
      </if>
      <if test="payPrice != null">
        pay_price = #{payPrice,jdbcType=INTEGER},
      </if>
      <if test="customizeType != null">
        customize_type = #{customizeType,jdbcType=CHAR},
      </if>
      <if test="customizeStatus != null">
        customize_status = #{customizeStatus,jdbcType=CHAR},
      </if>
      <if test="identityUrl != null">
        identity_url = #{identityUrl,jdbcType=VARCHAR},
      </if>
      <if test="voiceUrl != null">
        voice_url = #{voiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="voiceName != null">
        voice_name = #{voiceName,jdbcType=VARCHAR},
      </if>
      <if test="voiceDemo != null">
        voice_demo = #{voiceDemo,jdbcType=VARCHAR},
      </if>
      <if test="volcId != null">
        volc_id = #{volcId,jdbcType=VARCHAR},
      </if>
      <if test="tdhImg != null">
        tdh_img = #{tdhImg,jdbcType=VARCHAR},
      </if>
      <if test="tdhId != null">
        tdh_id = #{tdhId,jdbcType=VARCHAR},
      </if>
      <if test="tdhName != null">
        tdh_name = #{tdhName,jdbcType=VARCHAR},
      </if>
      <if test="tdhDemo != null">
        tdh_demo = #{tdhDemo,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=CHAR},
      </if>
      <if test="poseType != null">
        pose_type = #{poseType,jdbcType=VARCHAR},
      </if>
      <if test="videoUrl != null">
        video_url = #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="communication != null">
        communication = #{communication,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="originImage != null">
        origin_image = #{originImage,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord">
    <!--@mbg.generated-->
    update tdh_customize_record
    set user_id = #{userId,jdbcType=VARCHAR},
      tenant_code = #{tenantCode,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=CHAR},
      origin_price = #{originPrice,jdbcType=INTEGER},
      order_price = #{orderPrice,jdbcType=INTEGER},
      pay_price = #{payPrice,jdbcType=INTEGER},
      customize_type = #{customizeType,jdbcType=CHAR},
      customize_status = #{customizeStatus,jdbcType=CHAR},
      identity_url = #{identityUrl,jdbcType=VARCHAR},
      voice_url = #{voiceUrl,jdbcType=VARCHAR},
      voice_name = #{voiceName,jdbcType=VARCHAR},
      voice_demo = #{voiceDemo,jdbcType=VARCHAR},
      volc_id = #{volcId,jdbcType=VARCHAR},
      tdh_img = #{tdhImg,jdbcType=VARCHAR},
      tdh_id = #{tdhId,jdbcType=VARCHAR},
      tdh_name = #{tdhName,jdbcType=VARCHAR},
      tdh_demo = #{tdhDemo,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=CHAR},
      pose_type = #{poseType,jdbcType=VARCHAR},
      video_url = #{videoUrl,jdbcType=VARCHAR},
      communication = #{communication,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      is_valid = #{isValid,jdbcType=CHAR},
      sku_id = #{skuId,jdbcType=VARCHAR},
      origin_image = #{originImage,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update tdh_customize_record
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tenant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tenantCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderNo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.orderNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderStatus != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.orderStatus,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="origin_price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.originPrice != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.originPrice,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderPrice != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.orderPrice,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="pay_price = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payPrice != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.payPrice,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="customize_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customizeType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.customizeType,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="customize_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.customizeStatus != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.customizeStatus,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="identity_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.identityUrl != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.identityUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="voice_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.voiceUrl != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.voiceUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="voice_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.voiceName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.voiceName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="voice_demo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.voiceDemo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.voiceDemo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="volc_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.volcId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.volcId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tdh_img = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tdhImg != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tdhImg,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tdh_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tdhId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tdhId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tdh_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tdhName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tdhName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tdh_demo = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tdhDemo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tdhDemo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="gender = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.gender != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.gender,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="pose_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.poseType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.poseType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="video_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.videoUrl != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.videoUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="communication = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.communication != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.communication,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="start_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.startTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.startTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="end_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.endTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.endTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_user = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateUser != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateUser,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_valid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isValid != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isValid,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sku_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.skuId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.skuId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="origin_image = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.originImage != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.originImage,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tdh_customize_record
    (user_id, tenant_code, order_no, order_status, origin_price, order_price, pay_price,
      customize_type, customize_status, identity_url, voice_url, voice_name, voice_demo,
      volc_id, tdh_img, tdh_id, tdh_name, tdh_demo, gender, pose_type, video_url, communication,
      start_time, end_time, create_time, update_time, update_user, is_valid, sku_id,
      origin_image)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.userId,jdbcType=VARCHAR}, #{item.tenantCode,jdbcType=VARCHAR}, #{item.orderNo,jdbcType=VARCHAR},
        #{item.orderStatus,jdbcType=CHAR}, #{item.originPrice,jdbcType=INTEGER}, #{item.orderPrice,jdbcType=INTEGER},
        #{item.payPrice,jdbcType=INTEGER}, #{item.customizeType,jdbcType=CHAR}, #{item.customizeStatus,jdbcType=CHAR},
        #{item.identityUrl,jdbcType=VARCHAR}, #{item.voiceUrl,jdbcType=VARCHAR}, #{item.voiceName,jdbcType=VARCHAR},
        #{item.voiceDemo,jdbcType=VARCHAR}, #{item.volcId,jdbcType=VARCHAR}, #{item.tdhImg,jdbcType=VARCHAR},
        #{item.tdhId,jdbcType=VARCHAR}, #{item.tdhName,jdbcType=VARCHAR}, #{item.tdhDemo,jdbcType=VARCHAR},
        #{item.gender,jdbcType=CHAR}, #{item.poseType,jdbcType=VARCHAR}, #{item.videoUrl,jdbcType=VARCHAR},
        #{item.communication,jdbcType=VARCHAR}, #{item.startTime,jdbcType=TIMESTAMP}, #{item.endTime,jdbcType=TIMESTAMP},
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.updateUser,jdbcType=VARCHAR},
        #{item.isValid,jdbcType=CHAR}, #{item.skuId,jdbcType=VARCHAR}, #{item.originImage,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from tdh_customize_record where id in
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    <foreach collection="list" item="item" separator=";">
      update tdh_customize_record
      <set>
        <if test="item.userId != null">
          user_id = #{item.userId,jdbcType=VARCHAR},
        </if>
        <if test="item.tenantCode != null">
          tenant_code = #{item.tenantCode,jdbcType=VARCHAR},
        </if>
        <if test="item.orderNo != null">
          order_no = #{item.orderNo,jdbcType=VARCHAR},
        </if>
        <if test="item.orderStatus != null">
          order_status = #{item.orderStatus,jdbcType=CHAR},
        </if>
        <if test="item.originPrice != null">
          origin_price = #{item.originPrice,jdbcType=INTEGER},
        </if>
        <if test="item.orderPrice != null">
          order_price = #{item.orderPrice,jdbcType=INTEGER},
        </if>
        <if test="item.payPrice != null">
          pay_price = #{item.payPrice,jdbcType=INTEGER},
        </if>
        <if test="item.customizeType != null">
          customize_type = #{item.customizeType,jdbcType=CHAR},
        </if>
        <if test="item.customizeStatus != null">
          customize_status = #{item.customizeStatus,jdbcType=CHAR},
        </if>
        <if test="item.identityUrl != null">
          identity_url = #{item.identityUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.voiceUrl != null">
          voice_url = #{item.voiceUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.voiceName != null">
          voice_name = #{item.voiceName,jdbcType=VARCHAR},
        </if>
        <if test="item.voiceDemo != null">
          voice_demo = #{item.voiceDemo,jdbcType=VARCHAR},
        </if>
        <if test="item.volcId != null">
          volc_id = #{item.volcId,jdbcType=VARCHAR},
        </if>
        <if test="item.tdhImg != null">
          tdh_img = #{item.tdhImg,jdbcType=VARCHAR},
        </if>
        <if test="item.tdhId != null">
          tdh_id = #{item.tdhId,jdbcType=VARCHAR},
        </if>
        <if test="item.tdhName != null">
          tdh_name = #{item.tdhName,jdbcType=VARCHAR},
        </if>
        <if test="item.tdhDemo != null">
          tdh_demo = #{item.tdhDemo,jdbcType=VARCHAR},
        </if>
        <if test="item.gender != null">
          gender = #{item.gender,jdbcType=CHAR},
        </if>
        <if test="item.poseType != null">
          pose_type = #{item.poseType,jdbcType=VARCHAR},
        </if>
        <if test="item.videoUrl != null">
          video_url = #{item.videoUrl,jdbcType=VARCHAR},
        </if>
        <if test="item.communication != null">
          communication = #{item.communication,jdbcType=VARCHAR},
        </if>
        <if test="item.startTime != null">
          start_time = #{item.startTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.endTime != null">
          end_time = #{item.endTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateUser != null">
          update_user = #{item.updateUser,jdbcType=VARCHAR},
        </if>
        <if test="item.isValid != null">
          is_valid = #{item.isValid,jdbcType=CHAR},
        </if>
        <if test="item.skuId != null">
          sku_id = #{item.skuId,jdbcType=VARCHAR},
        </if>
        <if test="item.originImage != null">
          origin_image = #{item.originImage,jdbcType=VARCHAR},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsertSelectiveUseDefaultForNull" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tdh_customize_record
    (user_id, tenant_code, order_no, order_status, origin_price, order_price, pay_price,
      customize_type, customize_status, identity_url, voice_url, voice_name, voice_demo,
      volc_id, tdh_img, tdh_id, tdh_name, tdh_demo, gender, pose_type, video_url, communication,
      start_time, end_time, create_time, update_time, update_user, is_valid, sku_id,
      origin_image)
    values
    <foreach collection="list" item="item" separator=",">
      (
      <choose>
        <when test="item.userId != null">
          #{item.userId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.tenantCode != null">
          #{item.tenantCode,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.orderNo != null">
          #{item.orderNo,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.orderStatus != null">
          #{item.orderStatus,jdbcType=CHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.originPrice != null">
          #{item.originPrice,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.orderPrice != null">
          #{item.orderPrice,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.payPrice != null">
          #{item.payPrice,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.customizeType != null">
          #{item.customizeType,jdbcType=CHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.customizeStatus != null">
          #{item.customizeStatus,jdbcType=CHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.identityUrl != null">
          #{item.identityUrl,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.voiceUrl != null">
          #{item.voiceUrl,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.voiceName != null">
          #{item.voiceName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.voiceDemo != null">
          #{item.voiceDemo,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.volcId != null">
          #{item.volcId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.tdhImg != null">
          #{item.tdhImg,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.tdhId != null">
          #{item.tdhId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.tdhName != null">
          #{item.tdhName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.tdhDemo != null">
          #{item.tdhDemo,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.gender != null">
          #{item.gender,jdbcType=CHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.poseType != null">
          #{item.poseType,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.videoUrl != null">
          #{item.videoUrl,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.communication != null">
          #{item.communication,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.startTime != null">
          #{item.startTime,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.endTime != null">
          #{item.endTime,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createTime != null">
          #{item.createTime,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updateTime != null">
          #{item.updateTime,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updateUser != null">
          #{item.updateUser,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.isValid != null">
          #{item.isValid,jdbcType=CHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.skuId != null">
          #{item.skuId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.originImage != null">
          #{item.originImage,jdbcType=VARCHAR}
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tdh_customize_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      user_id,
      tenant_code,
      order_no,
      order_status,
      origin_price,
      order_price,
      pay_price,
      customize_type,
      customize_status,
      identity_url,
      voice_url,
      voice_name,
      voice_demo,
      volc_id,
      tdh_img,
      tdh_id,
      tdh_name,
      tdh_demo,
      gender,
      pose_type,
      video_url,
      communication,
      start_time,
      end_time,
      create_time,
      update_time,
      update_user,
      is_valid,
      sku_id,
      origin_image,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{userId,jdbcType=VARCHAR},
      #{tenantCode,jdbcType=VARCHAR},
      #{orderNo,jdbcType=VARCHAR},
      #{orderStatus,jdbcType=CHAR},
      #{originPrice,jdbcType=INTEGER},
      #{orderPrice,jdbcType=INTEGER},
      #{payPrice,jdbcType=INTEGER},
      #{customizeType,jdbcType=CHAR},
      #{customizeStatus,jdbcType=CHAR},
      #{identityUrl,jdbcType=VARCHAR},
      #{voiceUrl,jdbcType=VARCHAR},
      #{voiceName,jdbcType=VARCHAR},
      #{voiceDemo,jdbcType=VARCHAR},
      #{volcId,jdbcType=VARCHAR},
      #{tdhImg,jdbcType=VARCHAR},
      #{tdhId,jdbcType=VARCHAR},
      #{tdhName,jdbcType=VARCHAR},
      #{tdhDemo,jdbcType=VARCHAR},
      #{gender,jdbcType=CHAR},
      #{poseType,jdbcType=VARCHAR},
      #{videoUrl,jdbcType=VARCHAR},
      #{communication,jdbcType=VARCHAR},
      #{startTime,jdbcType=TIMESTAMP},
      #{endTime,jdbcType=TIMESTAMP},
      #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP},
      #{updateUser,jdbcType=VARCHAR},
      #{isValid,jdbcType=CHAR},
      #{skuId,jdbcType=VARCHAR},
      #{originImage,jdbcType=VARCHAR},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      user_id = #{userId,jdbcType=VARCHAR},
      tenant_code = #{tenantCode,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=CHAR},
      origin_price = #{originPrice,jdbcType=INTEGER},
      order_price = #{orderPrice,jdbcType=INTEGER},
      pay_price = #{payPrice,jdbcType=INTEGER},
      customize_type = #{customizeType,jdbcType=CHAR},
      customize_status = #{customizeStatus,jdbcType=CHAR},
      identity_url = #{identityUrl,jdbcType=VARCHAR},
      voice_url = #{voiceUrl,jdbcType=VARCHAR},
      voice_name = #{voiceName,jdbcType=VARCHAR},
      voice_demo = #{voiceDemo,jdbcType=VARCHAR},
      volc_id = #{volcId,jdbcType=VARCHAR},
      tdh_img = #{tdhImg,jdbcType=VARCHAR},
      tdh_id = #{tdhId,jdbcType=VARCHAR},
      tdh_name = #{tdhName,jdbcType=VARCHAR},
      tdh_demo = #{tdhDemo,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=CHAR},
      pose_type = #{poseType,jdbcType=VARCHAR},
      video_url = #{videoUrl,jdbcType=VARCHAR},
      communication = #{communication,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_user = #{updateUser,jdbcType=VARCHAR},
      is_valid = #{isValid,jdbcType=CHAR},
      sku_id = #{skuId,jdbcType=VARCHAR},
      origin_image = #{originImage,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.train.mapper.po.TdhCustomizeRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into tdh_customize_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="originPrice != null">
        origin_price,
      </if>
      <if test="orderPrice != null">
        order_price,
      </if>
      <if test="payPrice != null">
        pay_price,
      </if>
      <if test="customizeType != null">
        customize_type,
      </if>
      <if test="customizeStatus != null">
        customize_status,
      </if>
      <if test="identityUrl != null">
        identity_url,
      </if>
      <if test="voiceUrl != null">
        voice_url,
      </if>
      <if test="voiceName != null">
        voice_name,
      </if>
      <if test="voiceDemo != null">
        voice_demo,
      </if>
      <if test="volcId != null">
        volc_id,
      </if>
      <if test="tdhImg != null">
        tdh_img,
      </if>
      <if test="tdhId != null">
        tdh_id,
      </if>
      <if test="tdhName != null">
        tdh_name,
      </if>
      <if test="tdhDemo != null">
        tdh_demo,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="poseType != null">
        pose_type,
      </if>
      <if test="videoUrl != null">
        video_url,
      </if>
      <if test="communication != null">
        communication,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="skuId != null">
        sku_id,
      </if>
      <if test="originImage != null">
        origin_image,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=CHAR},
      </if>
      <if test="originPrice != null">
        #{originPrice,jdbcType=INTEGER},
      </if>
      <if test="orderPrice != null">
        #{orderPrice,jdbcType=INTEGER},
      </if>
      <if test="payPrice != null">
        #{payPrice,jdbcType=INTEGER},
      </if>
      <if test="customizeType != null">
        #{customizeType,jdbcType=CHAR},
      </if>
      <if test="customizeStatus != null">
        #{customizeStatus,jdbcType=CHAR},
      </if>
      <if test="identityUrl != null">
        #{identityUrl,jdbcType=VARCHAR},
      </if>
      <if test="voiceUrl != null">
        #{voiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="voiceName != null">
        #{voiceName,jdbcType=VARCHAR},
      </if>
      <if test="voiceDemo != null">
        #{voiceDemo,jdbcType=VARCHAR},
      </if>
      <if test="volcId != null">
        #{volcId,jdbcType=VARCHAR},
      </if>
      <if test="tdhImg != null">
        #{tdhImg,jdbcType=VARCHAR},
      </if>
      <if test="tdhId != null">
        #{tdhId,jdbcType=VARCHAR},
      </if>
      <if test="tdhName != null">
        #{tdhName,jdbcType=VARCHAR},
      </if>
      <if test="tdhDemo != null">
        #{tdhDemo,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=CHAR},
      </if>
      <if test="poseType != null">
        #{poseType,jdbcType=VARCHAR},
      </if>
      <if test="videoUrl != null">
        #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="communication != null">
        #{communication,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=CHAR},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="originImage != null">
        #{originImage,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=CHAR},
      </if>
      <if test="originPrice != null">
        origin_price = #{originPrice,jdbcType=INTEGER},
      </if>
      <if test="orderPrice != null">
        order_price = #{orderPrice,jdbcType=INTEGER},
      </if>
      <if test="payPrice != null">
        pay_price = #{payPrice,jdbcType=INTEGER},
      </if>
      <if test="customizeType != null">
        customize_type = #{customizeType,jdbcType=CHAR},
      </if>
      <if test="customizeStatus != null">
        customize_status = #{customizeStatus,jdbcType=CHAR},
      </if>
      <if test="identityUrl != null">
        identity_url = #{identityUrl,jdbcType=VARCHAR},
      </if>
      <if test="voiceUrl != null">
        voice_url = #{voiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="voiceName != null">
        voice_name = #{voiceName,jdbcType=VARCHAR},
      </if>
      <if test="voiceDemo != null">
        voice_demo = #{voiceDemo,jdbcType=VARCHAR},
      </if>
      <if test="volcId != null">
        volc_id = #{volcId,jdbcType=VARCHAR},
      </if>
      <if test="tdhImg != null">
        tdh_img = #{tdhImg,jdbcType=VARCHAR},
      </if>
      <if test="tdhId != null">
        tdh_id = #{tdhId,jdbcType=VARCHAR},
      </if>
      <if test="tdhName != null">
        tdh_name = #{tdhName,jdbcType=VARCHAR},
      </if>
      <if test="tdhDemo != null">
        tdh_demo = #{tdhDemo,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=CHAR},
      </if>
      <if test="poseType != null">
        pose_type = #{poseType,jdbcType=VARCHAR},
      </if>
      <if test="videoUrl != null">
        video_url = #{videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="communication != null">
        communication = #{communication,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
      <if test="skuId != null">
        sku_id = #{skuId,jdbcType=VARCHAR},
      </if>
      <if test="originImage != null">
        origin_image = #{originImage,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

    <select id="findByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tdh_customize_record
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and order_status = #{orderStatus,jdbcType=CHAR}
            </if>
            <if test="originPrice != null">
                and origin_price = #{originPrice,jdbcType=INTEGER}
            </if>
            <if test="orderPrice != null">
                and order_price = #{orderPrice,jdbcType=INTEGER}
            </if>
            <if test="payPrice != null">
                and pay_price = #{payPrice,jdbcType=INTEGER}
            </if>
            <if test="customizeType != null and customizeType != '' and customizeType != 'video'">
                and customize_type = #{customizeType,jdbcType=CHAR}
            </if>
            <if test="customizeType != null and customizeType != '' and customizeType == 'video' ">
              and customize_type in ('2d','2.5d_mtk')
            </if>
            <if test="customizeStatus != null and customizeStatus != ''">
                and customize_status = #{customizeStatus,jdbcType=CHAR}
            </if>
            <if test="identityUrl != null and identityUrl != ''">
                and identity_url = #{identityUrl,jdbcType=VARCHAR}
            </if>
            <if test="voiceUrl != null and voiceUrl != ''">
                and voice_url = #{voiceUrl,jdbcType=VARCHAR}
            </if>
            <if test="voiceName != null and voiceName != ''">
                and voice_name = #{voiceName,jdbcType=VARCHAR}
            </if>
            <if test="voiceDemo != null and voiceDemo != ''">
                and voice_demo = #{voiceDemo,jdbcType=VARCHAR}
            </if>
            <if test="volcId != null and volcId != ''">
                and volc_id = #{volcId,jdbcType=VARCHAR}
            </if>
            <if test="tdhImg != null and tdhImg != ''">
                and tdh_img = #{tdhImg,jdbcType=VARCHAR}
            </if>
            <if test="tdhId != null and tdhId != ''">
                and tdh_id = #{tdhId,jdbcType=VARCHAR}
            </if>
            <if test="tdhName != null and tdhName != ''">
                and tdh_name = #{tdhName,jdbcType=VARCHAR}
            </if>
            <if test="tdhDemo != null and tdhDemo != ''">
                and tdh_demo = #{tdhDemo,jdbcType=VARCHAR}
            </if>
            <if test="gender != null and gender != ''">
                and gender = #{gender,jdbcType=CHAR}
            </if>
            <if test="poseType != null and poseType != ''">
                and pose_type = #{poseType,jdbcType=VARCHAR}
            </if>
            <if test="videoUrl != null and videoUrl != ''">
                and video_url = #{videoUrl,jdbcType=VARCHAR}
            </if>
            <if test="communication != null and communication != ''">
                and communication = #{communication,jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                and start_time = #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                and end_time = #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUser != null and updateUser != ''">
                and update_user = #{updateUser,jdbcType=VARCHAR}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid,jdbcType=CHAR}
            </if>
        </where>
        order by GREATEST(create_time,COALESCE(update_time,create_time)) desc
    </select>

    <select id="list" resultMap="listResultMap">
        select  tcr.id,
        tcr.user_id,
        tcr.tenant_code,
        tenant.tenant_name,
        tcr.order_no,
        tcr.order_status,
        tcr.origin_price,
        tcr.order_price,
        tcr.pay_price,
        tcr.customize_type,
        tcr.customize_status,
        tcr.identity_url,
        tcr.voice_url,
        tcr.voice_name,
        tcr.voice_demo,
        tcr.volc_id,
        tcr.tdh_img,
        tcr.tdh_id,
        tcr.tdh_name,
        tcr.tdh_demo,
        tcr.gender,
        tcr.pose_type,
        tcr.video_url,
        tcr.communication,
        tcr.start_time,
        tcr.end_time,
        tcr.create_time,
        tcr.update_time,
        tcr.update_user,
        tcr.is_valid,
        tcr.sku_id,
        tcr.origin_image,
        tcr.first_view_time,
        tenant.tenant_name as company_name
        from nbchat_sys_tenant tenant
                 left join tdh_customize_record tcr on tenant.tenant_code = tcr.tenant_code
        <where>
            <if test="userId != null and userId != ''">
                and tcr.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and tcr.order_no = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="orderStatus != null and orderStatus != ''">
                and tcr.order_status = #{orderStatus,jdbcType=CHAR}
            </if>
            <if test="customizeType != null and customizeType != ''">
                and tcr.customize_type = #{customizeType,jdbcType=CHAR}
            </if>
            <if test="customizeType == null or customizeType == ''">
                and tcr.customize_type != 'audio'
            </if>
            <if test="customizeStatus != null and customizeStatus != '' ">
                and tcr.customize_status = #{customizeStatus,jdbcType=CHAR}
            </if>
            <if test="userType != null and userType != '' and userType == 0">
                and tcr.tenant_code = '00000000'
            </if>
            <if test="userType != null and userType != '' and userType == 1">
                and tcr.tenant_code != '00000000'
            </if>
            <if test="companyName != null and companyName != ''">
                and tenant.tenant_name like concat('%', #{companyName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="isValid != null and isValid != ''">
                and tcr.is_valid = #{isValid,jdbcType=CHAR}
            </if>
        </where>
      order by GREATEST(tcr.create_time,COALESCE(tcr.update_time,tcr.create_time)) desc
    </select>

    <select id="findByOrderNoAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tdh_customize_record
        where order_no=#{orderNo,jdbcType=VARCHAR} and user_id=#{userId,jdbcType=VARCHAR}
    </select>

  <update id="updateByOrderNo">
        update tdh_customize_record
        <set>
            <if test="updated.orderNo != null and updated.orderNo != ''">
                order_no = #{updated.orderNo,jdbcType=VARCHAR},
            </if>
            <if test="updated.orderStatus != null and updated.orderStatus != ''">
                order_status = #{updated.orderStatus,jdbcType=CHAR},
            </if>
            <if test="updated.originPrice != null">
                origin_price = #{updated.originPrice,jdbcType=INTEGER},
            </if>
            <if test="updated.orderPrice != null">
                order_price = #{updated.orderPrice,jdbcType=INTEGER},
            </if>
            <if test="updated.payPrice != null">
                pay_price = #{updated.payPrice,jdbcType=INTEGER},
            </if>
            <if test="updated.customizeStatus != null and updated.customizeStatus != ''">
                customize_status = #{updated.customizeStatus,jdbcType=CHAR},
            </if>
            <if test="updated.identityUrl != null and updated.identityUrl != ''">
                identity_url = #{updated.identityUrl,jdbcType=VARCHAR},
            </if>
            <if test="updated.voiceUrl != null and updated.voiceUrl != ''">
                voice_url = #{updated.voiceUrl,jdbcType=VARCHAR},
            </if>
            <if test="updated.voiceName != null and updated.voiceName != ''">
                voice_name = #{updated.voiceName,jdbcType=VARCHAR},
            </if>
            <if test="updated.voiceDemo != null and updated.voiceDemo != ''">
                voice_demo = #{updated.voiceDemo,jdbcType=VARCHAR},
            </if>
            <if test="updated.volcId != null and updated.volcId != ''">
                volc_id = #{updated.volcId,jdbcType=VARCHAR},
            </if>
            <if test="updated.tdhImg != null and updated.tdhImg != ''">
                tdh_img = #{updated.tdhImg,jdbcType=VARCHAR},
            </if>
            <if test="updated.tdhId != null and updated.tdhId != ''">
                tdh_id = #{updated.tdhId,jdbcType=VARCHAR},
            </if>
            <if test="updated.tdhName != null and updated.tdhName != ''">
                tdh_name = #{updated.tdhName,jdbcType=VARCHAR},
            </if>
            <if test="updated.tdhDemo != null and updated.tdhDemo != ''">
                tdh_demo = #{updated.tdhDemo,jdbcType=VARCHAR},
            </if>
            <if test="updated.gender != null and updated.gender != ''">
                gender = #{updated.gender,jdbcType=CHAR},
            </if>
            <if test="updated.poseType != null and updated.poseType != ''">
                pose_type = #{updated.poseType,jdbcType=VARCHAR},
            </if>
            <if test="updated.videoUrl != null and updated.videoUrl != ''">
                video_url = #{updated.videoUrl,jdbcType=VARCHAR},
            </if>
            <if test="updated.communication != null and updated.communication != ''">
                communication = #{updated.communication,jdbcType=VARCHAR},
            </if>
            <if test="updated.startTime != null">
                start_time = #{updated.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.endTime != null">
                end_time = #{updated.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updated.updateUser != null and updated.updateUser != ''">
                update_user = #{updated.updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updated.isValid != null and updated.isValid != ''">
                is_valid = #{updated.isValid,jdbcType=CHAR},
            </if>
            <if test="updated.skuId != null and updated.skuId != ''">
                sku_id = #{updated.skuId,jdbcType=VARCHAR},
            </if>
            <if test="updated.originImage != null and updated.originImage != ''">
                origin_image = #{updated.originImage,jdbcType=VARCHAR},
            </if>
            <if test="updated.firstViewTime != null">
              first_view_time = #{updated.firstViewTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where order_no=#{orderNo,jdbcType=VARCHAR}
    </update>

  <select id="findByOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from tdh_customize_record
    where order_no=#{orderNo,jdbcType=VARCHAR}
  </select>

  <update id="updateViewTime">
    update tdh_customize_record set first_view_time = now()
    where order_no=#{orderNo} and user_id=#{userId} and first_view_time is null
  </update>

  <update id="updateByOrderNoAndUserId">
    update tdh_customize_record
    <set>
      <if test="updated.orderNo != null and updated.orderNo != ''">
        order_no = #{updated.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="updated.orderStatus != null and updated.orderStatus != ''">
        order_status = #{updated.orderStatus,jdbcType=CHAR},
      </if>
      <if test="updated.originPrice != null">
        origin_price = #{updated.originPrice,jdbcType=INTEGER},
      </if>
      <if test="updated.orderPrice != null">
        order_price = #{updated.orderPrice,jdbcType=INTEGER},
      </if>
      <if test="updated.payPrice != null">
        pay_price = #{updated.payPrice,jdbcType=INTEGER},
      </if>
      <if test="updated.customizeType != null and updated.customizeType != ''">
        customize_type = #{updated.customizeType,jdbcType=CHAR},
      </if>
      <if test="updated.customizeStatus != null and updated.customizeStatus != ''">
        customize_status = #{updated.customizeStatus,jdbcType=CHAR},
      </if>
      <if test="updated.identityUrl != null and updated.identityUrl != ''">
        identity_url = #{updated.identityUrl,jdbcType=VARCHAR},
      </if>
      <if test="updated.voiceUrl != null and updated.voiceUrl != ''">
        voice_url = #{updated.voiceUrl,jdbcType=VARCHAR},
      </if>
      <if test="updated.voiceName != null and updated.voiceName != ''">
        voice_name = #{updated.voiceName,jdbcType=VARCHAR},
      </if>
      <if test="updated.voiceDemo != null and updated.voiceDemo != ''">
        voice_demo = #{updated.voiceDemo,jdbcType=VARCHAR},
      </if>
      <if test="updated.volcId != null and updated.volcId != ''">
        volc_id = #{updated.volcId,jdbcType=VARCHAR},
      </if>
      <if test="updated.tdhImg != null and updated.tdhImg != ''">
        tdh_img = #{updated.tdhImg,jdbcType=VARCHAR},
      </if>
      <if test="updated.tdhId != null and updated.tdhId != ''">
        tdh_id = #{updated.tdhId,jdbcType=VARCHAR},
      </if>
      <if test="updated.tdhName != null and updated.tdhName != ''">
        tdh_name = #{updated.tdhName,jdbcType=VARCHAR},
      </if>
      <if test="updated.tdhDemo != null and updated.tdhDemo != ''">
        tdh_demo = #{updated.tdhDemo,jdbcType=VARCHAR},
      </if>
      <if test="updated.gender != null and updated.gender != ''">
        gender = #{updated.gender,jdbcType=CHAR},
      </if>
      <if test="updated.poseType != null and updated.poseType != ''">
        pose_type = #{updated.poseType,jdbcType=VARCHAR},
      </if>
      <if test="updated.videoUrl != null and updated.videoUrl != ''">
        video_url = #{updated.videoUrl,jdbcType=VARCHAR},
      </if>
      <if test="updated.communication != null and updated.communication != ''">
        communication = #{updated.communication,jdbcType=VARCHAR},
      </if>
      <if test="updated.startTime != null">
        start_time = #{updated.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.endTime != null">
        end_time = #{updated.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.createTime != null">
        create_time = #{updated.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.updateTime != null">
        update_time = #{updated.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updated.updateUser != null and updated.updateUser != ''">
        update_user = #{updated.updateUser,jdbcType=VARCHAR},
      </if>
      <if test="updated.isValid != null and updated.isValid != ''">
        is_valid = #{updated.isValid,jdbcType=CHAR},
      </if>
      <if test="updated.skuId != null and updated.skuId != ''">
        sku_id = #{updated.skuId,jdbcType=VARCHAR},
      </if>
      <if test="updated.originImage != null and updated.originImage != ''">
        origin_image = #{updated.originImage,jdbcType=VARCHAR},
      </if>
    </set>
    where order_no=#{updated.orderNo,jdbcType=VARCHAR} and user_id=#{updated.userId,jdbcType=VARCHAR}
  </update>
</mapper>