<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.PptLayoutMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.PptLayout" id="PptLayoutMap">
        <result property="layoutId" column="layout_id" jdbcType="VARCHAR"/>
        <result property="layoutName" column="layout_name" jdbcType="VARCHAR"/>
        <result property="themeId" column="theme_id" jdbcType="VARCHAR"/>
        <result property="layoutType" column="layout_type" jdbcType="VARCHAR"/>
        <result property="layoutSource" column="layout_source" jdbcType="VARCHAR"/>
        <result property="layoutCount" column="layout_count" jdbcType="INTEGER"/>
        <result property="layoutContent" column="layout_content" jdbcType="VARCHAR"/>
        <result property="color" column="color" jdbcType="VARCHAR"/>
        <result property="category" column="category" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" javaType="Integer"/>
        <result property="orderIndex" column="order_index" jdbcType="INTEGER"/>
        <result property="isAdaptive" column="is_adaptive" jdbcType="VARCHAR"/>
        <result property="isImage" column="is_image" jdbcType="VARCHAR"/>
        <result property="imageNum" column="image_num" jdbcType="VARCHAR"/>
        <result property="previewUrl" column="preview_url" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="isDefault" column="is_default" jdbcType="VARCHAR"/>
        <result property="themeState" column="theme_state" jdbcType="VARCHAR"/>
        <result property="composeType" column="compose_type" jdbcType="VARCHAR"/>
        <result property="vipFlag" column="vip_flag" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
layout_id, layout_name, theme_id, layout_type, layout_source, layout_count, layout_content, color, category, sort,order_index,
         is_adaptive, is_image,image_num, preview_url, tenant_code, user_id, create_time, update_time, is_valid, is_default,theme_state,compose_type,vip_flag</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="PptLayoutMap">
        select
          <include refid="Base_Column_List" />
        from ppt_layout
        where layout_id = #{layoutId}
    </select>
    
    
    <select id="selectAll" resultMap="PptLayoutMap" parameterType="com.tydic.nbchat.train.mapper.po.PptLayoutSelectCondition">
        select
          <include refid="Base_Column_List" />
        from ppt_layout
         <where>
            <if test="layoutId != null and layoutId != ''">
                and layout_id = #{layoutId}
            </if>
            <choose>
                <when test='themeId != null and themeId != "" and composeType == "1"'>
                    and (theme_id = #{themeId} or compose_type = '1')
                </when>
                <when test="themeId != null and themeId != '' and (composeType == '' or composeType == null)">
                    and theme_id = #{themeId}
                </when>
            </choose>
            <if test="layoutType != null and layoutType != ''">
                and layout_type = #{layoutType}
            </if>
            <if test="layoutCount != null">
                and layout_count = #{layoutCount}
            </if>
            <if test="color != null and color != ''">
                and color = #{color}
            </if>
            <if test="category != null and category != ''">
                and category = #{category}
            </if>
            <if test="isAdaptive != null and isAdaptive != ''">
                and is_adaptive = #{isAdaptive}
            </if>
            <if test="isImage != null and isImage != ''">
                and is_image = #{isImage}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                 <!-- 私有化定制不能被其他人看到, 系统提供通用模板 user_id = 00000000 -->
                 and (
                 ( user_id = 'public' and layout_source = '0' )
                 <!-- user_id = #{tenantCode} 企业通用 -->
                 or ( user_id = #{tenantCode} and layout_source = '0' )
                 <!-- 个人定制 -->
                 or ( tenant_code = #{tenantCode} and user_id = #{userId} and layout_source = '1' )
                 )
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="isDefault != null and isDefault != ''">
                and is_default = #{isDefault}
            </if>
            <if test="themeState != null and themeState != ''">
                and theme_state = #{themeState}
            </if>
             <if test="vipFlag != null and vipFlag != ''">
                and vip_flag = #{vipFlag}
            </if>
             <!--<if test="composeType != null and composeType != ''">
                 and compose_type = #{composeType}
             </if>-->
        </where>
        order by sort asc
    </select>


    <insert id="insertSelective" keyProperty="layoutId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.PptLayout">
        insert into ppt_layout
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="layoutId != null and layoutId != ''">
                layout_id,
            </if>
            <if test="layoutName != null and layoutName != ''">
                layout_name,
            </if>
            <if test="themeId != null and themeId != ''">
                theme_id,
            </if>
            <if test="layoutType != null and layoutType != ''">
                layout_type,
            </if>
            <if test="layoutSource != null and layoutSource != ''">
                layout_source,
            </if>
            <if test="layoutCount != null">
                layout_count,
            </if>
            <if test="layoutContent != null and layoutContent != ''">
                layout_content,
            </if>
            <if test="color != null and color != ''">
                color,
            </if>
            <if test="category != null and category != ''">
                category,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="isAdaptive != null and isAdaptive != ''">
                is_adaptive,
            </if>
            <if test="isImage != null and isImage != ''">
                is_image,
            </if>
            <if test="imageNum !=null and imageNum !='' ">
                image_num,
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                preview_url,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
            <if test="isDefault != null and isDefault != ''">
                is_default,
            </if>
            <if test="themeState != null and themeState != ''">
                theme_state,
            </if>
            <if test="composeType != null and composeType != ''">
                compose_type,
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                vip_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="layoutId != null and layoutId != ''">
                #{layoutId},
            </if>
            <if test="layoutName != null and layoutName != ''">
                #{layoutName},
            </if>
            <if test="themeId != null and themeId != ''">
                #{themeId},
            </if>
            <if test="layoutType != null and layoutType != ''">
                #{layoutType},
            </if>
            <if test="layoutSource != null and layoutSource != ''">
                #{layoutSource},
            </if>
            <if test="layoutCount != null">
                #{layoutCount},
            </if>
            <if test="layoutContent != null and layoutContent != ''">
                #{layoutContent},
            </if>
            <if test="color != null and color != ''">
                #{color},
            </if>
            <if test="category != null and category != ''">
                #{category},
            </if>
            <if test="sort != null">
                #{sort},
            </if>
            <if test="orderIndex != null">
                #{orderIndex},
            </if>
            <if test="isAdaptive != null and isAdaptive != ''">
                #{isAdaptive},
            </if>
            <if test="isImage != null and isImage != ''">
                #{isImage},
            </if>
            <if test="imageNum !=null and imageNum !='' ">
                #{imageNum},
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                #{previewUrl},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
            <if test="isDefault != null and isDefault != ''">
                #{isDefault},
            </if>
            <if test="themeState != null and themeState != ''">
                #{themeState},
            </if>
            <if test="composeType != null and composeType != ''">
                #{composeType},
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                #{vipFlag},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="layoutId" useGeneratedKeys="true">
        insert into ppt_layout(layout_nametheme_idlayout_typelayout_sourcelayout_countlayout_contentcolorcategoryorder_indexis_adaptiveis_imagepreview_urltenant_codeuser_idcreate_timeupdate_timeis_valid)
        values (#{layoutName}#{themeId}#{layoutType}#{layoutSource}#{layoutCount}#{layoutContent}#{color}#{category}#{orderIndex}#{isAdaptive}#{isImage}#{previewUrl}#{tenantCode}#{userId}#{createTime}#{updateTime}#{isValid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update ppt_layout
        <set>
            <if test="layoutName != null and layoutName != ''">
                layout_name = #{layoutName},
            </if>
            <if test="themeId != null and themeId != ''">
                theme_id = #{themeId},
            </if>
            <if test="layoutType != null and layoutType != ''">
                layout_type = #{layoutType},
            </if>
            <if test="layoutSource != null and layoutSource != ''">
                layout_source = #{layoutSource},
            </if>
            <if test="layoutCount != null">
                layout_count = #{layoutCount},
            </if>
            <if test="layoutContent != null and layoutContent != ''">
                layout_content = #{layoutContent},
            </if>
            <if test="color != null and color != ''">
                color = #{color},
            </if>
            <if test="category != null and category != ''">
                category = #{category},
            </if>
            <if test="sort != null">
                sort = #{sort},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex},
            </if>
            <if test="isAdaptive != null and isAdaptive != ''">
                is_adaptive = #{isAdaptive},
            </if>
            <if test="isImage != null and isImage != ''">
                is_image = #{isImage},
            </if>
            <if test="imageNum !=null and imageNum !='' ">
                image_num = #{imageNum},
            </if>
            <if test="previewUrl != null and previewUrl != ''">
                preview_url = #{previewUrl},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
            <if test="isDefault != null and isDefault != ''">
                is_default = #{isDefault},
            </if>
            <if test="themeState != null and themeState != ''">
                theme_state = #{themeState},
            </if>
            <if test="composeType != null and composeType != ''">
                compose_type = #{composeType},
            </if>
            <if test="vipFlag != null and vipFlag != ''">
                vip_flag = #{vipFlag},
            </if>
        </set>
        where layout_id = #{layoutId}
    </update>
    <update id="updateByThemeId">
        update ppt_layout set is_valid = '0' where theme_id = #{themeId}
    </update>
    <!--通过主键删除-->
    <delete id="deleteById">
        delete from ppt_layout where layout_id = #{layoutId}
    </delete>

    <update id="updateList" parameterType="java.util.List">
        update ppt_layout
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="layout_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.layoutName != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.layoutName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="theme_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.themeId != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.themeId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="layout_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.layoutType != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.layoutType,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="layout_source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.layoutSource != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.layoutSource,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="layout_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.layoutCount != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.layoutCount,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="layout_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.layoutContent != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.layoutContent,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.color != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.color,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.category != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.category,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="sort = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.sort != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.sort,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderIndex != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.orderIndex,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_adaptive = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isAdaptive != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.isAdaptive,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_image = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isImage != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.isImage,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="image_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.imageNum != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.imageNum,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="preview_url = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.previewUrl != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.previewUrl,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.userId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_valid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isValid != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.isValid,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_default = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDefault != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.isDefault,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="theme_state = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.themeState != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.themeState,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="compose_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.composeType != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.composeType,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="vip_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.vipFlag != null">
                        when layout_id = #{item.layoutId,jdbcType=VARCHAR} then #{item.vipFlag,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where layout_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.layoutId,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>

