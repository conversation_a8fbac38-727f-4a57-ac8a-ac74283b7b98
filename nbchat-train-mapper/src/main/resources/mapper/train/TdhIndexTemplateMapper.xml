<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TdhIndexTemplateMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
    <!--@mbg.generated-->
    <!--@Table tdh_index_template-->
    <id column="tpl_id" jdbcType="VARCHAR" property="tplId" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="tpl_name" jdbcType="VARCHAR" property="tplName" />
    <result column="tpl_desc" jdbcType="VARCHAR" property="tplDesc" />
    <result column="tpl_config" jdbcType="VARCHAR" property="tplConfig" />
    <result column="tpl_content" jdbcType="VARCHAR" property="tplContent" />
    <result column="tpl_type" jdbcType="CHAR" property="tplType" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="order_index" jdbcType="INTEGER" property="orderIndex" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
    <result column="tpl_size" jdbcType="CHAR" property="tplSize" />
    <result column="status" jdbcType="CHAR" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    tpl_id, tenant_code, tpl_name, tpl_desc, tpl_config, tpl_content, tpl_type, category_code, 
    order_index, create_time, is_valid, tpl_size, `status`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from tdh_index_template
    where tpl_id = #{tplId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from tdh_index_template
    where tpl_id = #{tplId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
    <!--@mbg.generated-->
    insert into tdh_index_template (tpl_id, tenant_code, tpl_name, 
      tpl_desc, tpl_config, tpl_content, 
      tpl_type, category_code, order_index, 
      create_time, is_valid, tpl_size, 
      `status`)
    values (#{tplId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{tplName,jdbcType=VARCHAR}, 
      #{tplDesc,jdbcType=VARCHAR}, #{tplConfig,jdbcType=VARCHAR}, #{tplContent,jdbcType=VARCHAR}, 
      #{tplType,jdbcType=CHAR}, #{categoryCode,jdbcType=VARCHAR}, #{orderIndex,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{isValid,jdbcType=CHAR}, #{tplSize,jdbcType=CHAR}, 
      #{status,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
    <!--@mbg.generated-->
    insert into tdh_index_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tplId != null">
        tpl_id,
      </if>
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="tplName != null">
        tpl_name,
      </if>
      <if test="tplDesc != null">
        tpl_desc,
      </if>
      <if test="tplConfig != null">
        tpl_config,
      </if>
      <if test="tplContent != null">
        tpl_content,
      </if>
      <if test="tplType != null">
        tpl_type,
      </if>
      <if test="categoryCode != null">
        category_code,
      </if>
      <if test="orderIndex != null">
        order_index,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="tplSize != null">
        tpl_size,
      </if>
      <if test="status != null">
        `status`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tplId != null">
        #{tplId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="tplName != null">
        #{tplName,jdbcType=VARCHAR},
      </if>
      <if test="tplDesc != null">
        #{tplDesc,jdbcType=VARCHAR},
      </if>
      <if test="tplConfig != null">
        #{tplConfig,jdbcType=VARCHAR},
      </if>
      <if test="tplContent != null">
        #{tplContent,jdbcType=VARCHAR},
      </if>
      <if test="tplType != null">
        #{tplType,jdbcType=CHAR},
      </if>
      <if test="categoryCode != null">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="orderIndex != null">
        #{orderIndex,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=CHAR},
      </if>
      <if test="tplSize != null">
        #{tplSize,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
    <!--@mbg.generated-->
    update tdh_index_template
    <set>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="tplName != null">
        tpl_name = #{tplName,jdbcType=VARCHAR},
      </if>
      <if test="tplDesc != null">
        tpl_desc = #{tplDesc,jdbcType=VARCHAR},
      </if>
      <if test="tplConfig != null">
        tpl_config = #{tplConfig,jdbcType=VARCHAR},
      </if>
      <if test="tplContent != null">
        tpl_content = #{tplContent,jdbcType=VARCHAR},
      </if>
      <if test="tplType != null">
        tpl_type = #{tplType,jdbcType=CHAR},
      </if>
      <if test="categoryCode != null">
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="orderIndex != null">
        order_index = #{orderIndex,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
      <if test="tplSize != null">
        tpl_size = #{tplSize,jdbcType=CHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=CHAR},
      </if>
    </set>
    where tpl_id = #{tplId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
    <!--@mbg.generated-->
    update tdh_index_template
    set tenant_code = #{tenantCode,jdbcType=VARCHAR},
      tpl_name = #{tplName,jdbcType=VARCHAR},
      tpl_desc = #{tplDesc,jdbcType=VARCHAR},
      tpl_config = #{tplConfig,jdbcType=VARCHAR},
      tpl_content = #{tplContent,jdbcType=VARCHAR},
      tpl_type = #{tplType,jdbcType=CHAR},
      category_code = #{categoryCode,jdbcType=VARCHAR},
      order_index = #{orderIndex,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      is_valid = #{isValid,jdbcType=CHAR},
      tpl_size = #{tplSize,jdbcType=CHAR},
      `status` = #{status,jdbcType=CHAR}
    where tpl_id = #{tplId,jdbcType=VARCHAR}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update tdh_index_template
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="tenant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantCode != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tpl_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tplName != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tpl_desc = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tplDesc != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplDesc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tpl_config = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tplConfig != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplConfig,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tpl_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tplContent != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplContent,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tpl_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tplType != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplType,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="category_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.categoryCode != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.categoryCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderIndex != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.orderIndex,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_valid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isValid != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.isValid,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tpl_size = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tplSize != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplSize,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.status,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where tpl_id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.tplId,jdbcType=VARCHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into tdh_index_template
    (tpl_id, tenant_code, tpl_name, tpl_desc, tpl_config, tpl_content, tpl_type, category_code, 
      order_index, create_time, is_valid, tpl_size, `status`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tplId,jdbcType=VARCHAR}, #{item.tenantCode,jdbcType=VARCHAR}, #{item.tplName,jdbcType=VARCHAR}, 
        #{item.tplDesc,jdbcType=VARCHAR}, #{item.tplConfig,jdbcType=VARCHAR}, #{item.tplContent,jdbcType=VARCHAR}, 
        #{item.tplType,jdbcType=CHAR}, #{item.categoryCode,jdbcType=VARCHAR}, #{item.orderIndex,jdbcType=INTEGER}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.isValid,jdbcType=CHAR}, #{item.tplSize,jdbcType=CHAR}, 
        #{item.status,jdbcType=CHAR})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from tdh_index_template where tpl_id in 
    <foreach close=")" collection="list" item="tplId" open="(" separator=", ">
      #{tplId,jdbcType=VARCHAR}
    </foreach>
  </delete>

    <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
        <id column="tpl_id" jdbcType="VARCHAR" property="tplId" />
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
        <result column="tpl_name" jdbcType="VARCHAR" property="tplName" />
        <result column="tpl_desc" jdbcType="VARCHAR" property="tplDesc" />
        <result column="tpl_config" jdbcType="VARCHAR" property="tplConfig" />
        <result column="tpl_content" jdbcType="VARCHAR" property="tplContent" />
        <result column="tpl_type" jdbcType="CHAR" property="tplType" />
        <result column="order_index" jdbcType="INTEGER" property="orderIndex" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="is_valid" jdbcType="CHAR" property="isValid" />
    </resultMap>

    <sql id="Base_Column_List">
        tpl_id,
        tenant_code,
        tpl_name,
        tpl_desc,
        tpl_config,
        tpl_content,
        tpl_type,
        order_index,
        create_time,
        is_valid
    </sql>
    <sql id="List_Column">
        tpl_id,
        tenant_code,
        tpl_name,
        tpl_desc,
        tpl_config,
        tpl_type,
        order_index,
        create_time,
        is_valid
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tdh_index_template
        where tpl_id = #{tplId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from tdh_index_template
        where tpl_id = #{tplId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
        insert into tdh_index_template (tpl_id, tenant_code, tpl_name,
                                        tpl_desc, tpl_config, tpl_content,
                                        tpl_type, order_index, create_time,
                                        is_valid)
        values (#{tplId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{tplName,jdbcType=VARCHAR},
                #{tplDesc,jdbcType=VARCHAR}, #{tplConfig,jdbcType=VARCHAR}, #{tplContent,jdbcType=VARCHAR},
                #{tplType,jdbcType=CHAR}, #{orderIndex,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
                #{isValid,jdbcType=CHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
        insert into tdh_index_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tplId != null">
                tpl_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="tplName != null">
                tpl_name,
            </if>
            <if test="tplDesc != null">
                tpl_desc,
            </if>
            <if test="tplConfig != null">
                tpl_config,
            </if>
            <if test="tplContent != null">
                tpl_content,
            </if>
            <if test="tplType != null">
                tpl_type,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tplId != null">
                #{tplId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="tplName != null">
                #{tplName,jdbcType=VARCHAR},
            </if>
            <if test="tplDesc != null">
                #{tplDesc,jdbcType=VARCHAR},
            </if>
            <if test="tplConfig != null">
                #{tplConfig,jdbcType=VARCHAR},
            </if>
            <if test="tplContent != null">
                #{tplContent,jdbcType=VARCHAR},
            </if>
            <if test="tplType != null">
                #{tplType,jdbcType=CHAR},
            </if>
            <if test="orderIndex != null">
                #{orderIndex,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=CHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
        update tdh_index_template
        <set>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="tplName != null">
                tpl_name = #{tplName,jdbcType=VARCHAR},
            </if>
            <if test="tplDesc != null">
                tpl_desc = #{tplDesc,jdbcType=VARCHAR},
            </if>
            <if test="tplConfig != null">
                tpl_config = #{tplConfig,jdbcType=VARCHAR},
            </if>
            <if test="tplContent != null">
                tpl_content = #{tplContent,jdbcType=VARCHAR},
            </if>
            <if test="tplType != null">
                tpl_type = #{tplType,jdbcType=CHAR},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
        </set>
        where tpl_id = #{tplId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
        update tdh_index_template
        set tenant_code = #{tenantCode,jdbcType=VARCHAR},
            tpl_name    = #{tplName,jdbcType=VARCHAR},
            tpl_desc    = #{tplDesc,jdbcType=VARCHAR},
            tpl_config  = #{tplConfig,jdbcType=VARCHAR},
            tpl_content = #{tplContent,jdbcType=VARCHAR},
            tpl_type    = #{tplType,jdbcType=CHAR},
            order_index = #{orderIndex,jdbcType=INTEGER},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            is_valid    = #{isValid,jdbcType=CHAR}
        where tpl_id = #{tplId,jdbcType=VARCHAR}
    </update>
    <update id="updateBatchList" parameterType="java.util.List">
        update tdh_index_template
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tpl_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tpl_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplDesc,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tpl_config = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplConfig,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tpl_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplContent,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tpl_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplType,jdbcType=CHAR}
                </foreach>
            </trim>
            <trim prefix="order_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.orderIndex,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_valid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.isValid,jdbcType=CHAR}
                </foreach>
            </trim>
        </trim>
        where tpl_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.tplId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        update tdh_index_template
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tpl_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tplName != null">
                        when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tpl_desc = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tplDesc != null">
                        when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplDesc,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tpl_config = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tplConfig != null">
                        when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplConfig,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tpl_content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tplContent != null">
                        when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplContent,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tpl_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tplType != null">
                        when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.tplType,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="order_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.orderIndex != null">
                        when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.orderIndex,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_valid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isValid != null">
                        when tpl_id = #{item.tplId,jdbcType=VARCHAR} then #{item.isValid,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where tpl_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.tplId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        insert into tdh_index_template
        (tpl_id, tenant_code, tpl_name, tpl_desc, tpl_config, tpl_content, tpl_type, order_index,
         create_time, is_valid)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tplId,jdbcType=VARCHAR}, #{item.tenantCode,jdbcType=VARCHAR}, #{item.tplName,jdbcType=VARCHAR},
             #{item.tplDesc,jdbcType=VARCHAR}, #{item.tplConfig,jdbcType=VARCHAR}, #{item.tplContent,jdbcType=VARCHAR},
             #{item.tplType,jdbcType=CHAR}, #{item.orderIndex,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
             #{item.isValid,jdbcType=CHAR})
        </foreach>
    </insert>
    <delete id="deleteByPrimaryKeyIn">
        delete
        from tdh_index_template where tpl_id in
        <foreach close=")" collection="list" item="tplId" open="(" separator=", ">
            #{tplId,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update tdh_index_template
            <set>
                <if test="item.tenantCode != null">
                    tenant_code = #{item.tenantCode,jdbcType=VARCHAR},
                </if>
                <if test="item.tplName != null">
                    tpl_name = #{item.tplName,jdbcType=VARCHAR},
                </if>
                <if test="item.tplDesc != null">
                    tpl_desc = #{item.tplDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.tplConfig != null">
                    tpl_config = #{item.tplConfig,jdbcType=VARCHAR},
                </if>
                <if test="item.tplContent != null">
                    tpl_content = #{item.tplContent,jdbcType=VARCHAR},
                </if>
                <if test="item.tplType != null">
                    tpl_type = #{item.tplType,jdbcType=CHAR},
                </if>
                <if test="item.orderIndex != null">
                    order_index = #{item.orderIndex,jdbcType=INTEGER},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.isValid != null">
                    is_valid = #{item.isValid,jdbcType=CHAR},
                </if>
            </set>
            where tpl_id = #{item.tplId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <insert id="batchInsertSelectiveUseDefaultForNull" parameterType="map">
        insert into tdh_index_template
        (tpl_id, tenant_code, tpl_name, tpl_desc, tpl_config, tpl_content, tpl_type, order_index,
         create_time, is_valid)
        values
        <foreach collection="list" item="item" separator=",">
            (
            <choose>
                <when test="item.tplId != null">
                    #{item.tplId,jdbcType=VARCHAR},
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT,
                </otherwise>
            </choose>
            <choose>
                <when test="item.tenantCode != null">
                    #{item.tenantCode,jdbcType=VARCHAR},
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT,
                </otherwise>
            </choose>
            <choose>
                <when test="item.tplName != null">
                    #{item.tplName,jdbcType=VARCHAR},
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT,
                </otherwise>
            </choose>
            <choose>
                <when test="item.tplDesc != null">
                    #{item.tplDesc,jdbcType=VARCHAR},
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT,
                </otherwise>
            </choose>
            <choose>
                <when test="item.tplConfig != null">
                    #{item.tplConfig,jdbcType=VARCHAR},
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT,
                </otherwise>
            </choose>
            <choose>
                <when test="item.tplContent != null">
                    #{item.tplContent,jdbcType=VARCHAR},
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT,
                </otherwise>
            </choose>
            <choose>
                <when test="item.tplType != null">
                    #{item.tplType,jdbcType=CHAR},
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT,
                </otherwise>
            </choose>
            <choose>
                <when test="item.orderIndex != null">
                    #{item.orderIndex,jdbcType=INTEGER},
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT,
                </otherwise>
            </choose>
            <choose>
                <when test="item.createTime != null">
                    #{item.createTime,jdbcType=TIMESTAMP},
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT,
                </otherwise>
            </choose>
            <choose>
                <when test="item.isValid != null">
                    #{item.isValid,jdbcType=CHAR}
                </when>
                <!--@ignoreSql-->
                <otherwise>
                    DEFAULT
                </otherwise>
            </choose>
            )
        </foreach>
    </insert>
    <insert id="insertOrUpdate" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
        insert into tdh_index_template
        (tpl_id, tenant_code, tpl_name, tpl_desc, tpl_config, tpl_content, tpl_type, order_index,
         create_time, is_valid)
        values (#{tplId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{tplName,jdbcType=VARCHAR},
                #{tplDesc,jdbcType=VARCHAR}, #{tplConfig,jdbcType=VARCHAR}, #{tplContent,jdbcType=VARCHAR},
                #{tplType,jdbcType=CHAR}, #{orderIndex,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
                #{isValid,jdbcType=CHAR})
        on duplicate key update tpl_id      = #{tplId,jdbcType=VARCHAR},
                                tenant_code = #{tenantCode,jdbcType=VARCHAR},
                                tpl_name    = #{tplName,jdbcType=VARCHAR},
                                tpl_desc    = #{tplDesc,jdbcType=VARCHAR},
                                tpl_config  = #{tplConfig,jdbcType=VARCHAR},
                                tpl_content = #{tplContent,jdbcType=VARCHAR},
                                tpl_type    = #{tplType,jdbcType=CHAR},
                                order_index = #{orderIndex,jdbcType=INTEGER},
                                create_time = #{createTime,jdbcType=TIMESTAMP},
                                is_valid    = #{isValid,jdbcType=CHAR}
    </insert>
    <insert id="insertOrUpdateSelective" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate">
        insert into tdh_index_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tplId != null">
                tpl_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="tplName != null">
                tpl_name,
            </if>
            <if test="tplDesc != null">
                tpl_desc,
            </if>
            <if test="tplConfig != null">
                tpl_config,
            </if>
            <if test="tplContent != null">
                tpl_content,
            </if>
            <if test="tplType != null">
                tpl_type,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tplId != null">
                #{tplId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="tplName != null">
                #{tplName,jdbcType=VARCHAR},
            </if>
            <if test="tplDesc != null">
                #{tplDesc,jdbcType=VARCHAR},
            </if>
            <if test="tplConfig != null">
                #{tplConfig,jdbcType=VARCHAR},
            </if>
            <if test="tplContent != null">
                #{tplContent,jdbcType=VARCHAR},
            </if>
            <if test="tplType != null">
                #{tplType,jdbcType=CHAR},
            </if>
            <if test="orderIndex != null">
                #{orderIndex,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=CHAR},
            </if>
        </trim>
        on duplicate key update
        <trim suffixOverrides=",">
            <if test="tplId != null">
                tpl_id = #{tplId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="tplName != null">
                tpl_name = #{tplName,jdbcType=VARCHAR},
            </if>
            <if test="tplDesc != null">
                tpl_desc = #{tplDesc,jdbcType=VARCHAR},
            </if>
            <if test="tplConfig != null">
                tpl_config = #{tplConfig,jdbcType=VARCHAR},
            </if>
            <if test="tplContent != null">
                tpl_content = #{tplContent,jdbcType=VARCHAR},
            </if>
            <if test="tplType != null">
                tpl_type = #{tplType,jdbcType=CHAR},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
        </trim>
    </insert>

    <select id="selectList" parameterType="com.tydic.nbchat.train.mapper.po.TdhIndexTemplate" resultMap="BaseResultMap">
        select
        <include refid="List_Column" />
        from tdh_index_template
        <where>
            <if test="tenantCode != null and tenantCode !='' ">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="tplName != null and tplName !='' ">
                and tpl_name like concat('%',#{tplName,jdbcType=VARCHAR},'%')
            </if>
            <if test="tplType != null and tplType !='' ">
                and tpl_type = #{tplType,jdbcType=CHAR}
            </if>
            <if test="categoryCode != null and categoryCode != ''">
                and category_code = #{categoryCode,jdbcType=VARCHAR}
            </if>
            <if test="tplSize != null and tplSize != ''">
                and tpl_size = #{tplSize,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and status = #{status,jdbcType=CHAR}
            </if>
            <if test="isValid != null and  isValid !='' ">
                and is_valid = #{isValid,jdbcType=CHAR}
            </if>
        </where>
        order by order_index
    </select>
</mapper>