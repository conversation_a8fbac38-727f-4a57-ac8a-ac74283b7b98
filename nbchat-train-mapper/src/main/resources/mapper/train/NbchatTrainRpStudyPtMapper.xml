<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.NbchatTrainRpStudyPtMapper">

    <resultMap type="com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPt" id="NbchatTrainRpStudyPtMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="courseId" column="course_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="dateDay" column="date_day" jdbcType="TIMESTAMP"/>
        <result property="dateTime" column="date_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id, tenant_code, course_id, user_id, date_day, date_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatTrainRpStudyPtMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_train_rp_study_pt
        where id = #{id}
    </select>
    
    
    <select id="selectAll" resultMap="NbchatTrainRpStudyPtMap" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPt">
        select
          <include refid="Base_Column_List" />
        from nbchat_train_rp_study_pt
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="courseId != null and courseId != ''">
                and course_id = #{courseId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="dateDay != null">
                and date_day = #{dateDay}
            </if>
            <if test="dateTime != null">
                and date_time = #{dateTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.train.mapper.po.NbchatTrainRpStudyPt">
        insert into nbchat_train_rp_study_pt
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="courseId != null and courseId != ''">
                course_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="dateDay != null">
                date_day,
            </if>
            <if test="dateTime != null">
                date_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="courseId != null and courseId != ''">
                #{courseId},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="dateDay != null">
                #{dateDay},
            </if>
            <if test="dateTime != null">
                #{dateTime},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_train_rp_study_pt(tenant_code, course_id, user_id, date_day, date_time)
        values (#{tenantCode}, #{courseId}, #{userId}, #{dateDay}, #{dateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_train_rp_study_pt
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="courseId != null and courseId != ''">
                course_id = #{courseId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="dateDay != null">
                date_day = #{dateDay},
            </if>
            <if test="dateTime != null">
                date_time = #{dateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_train_rp_study_pt where id = #{id}
    </delete>

</mapper>

