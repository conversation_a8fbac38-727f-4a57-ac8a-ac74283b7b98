<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.train.mapper.TrainCourseVideosMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.train.mapper.po.TrainCourseVideosPO" >
        <id column="course_id" property="courseId" jdbcType="VARCHAR" />
        <result column="course_id" property="courseId" jdbcType="VARCHAR" />
        <result column="course_name" property="courseName" jdbcType="VARCHAR" />
        <result column="category" property="category" jdbcType="VARCHAR" />
        <result column="category2" property="category2" jdbcType="VARCHAR" />
        <result column="img_avatar" property="imgAvatar" jdbcType="VARCHAR" />
        <result column="video_url" property="videoUrl" jdbcType="VARCHAR" />
        <result column="video_img" property="videoImg" jdbcType="VARCHAR" />
        <result column="sections_index" property="sectionsIndex" jdbcType="SMALLINT" />
        <result column="section_id" property="sectionId" jdbcType="VARCHAR"></result>
    </resultMap>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT a.course_id,a.course_name,a.category,a.category2,a.img_avatar,b.video_url,b.video_img,b.sections_index,b.section_id
        FROM nbchat_train_course a LEFT JOIN nbchat_train_sections b ON a.course_id = b.course_id AND course_type = '1'
        <where>

            <if test="courseName != null and courseName != ''">
                AND a.course_name LIKE CONCAT('%',#{courseName},'%')
            </if>
            AND a.tenant_code = #{tenantCode}
            AND a.is_valid='1'
            AND b.video_url is not null and b.video_url != ''
        </where>
        ORDER BY a.course_id,b.sections_index,a.create_time desc
    </select>
</mapper>


