package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhVirtualAnchor;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 主播配置(TdhVirtualAnchor)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-22 15:51:12
 */
public interface TdhVirtualAnchorMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param anchorId 主键
     * @return 实例对象
     */
    TdhVirtualAnchor queryById(String anchorId);

    List<TdhVirtualAnchor> selectAll(TdhVirtualAnchor tdhVirtualAnchor);


    List<TdhVirtualAnchor> selectByIds(@Param("ids")List<String> ids);

    /**
     * 新增数据
     *
     * @param tdhVirtualAnchor 实例对象
     * @return 影响行数
     */
    int insert(TdhVirtualAnchor tdhVirtualAnchor);


    int insertSelective(TdhVirtualAnchor tdhVirtualAnchor);

      /**
     * 修改数据
     *
     * @param tdhVirtualAnchor 实例对象
     * @return 影响行数
     */
    int update(TdhVirtualAnchor tdhVirtualAnchor);

    /**
     * 根据订单号修改数据
     * @param tdhVirtualAnchor
     * @return
     */
    int updateByOrderNo(TdhVirtualAnchor tdhVirtualAnchor);

    /**
     * 通过主键删除数据
     *
     * @param anchorId 主键
     * @return 影响行数
     */
    int deleteById(String anchorId);

    TdhVirtualAnchor findByOrderNoAndUserId(@Param("orderNo")String orderNo,@Param("userId")String userId);



}

