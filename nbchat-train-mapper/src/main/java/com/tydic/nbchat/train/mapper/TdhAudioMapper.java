package com.tydic.nbchat.train.mapper;

import com.tydic.nbchat.train.mapper.po.TdhAudio;

import java.util.List;

/**
 * 数字人-前景(TdhAudio)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-16 14:18:08
 */
public interface TdhAudioMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param objectId 主键
     * @return 实例对象
     */
    TdhAudio queryById(String objectId);

    List<TdhAudio> selectAll(TdhAudio tdhAudio);

    /**
     * 新增数据
     *
     * @param tdhAudio 实例对象
     * @return 影响行数
     */
    int insert(TdhAudio tdhAudio);


    int insertSelective(TdhAudio tdhAudio);

      /**
     * 修改数据
     *
     * @param tdhAudio 实例对象
     * @return 影响行数
     */
    int update(TdhAudio tdhAudio);

    /**
     * 通过主键删除数据
     *
     * @param objectId 主键
     * @return 影响行数
     */
    int deleteById(String objectId);

}

