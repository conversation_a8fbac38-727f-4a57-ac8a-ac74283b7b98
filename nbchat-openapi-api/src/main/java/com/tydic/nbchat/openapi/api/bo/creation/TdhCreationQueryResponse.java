package com.tydic.nbchat.openapi.api.bo.creation;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@Data
public class TdhCreationQueryResponse implements Serializable {

    private String taskId;
    private String creationId;
    private String creationName;
    private Date startTime;
    private Date endTime;
    /**
     * 总视频段
     */
    private Integer partCountTotal;
    /**
     * 完成视频段
     */
    private Integer partCountDone;
    private String videoDuration;
    /**
     * 视频下载地址
     */
    private String videoUrl;
    // 播放地址
    private String playUrl;
    /**
     * 0 生成中  1 任务完成 2 任务异常
     */
    private String taskState;
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误描述
     */
    private String errorDesc;
}
