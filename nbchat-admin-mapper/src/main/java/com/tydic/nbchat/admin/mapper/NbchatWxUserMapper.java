package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.NbchatWxUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25 14:23
 * @description
 */

public interface NbchatWxUserMapper {
    /**
     * delete by primary key
     *
     * @param openId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(String openId);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(NbchatWxUser record);

    /**
     * select by primary key
     *
     * @param openId primary key
     * @return object by primary key
     */
    NbchatWxUser selectByPrimaryKey(String openId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(NbchatWxUser record);

    int updateBatch(@Param("list") List<NbchatWxUser> list);

    int updateBatchSelective(@Param("list") List<NbchatWxUser> list);

    int batchInsert(@Param("list") List<NbchatWxUser> list);

    /**
     * 根据租户代码和用户ID查找微信用户信息。
     *
     * @param tenantCode 租户代码
     * @param userId     用户ID
     * @return 返回符合条件的微信用户信息，如果未找到则返回null
     */
    NbchatWxUser findOneByTenantCodeAndUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);
}
