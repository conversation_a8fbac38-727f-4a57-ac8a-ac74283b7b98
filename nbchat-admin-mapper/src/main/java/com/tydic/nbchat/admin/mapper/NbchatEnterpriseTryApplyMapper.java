package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.NbchatEnterpriseTryApply;

import java.util.List;

/**
 * 租户申请表(NbchatEnterpriseTryApply)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-11 14:31:32
 */
public interface NbchatEnterpriseTryApplyMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    NbchatEnterpriseTryApply queryById(Long id);

    List<NbchatEnterpriseTryApply> selectAll(NbchatEnterpriseTryApply nbchatEnterpriseTryApply);

    /**
     * 新增数据
     *
     * @param nbchatEnterpriseTryApply 实例对象
     * @return 影响行数
     */
    int insert(NbchatEnterpriseTryApply nbchatEnterpriseTryApply);


    int insertSelective(NbchatEnterpriseTryApply nbchatEnterpriseTryApply);

      /**
     * 修改数据
     *
     * @param nbchatEnterpriseTryApply 实例对象
     * @return 影响行数
     */
    int update(NbchatEnterpriseTryApply nbchatEnterpriseTryApply);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

