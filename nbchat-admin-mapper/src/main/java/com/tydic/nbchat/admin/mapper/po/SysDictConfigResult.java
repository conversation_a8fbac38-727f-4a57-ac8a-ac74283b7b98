package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
@Data
public class SysDictConfigResult implements Serializable {
    /**
     * 字典id
     */
    private Integer dictId;
    /**
     * 字典编码
     */
    private String dictCode;
    /**
     * 字典名称
     */
    private String dictName;
    /**
     * 字典值
     */
    private String dictValue;
    /**
     * 字典描述
     */
    private String dictDesc;
    /**
     * 每组字典个数
     */
    private Integer dictCount;
    /**
     * 排序
     */
    private Integer orderIndex;

}
