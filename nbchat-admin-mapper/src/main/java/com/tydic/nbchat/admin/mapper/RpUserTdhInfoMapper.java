package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpUserTdhInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:44
 * @description
 */

public interface RpUserTdhInfoMapper {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RpUserTdhInfo record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateSelective(RpUserTdhInfo record);

    List<RpUserTdhInfo> selectRpByUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    RpUserTdhInfo findOneByTenantCodeAndUserIdAndTdhType(@Param("tenantCode") String tenantCode, @Param("userId") String userId, @Param("tdhType") String tdhType);

    /**
     * 每日更新统计数据
     *
     * @return update count
     */
    int insertForAll();

    int deleteAll();

    List<RpUserTdhInfo> selectAllTdhInfo();

    List<RpUserTdhInfo> selectTdhInfoByUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);
}
