package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 岗位用户关联表(SysPostUserRel)实体类
 *
 * <AUTHOR>
 * @since 2024-07-15 15:54:50
 */
@Data
public class SysPostUserRel implements Serializable {
    private static final long serialVersionUID = 906182311343421948L;
/**
     * 主键
     */
    private Integer id;
/**
     * 租户编码
     */
    private String tenantCode;
/**
     * 岗位ID
     */
    private Integer postId;
/**
     * 用户ID
     */
    private String userId;
/**
     * 创建时间
     */
    private Date createTime;


}

