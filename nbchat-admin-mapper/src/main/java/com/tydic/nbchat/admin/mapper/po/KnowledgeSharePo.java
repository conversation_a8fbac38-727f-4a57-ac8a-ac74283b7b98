package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;

public class KnowledgeSharePo {
    private String sentenceShareId;

    private String tenantCode;

    private String userId;

    private String sentenceId;

    private String shareKey;

    private Date expiredDate;

    private Date createdAt;

    private String shareUrl;

    private String isValid;

    public String getSentenceShareId() {
        return sentenceShareId;
    }

    public void setSentenceShareId(String sentenceShareId) {
        this.sentenceShareId = sentenceShareId == null ? null : sentenceShareId.trim();
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode == null ? null : tenantCode.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getSentenceId() {
        return sentenceId;
    }

    public void setSentenceId(String sentenceId) {
        this.sentenceId = sentenceId == null ? null : sentenceId.trim();
    }

    public String getShareKey() {
        return shareKey;
    }

    public void setShareKey(String shareKey) {
        this.shareKey = shareKey == null ? null : shareKey.trim();
    }

    public Date getExpiredDate() {
        return expiredDate;
    }

    public void setExpiredDate(Date expiredDate) {
        this.expiredDate = expiredDate;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getShareUrl() {
        return shareUrl;
    }

    public void setShareUrl(String shareUrl) {
        this.shareUrl = shareUrl == null ? null : shareUrl.trim();
    }

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid == null ? null : isValid.trim();
    }
}