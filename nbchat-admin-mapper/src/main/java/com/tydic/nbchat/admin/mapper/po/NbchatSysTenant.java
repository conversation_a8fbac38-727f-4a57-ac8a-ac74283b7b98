package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;
@Data
public class NbchatSysTenant {
    private String userRealityName;
    private String tenantCode;

    private String tenantName;

    private String tenantDesc;

    private String domain;

    private String linkman;

    private String contactNumber;

    private String address;

    private String createUser;

    private Date createTime;

    private String updateUser;

    private Date updateTime;

    private Integer status;

    private String isValid;

    private String imgAvatar;

    private String extInfo;
    private String authApi;
    private String authConfig;
    private String tplCode;
    //租户配置
    private String tenantConfig;
    private String customConfig;
    private Integer userLimit;

    private String payType; //0企业支付 1个人支付

}