package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:44
 * @description 数字人付费信息
 */
@Data
public class RpUserTdhInfo {
    private Integer id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 类型 2d-照片数字人/2.5d_mtk-视频数字人/audio-声音音色
     */
    private String tdhType;

    /**
     * 发起定制次数
     */
    private Integer launchTimes;

    /**
     * 实际定制次数
     */
    private Integer actualTimes;

    /**
     * 累计付费金额，单位：分
     */
    private Integer totalAmount;

    /**
     * 更新时间
     */
    private Date updateTime;
}
