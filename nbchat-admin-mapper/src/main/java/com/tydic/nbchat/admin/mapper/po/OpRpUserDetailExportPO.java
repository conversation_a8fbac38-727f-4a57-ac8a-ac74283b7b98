package com.tydic.nbchat.admin.mapper.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

@Data
@ContentRowHeight(30)
@HeadRowHeight(30)
@ColumnWidth(25)
public class OpRpUserDetailExportPO implements Serializable {
    @ExcelProperty("主键")
    private Long id;

    @ExcelProperty("用户ID")
    private String userId;

    @ExcelProperty("用户名称")
    private String userName;

    @ExcelProperty("手机号")
    private String phone;

    @ExcelProperty("用户属性")
    private String userType;

    @ExcelProperty("租户编码")
    private String tenantCode;

    @ExcelProperty("企业名称")
    private String companyName;

    @ExcelProperty("注册时间")
    private Date regTime;

    @ExcelProperty("注册渠道")
    private String regChannel;

    @ExcelProperty("注册来源")
    private String regSource;

    @ExcelProperty("关键词")
    private String promKey;

    @ExcelProperty("渠道Id")
    private String promId;

    @ExcelProperty("推广渠道")
    private String promChannel;

    @ExcelProperty("会员类型")
    private String vipType;

    @ExcelProperty("付费状态")
    private String paymentStatus;

    @ExcelProperty("会员状态")
    private String vipStatus;

    @ExcelProperty("付费时间:vip开始时间")
    private Date vipStartTime;

    @ExcelProperty("最近支付金额")
    private String lastPayPrice;

    @ExcelProperty("总支付金额")
    private String totalRevenue;

    @ExcelProperty("首次开通时间")
    private Date vipFirstTime;

    @ExcelProperty("购买版本:月付/年付")
    private String buyVersion;

    @ExcelProperty("购买次数")
    private Integer vipBuyCount;

    @ExcelProperty("最近支付时间")
    private Date lastPayTime;

    @ExcelProperty("当前版本")
    private String lastGoodsName;

    @ExcelProperty("算力点加油包购买次数")
    private Integer scoreBuyCount;

    @ExcelProperty("到期时间")
    private Date vipEndTime;

    @ExcelProperty("上次登录时间")
    private Date loginTime;

    @ExcelProperty("登录次数")
    private Integer loginCount;

    @ExcelProperty("课件帮平台登录次数")
    private Integer tdhLoginCount;

    @ExcelProperty("累计充值算力点")
    private Integer scoreRechargeTotal;

    @ExcelProperty("剩余算力点")
    private Integer scoreBalance;

    @ExcelProperty("消耗算力点")
    private Integer scoreConsumeTotal;

    @ExcelProperty("视频制作次数")
    private Integer videoMakeCount;

    @ExcelProperty("成功次数")
    private Integer videoSuccessCount;

    @ExcelProperty("ppt制作次数")
    private Integer pptMakeCount;

    @ExcelProperty("成功次数")
    private Integer pptSuccessCount;

    @ExcelProperty("出题次数")
    private Integer examMakeCount;

    @ExcelProperty("成功次数")
    private Integer examSuccessCount;

    @ExcelProperty("更新时间")
    private Date updateTime;

    //最近支付金额（分）
    @ExcelIgnore
    private Integer lastPayAmount;
    //支付总金额（分）
    @ExcelIgnore
    private Integer totalPayAmount;
}
