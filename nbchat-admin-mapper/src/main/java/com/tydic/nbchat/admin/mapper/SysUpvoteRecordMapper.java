package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUpvoteRecord;

import java.util.List;

/**
 * 点赞记录表(SysUpvoteRecord)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-14 14:14:33
 */
public interface SysUpvoteRecordMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SysUpvoteRecord queryById(Long id);

    List<SysUpvoteRecord> selectAll(SysUpvoteRecord sysUpvoteRecord);

    /**
     * 新增数据
     *
     * @param sysUpvoteRecord 实例对象
     * @return 影响行数
     */
    int insert(SysUpvoteRecord sysUpvoteRecord);


    int insertSelective(SysUpvoteRecord sysUpvoteRecord);

      /**
     * 修改数据
     *
     * @param sysUpvoteRecord 实例对象
     * @return 影响行数
     */
    int update(SysUpvoteRecord sysUpvoteRecord);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Long id);

}

