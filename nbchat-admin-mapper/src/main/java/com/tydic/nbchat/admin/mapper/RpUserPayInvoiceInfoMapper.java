package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpUserPayInvoiceInfo;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2025/4/1 15:05
 * @description
 */

public interface RpUserPayInvoiceInfoMapper {
    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RpUserPayInvoiceInfo record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateBySelective(RpUserPayInvoiceInfo record);

    RpUserPayInvoiceInfo findOneByTenantCodeAndUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    /**
     * 每日更新统计数据
     *
     * @return update count
     */
    int insertForAll();

    int deleteAll();
}
