package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 岗位配置表(SysPost)实体类
 *
 * <AUTHOR>
 * @since 2024-07-15 15:54:16
 */
@Data
public class SysPost implements Serializable {
    private static final long serialVersionUID = 884444762747405082L;
/**
     * 主键
     */
    private Integer postId;
/**
     * 租户编码
     */
    private String tenantCode;
/**
     * 岗位名称
     */
    private String postName;
/**
     * 岗位描述
     */
    private String postDesc;
    /**
     * 排序
     */
    private Integer orderIndex;
    /**
     * 岗位状态
     */
    private String postStatus;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 是否删除
     */
    private String isValid;

}

