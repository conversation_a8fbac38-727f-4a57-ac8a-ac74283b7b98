package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysMenuButton;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysMenuButtonMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(SysMenuButton record);

    int insertSelective(SysMenuButton record);

    SysMenuButton selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SysMenuButton record);

    int updateByPrimaryKey(SysMenuButton record);

    int existButtonCode(String buttonCode);

    List<SysMenuButton> selectByMenuCode(String menuCode);

    int deleteByCode(@Param("menuCode") String menuCode,@Param("buttonCode") String buttonCode);
}