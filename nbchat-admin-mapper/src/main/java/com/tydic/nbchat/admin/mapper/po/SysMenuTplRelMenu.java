package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;

public class SysMenuTplRelMenu {
    private Integer id;

    private String tplCode;

    private String menuCode;

    private String buttonCodes;

    private Date createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTplCode() {
        return tplCode;
    }

    public void setTplCode(String tplCode) {
        this.tplCode = tplCode == null ? null : tplCode.trim();
    }

    public String getMenuCode() {
        return menuCode;
    }

    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode == null ? null : menuCode.trim();
    }

    public String getButtonCodes() {
        return buttonCodes;
    }

    public void setButtonCodes(String buttonCodes) {
        this.buttonCodes = buttonCodes == null ? null : buttonCodes.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}