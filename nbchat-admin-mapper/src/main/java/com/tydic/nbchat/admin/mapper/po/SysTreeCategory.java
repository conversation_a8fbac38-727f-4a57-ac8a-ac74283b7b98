package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * 系统通用树形类目表(SysTreeCategory)实体类
 *
 * <AUTHOR>
 * @since 2024-10-16 14:40:02
 */
@Data
public class SysTreeCategory implements Serializable {
    private static final long serialVersionUID = 277811517626417979L;
/**
     * 树ID
     */
    private String cateId;
/**
     * 父ID
     */
    private String parentId;
/**
     * 用户ID
     */
    private String userId;
/**
     * 租户ID
     */
    private String tenantCode;
/**
     * 分类名称
     */
    private String cateName;
/**
     * 级别
     */
    private Integer cateLevel;
/**
     * 路径
     */
    private String ancestors;
/**
     * 0 系统默认 1 用户自定义
     */
    private String cateSource;
/**
     * 业务类型,区分不同业务用
     */
    private String busiType;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 创建人
     */
    private String createBy;
/**
     * 更新时间
     */
    private Date updateTime;
/**
     * 更新人
     */
    private String updateBy;
/**
     * 是否有效 1 有效 0 无效
     */
    private String isValid;
/**
     * 排序
     */
    private Integer orderIndex;

}

