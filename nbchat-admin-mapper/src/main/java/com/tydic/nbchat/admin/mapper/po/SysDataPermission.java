package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;
import lombok.Data;

/**
 * 通用权限表
 */
@Data
public class SysDataPermission {
    private Long id;

    /**
     * 业务类型
     */
    private String busiType;

    /**
     * 业务编码
     */
    private String busiId;

    /**
     * 权限: edit/view
     */
    private String permission;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 岗位ID
     */
    private String postId;

    /**
     * 租户编码
     */
    private String tenantCode;
}