package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUserRole;
import com.tydic.nbchat.admin.mapper.po.SysUserRoleRel;
import com.tydic.nbchat.admin.mapper.po.UserSettingPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysUserRoleMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(SysUserRole record);

    SysUserRole selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SysUserRole record);

    List<SysUserRole> selectBySubsystem(@Param("subsystem") String subsystem);

    List<SysUserRoleRel> selectUserRoles(@Param("subsystem") String subsystem,
                                         @Param("userId") String userId);

    int deleteUserRoles(@Param("subsystem") String subsystem,
                        @Param("userId") String userId,
                        @Param("role") String role);

    int addUserRoles(List<SysUserRoleRel> list);

    List<UserSettingPO> selectAllUserSetting();

    SysUserRoleRel selectUserRole(@Param("subsystem") String subsystem,
                                        @Param("userId") String userId,
                                        @Param("role") String role);
}