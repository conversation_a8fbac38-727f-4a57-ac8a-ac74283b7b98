package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpPptLayoutUsage;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface RpPptLayoutUsageMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(RpPptLayoutUsage record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RpPptLayoutUsage record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    RpPptLayoutUsage selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RpPptLayoutUsage record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RpPptLayoutUsage record);

    List<RpPptLayoutUsage> selectByAll(RpPptLayoutUsage rpPptLayoutUsage);

    int updateBatchSelective(@Param("list") List<RpPptLayoutUsage> list);

    int batchInsert(@Param("list") List<RpPptLayoutUsage> list);
}