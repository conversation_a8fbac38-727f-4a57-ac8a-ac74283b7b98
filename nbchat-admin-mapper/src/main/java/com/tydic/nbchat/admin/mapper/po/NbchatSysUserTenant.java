package com.tydic.nbchat.admin.mapper.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NbchatSysUserTenant {
    /**
     * ID
     */
    private Integer id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 租代码
     */
    private String tenantCode;

    /**
     * 用户真实姓名
     */
    private String userRealityName;

    /**
     * 创建类型
     */
    private String joinType;

    /**
     * 创建时间
     */
    private Date createTime;

    // 以下是新增字段  用户头像、性别、身份证号、岗位、出生日期
    private String avatar;
    private String idCard;
    private Date birthday;
    private String gender;
    private Date updateTime;
    private String userStatus;

}