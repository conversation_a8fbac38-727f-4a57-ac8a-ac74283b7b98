package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysDictConfig;
import com.tydic.nbchat.admin.mapper.po.SysDictConfigResult;
import com.tydic.nbchat.admin.mapper.po.SysDictConfigSelectCondition;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysDictConfigMapper {
    List<SysDictConfig> selectByCode(
            @Param("tenantCode") String tenantCode
            ,@Param("dictCode") String dictCode);

    /**
     * 根据字典名称和字典描述对dict_code分组查询
     */
    List<SysDictConfigResult> selectByGroup(SysDictConfigSelectCondition reqBO);
    /**
     * 根据dict_code删除列表
     */
    Integer deleteByDictCode(@Param("dictCode") String dictCode);
    /**
     * 根据dict_code 批量新增字典配置
     */
    Integer insertDictCode(@Param("list") List<SysDictConfig> list);
    /**
     * 根据指定字典code更新状态
     */
    int updateDictStatus(@Param("list") List<SysDictConfig> dictList);
}