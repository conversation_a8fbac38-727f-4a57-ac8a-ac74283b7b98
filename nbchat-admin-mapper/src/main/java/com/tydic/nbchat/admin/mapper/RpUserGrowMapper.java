package com.tydic.nbchat.admin.mapper;

import org.springframework.data.repository.query.Param;

import java.util.Date;

public interface RpUserGrowMapper {
    /**
     * 查询新增付费用户
     * @param beginDate
     * @param endDate
     * @return
     */
    int queryVipUser(Date beginDate, Date endDate);

    /**
     * 查询用户总数
     * @param endDate
     * @return
     */
    int queryTotalUser(Date beginDate, Date endDate);

    /**
     * 查询新增免费用户
     * @param beginDate
     * @param endDate
     * @return
     */
    int queryNewFreeUser(Date beginDate, Date endDate);
}
