package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUserStar;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface SysUserStarMapper {
    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SysUserStar record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SysUserStar record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SysUserStar selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SysUserStar record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SysUserStar record);

    List<SysUserStar> selectByAll(SysUserStar sysUserStar);

    int updateBatchSelective(@Param("list") List<SysUserStar> list);

    int batchInsert(@Param("list") List<SysUserStar> list);

    int deleteById(@Param("id")Long id);

    SysUserStar selectOne(SysUserStar sysUserStar);

}