package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpUserPaymentInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:43
 * @description
 */
public interface RpUserPaymentInfoMapper {

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RpUserPaymentInfo record);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RpUserPaymentInfo record);

    int updateBatchSelective(@Param("list") List<RpUserPaymentInfo> list);

    int updateForStat(RpUserPaymentInfo record);

    RpUserPaymentInfo findOneByTenantCodeAndUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    List<RpUserPaymentInfo> selectScoreBalance();

    List<RpUserPaymentInfo> selectScoreDetail();

    List<RpUserPaymentInfo> selectRefund();

    List<RpUserPaymentInfo> selectPayInfo();

    RpUserPaymentInfo selectScoreBalanceByUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    RpUserPaymentInfo selectScoreDetailByUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    RpUserPaymentInfo selectRefundByUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    RpUserPaymentInfo selectPayInfoByUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    int deleteAll();
}
