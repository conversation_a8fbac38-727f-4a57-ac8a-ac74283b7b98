package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;
import lombok.Data;

/**
 * 租户对应的子系统配置
 */
@Data
public class SysTenantSubsystem {
    private Integer id;

    /**
    * 租户ID
    */
    private String tenantCode;

    /**
    * 子系统ID
    */
    private String subsystem;

    /**
    * 描述
    */
    private String remark;

    /**
    * 菜单模板ID
    */
    private String tplCode;

    /**
    * 个性化配置
    */
    private String extInfo;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 状态 0-禁用 1-启用
    */
    private String status;
}