package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/25 15:39
 * @description
 */

/**
 * 用户会员表
 */
@Data
public class NbchatUserVip {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户id
     */
    private String tenantCode;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 会员类型 1体验会员 2高级会员 3专业会员
     */
    private String vipType;

    /**
     * 会员描述
     */
    private String vipDesc;

    /**
     * 会员状态 0 已过期 1 有效
     */
    private String vipStatus;

    /**
     * 是否企业会员 0否 1是
     */
    private String isCompany;

    /**
     * 会员开始时间
     */
    private Date vipStart;

    /**
     * 会员结束时间
     */
    private Date vipEnd;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 0未体验 1已体验
     */
    private String isExperienced;

    /**
     * vip版本 0:体验 1:月费 2:年费
     */
    private String vipVersion;

    private Date firstVipTime;
    private Date lastVipTime;
    private Integer totalAmount;
    private Integer vipDays;
    private Integer vipTimes;
}
