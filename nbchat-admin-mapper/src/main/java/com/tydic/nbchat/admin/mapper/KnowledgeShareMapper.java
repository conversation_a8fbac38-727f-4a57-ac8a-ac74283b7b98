package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.KnowledgeSharePo;

public interface KnowledgeShareMapper {
    int deleteByPrimaryKey(String sentenceShareId);

    int insert(KnowledgeSharePo record);

    int insertSelective(KnowledgeSharePo record);

    KnowledgeSharePo queryByCondition(KnowledgeSharePo po);

    KnowledgeSharePo queryById( String sentenceShareId);

    KnowledgeSharePo selectByPrimaryKey(String sentenceShareId);

    int updateByPrimaryKeySelective(KnowledgeSharePo record);

    int updateByPrimaryKey(KnowledgeSharePo record);
}