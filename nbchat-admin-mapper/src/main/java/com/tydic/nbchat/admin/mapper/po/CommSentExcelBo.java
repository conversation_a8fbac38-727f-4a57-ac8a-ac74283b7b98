package com.tydic.nbchat.admin.mapper.po;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ContentRowHeight(10)
@HeadRowHeight(20)
@ColumnWidth(25)
public class CommSentExcelBo implements Serializable {
//    @ExcelProperty("编号")
//    private String sentenceId;//编号
    @ExcelProperty("所属分类")
    private String typeName;
    @ExcelProperty("内容名称")
    private String contentTitle;//内容名称
    @ExcelProperty("内容")
    private String content;//知识内容
    @ExcelProperty("创建时间")
    private Date createTime;//创建时间
    @ExcelProperty("更新时间")
    private Date updateTime;//更新时间
    @ExcelIgnore
    private Long parentTypeId;

}
