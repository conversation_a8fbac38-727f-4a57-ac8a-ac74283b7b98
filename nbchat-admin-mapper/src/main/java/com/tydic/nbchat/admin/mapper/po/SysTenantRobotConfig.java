package com.tydic.nbchat.admin.mapper.po;

import java.io.Serializable;
import java.util.Date;

public class SysTenantRobotConfig implements Serializable {
    private static final long serialVersionUID = 235713026012617340L;
    /**
     * 主键自增
     */
    private Integer id;
    /**
     * 0 租户 
1 用户
     */
    private String configType;
    /**
     * 租户id/用户id
     */
    private String configId;
    /**
     * 默认值
     */
    private String configValue;
    /**
     * 选项列表[“chatgpt”,”chatglm”]
     */
    private String configList;
    
    private Date createTime;
    
    private Date updateTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getConfigType() {
        return configType;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getConfigList() {
        return configList;
    }

    public void setConfigList(String configList) {
        this.configList = configList;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

}

