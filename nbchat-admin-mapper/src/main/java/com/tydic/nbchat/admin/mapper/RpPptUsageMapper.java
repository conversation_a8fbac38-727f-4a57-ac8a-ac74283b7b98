package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpPptUsage;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface RpPptUsageMapper {
    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(RpPptUsage record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(RpPptUsage record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    RpPptUsage selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(RpPptUsage record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(RpPptUsage record);

    List<RpPptUsage> selectByAll(RpPptUsage rpPptUsage);

    int updateBatchSelective(@Param("list") List<RpPptUsage> list);

    int batchInsert(@Param("list") List<RpPptUsage> list);

    int deleteByPrimaryKeyIn(List<Long> list);
}