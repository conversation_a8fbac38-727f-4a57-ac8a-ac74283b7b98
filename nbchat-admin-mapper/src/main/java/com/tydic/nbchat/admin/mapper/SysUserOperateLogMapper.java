package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUserOperateLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/20 19:16
 * @description
 */

public interface SysUserOperateLogMapper {

    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SysUserOperateLog record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SysUserOperateLog record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SysUserOperateLog selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SysUserOperateLog record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SysUserOperateLog record);

    int updateBatch(@Param("list") List<SysUserOperateLog> list);

    int updateBatchSelective(@Param("list") List<SysUserOperateLog> list);

    int batchInsert(@Param("list") List<SysUserOperateLog> list);

    /**
     * 统计并分组用户操作日志记录，依据租户代码和用户ID进行分组。
     *
     * @return 按照租户代码和用户ID分组后的SysUserOperateLog列表
     */
    List<SysUserOperateLog> countGroupByTenantCodeAndUserIdSelective();
}
