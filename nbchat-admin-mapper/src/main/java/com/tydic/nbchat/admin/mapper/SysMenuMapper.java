package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysMenu;

import java.util.List;

public interface SysMenuMapper {

    int deleteByMenuCode(String menuCode);

    int existMenuCode(String menuCode);

    int deleteByPrimaryKey(Integer id);

    int insertSelective(SysMenu record);

    SysMenu selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SysMenu record);

    List<SysMenu> selectByCode(String menuCode);

}