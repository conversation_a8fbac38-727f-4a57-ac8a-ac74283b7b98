package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;

public class SysDictConfig {

    private Integer dictId;
    private String tenantCode;
    private String channelCode;
    private String dictCode;
    private Date crtTime;
    private String dictName;
    private String dictValue;
    private String dictDesc;
    private Integer orderIndex;
    private String isValid;

    public String getIsValid() {
        return isValid;
    }

    public void setIsValid(String isValid) {
        this.isValid = isValid;
    }

    public Integer getDictId() {
        return dictId;
    }

    public void setDictId(Integer dictId) {
        this.dictId = dictId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getDictCode() {
        return dictCode;
    }

    public void setDictCode(String dictCode) {
        this.dictCode = dictCode;
    }

    public Date getCrtTime() {
        return crtTime;
    }

    public void setCrtTime(Date crtTime) {
        this.crtTime = crtTime;
    }

    public String getDictName() {
        return dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    public String getDictValue() {
        return dictValue;
    }

    public void setDictValue(String dictValue) {
        this.dictValue = dictValue;
    }

    public String getDictDesc() {
        return dictDesc;
    }

    public void setDictDesc(String dictDesc) {
        this.dictDesc = dictDesc;
    }

    public Integer getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(Integer orderIndex) {
        this.orderIndex = orderIndex;
    }

    @Override
    public String toString() {
        return "SysDictConfig{" +
                "dictId=" + dictId +
                ", tenantCode='" + tenantCode + '\'' +
                ", channelCode='" + channelCode + '\'' +
                ", dictCode='" + dictCode + '\'' +
                ", crtTime=" + crtTime +
                ", dictName='" + dictName + '\'' +
                ", dictValue='" + dictValue + '\'' +
                ", dictDesc='" + dictDesc + '\'' +
                ", orderIndex=" + orderIndex +
                '}';
    }
}