package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.PermissionObj;
import com.tydic.nbchat.admin.mapper.po.PermissionObjectsCondition;
import com.tydic.nbchat.admin.mapper.po.SysDataPermission;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

public interface SysDataPermissionMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(SysDataPermission record);

    int insertOrUpdate(SysDataPermission record);

    int insertOrUpdateSelective(SysDataPermission record);

    int insertSelective(SysDataPermission record);

    SysDataPermission selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysDataPermission record);

    int updateByPrimaryKey(SysDataPermission record);

    List<SysDataPermission> findByAll(SysDataPermission sysDataPermission);

    SysDataPermission queryById(@Param("id") Long id);

    int updateBatch(@Param("list") List<SysDataPermission> list);

    int updateBatchSelective(@Param("list") List<SysDataPermission> list);

    int batchInsert(@Param("list") List<SysDataPermission> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<SysDataPermission> list);

    int deleteByBusiIdAndType(@Param("busiType") String busiType, @Param("busiId") String busiId);

    /**
     * 根据业务id和业务类型查询权限
     * @param busiId
     * @param busiType
     * @param object
     * @return
     */
    Set<String> selectPermissionByBusiIdAndType(
            @Param("busiId") String busiId,
            @Param("busiType") String busiType,
            @Param("object") PermissionObjectsCondition object);


    List<SysDataPermission> selectByBusiIdAndType(@Param("busiType") String busiType, @Param("busiId") String busiId);

    List<PermissionObj> queryDataPermission(@Param("tenantCode") String tenantCode,
                                            @Param("busiType") String busiType,
                                            @Param("busiId") String busiId
                                            );
}