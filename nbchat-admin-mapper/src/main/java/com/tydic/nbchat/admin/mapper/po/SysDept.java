package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (NbchatSysDept)实体类
 *
 * <AUTHOR>
 * @since 2023-11-20 17:34:50
 */
@Data
public class SysDept implements Serializable {
    private static final long serialVersionUID = -23780976380255029L;
/**
     * 部门id
     */
    private String deptId;
/**
     * 上级部门
     */
    private String parentId;
/**
     * 层级
     */
    private Integer level;
/**
     * 部门
     */
    private String deptName;

/**
     * 描述
     */
    private String deptDesc;
/**
     * 租户编码
     */
    private String tenantCode;
/**
     * 子系统
     */
    private String subsystem;
/**
     * 创建时间
     */
    private Date createTime;
    private String createBy;
    private Date updateTime;
    private String updateBy;
    private String deptType;
    private String ancestors;
    private Integer orderIndex;
    private String leader;
    private String phone;
    private String email;
    private String status;
    /**
     * 1 正常 0 已删除
     */
    private String isValid;

}

