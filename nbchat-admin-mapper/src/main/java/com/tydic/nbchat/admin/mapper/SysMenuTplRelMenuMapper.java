package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysMenuTplRelMenu;

import java.util.List;

public interface SysMenuTplRelMenuMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(SysMenuTplRelMenu record);

    SysMenuTplRelMenu selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(SysMenuTplRelMenu record);

    int deleteByTplCode(String tplCode);

    int insertBatch(List<SysMenuTplRelMenu> list);

    /**
     * 根据模板编码查询
     * @param tplCode
     * @return
     */
    List<SysMenuTplRelMenu> selectAllByTplCode(String tplCode);
}