package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;
import lombok.Data;

/**
 * 帮助中心表
 */
@Data
public class SysHelpCenter {
    /**
     * 树ID
     */
    private String cateId;
    /**
     * 树名称
     */
    private String cateName;
    /**
    * 主键
    */
    private Integer id;

    /**
    * 租户编码
    */
    private String tenantCode;

    /**
    * 标题
    */
    private String title;

    /**
    * 缩略图
    */
    private String thumbUrl;

    /**
    * 内容: 富文本内容/视频链接
    */
    private String content;

    /**
    * 分类
    */
    private String category;

    /**
    * 业务类型,区分不同业务用
    */
    private String busiType;

    /**
    * 状态 0 下架 / 1 上架
    */
    private String status;

    /**
    * 标签
    */
    private String tag;

    /**
    * 创建人
    */
    private String createUser;

    /**
    * 创建时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 排序
    */
    private Integer orderIndex;

    /**
    * 是否有效 0 删除 1 有效
    */
    private String isValid;
    /**
     * 视频时长
     */
    private Integer duration;
}