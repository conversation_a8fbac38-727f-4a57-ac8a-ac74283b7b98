package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUserCooperate;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SysUserCooperateMapper {
    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(SysUserCooperate record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(SysUserCooperate record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    SysUserCooperate selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SysUserCooperate record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SysUserCooperate record);

    List<SysUserCooperate> selectByAll(SysUserCooperate sysUserCooperate);

    int updateBatchSelective(@Param("list") List<SysUserCooperate> list);

    int batchInsert(@Param("list") List<SysUserCooperate> list);

    int deleteByPrimaryKeyIn(List<Integer> list);
}