package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface KnowledgeContentMapper {
    int deleteByPrimaryKey(String sentenceId);

    int insert(KnowledgeContentPo record);

    int insertSelective(KnowledgeContentPo record);

    KnowledgeContentPo selectByPrimaryKey(String sentenceId);

    int updateByPrimaryKeySelective(KnowledgeContentPo record);

    int updateByPrimaryKey(KnowledgeContentPo record);

    List<KnowledgeContentPo> selectByPo(KnowledgeContentPo knowledgeContentPo);

    List<KnowledgeContentPo> selectByContent(KnowledgeContentPo po);

    int deleteByTypeIds(@Param("createUserId") String createUserId,@Param("typeIds") List<Long> typeIds);
}
