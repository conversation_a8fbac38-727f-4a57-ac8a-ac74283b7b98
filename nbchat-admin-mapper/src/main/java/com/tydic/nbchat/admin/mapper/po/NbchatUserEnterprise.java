package com.tydic.nbchat.admin.mapper.po;

import java.io.Serializable;
import java.util.Date;

/**
 * (NbchatUserEnterprise)实体类
 *
 * <AUTHOR>
 * @since 2024-03-01 11:19:34
 */
public class NbchatUserEnterprise implements Serializable {
    private static final long serialVersionUID = -36642848376307488L;

    private String userId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 所属行业
     */
    private String businessSector;
    /**
     * 需求
     */
    private String requirement;
    /**
     * 创建时间
     */
    private Date createTime;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getBusinessSector() {
        return businessSector;
    }

    public void setBusinessSector(String businessSector) {
        this.businessSector = businessSector;
    }

    public String getRequirement() {
        return requirement;
    }

    public void setRequirement(String requirement) {
        this.requirement = requirement;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}

