package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;
import lombok.Data;

/**
 * 更新日志记录
 */
@Data
public class SysUpdateLog {
    private Integer id;

    /**
     * 版本号/名称
     */
    private String version;

    /**
     * 描述
     */
    private String remark;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 点击量
     */
    private Integer clickCount;

    /**
     * 浏览量
     */
    private Integer viewCount;

    /**
     * 更新内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 0-未发布/1-已发布
     */
    private String status;

    /**
     * 0-删除/1-有效
     */
    private String isValid;
}