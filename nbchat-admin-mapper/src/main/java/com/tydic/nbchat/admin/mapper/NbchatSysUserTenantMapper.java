package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.NbchatSysUserTenant;
import com.tydic.nbchat.admin.mapper.po.SysDeptUserSelectCondition;
import com.tydic.nbchat.admin.mapper.po.SysDeptUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface NbchatSysUserTenantMapper {
    int deleteByPrimaryKey(Integer id);

    int insertSelective(NbchatSysUserTenant record);

    NbchatSysUserTenant selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(NbchatSysUserTenant record);

    List<NbchatSysUserTenant> selectList(SysDeptUserSelectCondition condition);

    NbchatSysUserTenant selectByUserIdAndTenantCode(@Param("userId") String userId,
                                                    @Param("tenantCode") String targetTenant);

    List<SysDeptUser> selectDeptUserByCondition(SysDeptUserSelectCondition condition);

    String getPhone(String userId);

    List<NbchatSysUserTenant> selectNotSyncUserInfo();

    NbchatSysUserTenant selectUserInfo(String userId, String tenantCode);

    int updateUserInfo(NbchatSysUserTenant userTenant);

    Boolean checkIfExists(String role, String deptId, Integer postId);

    int selectUserCount(String targetTenant);
}