package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PayOrderPO implements Serializable {
    private static final long serialVersionUID = 882589432098110065L;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 支付渠道 JSAPI：公众号支付
     NATIVE：扫码支付
     App：App支付
     MICROPAY：付款码支付
     MWEB：H5支付
     FACEPAY：刷脸支付
     */
    private String channel;
    /**
     * 订单总金额/分
     */
    private Integer totalPrice;
    /**
     * 优惠总金额/分
     */
    private Integer discountPrice;
    /**
     * 订单状态 0 待支付 1 已支付 2 已发货 3 已关闭 4支付失败
     */
    private String orderStatus;
    /**
     * 下单时间
     */
    private Date orderTime;
    /**
     * 支付时间
     */
    private Date payTime;
    /**
     * 支付方式 WX微信 ALI支付宝 BANK银联
     */
    private String payType;
    /**
     * 支付流水号
     */
    private String tradeNo;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否有效 0无效 1有效
     */
    private String isValid;
    private Date beginDate;
    private Date endDate;
}
