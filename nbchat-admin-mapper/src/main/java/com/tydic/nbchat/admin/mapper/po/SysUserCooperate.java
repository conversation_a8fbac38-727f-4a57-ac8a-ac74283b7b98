package com.tydic.nbchat.admin.mapper.po;

import java.util.Date;
import lombok.Data;

/**
 * 用户合作申请
 */
@Data
public class SysUserCooperate {
    private Integer id;

    /**
    * 姓名
    */
    private String name;

    /**
    * 申请人电话
    */
    private String phone;

    /**
    * 省份
    */
    private String province;

    /**
    * 城市
    */
    private String city;

    /**
    * 公司名称
    */
    private String company;

    /**
    * 短信验证码
    */
    private String smsCode;

    /**
    * 申请时间
    */
    private Date createTime;

    /**
    * 更新时间
    */
    private Date updateTime;

    /**
    * 申请状态: 0 未处理 1 已处理
    */
    private String status;

    /**
    * 是否有效 0 删除 1 有效
    */
    private String isValid;
}