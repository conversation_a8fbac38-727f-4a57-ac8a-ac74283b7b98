package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:26
 * @description 用户使用明细报表
 */
@Data
public class OpRpUserDetail {
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 用户属性
     */
    private String userType;

    /**
     * 租户编码
     */
    private String tenantCode;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 注册时间
     */
    private Date regTime;

    /**
     * 注册渠道
     */
    private String regChannel;

    /**
     * 会员状态:2 未开通(免费)  0 已过期 1 有效
     */
    private String vipStatus;

    /**
     * 会员类型
     */
    private String vipType;

    /**
     * 付费时间:vip开始时间
     */
    private Date vipStartTime;

    /**
     * 到期时间
     */
    private Date vipEndTime;

    /**
     * 首次开通时间
     */
    private Date vipFirstTime;

    /**
     * 会员购买次数
     */
    private Integer vipBuyCount;

    /**
     * 购买版本:月付/年付
     */
    private String buyVersion;

    /**
     * 上次登录时间
     */
    private Date loginTime;

    /**
     * 是否绑定微信 0-否/1-是
     */
    private String isBindWx;

    /**
     * 剩余算力点
     */
    private Integer scoreBalance;
    /**
     * 充值积分
     */
    private Integer scoreRecharge;
    /**
     * 消耗积分
     */
    private Integer scoreConsume;

    /**
     * 登录次数
     */
    private Integer loginCount;

    /**
     * 视频制作次数
     */
    private Integer videoMakeCount;

    /**
     * 成功次数
     */
    private Integer videoSuccessCount;

    /**
     * ppt制作次数
     */
    private Integer pptMakeCount;

    /**
     * 成功次数
     */
    private Integer pptSuccessCount;

    /**
     * 出题次数
     */
    private Integer examMakeCount;

    /**
     * 成功次数
     */
    private Integer examSuccessCount;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 注册来源: 课件帮平台、迪易学、迪问、人工创建、企业邀请码
     */
    private String regSource;

    /**
     * 搜索关键词
     */
    private String promKey;

    /**
     * 推广id: 百度搜索携带的id
     */
    private String promId;

    /**
     * 推广渠道: 百度
     */
    private String promChannel;
    /**
     * 课件帮登次数
     */
    private Integer tdhLoginCount;
    //临时字段
    private Date payTime;

    /**
     * 最近支付时间
     */
    private Date lastPayTime;

    /**
     * 最近支付金额
     */
    private Integer lastPayAmount;

    /**
     * 支付总金额
     */
    private Integer totalPayAmount;

    /**
     * 加油包购买次数
     */
    private Integer scoreBuyCount;

    /**
     * 累计充值算力点
     */
    private Integer scoreRechargeTotal;

    /**
     * 算力点累计消耗
     */
    private Integer scoreConsumeTotal;

    /**
     * 最近购买商品名称
     */
    private String lastGoodsName;
}
