package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.NbchatSysTenant;
import com.tydic.nbchat.admin.mapper.po.NbchatSysTenantSelectCondition;
import com.tydic.nbchat.admin.mapper.po.SysUserInTenant;
import org.apache.ibatis.annotations.Param;


import java.util.List;

public interface NbchatSysTenantMapper {
    int deleteByPrimaryKey(String tenantCode);

    int insertSelective(NbchatSysTenant record);

    NbchatSysTenant selectByPrimaryKey(String tenantCode);

    int updateByPrimaryKeySelective(NbchatSysTenant record);

    List<NbchatSysTenant> selectByCondition(NbchatSysTenantSelectCondition condition);

    String getUserRole(@Param("userId") String userId);

    List<NbchatSysTenant> selectTenantList(NbchatSysTenantSelectCondition condition);

    /**
     * 查询用户管理的租户列表
     * @param userId
     * @param role
     * @return
     */
    List<NbchatSysTenant> selectTenantListByUseRole(@Param("userId") String userId);

    /**
     * 查询用户归属的租户列表
     * @param userId
     * @return
     */
    List<SysUserInTenant> selectUserTenantList(@Param("userId") String userId);

    /**
     * 校验是否存在租户下
     * @param userId
     * @return
     */
    int checkUserInTenant(@Param("tenantCode") String tenantCode, @Param("userId") String userId);
}