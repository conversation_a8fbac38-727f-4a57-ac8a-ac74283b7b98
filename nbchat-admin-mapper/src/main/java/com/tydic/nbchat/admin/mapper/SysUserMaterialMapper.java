package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUserMaterial;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SysUserMaterialMapper {
    List<SysUserMaterial> selectAll(SysUserMaterial sysUserMaterial);
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(SysUserMaterial record);

    int insertOrUpdate(SysUserMaterial record);

    int insertOrUpdateSelective(SysUserMaterial record);

    int insertSelective(SysUserMaterial record);

    SysUserMaterial selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysUserMaterial record);

    int updateByPrimaryKey(SysUserMaterial record);

    int updateBatch(@Param("list") List<SysUserMaterial> list);

    int updateBatchSelective(@Param("list") List<SysUserMaterial> list);

    int batchInsert(@Param("list") List<SysUserMaterial> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<SysUserMaterial> list);
}