package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

@Data
public class SysDeptUser {
    /**
     * 用户名
     */
    private String userName;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 名字
     */
    private String name;
    /**
     * id
     */
    private Integer id;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 电话
     */
    private String phone;
    /**
     * 用户真实姓名
     */
    private String userRealityName;
    /**
     * 创建类型
     */
    private String joinType;
    /**
     * 租户代码
     */
    private String tenantCode;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 排序
     */
    private Integer balanceScore;

    private String idCard;
    private String postId;
    private Date birthday;
    private String gender;
    private Date updateTime;
    private String userStatus;
}
