package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.CommSentExcelBo;
import com.tydic.nbchat.admin.mapper.po.CommSentenceRowPO;
import com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * @Author: shenzh
 * @Date: Create in 15:03 2020/1/7
 */
@Mapper
public interface KnowledgeMapper {

    /**
     * 新增知识
     *
     * @param record
     */
    int insert(KnowledgeContentPo record);

    int insertSelective(KnowledgeContentPo record);

    /**
     * 分页查询知识
     *
     * @param params
     * @return
     */
    List<CommSentenceRowPO> getCommSentencePageList(Map<String, Object> params);

    /**
     * 获取知识分类下拉列表
     *
     * @param params
     * @return
     */
    List<Map<String, String>> getCommSentenceTypeList(Map<String, Object> params);

    List<CommSentExcelBo> getExportList(KnowledgeContentPo knowledgeContentPo);

}
