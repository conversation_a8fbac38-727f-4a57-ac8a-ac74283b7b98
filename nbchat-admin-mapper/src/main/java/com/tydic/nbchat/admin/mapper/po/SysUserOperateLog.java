package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/20 19:16
 * @description 用户操作日志表
 */
@Data
public class SysUserOperateLog {

    private Integer id;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 操作类型: 参考常量定义
     */
    private String type;
    /**
     * 业务ID
     */
    private String busiId;
    /**
     * 操作内容
     */
    private String content;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 首次访问时间
     */
    private Date firstAccessTime;
    /**
     * 最近访问时间
     */
    private Date lastAccessTime;
}
