package com.tydic.nbchat.admin.mapper;



import com.tydic.nbchat.admin.mapper.po.NbchatUserEnterprise;
import java.util.List;

/**
 * (NbchatUserEnterprise)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-01 11:16:47
 */
public interface NbchatUserEnterpriseMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param userId 主键
     * @return 实例对象
     */
    NbchatUserEnterprise queryById(String userId);

    List<NbchatUserEnterprise> selectAll(NbchatUserEnterprise nbchatUserEnterprise);

    /**
     * 新增数据
     *
     * @param nbchatUserEnterprise 实例对象
     * @return 影响行数
     */
    int insert(NbchatUserEnterprise nbchatUserEnterprise);


    int insertSelective(NbchatUserEnterprise nbchatUserEnterprise);

      /**
     * 修改数据
     *
     * @param nbchatUserEnterprise 实例对象
     * @return 影响行数
     */
    int update(NbchatUserEnterprise nbchatUserEnterprise);

    /**
     * 通过主键删除数据
     *
     * @param userId 主键
     * @return 影响行数
     */
    int deleteById(String userId);

    List<String> queryEnterpriseList(String companyName);
}

