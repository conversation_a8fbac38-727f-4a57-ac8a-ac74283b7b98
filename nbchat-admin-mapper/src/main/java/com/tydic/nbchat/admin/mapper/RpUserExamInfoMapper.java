package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpUserExamInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:43
 * @description
 */

public interface RpUserExamInfoMapper {

    int batchInsertForTimer(@Param("list") List<RpUserExamInfo> list);

    /**
     * Performs a batch update on a list of RpUserExamInfo objects.
     *
     * @param incrementFlag 是否为增量
     * @param list          the list of RpUserExamInfo objects to be updated
     * @return the number of rows affected by the batch update
     */
    int batchUpdateForTimer(@Param("incrementFlag") boolean incrementFlag, @Param("list") List<RpUserExamInfo> list);

    /**
     * 查询指定租户代码和用户ID的考试信息。
     *
     * @param tenantCode 租户代码
     * @param userId     用户ID
     * @return 符合条件的RpUserExamInfo对象，如果未找到则返回null
     */
    RpUserExamInfo findOneByTenantCodeAndUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    /**
     * 每日更新统计数据
     *
     * @return update count
     */
    int insertForAll();

    int deleteAll();
}
