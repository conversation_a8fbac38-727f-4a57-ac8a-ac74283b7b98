package com.tydic.nbchat.admin.mapper.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUserRecord {
    private Integer id;
    private String table;
    private String column;
    private String oldValue;
    private String newValue;
    private Date createTime;

    public UpdateUserRecord(String table, String column, String oldValue, String newValue, Date createTime) {
        this.table = table;
        this.column = column;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.createTime = createTime;
    }
}
