package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.ExamCreationRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/31 17:42
 * @description
 */
public interface ExamCreationRecordMapper {

    List<ExamCreationRecord> countByGroupUserIdWhenCreateTimeBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
