package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpActivatePO;
import com.tydic.nbchat.admin.mapper.po.SysLoginLog;

import java.util.List;

public interface RpActivateMapper {

    List<SysLoginLog> selectBaseData(RpActivatePO po);

    //活跃用户数
    int activateUserCount(RpActivatePO po);

    //免费产品活跃用户数
    int activateFreeUserCount(RpActivatePO po);

    //新注册用户活跃数
    int activateNewUserCount(RpActivatePO po);

    //付费产品活跃数
    int activateVipUserCount(RpActivatePO po);
}
