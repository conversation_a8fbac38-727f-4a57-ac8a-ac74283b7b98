package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysTenantSubsystem;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SysTenantSubsystemMapper {

    List<SysTenantSubsystem> selectByTenantCode(String tenantCode);

    /**
     * 根据租户编码删除子系统配置
     *
     * @param tenantCode 租户编码
     */
    void deleteByTenantCode(String tenantCode);
    /**
     * 根据租户编码和子系统查询子系统配置
     *
     * @param tenantCode 租户编码
     * @param tenantSubsystem 子系统
     * @return 子系统配置
     */
    List<SysTenantSubsystem> selectByTenantCodeAndSubsystem(@Param("tenantCode") String tenantCode,@Param("platformCode") String platformCode);

    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    int deleteByPrimaryKeyIn(List<Integer> list);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(SysTenantSubsystem record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(SysTenantSubsystem record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    SysTenantSubsystem selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SysTenantSubsystem record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SysTenantSubsystem record);

    List<SysTenantSubsystem> selectByAll(SysTenantSubsystem sysTenantSubsystem);

    int updateBatchSelective(@Param("list") List<SysTenantSubsystem> list);

    int batchInsert(@Param("list") List<SysTenantSubsystem> list);
}