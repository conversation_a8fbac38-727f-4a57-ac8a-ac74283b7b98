package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.PermissionObj;
import com.tydic.nbchat.admin.mapper.po.PermissionObjectsCondition;
import com.tydic.nbchat.admin.mapper.po.SysDataPermission;
import com.tydic.nbchat.admin.mapper.po.SysTrainPermission;

import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

public interface SysTrainPermissionMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(SysTrainPermission record);

    int insertOrUpdate(SysTrainPermission record);

    int insertOrUpdateSelective(SysTrainPermission record);

    int insertSelective(SysTrainPermission record);

    SysTrainPermission selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SysTrainPermission record);

    int updateByPrimaryKey(SysTrainPermission record);

    int updateBatch(@Param("list") List<SysTrainPermission> list);

    int updateBatchSelective(@Param("list") List<SysTrainPermission> list);

    int batchInsert(@Param("list") List<SysTrainPermission> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<SysTrainPermission> list);

    /**
     * 根据业务id和业务类型删除
     * @param busiId
     * @param busiType
     * @return
     */
    int deleteByBusiIdAndType(@Param("busiType") String busiType,@Param("busiId") String busiId);

    /**
     * 根据业务id和业务类型查询权限
     * @param busiId
     * @param busiType
     * @param object
     * @return
     */
    Set<String> selectPermissionByBusiIdAndType(
                         @Param("busiId") String busiId,
                          @Param("busiType") String busiType,
                          @Param("object") PermissionObjectsCondition object);

    List<SysTrainPermission> selectByBusiIdAndType(@Param("busiType") String busiType, @Param("busiId") String busiId);

    List<PermissionObj> queryTrainPermission(@Param("tenantCode") String tenantCode,
                                            @Param("busiType") String busiType,
                                            @Param("busiId") String busiId

    );
}