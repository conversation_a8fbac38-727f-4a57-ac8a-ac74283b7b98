package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.RpUserPromotionInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:44
 * @description
 */

public interface RpUserPromotionInfoMapper {

    int batchInsert(@Param("list") List<RpUserPromotionInfo> list);

    int batchUpdate(@Param("incrementFlag") boolean incrementFlag, @Param("list") List<RpUserPromotionInfo> list);

    RpUserPromotionInfo findOneByTenantCodeAndUserId(@Param("tenantCode") String tenantCode, @Param("userId") String userId);

    /**
     * 每日更新统计数据
     *
     * @return update count
     */
    int insertForAll();

    int deleteAll();
}
