package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.NbchatUserAssistDetail;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/1 16:50
 * @description
 */

public interface NbchatUserAssistDetailMapper {

    List<NbchatUserAssistDetail> countByGroupUserIdWhenCreateTimeBetween(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
