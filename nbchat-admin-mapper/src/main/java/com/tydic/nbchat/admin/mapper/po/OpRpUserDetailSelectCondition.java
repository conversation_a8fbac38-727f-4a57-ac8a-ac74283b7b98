package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

@Data
public class OpRpUserDetailSelectCondition {

    //手机号 用户属性 企业名称 会员类型 注册时间 5个筛选条件

    private String keyword;

    private Long id;

    private String userId;

    private String userName;

    private String phone;

    private String userType;

    private String tenantCode;

    private String companyName;

    //检索条件
    private Date regStartTime;
    private Date regEndTime;

    private String regChannel;

    private String vipStatus;

    private String vipType;
    /**
     * 注册来源
     */
    private String regSource;
    /**
     * 关键词
     */
    private String promKey;
    /**
     * 渠道Id
     */
    private String promId;
    /**
     * 推广渠道
     */
    private String promChannel;
    private Date payStartTime;
    private Date payEndTime;
    //是否付费
    private String isPay;

    /**
     * 视频制作次数
     */
    private String videoMakeCountFilter;
    /**
     * PPT制作次数
     */
    private String pptMakeCountFilter;
    /**
     * 试题制作次数
     */
    private String examMakeCountFilter;

}