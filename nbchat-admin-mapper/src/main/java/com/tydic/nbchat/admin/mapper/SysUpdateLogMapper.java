package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysUpdateLog;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface SysUpdateLogMapper {
    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SysUpdateLog record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SysUpdateLog record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SysUpdateLog selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SysUpdateLog record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SysUpdateLog record);

    List<SysUpdateLog> selectByAll(SysUpdateLog sysUpdateLog);

    int updateBatchSelective(@Param("list") List<SysUpdateLog> list);

    int batchInsert(@Param("list") List<SysUpdateLog> list);

    int deleteByPrimaryKeyIn(List<Integer> list);
}