package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysPost;
import com.tydic.nbchat.admin.mapper.po.SysPostUserRel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 岗位用户关联表(SysPostUserRel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-15 15:54:50
 */
public interface SysPostUserRelMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SysPostUserRel queryById(Integer id);

    List<SysPostUserRel> selectAll(SysPostUserRel sysPostUserRel);

    /**
     * 新增数据
     *
     * @param sysPostUserRel 实例对象
     * @return 影响行数
     */
    int insert(SysPostUserRel sysPostUserRel);


    int insertSelective(SysPostUserRel sysPostUserRel);

      /**
     * 修改数据
     *
     * @param sysPostUserRel 实例对象
     * @return 影响行数
     */
    int update(SysPostUserRel sysPostUserRel);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    List<SysPostUserRel> selectByUserId(@Param("tenantCode") String tenantCode,
                                        @Param("userId") String userId);

    int deleteByUserId(@Param("tenantCode") String tenantCode,
                       @Param("userId") String userId);

    List<SysPost> selectUserPostList(@Param("tenantCode") String tenantCode,
                                     @Param("userId") String userId);
}

