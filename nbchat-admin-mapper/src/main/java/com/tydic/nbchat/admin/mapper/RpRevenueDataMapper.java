package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.PayOrderPO;
import org.apache.ibatis.annotations.MapKey;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface RpRevenueDataMapper {

    /**
     * 查询订单列表
     *
     * @param payOrderPO
     * @return
     */
    List queryOrderList(PayOrderPO payOrderPO);

    /**
     * 查询sku列表
     *
     * @param orderNoList
     * @return
     */
    List<String> querySkuIdList(@Param("list") List<String> orderNoList);

    /**
     * 查询续费用户数
     *
     * @param userIdList
     * @return
     */
    int queryRenewalUser(@Param("list") List<String> userIdList);

    /**
     * 查询到期用户数
     *
     * @param beginDate
     * @param endDate
     * @return
     */
    int queryExpireUser(Date beginDate, Date endDate);

    String querySpuName(String key);

    /**
     * 查询最近购买版本
     * @param orderNo
     * @return
     */
    String selectSkuNameByOrderNo(String orderNo);
}
