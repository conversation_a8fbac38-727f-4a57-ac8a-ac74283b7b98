package com.tydic.nbchat.admin.mapper;


import com.tydic.nbchat.admin.mapper.po.FileUploadRecord;

import java.util.List;

public interface FileUploadRecordMapper {

    FileUploadRecord selectByFileNo(String fileNo);

    int insertSelective(FileUploadRecord record);

    int deleteByFileNo(String fileNo);

    int deleteByPath(String filePath);

    List<FileUploadRecord> selectByBusiCode(String busiCode);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByFileNo(FileUploadRecord record);
}