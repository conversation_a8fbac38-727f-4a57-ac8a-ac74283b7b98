package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:43
 * @description 制作信息-试题
 */
@Data
public class RpUserExamInfo {
    private Integer id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 出题总数
     */
    private Integer examMakeTimes;

    /**
     * 出题次数
     */
    private Integer examChickTimes;

    /**
     * 导出次数
     */
    private Integer examDownloadTimes;

    /**
     * 更新时间
     */
    private Date updateTime;
}
