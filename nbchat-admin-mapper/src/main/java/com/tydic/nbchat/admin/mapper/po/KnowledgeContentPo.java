package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
@Data
public class KnowledgeContentPo implements Serializable {

    //类型 1 文本| 2 文件 | 6 富文本
    private String contentType;
    private String sentenceId;

    private String tenantCode;

    private Long typeId;

    private String content;
    private String contentTitle;

    private Integer sortId;

    private Date createTime;

    private String createUserId;

    private String createUserName;

    private Date updateTime;

    private String updateUserId;

    private String updateUserName;

    private String typeGroup;

    private String csCode;

    private Integer hitCount;

    private String classes;

    private String isValid;

    private List<Long> typeIdList;

    private List<Long> sentenceIdList;
    private static final long serialVersionUID = -4155323032613768261L;


}
