package com.tydic.nbchat.admin.mapper;

import com.tydic.nbchat.admin.mapper.po.SysTenantRobotConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SysTenantRobotConfig)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-07 15:32:04
 */
public interface SysTenantRobotConfigMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    SysTenantRobotConfig queryById(Integer id);

    List<SysTenantRobotConfig> selectAll(SysTenantRobotConfig SysTenantRobotConfig);


    SysTenantRobotConfig selectConfig(@Param("configType") String configType,
                                        @Param("configId") String configId);


    /**
     * 新增数据
     *
     * @param SysTenantRobotConfig 实例对象
     * @return 影响行数
     */
    int insert(SysTenantRobotConfig SysTenantRobotConfig);


    int insertSelective(SysTenantRobotConfig SysTenantRobotConfig);

      /**
     * 修改数据
     *
     * @param SysTenantRobotConfig 实例对象
     * @return 影响行数
     */
    int update(SysTenantRobotConfig SysTenantRobotConfig);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 影响行数
     */
    int deleteById(Integer id);

    String queryRobotName(String config);
    Integer queryRobotMaxChars(String config);
}

