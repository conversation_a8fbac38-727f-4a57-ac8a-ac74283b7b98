package com.tydic.nbchat.admin.mapper.po;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/25 14:23
 * @description
 */

/**
 * 微信用户表
 */
@Data
public class NbchatWxUser {
    /**
     * 微信openid
     */
    private String openId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户统一标识
     */
    private String unionId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 微信头像链接
     */
    private String avatar;

    /**
     * 性别
     */
    private String gender;

    /**
     * 状态;1表示在等待队列；2代表已经输入邀请码为正常用户
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 是否删除;1表示删除0表示未删除
     */
    private Boolean isDeleted;

    /**
     * 微信渠道 1迪问 2课件帮
     */
    private String channel;
}
