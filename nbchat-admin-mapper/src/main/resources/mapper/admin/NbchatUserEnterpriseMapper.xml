<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.NbchatUserEnterpriseMapper">

    <resultMap type="com.tydic.nbchat.admin.mapper.po.NbchatUserEnterprise" id="NbchatUserEnterpriseMap">
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="businessSector" column="business_sector" jdbcType="VARCHAR"/>
        <result property="requirement" column="requirement" jdbcType="VARCHAR"/>
        <result property="createTime"  column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        user_id,name,email,company_name,business_sector,requirement,create_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatUserEnterpriseMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_user_enterprise
        where user_id = #{userId}
    </select>


    <select id="selectAll" resultMap="NbchatUserEnterpriseMap"
            parameterType="com.tydic.nbchat.admin.mapper.po.NbchatUserEnterprise">
        select
        <include refid="Base_Column_List"/>
        from nbchat_user_enterprise
        <where>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="companyName != null and companyName != ''">
                and company_name LIKE CONCAT('%', #{companyName}, '%')
            </if>
            <if test="businessSector != null and businessSector != ''">
                and business_sector = #{businessSector}
            </if>
            <if test="requirement != null and requirement != ''">
                and requirement = #{requirement}
            </if>
        </where>
    </select>
    <select id="queryEnterpriseList" resultType="java.lang.String">
        select company_name from nbchat_user_enterprise where company_name is not null and company_name != '' and company_name LIKE CONCAT('%', #{companyName}, '%')  group by company_name
    </select>


    <insert id="insertSelective" keyProperty="userId" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.admin.mapper.po.NbchatUserEnterprise">
        insert into nbchat_user_enterprise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="email != null and email != ''">
                email,
            </if>
            <if test="companyName != null and companyName != ''">
                company_name,
            </if>
            <if test="businessSector != null and businessSector != ''">
                business_sector,
            </if>
            <if test="requirement != null and requirement != ''">
                requirement,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="name != null and name != ''">
                #{name},
            </if>
            <if test="email != null and email != ''">
                #{email},
            </if>
            <if test="companyName != null and companyName != ''">
                #{companyName},
            </if>
            <if test="businessSector != null and businessSector != ''">
                #{businessSector},
            </if>
            <if test="requirement != null and requirement != ''">
                #{requirement},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>


    <!--新增所有列-->
    <insert id="insert" keyProperty="userId" useGeneratedKeys="true">
        insert into nbchat_user_enterprise(nameemailcompany_namebusiness_sectorrequirement)
        values (#{name}#{email}#{companyName}#{businessSector}#{requirement})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_user_enterprise
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="companyName != null and companyName != ''">
                company_name = #{companyName},
            </if>
            <if test="businessSector != null and businessSector != ''">
                business_sector = #{businessSector},
            </if>
            <if test="requirement != null and requirement != ''">
                requirement = #{requirement},
            </if>
        </set>
        where user_id = #{userId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from nbchat_user_enterprise
        where user_id = #{userId}
    </delete>

</mapper>

