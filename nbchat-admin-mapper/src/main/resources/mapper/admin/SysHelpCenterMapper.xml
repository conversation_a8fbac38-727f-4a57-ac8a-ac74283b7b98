<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysHelpCenterMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysHelpCenter">
    <!--@mbg.generated-->
    <!--@Table sys_help_center-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="thumb_url" jdbcType="VARCHAR" property="thumbUrl" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="busi_type" jdbcType="VARCHAR" property="busiType" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_index" jdbcType="INTEGER" property="orderIndex" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_code, title, thumb_url, content, category, busi_type, `status`, tag, create_user, 
    create_time, update_time, order_index, is_valid,duration
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from sys_help_center
    where id = #{id,jdbcType=INTEGER}
  </select>

  <!--根据分类和标题查询-->
  <select id="selectByCategoryAndTitle" parameterType="com.tydic.nbchat.admin.mapper.po.SysHelpCenterCondition" resultMap="BaseResultMap">
    SELECT
    a.*,
    b.cate_id AS cateId,
    b.cate_name AS cateName
    FROM sys_help_center a
    LEFT JOIN sys_tree_category b ON a.category COLLATE utf8mb4_general_ci = b.cate_id COLLATE utf8mb4_general_ci
    WHERE 1=1
    <if test="cateId != null and cateId != ''">
      AND b.cate_id = #{cateId, jdbcType=VARCHAR}
    </if>
    <if test="title != null and title != ''">
      AND a.title LIKE CONCAT('%', #{title, jdbcType=VARCHAR}, '%')
    </if>
    AND a.is_valid = '1'
    order by a.order_index,a.create_time desc
  </select>

  <!--根据分类查询-用户中心配置查询-->
  <select id="selectByCategory" parameterType="com.tydic.nbchat.admin.mapper.po.SysHelpCenterCondition" resultMap="BaseResultMap">
    SELECT
    a.*,
    b.cate_id AS cateId,
    b.cate_name AS cateName
    FROM sys_help_center a
    LEFT JOIN sys_tree_category b ON a.category COLLATE utf8mb4_general_ci = b.cate_id COLLATE utf8mb4_general_ci
    WHERE 1=1
    <if test="cateId != null and cateId != ''">
      AND b.cate_id = #{cateId, jdbcType=VARCHAR}
    </if>
    and a.status = '1'
    and a.is_valid = '1'
    order by a.order_index,a.create_time desc
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysHelpCenter" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_help_center (tenant_code, title, thumb_url, 
      content, category, busi_type, 
      `status`, tag, create_user, 
      create_time, update_time, order_index, 
      is_valid,duration)
    values (#{tenantCode,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{thumbUrl,jdbcType=VARCHAR}, 
      #{content,jdbcType=LONGVARCHAR}, #{category,jdbcType=VARCHAR}, #{busiType,jdbcType=VARCHAR}, 
      #{status,jdbcType=CHAR}, #{tag,jdbcType=VARCHAR}, #{createUser,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderIndex,jdbcType=INTEGER}, 
      #{isValid,jdbcType=CHAR}),#{duration,jdbcType=INTEGER}
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysHelpCenter" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_help_center
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantCode != null and tenantCode != ''">
        tenant_code,
      </if>
      <if test="title != null and title != ''">
        title,
      </if>
      <if test="thumbUrl != null and thumbUrl != ''">
        thumb_url,
      </if>
      <if test="content != null and content != ''">
        content,
      </if>
      <if test="category != null and category != ''">
        category,
      </if>
      <if test="busiType != null and busiType != ''">
        busi_type,
      </if>
      <if test="status != null and status != ''">
        `status`,
      </if>
      <if test="tag != null and tag != ''">
        tag,
      </if>
      <if test="createUser != null and createUser != ''">
        create_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderIndex != null">
        order_index,
      </if>
      <if test="isValid != null and isValid != ''">
        is_valid,
      </if>
        <if test="duration != null">
          duration,
        </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantCode != null and tenantCode != ''">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="title != null and title != ''">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="thumbUrl != null and thumbUrl != ''">
        #{thumbUrl,jdbcType=VARCHAR},
      </if>
      <if test="content != null and content != ''">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="category != null and category != ''">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="busiType != null and busiType != ''">
        #{busiType,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        #{status,jdbcType=CHAR},
      </if>
      <if test="tag != null and tag != ''">
        #{tag,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null and createUser != ''">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderIndex != null">
        #{orderIndex,jdbcType=INTEGER},
      </if>
      <if test="isValid != null and isValid != ''">
        #{isValid,jdbcType=CHAR},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysHelpCenter">
    <!--@mbg.generated-->
    update sys_help_center
    <set>
      <if test="tenantCode != null and tenantCode != ''">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="title != null and title != ''">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="thumbUrl != null and thumbUrl != ''">
        thumb_url = #{thumbUrl,jdbcType=VARCHAR},
      </if>
      <if test="content != null and content != ''">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="category != null and category != ''">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="busiType != null and busiType != ''">
        busi_type = #{busiType,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        `status` = #{status,jdbcType=CHAR},
      </if>
      <if test="tag != null and tag != ''">
        tag = #{tag,jdbcType=VARCHAR},
      </if>
      <if test="createUser != null and createUser != ''">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderIndex != null">
        order_index = #{orderIndex,jdbcType=INTEGER},
      </if>
      <if test="isValid != null and isValid != ''">
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysHelpCenter">
    <!--@mbg.generated-->
    update sys_help_center
    set tenant_code = #{tenantCode,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      thumb_url = #{thumbUrl,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR},
      category = #{category,jdbcType=VARCHAR},
      busi_type = #{busiType,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=CHAR},
      tag = #{tag,jdbcType=VARCHAR},
      create_user = #{createUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_index = #{orderIndex,jdbcType=INTEGER},
      is_valid = #{isValid,jdbcType=CHAR},
      duration = #{duration,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from sys_help_center
        <where>
            <if test="id != null">
                and id=#{id,jdbcType=INTEGER}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code=#{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != ''">
                and title=#{title,jdbcType=VARCHAR}
            </if>
            <if test="thumbUrl != null and thumbUrl != ''">
                and thumb_url=#{thumbUrl,jdbcType=VARCHAR}
            </if>
            <if test="content != null and content != ''">
                and content=#{content,jdbcType=LONGVARCHAR}
            </if>
            <if test="category != null and category != ''">
                and category=#{category,jdbcType=VARCHAR}
            </if>
            <if test="busiType != null and busiType != ''">
                and busi_type=#{busiType,jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and `status`=#{status,jdbcType=CHAR}
            </if>
            <if test="tag != null and tag != ''">
                and tag=#{tag,jdbcType=VARCHAR}
            </if>
            <if test="createUser != null and createUser != ''">
                and create_user=#{createUser,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="orderIndex != null">
                and order_index=#{orderIndex,jdbcType=INTEGER}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid=#{isValid,jdbcType=CHAR}
            </if>
        </where>
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update sys_help_center
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="tenant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantCode != null and item.tenantCode != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.tenantCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="title = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.title != null and item.title != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.title,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="thumb_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.thumbUrl != null and item.thumbUrl != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.thumbUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.content != null and item.content != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.content,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="category = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.category != null and item.category != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.category,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="busi_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.busiType != null and item.busiType != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.busiType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null and item.status != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tag = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tag != null and item.tag != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.tag,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_user = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createUser != null and item.createUser != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createUser,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="order_index = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.orderIndex != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.orderIndex,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_valid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isValid != null and item.isValid != ''">
            when id = #{item.id,jdbcType=INTEGER} then #{item.isValid,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_help_center
    (tenant_code, title, thumb_url, content, category, busi_type, `status`, tag, create_user, 
      create_time, update_time, order_index, is_valid)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantCode,jdbcType=VARCHAR}, #{item.title,jdbcType=VARCHAR}, #{item.thumbUrl,jdbcType=VARCHAR}, 
        #{item.content,jdbcType=LONGVARCHAR}, #{item.category,jdbcType=VARCHAR}, #{item.busiType,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=CHAR}, #{item.tag,jdbcType=VARCHAR}, #{item.createUser,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.orderIndex,jdbcType=INTEGER}, 
        #{item.isValid,jdbcType=CHAR})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from sys_help_center where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=INTEGER}
    </foreach>
  </delete>
</mapper>