<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysUserCooperateMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysUserCooperate">
    <!--@mbg.generated-->
    <!--@Table sys_user_cooperate-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="province" jdbcType="VARCHAR" property="province" />
    <result column="city" jdbcType="VARCHAR" property="city" />
    <result column="company" jdbcType="VARCHAR" property="company" />
    <result column="sms_code" jdbcType="VARCHAR" property="smsCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, phone, province, city, company, sms_code, create_time, update_time, `status`, 
    is_valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from sys_user_cooperate
    where id = #{id,jdbcType=INTEGER}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserCooperate" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_cooperate (`name`, phone, province, 
      city, company, sms_code, 
      create_time, update_time, `status`, 
      is_valid)
    values (#{name,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{province,jdbcType=VARCHAR}, 
      #{city,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, #{smsCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{status,jdbcType=VARCHAR}, 
      #{isValid,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserCooperate" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_cooperate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="province != null">
        province,
      </if>
      <if test="city != null">
        city,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="smsCode != null">
        sms_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        #{city,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="smsCode != null">
        #{smsCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserCooperate">
    <!--@mbg.generated-->
    update sys_user_cooperate
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="province != null">
        province = #{province,jdbcType=VARCHAR},
      </if>
      <if test="city != null">
        city = #{city,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        company = #{company,jdbcType=VARCHAR},
      </if>
      <if test="smsCode != null">
        sms_code = #{smsCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserCooperate">
    <!--@mbg.generated-->
    update sys_user_cooperate
    set `name` = #{name,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      province = #{province,jdbcType=VARCHAR},
      city = #{city,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      sms_code = #{smsCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      `status` = #{status,jdbcType=VARCHAR},
      is_valid = #{isValid,jdbcType=CHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from sys_user_cooperate
        <where>
            <if test="id != null">
                and id=#{id,jdbcType=INTEGER}
            </if>
            <if test="name != null">
                and `name`=#{name,jdbcType=VARCHAR}
            </if>
            <if test="phone != null">
                and phone=#{phone,jdbcType=VARCHAR}
            </if>
            <if test="province != null">
                and province=#{province,jdbcType=VARCHAR}
            </if>
            <if test="city != null">
                and city=#{city,jdbcType=VARCHAR}
            </if>
            <if test="company != null">
                and company=#{company,jdbcType=VARCHAR}
            </if>
            <if test="smsCode != null">
                and sms_code=#{smsCode,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="status != null">
                and `status`=#{status,jdbcType=VARCHAR}
            </if>
            <if test="isValid != null">
                and is_valid=#{isValid,jdbcType=CHAR}
            </if>
        </where>
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update sys_user_cooperate
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.name,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="phone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.phone != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.phone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="province = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.province != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.province,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="city = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.city != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.city,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="company = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.company != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.company,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sms_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.smsCode != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.smsCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_valid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isValid != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.isValid,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_cooperate
    (`name`, phone, province, city, company, sms_code, create_time, update_time, `status`, 
      is_valid)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, #{item.province,jdbcType=VARCHAR}, 
        #{item.city,jdbcType=VARCHAR}, #{item.company,jdbcType=VARCHAR}, #{item.smsCode,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.status,jdbcType=VARCHAR}, 
        #{item.isValid,jdbcType=CHAR})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from sys_user_cooperate where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=INTEGER}
    </foreach>
  </delete>
</mapper>