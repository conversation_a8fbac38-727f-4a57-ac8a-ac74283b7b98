<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpUserPptVideoInfoMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpUserPptVideoInfo">
        <!--@mbg.generated-->
        <!--@Table rp_user_ppt_video_info-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="ppt_make_times" jdbcType="INTEGER" property="pptMakeTimes"/>
        <result column="ppt_download_times" jdbcType="INTEGER" property="pptDownloadTimes"/>
        <result column="ppt_ai_count" jdbcType="INTEGER" property="pptAiCount"/>
        <result column="ppt_doc_count" jdbcType="INTEGER" property="pptDocCount"/>
        <result column="ppt_txt_count" jdbcType="INTEGER" property="pptTxtCount"/>
        <result column="video_make_times" jdbcType="INTEGER" property="videoMakeTimes"/>
        <result column="video_build_times" jdbcType="INTEGER" property="videoBuildTimes"/>
        <result column="video_total_duration" jdbcType="INTEGER" property="videoTotalDuration"/>
        <result column="video_avg_duration" jdbcType="INTEGER" property="videoAvgDuration"/>
        <result column="video_total_size" jdbcType="INTEGER" property="videoTotalSize"/>
        <result column="video_download_times" jdbcType="INTEGER" property="videoDownloadTimes"/>
        <result column="video_ppt_count" jdbcType="INTEGER" property="videoPptCount"/>
        <result column="video_ppt_upload_count" jdbcType="INTEGER" property="videoPptUploadCount"/>
        <result column="video_ppt_platform_count" jdbcType="INTEGER" property="videoPptPlatformCount"/>
        <result column="video_word_speech_count" jdbcType="INTEGER" property="videoWordSpeechCount"/>
        <result column="video_ai_count" jdbcType="INTEGER" property="videoAiCount"/>
        <result column="video_word_generate_count" jdbcType="INTEGER" property="videoWordGenerateCount"/>
        <result column="video_template_count" jdbcType="INTEGER" property="videoTemplateCount"/>
        <result column="video_custom_count" jdbcType="INTEGER" property="videoCustomCount"/>
        <result column="video_cartoon_count" jdbcType="INTEGER" property="videoCartoonCount"/>
        <result column="video_25d_count" jdbcType="INTEGER" property="video25dCount"/>
        <result column="video_2d_count" jdbcType="INTEGER" property="video2dCount"/>
        <result column="video_none_count" jdbcType="INTEGER" property="videoNoneCount"/>
        <result column="video_custom_25d_count" jdbcType="INTEGER" property="videoCustom25dCount"/>
        <result column="video_custom_2d_count" jdbcType="INTEGER" property="videoCustom2dCount"/>
        <result column="video_custom_cartoon_count" jdbcType="INTEGER" property="videoCustomCartoonCount"/>
        <result column="video_audio_count" jdbcType="INTEGER" property="videoAudioCount"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="video_success_times" property="videoSuccessTimes"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_code, user_id, ppt_make_times, ppt_download_times, ppt_ai_count, ppt_doc_count,
        ppt_txt_count, video_make_times, video_build_times, video_total_duration, video_avg_duration,
        video_total_size, video_download_times, video_ppt_count, video_ppt_upload_count,
        video_ppt_platform_count, video_word_speech_count, video_ai_count, video_word_generate_count,
        video_template_count, video_custom_count, video_cartoon_count, video_25d_count, video_2d_count,
        video_none_count, video_custom_25d_count, video_custom_2d_count, video_custom_cartoon_count,
        video_audio_count, update_time
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.RpUserPptVideoInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_user_ppt_video_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            tenant_code,
            user_id,
            <if test="pptMakeTimes != null">
                ppt_make_times,
            </if>
            <if test="pptAiCount != null">
                ppt_ai_count,
            </if>
            <if test="pptDocCount != null">
                ppt_doc_count,
            </if>
            <if test="pptTxtCount != null">
                ppt_txt_count,
            </if>
            <if test="videoMakeTimes != null">
                video_make_times,
            </if>
            <if test="videoBuildTimes != null">
                video_build_times,
            </if>
            <if test="videoTotalDuration != null">
                video_total_duration,
                video_avg_duration,
            </if>
            <if test="videoTotalSize != null">
                video_total_size,
            </if>
            <if test="videoPptCount != null">
                video_ppt_count,
            </if>
            <if test="videoPptUploadCount != null">
                video_ppt_upload_count,
            </if>
            <if test="videoPptPlatformCount != null">
                video_ppt_platform_count,
            </if>
            <if test="videoWordSpeechCount != null">
                video_word_speech_count,
            </if>
            <if test="videoAiCount != null">
                video_ai_count,
            </if>
            <if test="videoWordGenerateCount != null">
                video_word_generate_count,
            </if>
            <if test="videoTemplateCount != null">
                video_template_count,
            </if>
            <if test="videoCustomCount != null">
                video_custom_count,
            </if>
            <if test="videoCartoonCount != null">
                video_cartoon_count,
            </if>
            <if test="video25dCount != null">
                video_25d_count,
            </if>
            <if test="video2dCount != null">
                video_2d_count,
            </if>
            <if test="videoNoneCount != null">
                video_none_count,
            </if>
            <if test="videoCustom25dCount != null">
                video_custom_25d_count,
            </if>
            <if test="videoCustom2dCount != null">
                video_custom_2d_count,
            </if>
            <if test="videoCustomCartoonCount != null">
                video_custom_cartoon_count,
            </if>
            <if test="videoAudioCount != null">
                video_audio_count,
            </if>
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{tenantCode,jdbcType=VARCHAR},
            #{userId,jdbcType=VARCHAR},
            <if test="pptMakeTimes != null">
                #{pptMakeTimes,jdbcType=INTEGER},
            </if>
            <if test="pptAiCount != null">
                #{pptAiCount,jdbcType=INTEGER},
            </if>
            <if test="pptDocCount != null">
                #{pptDocCount,jdbcType=INTEGER},
            </if>
            <if test="pptTxtCount != null">
                #{pptTxtCount,jdbcType=INTEGER},
            </if>
            <if test="videoMakeTimes != null">
                #{videoMakeTimes,jdbcType=INTEGER},
            </if>
            <if test="videoBuildTimes != null">
                #{videoBuildTimes,jdbcType=INTEGER},
            </if>
            <if test="videoTotalDuration != null">
                #{videoTotalDuration,jdbcType=INTEGER},
                #{videoTotalDuration,jdbcType=INTEGER},
            </if>
            <if test="videoTotalSize != null">
                #{videoTotalSize,jdbcType=INTEGER},
            </if>
            <if test="videoPptCount != null">
                #{videoPptCount,jdbcType=INTEGER},
            </if>
            <if test="videoPptUploadCount != null">
                #{videoPptUploadCount,jdbcType=INTEGER},
            </if>
            <if test="videoPptPlatformCount != null">
                #{videoPptPlatformCount,jdbcType=INTEGER},
            </if>
            <if test="videoWordSpeechCount != null">
                #{videoWordSpeechCount,jdbcType=INTEGER},
            </if>
            <if test="videoAiCount != null">
                #{videoAiCount,jdbcType=INTEGER},
            </if>
            <if test="videoWordGenerateCount != null">
                #{videoWordGenerateCount,jdbcType=INTEGER},
            </if>
            <if test="videoTemplateCount != null">
                #{videoTemplateCount,jdbcType=INTEGER},
            </if>
            <if test="videoCustomCount != null">
                #{videoCustomCount,jdbcType=INTEGER},
            </if>
            <if test="videoCartoonCount != null">
                #{videoCartoonCount,jdbcType=INTEGER},
            </if>
            <if test="video25dCount != null">
                #{video25dCount,jdbcType=INTEGER},
            </if>
            <if test="video2dCount != null">
                #{video2dCount,jdbcType=INTEGER},
            </if>
            <if test="videoNoneCount != null">
                #{videoNoneCount,jdbcType=INTEGER},
            </if>
            <if test="videoCustom25dCount != null">
                #{videoCustom25dCount,jdbcType=INTEGER},
            </if>
            <if test="videoCustom2dCount != null">
                #{videoCustom2dCount,jdbcType=INTEGER},
            </if>
            <if test="videoCustomCartoonCount != null">
                #{videoCustomCartoonCount,jdbcType=INTEGER},
            </if>
            <if test="videoAudioCount != null">
                #{videoAudioCount,jdbcType=INTEGER},
            </if>
            current_timestamp
        </trim>
    </insert>
    <update id="updateSelective" parameterType="com.tydic.nbchat.admin.mapper.po.RpUserPptVideoInfo">
        <!--@mbg.generated-->
        update rp_user_ppt_video_info
        <set>
            <if test="pptMakeTimes != null">
                ppt_make_times = ppt_make_times + #{pptMakeTimes,jdbcType=INTEGER},
            </if>
            <if test="pptAiCount != null">
                ppt_ai_count = ppt_ai_count + #{pptAiCount,jdbcType=INTEGER},
            </if>
            <if test="pptDocCount != null">
                ppt_doc_count = ppt_doc_count + #{pptDocCount,jdbcType=INTEGER},
            </if>
            <if test="pptTxtCount != null">
                ppt_txt_count = ppt_txt_count = #{pptTxtCount,jdbcType=INTEGER},
            </if>
            <if test="videoMakeTimes != null">
                video_make_times = video_make_times + #{videoMakeTimes,jdbcType=INTEGER},
            </if>
            <if test="videoBuildTimes != null">
                video_build_times = video_build_times + #{videoBuildTimes,jdbcType=INTEGER},
            </if>
            <if test="videoTotalDuration != null">
                video_total_duration = video_total_duration + #{videoTotalDuration,jdbcType=INTEGER},
                <if test="videoSuccessTimes != null and videoSuccessTimes != 0">
                    video_avg_duration = video_total_duration / #{videoSuccessTimes,jdbcType=INTEGER},
                </if>
            </if>
            <if test="videoTotalSize != null">
                video_total_size = video_total_size + #{videoTotalSize,jdbcType=INTEGER},
            </if>
            <if test="videoPptCount != null">
                video_ppt_count = video_ppt_count + #{videoPptCount,jdbcType=INTEGER},
            </if>
            <if test="videoPptUploadCount != null">
                video_ppt_upload_count = video_ppt_upload_count+#{videoPptUploadCount,jdbcType=INTEGER},
            </if>
            <if test="videoPptPlatformCount != null">
                video_ppt_platform_count = video_ppt_platform_count+#{videoPptPlatformCount,jdbcType=INTEGER},
            </if>
            <if test="videoWordSpeechCount != null">
                video_word_speech_count =video_word_speech_count+ #{videoWordSpeechCount,jdbcType=INTEGER},
            </if>
            <if test="videoAiCount != null">
                video_ai_count = video_ai_count+#{videoAiCount,jdbcType=INTEGER},
            </if>
            <if test="videoWordGenerateCount != null">
                video_word_generate_count = video_word_generate_count+#{videoWordGenerateCount,jdbcType=INTEGER},
            </if>
            <if test="videoTemplateCount != null">
                video_template_count =video_template_count+ #{videoTemplateCount,jdbcType=INTEGER},
            </if>
            <if test="videoCustomCount != null">
                video_custom_count = video_custom_count+#{videoCustomCount,jdbcType=INTEGER},
            </if>
            <if test="videoCartoonCount != null">
                video_cartoon_count = video_cartoon_count+#{videoCartoonCount,jdbcType=INTEGER},
            </if>
            <if test="video25dCount != null">
                video_25d_count = video_25d_count+#{video25dCount,jdbcType=INTEGER},
            </if>
            <if test="video2dCount != null">
                video_2d_count = video_2d_count+#{video2dCount,jdbcType=INTEGER},
            </if>
            <if test="videoNoneCount != null">
                video_none_count =video_none_count+ #{videoNoneCount,jdbcType=INTEGER},
            </if>
            <if test="videoCustom25dCount != null">
                video_custom_25d_count = video_custom_25d_count+#{videoCustom25dCount,jdbcType=INTEGER},
            </if>
            <if test="videoCustom2dCount != null">
                video_custom_2d_count = video_custom_2d_count+#{videoCustom2dCount,jdbcType=INTEGER},
            </if>
            <if test="videoCustomCartoonCount != null">
                video_custom_cartoon_count = video_custom_cartoon_count+#{videoCustomCartoonCount,jdbcType=INTEGER},
            </if>
            <if test="videoAudioCount != null">
                video_audio_count = video_audio_count+ #{videoAudioCount,jdbcType=INTEGER},
            </if>
            update_time = current_timestamp
        </set>
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </update>
    <select id="findOneByTenantCodeAndUserId" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rp_user_ppt_video_info
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
        limit 1
    </select>
    <insert id="batchInsertForTimer" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_user_ppt_video_info
        (tenant_code, user_id, ppt_download_times, video_download_times, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR},
            #{item.pptDownloadTimes,jdbcType=INTEGER}, #{item.videoDownloadTimes,jdbcType=INTEGER},
            current_timestamp)
        </foreach>
    </insert>
    <update id="batchUpdateForTimer">
        <!--@mbg.generated-->
        update rp_user_ppt_video_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ppt_download_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pptDownloadTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then ppt_download_times +
                        #{item.pptDownloadTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_download_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoDownloadTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then video_download_times +
                        #{item.videoDownloadTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then current_timestamp
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <select id="selectAllPptMake" resultMap="BaseResultMap">
        select tenant_code,
               user_id,
               count(0) as ppt_make_times
        from ppt_creation_record
        group by tenant_code, user_id
    </select>
    <select id="selectAllTdh" resultMap="BaseResultMap">
        select tct.tenant_code                                                      as tenant_code,
               tct.user_id                                                          as user_id,
               (select count(0)
                from tdh_creation_record
                where tenant_code = tct.tenant_code
                  and user_id = tct.user_id)                                        as video_make_times,
               count(tct.creation_id)                                               as video_build_times,
               sum(if(tct.task_state = '1', 1, 0))                                  as video_success_times,
               sum(if(tct.task_state = '1', tct.video_duration, 0))                 as video_total_duration,
               sum(if(tct.task_state = '1', tct.file_size / 1048576, 0))            as video_total_size,
               sum(if(tcr.creation_source = '2', 1, 0))                             as video_ppt_count,
               sum(if(tcr.creation_source = '2' and tcr.creation_type = '1', 1, 0)) as video_ppt_upload_count,
               sum(if(tcr.creation_source = '2' and tcr.creation_type = '2', 1, 0)) as video_ppt_platform_count,
               sum(if(tcr.creation_source = '3', 1, 0))                             as video_word_speech_count,
               sum(if(tcr.creation_source = '4', 1, 0))                             as video_ai_count,
               sum(if(tcr.creation_source = '5', 1, 0))                             as video_word_generate_count,
               sum(if(tcr.creation_source = '6', 1, 0))                             as video_template_count,
               sum(if(tcr.creation_source = '1', 1, 0))                             as video_custom_count,
               sum(if(tct.tdh_type = '2d_gif', 1, 0))                               as video_cartoon_count,
               sum(if(tct.tdh_type in ('2.5d', '2.5d_wp'), 1, 0))                   as video_25d_count,
               sum(if(tct.tdh_type in ('2d', '2d_echo'), 1, 0))                     as video_2d_count,
               sum(if(tct.tdh_type = 'none', 1, 0))                                 as video_none_count,
               sum(if(tct.tdh_type = '2.5d_mtk', 1, 0))                             as video_custom_25d_count,
               sum(if(tct.tdh_type = '2d_mtk', 1, 0))                               as video_custom_2d_count,
               0                                                                    as video_custom_cartoon_count, -- TODO 定制卡通还没有
               0                                                                    as video_audio_count           -- TODO 定制音频没有写进去
        from tdh_creation_task tct
                 left join
             tdh_creation_record tcr
             on tcr.creation_id = tct.creation_id
        where tct.user_id != '1'
          and tct.creation_id != '1'
        group by tct.tenant_code, tct.user_id
    </select>
    <select id="selectAllDownload" resultMap="BaseResultMap">
        select tenant_code,
               user_id,
               sum(if(type = 'ppt_down', 1, 0))   as ppt_download_times,
               sum(if(type = 'video_down', 1, 0)) as video_download_times
        from sys_user_operate_log
        where type in ('ppt_down', 'video_down')
        group by tenant_code, user_id
    </select>
    <update id="updateByStartTimeBetween">
        update rp_user_ppt_video_info rp
        join
        (select tdh.tenant_code,
        tdh.user_id,
        ifnull(tdh.build_times, 0) as video_build_times,
        ifnull(tdh.total_duration, 0) as video_total_duration,
        ifnull(success_times, 0) as video_success_times,
        ifnull(tdh.total_size, 0) as video_total_size,
        ifnull(tdh.ppt_count, 0) as video_ppt_count,
        ifnull(tdh.ppt_upload_count, 0) as video_ppt_upload_count,
        ifnull(tdh.ppt_platform_count, 0) as video_ppt_platform_count,
        ifnull(tdh.word_speech_count, 0) as video_word_speech_count,
        ifnull(tdh.ai_count, 0) as video_ai_count,
        ifnull(tdh.word_generate_count, 0) as video_word_generate_count,
        ifnull(tdh.template_count, 0) as video_template_count,
        ifnull(tdh.custom_count, 0) as video_custom_count,
        ifnull(tdh.cartoon_count, 0) as video_cartoon_count,
        ifnull(tdh.25d_count, 0) as video_25d_count,
        ifnull(tdh.2d_count, 0) as video_2d_count,
        ifnull(tdh.none_count, 0) as video_none_count,
        ifnull(tdh.custom_25d_count, 0) as video_custom_25d_count,
        ifnull(tdh.custom_2d_count, 0) as video_custom_2d_count,
        ifnull(tdh.custom_cartoon_count, 0) as video_custom_cartoon_count,
        ifnull(tdh.audio_count, 0) as video_audio_count
        from (select tct.tenant_code as tenant_code,
        tct.user_id as user_id,
        count(tct.creation_id) as build_times,
        (select count(0)
        from tdh_creation_task
        where task_state = '1'
        and user_id != '1'
        and creation_id != '1') as success_times,
        sum(if(tct.task_state = '1', tct.video_duration, 0)) as total_duration,
        sum(if(tct.task_state = '1', round(tct.file_size / 1048576), 0)) as total_size,
        sum(if(tcr.creation_source = '2', 1, 0)) as ppt_count,
        sum(if(tcr.creation_source = '2' and tcr.creation_type = '1', 1, 0)) as ppt_upload_count,
        sum(if(tcr.creation_source = '2' and tcr.creation_type = '2', 1, 0)) as ppt_platform_count,
        sum(if(tcr.creation_source = '3', 1, 0)) as word_speech_count,
        sum(if(tcr.creation_source = '4', 1, 0)) as ai_count,
        sum(if(tcr.creation_source = '5', 1, 0)) as word_generate_count,
        sum(if(tcr.creation_source = '6', 1, 0)) as template_count,
        sum(if(tcr.creation_source = '1', 1, 0)) as custom_count,
        sum(if(tct.tdh_type = '2d_gif', 1, 0)) as cartoon_count,
        sum(if(tct.tdh_type in ('2.5d', '2.5d_wp'), 1, 0)) as 25d_count,
        sum(if(tct.tdh_type in ('2d', '2d_echo'), 1, 0)) as 2d_count,
        sum(if(tct.tdh_type = 'none', 1, 0)) as none_count,
        sum(if(tct.tdh_type = '2.5d_mtk', 1, 0)) as custom_25d_count,
        sum(if(tct.tdh_type = '2d_mtk', 1, 0)) as custom_2d_count,
        0 as custom_cartoon_count, -- TODO 定制卡通还没有
        0 as audio_count -- TODO 定制音频没有写进去
        from tdh_creation_task tct
        left join
        tdh_creation_record tcr
        on tcr.creation_id = tct.creation_id
        where tct.user_id != '1'
        and tct.creation_id != '1'
        and tct.create_time &lt; #{endTime,jdbcType=TIMESTAMP}
        <if test="startTime != null">
            and tct.create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        group by tct.tenant_code, tct.user_id) tdh) t
        on rp.tenant_code = t.tenant_code
        and rp.user_id = t.user_id
        set rp.video_build_times = rp.video_build_times + t.video_build_times,
        rp.video_total_duration = rp.video_total_duration + t.video_total_duration,
        rp.video_avg_duration = if(t.video_success_times != 0, rp.video_total_duration / t.video_success_times, 0),
        rp.video_total_size = rp.video_total_size + t.video_total_size,
        rp.video_ppt_count = rp.video_ppt_count + t.video_ppt_count,
        rp.video_ppt_upload_count = rp.video_ppt_upload_count + t.video_ppt_upload_count,
        rp.video_ppt_platform_count = rp.video_ppt_platform_count + t.video_ppt_platform_count,
        rp.video_word_speech_count = rp.video_word_speech_count + t.video_word_speech_count,
        rp.video_ai_count = rp.video_ai_count + t.video_ai_count,
        rp.video_word_generate_count = rp.video_word_generate_count + t.video_word_generate_count,
        rp.video_template_count = rp.video_template_count + t.video_template_count,
        rp.video_custom_count = rp.video_custom_count + t.video_custom_count,
        rp.video_cartoon_count = rp.video_cartoon_count + t.video_cartoon_count,
        rp.video_25d_count = rp.video_25d_count + t.video_25d_count,
        rp.video_2d_count = rp.video_2d_count + t.video_2d_count,
        rp.video_none_count = rp.video_none_count + t.video_none_count,
        rp.video_custom_25d_count = rp.video_custom_25d_count + t.video_custom_25d_count,
        rp.video_custom_2d_count = rp.video_custom_2d_count + t.video_custom_2d_count,
        rp.video_custom_cartoon_count = rp.video_custom_cartoon_count + t.video_custom_cartoon_count,
        rp.video_audio_count = rp.video_audio_count + t.video_audio_count,
        rp.update_time = current_timestamp
    </update>

    <insert id="insertByStartTimeBetween">
        insert into rp_user_ppt_video_info (tenant_code, user_id, video_build_times, video_total_duration,
        video_avg_duration, video_total_size, video_ppt_count, video_ppt_upload_count, video_ppt_platform_count,
        video_word_speech_count, video_ai_count, video_word_generate_count, video_template_count, video_custom_count,
        video_cartoon_count, video_25d_count, video_2d_count, video_none_count, video_custom_25d_count,
        video_custom_2d_count, video_custom_cartoon_count, video_audio_count, update_time)
        select tdh.tenant_code,
        tdh.user_id,
        ifnull(tdh.build_times, 0),
        ifnull(tdh.total_duration, 0),
        ifnull(if(tdh.success_times = 0, 0, tdh.total_duration / tdh.success_times), 0),
        ifnull(round(tdh.total_size), 0),
        ifnull(tdh.ppt_count, 0),
        ifnull(tdh.ppt_upload_count, 0),
        ifnull(tdh.ppt_platform_count, 0),
        ifnull(tdh.word_speech_count, 0),
        ifnull(tdh.ai_count, 0),
        ifnull(tdh.word_generate_count, 0),
        ifnull(tdh.template_count, 0),
        ifnull(tdh.custom_count, 0),
        ifnull(tdh.cartoon_count, 0),
        ifnull(tdh.25d_count, 0),
        ifnull(tdh.2d_count, 0),
        ifnull(tdh.none_count, 0),
        ifnull(tdh.custom_25d_count, 0),
        ifnull(tdh.custom_2d_count, 0),
        ifnull(tdh.custom_cartoon_count, 0),
        ifnull(tdh.audio_count, 0),
        current_timestamp
        from (select tct.tenant_code as tenant_code,
        tct.user_id as user_id,
        count(tct.creation_id) as build_times,
        (select count(0)
        from tdh_creation_task
        where task_state = '1'
        and user_id != '1'
        and creation_id != '1') as success_times,
        sum(if(tct.task_state = '1', tct.video_duration, 0)) as total_duration,
        sum(if(tct.task_state = '1', round(tct.file_size / 1048576), 0)) as total_size,
        sum(if(tcr.creation_source = '2', 1, 0)) as ppt_count,
        sum(if(tcr.creation_source = '2' and tcr.creation_type = '1', 1, 0)) as ppt_upload_count,
        sum(if(tcr.creation_source = '2' and tcr.creation_type = '2', 1, 0)) as ppt_platform_count,
        sum(if(tcr.creation_source = '3', 1, 0)) as word_speech_count,
        sum(if(tcr.creation_source = '4', 1, 0)) as ai_count,
        sum(if(tcr.creation_source = '5', 1, 0)) as word_generate_count,
        sum(if(tcr.creation_source = '6', 1, 0)) as template_count,
        sum(if(tcr.creation_source = '1', 1, 0)) as custom_count,
        sum(if(tct.tdh_type = '2d_gif', 1, 0)) as cartoon_count,
        sum(if(tct.tdh_type in ('2.5d', '2.5d_wp'), 1, 0)) as 25d_count,
        sum(if(tct.tdh_type in ('2d', '2d_echo'), 1, 0)) as 2d_count,
        sum(if(tct.tdh_type = 'none', 1, 0)) as none_count,
        sum(if(tct.tdh_type = '2.5d_mtk', 1, 0)) as custom_25d_count,
        sum(if(tct.tdh_type = '2d_mtk', 1, 0)) as custom_2d_count,
        0 as custom_cartoon_count, -- TODO 定制卡通还没有
        0 as audio_count -- TODO 定制音频没有写进去
        from tdh_creation_task tct
        left join
        tdh_creation_record tcr
        on tcr.creation_id = tct.creation_id
        where tct.user_id != '1'
        and tct.creation_id != '1'
        and tct.create_time &lt; #{endTime,jdbcType=TIMESTAMP}
        <if test="startTime != null">
            and tct.create_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        group by tct.tenant_code, tct.user_id) tdh
        where (tdh.tenant_code, tdh.user_id) not in (select tenant_code, user_id from rp_user_ppt_video_info)
    </insert>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update rp_user_ppt_video_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.userId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ppt_make_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pptMakeTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.pptMakeTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ppt_download_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pptDownloadTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.pptDownloadTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ppt_ai_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pptAiCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.pptAiCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ppt_doc_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pptDocCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.pptDocCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ppt_txt_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pptTxtCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.pptTxtCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_make_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoMakeTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoMakeTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_build_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoBuildTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoBuildTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_total_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoTotalDuration != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoTotalDuration,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_avg_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoAvgDuration != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoAvgDuration,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_total_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoTotalSize != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoTotalSize,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_download_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoDownloadTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoDownloadTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_ppt_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoPptCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoPptCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_ppt_upload_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoPptUploadCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoPptUploadCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_ppt_platform_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoPptPlatformCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoPptPlatformCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_word_speech_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoWordSpeechCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoWordSpeechCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_ai_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoAiCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoAiCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_word_generate_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoWordGenerateCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoWordGenerateCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_template_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoTemplateCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoTemplateCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_custom_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoCustomCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoCustomCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_cartoon_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoCartoonCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoCartoonCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_25d_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.video25dCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.video25dCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_2d_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.video2dCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.video2dCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_none_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoNoneCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoNoneCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_custom_25d_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoCustom25dCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoCustom25dCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_custom_2d_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoCustom2dCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoCustom2dCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_custom_cartoon_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoCustomCartoonCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoCustomCartoonCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="video_audio_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.videoAudioCount != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.videoAudioCount,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <delete id="deleteAll">
        delete
        from rp_user_ppt_video_info
    </delete>
</mapper>
