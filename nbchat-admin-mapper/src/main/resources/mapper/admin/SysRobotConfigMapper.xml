<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysTenantRobotConfigMapper">

    <resultMap type="com.tydic.nbchat.admin.mapper.po.SysTenantRobotConfig" id="SysTenantRobotConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="configType" column="config_type" jdbcType="VARCHAR"/>
        <result property="configId" column="config_id" jdbcType="VARCHAR"/>
        <result property="configValue" column="config_value" jdbcType="VARCHAR"/>
        <result property="configList" column="config_list" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id, config_type, config_id, config_value, config_list, create_time, update_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="SysTenantRobotConfigMap">
        select
          <include refid="Base_Column_List" />
        from sys_robot_config
        where id = #{id}
    </select>


    <select id="selectConfig" resultMap="SysTenantRobotConfigMap" parameterType="string">
        select
            <include refid="Base_Column_List" />
        from sys_robot_config
        where config_type = #{configType} and config_id = #{configId} limit 1
    </select>
    
    <select id="selectAll" resultMap="SysTenantRobotConfigMap" parameterType="com.tydic.nbchat.admin.mapper.po.SysTenantRobotConfig">
        select
          <include refid="Base_Column_List" />
        from sys_robot_config
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="configType != null and configType != ''">
                and config_type = #{configType}
            </if>
            <if test="configId != null and configId != ''">
                and config_id = #{configId}
            </if>
            <if test="configValue != null and configValue != ''">
                and config_value = #{configValue}
            </if>
            <if test="configList != null and configList != ''">
                and config_list = #{configList}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime}
            </if>
        </where>
    </select>
    <select id="queryRobotName" resultType="java.lang.String">
        select dict_name from sys_dict_config where dict_value = #{config} and dict_code ='sysRobotType' and is_valid = 1
    </select>

    <select id="queryRobotMaxChars" resultType="java.lang.Integer">
        select max_chars from nbchat_robot_token where robot_type = #{config} and is_valid = 1 limit 1
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.admin.mapper.po.SysTenantRobotConfig">
        insert into sys_robot_config
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="configType != null and configType != ''">
                config_type,
            </if>
            <if test="configId != null and configId != ''">
                config_id,
            </if>
            <if test="configValue != null and configValue != ''">
                config_value,
            </if>
            <if test="configList != null and configList != ''">
                config_list,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="configType != null and configType != ''">
                #{configType},
            </if>
            <if test="configId != null and configId != ''">
                #{configId},
            </if>
            <if test="configValue != null and configValue != ''">
                #{configValue},
            </if>
            <if test="configList != null and configList != ''">
                #{configList},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_robot_config(config_type, config_id, config_value, config_list, create_time, update_time)
        values (#{configType}, #{configId}, #{configValue}, #{configList}, #{createTime}, #{updateTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_robot_config
        <set>
            <if test="configType != null and configType != ''">
                config_type = #{configType},
            </if>
            <if test="configId != null and configId != ''">
                config_id = #{configId},
            </if>
            <if test="configValue != null and configValue != ''">
                config_value = #{configValue},
            </if>
            <if test="configList != null and configList != ''">
                config_list = #{configList},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_robot_config where id = #{id}
    </delete>

</mapper>

