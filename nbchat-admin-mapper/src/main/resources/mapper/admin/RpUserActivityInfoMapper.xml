<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpUserActivityInfoMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpUserActivityInfo">
        <!--@mbg.generated-->
        <!--@Table rp_user_activity_info-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="access_times" jdbcType="INTEGER" property="accessTimes"/>
        <result column="first_access_time" jdbcType="TIMESTAMP" property="firstAccessTime"/>
        <result column="last_access_time" jdbcType="TIMESTAMP" property="lastAccessTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_code, user_id, access_times, first_access_time, last_access_time
    </sql>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update rp_user_activity_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.userId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="access_times = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.accessTimes != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.accessTimes,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="first_access_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.firstAccessTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.firstAccessTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="last_access_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.lastAccessTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.lastAccessTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_user_activity_info
        (tenant_code, user_id, access_times, first_access_time, last_access_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.accessTimes,jdbcType=INTEGER},
            #{item.firstAccessTime,jdbcType=TIMESTAMP}, #{item.lastAccessTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
    <select id="selectByUserId" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rp_user_activity_info
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
        limit 1
    </select>
    <insert id="insertForAll">
        insert into rp_user_activity_info (tenant_code, user_id, access_times, first_access_time, last_access_time)
        select tenant_code,
               user_id,
               count(0),
               min(create_time),
               max(create_time)
        from sys_user_operate_log
        group by tenant_code, user_id
    </insert>
    <delete id="deleteAll">
        delete
        from rp_user_activity_info
    </delete>
</mapper>
