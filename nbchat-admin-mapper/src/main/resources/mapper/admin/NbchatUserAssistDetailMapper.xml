<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.NbchatUserAssistDetailMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.NbchatUserAssistDetail">
        <!--@mbg.generated-->
        <!--@Table nbchat_user_assist_detail-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="assist_id" jdbcType="VARCHAR" property="assistId"/>
        <result column="assist_time" jdbcType="TIMESTAMP" property="assistTime"/>
        <result column="assistNum" jdbcType="INTEGER" property="assistNum"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_code, user_id, assist_id, assist_time
    </sql>

    <select id="countByGroupUserIdWhenCreateTimeBetween" resultMap="BaseResultMap">
        select tenant_code, user_id, count(0) as assistNum
        from nbchat_user_assist_detail
        where assist_time &lt; #{endTime,jdbcType=TIMESTAMP}
        <if test="startTime != null">
            and assist_time >= #{startTime,jdbcType=TIMESTAMP}
        </if>
        group by tenant_code, user_id
    </select>
</mapper>
