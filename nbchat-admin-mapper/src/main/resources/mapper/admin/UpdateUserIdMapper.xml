<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.UpdateUserIdMapper">
    <insert id="insertRecord" parameterType="com.tydic.nbchat.admin.mapper.po.UpdateUserRecord" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_update_user_record(`table`, `column`, old_value, new_value, create_time)
        VALUES (#{table}, #{column}, #{oldValue}, #{newValue}, #{createTime})
    </insert>

    <select id="selectByUserId" resultType="java.lang.Integer" parameterType="com.tydic.nbchat.admin.mapper.po.UpdateUserIdPO">
        select count(${column}) from ${table} where ${column} = #{oldUserId}
    </select>

    <update id="updateByUserId" parameterType="com.tydic.nbchat.admin.mapper.po.UpdateUserIdPO">
        update ${table} set ${column} = #{newUserId} where ${column} = #{oldUserId};
    </update>

</mapper>