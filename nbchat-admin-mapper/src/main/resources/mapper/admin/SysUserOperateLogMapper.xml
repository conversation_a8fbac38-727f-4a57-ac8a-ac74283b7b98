<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysUserOperateLogMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysUserOperateLog">
        <!--@mbg.generated-->
        <!--@Table sys_user_operate_log-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="busi_id" jdbcType="VARCHAR" property="busiId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="firstAccessTime" jdbcType="TIMESTAMP" property="firstAccessTime"/>
        <result column="lastAccessTime" jdbcType="TIMESTAMP" property="lastAccessTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_code, user_id, `type`, busi_id, content, create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from sys_user_operate_log
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from sys_user_operate_log
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysUserOperateLog" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sys_user_operate_log (tenant_code, user_id, `type`,
        busi_id, content, create_time)
        values (#{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR},
        #{busiId,jdbcType=VARCHAR}, #{content,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysUserOperateLog" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sys_user_operate_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="type != null and type != ''">
                `type`,
            </if>
            <if test="busiId != null and busiId != ''">
                busi_id,
            </if>
            <if test="content != null and content != ''">
                content,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null and userId != ''">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != ''">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="busiId != null and busiId != ''">
                #{busiId,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != ''">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserOperateLog">
        <!--@mbg.generated-->
        update sys_user_operate_log
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != ''">
                `type` = #{type,jdbcType=VARCHAR},
            </if>
            <if test="busiId != null and busiId != ''">
                busi_id = #{busiId,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != ''">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserOperateLog">
        <!--@mbg.generated-->
        update sys_user_operate_log
        set tenant_code = #{tenantCode,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=VARCHAR},
        `type` = #{type,jdbcType=VARCHAR},
        busi_id = #{busiId,jdbcType=VARCHAR},
        content = #{content,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update sys_user_operate_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.tenantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.userId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`type` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.type,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="busi_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.busiId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.content,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update sys_user_operate_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.userId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`type` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.type != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.type,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="busi_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.busiId != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.busiId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="content = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.content != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.content,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sys_user_operate_log
        (tenant_code, user_id, `type`, busi_id, content, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.type,jdbcType=VARCHAR},
            #{item.busiId,jdbcType=VARCHAR}, #{item.content,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <select id="countGroupByTenantCodeAndUserIdSelective"
            resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select tenant_code,
        user_id,
        min(create_time) as firstAccessTime,
        max(create_time) as lastAccessTime
        from sys_user_operate_log
        where to_days(create_time) = subdate(curdate(), 1)
        group by tenant_code, user_id
    </select>
</mapper>
