<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysPostMapper">

    <resultMap type="com.tydic.nbchat.admin.mapper.po.SysPost" id="SysPostMap">
        <result property="postId" column="post_id" jdbcType="INTEGER"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="postName" column="post_name" jdbcType="VARCHAR"/>
        <result property="postStatus" column="post_status" jdbcType="VARCHAR"/>
        <result property="postDesc" column="post_desc" jdbcType="VARCHAR"/>
        <result property="orderIndex" column="order_index" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
post_id, tenant_code, post_name,post_status, post_desc, order_index, create_time, is_valid</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="SysPostMap">
        select
          <include refid="Base_Column_List" />
        from sys_post
        where post_id = #{postId}
    </select>
    
    
    <select id="selectAll" resultMap="SysPostMap" parameterType="com.tydic.nbchat.admin.mapper.po.SysPost">
        select
          <include refid="Base_Column_List" />
        from sys_post
         <where>
            <if test="postId != null">
                and post_id = #{postId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
             <if test="postName != null and postName != ''">
                 and post_name like concat('%', #{postName}, '%')
             </if>
            <if test="postStatus != null and postStatus != ''">
                and post_status = #{postStatus}
            </if>
            <if test="postDesc != null and postDesc != ''">
                and post_desc = #{postDesc}
            </if>
            <if test="orderIndex != null">
                and order_index = #{orderIndex}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
        and is_valid = '1'
        </where>
        order by order_index, create_time desc
    </select>


    <insert id="insertSelective" keyProperty="postId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.admin.mapper.po.SysPost">
        insert into sys_post
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="postId != null">
                post_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="postName != null">
                post_name,
            </if>
            <if test="postStatus != null">
                post_status,
            </if>
            <if test="postDesc != null">
                post_desc,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="postId != null">
                #{postId},
            </if>
            <if test="tenantCode != null">
                #{tenantCode},
            </if>
            <if test="postName != null">
                #{postName},
            </if>
            <if test="postStatus != null">
                #{postStatus},
            </if>
            <if test="postDesc != null">
                #{postDesc},
            </if>
            <if test="orderIndex != null">
                #{orderIndex},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="isValid != null">
                #{isValid},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="postId" useGeneratedKeys="true">
        insert into sys_post(tenant_codepost_namepost_descorder_indexcreate_timeis_valid)
        values (#{tenantCode}#{postName}#{postDesc}#{orderIndex}#{createTime}#{isValid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_post
        <set>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode},
            </if>
            <if test="postName != null">
                post_name = #{postName},
            </if>
            <if test="postStatus != null and postStatus != ''">
                post_status = #{postStatus},
            </if>
            <if test="postDesc != null">
                post_desc = #{postDesc},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid},
            </if>
        </set>
        where post_id = #{postId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_post where post_id = #{postId}
    </delete>

    <select id="selectPostIdByPostName" resultType="java.lang.Integer">
        select post_id from sys_post where post_name = #{param1} and tenant_code = #{param2}
    </select>
</mapper>

