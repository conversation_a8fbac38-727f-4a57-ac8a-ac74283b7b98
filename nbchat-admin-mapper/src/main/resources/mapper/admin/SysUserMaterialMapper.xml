<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysUserMaterialMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysUserMaterial">
    <!--@mbg.generated-->
    <!--@Table sys_user_material-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="type" jdbcType="CHAR" property="type" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="word_count" jdbcType="INTEGER" property="wordCount" />
    <result column="suffix" jdbcType="VARCHAR" property="suffix" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_code, user_id, `type`, url, `name`, content, word_count, suffix, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from sys_user_material
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserMaterial">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from sys_user_material
 <where>
      <if test="tenantCode != null and tenantCode != ''">
        and tenant_code = #{tenantCode,jdbcType=VARCHAR}
      </if>
      <if test="userId != null and userId != ''">
        and user_id = #{userId,jdbcType=VARCHAR}
      </if>
      <if test="type != null">
        and type = #{type,jdbcType=CHAR}
      </if>
      <if test="url != null">
        and url = #{url,jdbcType=VARCHAR}
      </if>
      <if test="name != null and name != ''">
        and name = #{name,jdbcType=VARCHAR}
      </if>
      <if test="content != null and content != ''">
        and content = #{content,jdbcType=LONGVARCHAR}
      </if>
      <if test="wordCount != null and wordCount != ''">
        and word_count = #{wordCount,jdbcType=INTEGER}
      </if>
      <if test="suffix != null and suffix != ''">
        and suffix = #{suffix,jdbcType=VARCHAR}
      </if>
      <if test="createTime != null and createTime != ''">
        and create_time = #{createTime,jdbcType=TIMESTAMP}
      </if>
      <if test="updateTime != null and updateTime != ''">
        and update_time = #{updateTime,jdbcType=TIMESTAMP}
      </if>
    </where>
    order by create_time desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from sys_user_material
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserMaterial" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_material (tenant_code, user_id, `type`, 
      url, `name`, content, 
      word_count, suffix, create_time, 
      update_time)
    values (#{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{type,jdbcType=CHAR}, 
      #{url,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR}, 
      #{wordCount,jdbcType=INTEGER}, #{suffix,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserMaterial" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="wordCount != null">
        word_count,
      </if>
      <if test="suffix != null">
        suffix,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=CHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="wordCount != null">
        #{wordCount,jdbcType=INTEGER},
      </if>
      <if test="suffix != null">
        #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserMaterial">
    <!--@mbg.generated-->
    update sys_user_material
    <set>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=CHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="wordCount != null">
        word_count = #{wordCount,jdbcType=INTEGER},
      </if>
      <if test="suffix != null">
        suffix = #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserMaterial">
    <!--@mbg.generated-->
    update sys_user_material
    set tenant_code = #{tenantCode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=CHAR},
      url = #{url,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR},
      word_count = #{wordCount,jdbcType=INTEGER},
      suffix = #{suffix,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update sys_user_material
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="tenant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tenantCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.type != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.type,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.url != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.url,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.content != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.content,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="word_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.wordCount != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.wordCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="suffix = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.suffix != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.suffix,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_material
    (tenant_code, user_id, `type`, url, `name`, content, word_count, suffix, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.type,jdbcType=CHAR}, 
        #{item.url,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.content,jdbcType=LONGVARCHAR}, 
        #{item.wordCount,jdbcType=INTEGER}, #{item.suffix,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, 
        #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from sys_user_material where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    <foreach collection="list" item="item" separator=";">
      update sys_user_material
      <set>
        <if test="item.tenantCode != null">
          tenant_code = #{item.tenantCode,jdbcType=VARCHAR},
        </if>
        <if test="item.userId != null">
          user_id = #{item.userId,jdbcType=VARCHAR},
        </if>
        <if test="item.type != null">
          `type` = #{item.type,jdbcType=CHAR},
        </if>
        <if test="item.url != null">
          url = #{item.url,jdbcType=VARCHAR},
        </if>
        <if test="item.name != null">
          `name` = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.content != null">
          content = #{item.content,jdbcType=LONGVARCHAR},
        </if>
        <if test="item.wordCount != null">
          word_count = #{item.wordCount,jdbcType=INTEGER},
        </if>
        <if test="item.suffix != null">
          suffix = #{item.suffix,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsertSelectiveUseDefaultForNull" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_material
    (tenant_code, user_id, `type`, url, `name`, content, word_count, suffix, create_time, 
      update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (
      <choose>
        <when test="item.tenantCode != null">
          #{item.tenantCode,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.userId != null">
          #{item.userId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.type != null">
          #{item.type,jdbcType=CHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.url != null">
          #{item.url,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.name != null">
          #{item.name,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.content != null">
          #{item.content,jdbcType=LONGVARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.wordCount != null">
          #{item.wordCount,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.suffix != null">
          #{item.suffix,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createTime != null">
          #{item.createTime,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updateTime != null">
          #{item.updateTime,jdbcType=TIMESTAMP}
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserMaterial" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      tenant_code,
      user_id,
      `type`,
      url,
      `name`,
      content,
      word_count,
      suffix,
      create_time,
      update_time,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{tenantCode,jdbcType=VARCHAR},
      #{userId,jdbcType=VARCHAR},
      #{type,jdbcType=CHAR},
      #{url,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR},
      #{content,jdbcType=LONGVARCHAR},
      #{wordCount,jdbcType=INTEGER},
      #{suffix,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      tenant_code = #{tenantCode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=CHAR},
      url = #{url,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR},
      word_count = #{wordCount,jdbcType=INTEGER},
      suffix = #{suffix,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserMaterial" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_user_material
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="wordCount != null">
        word_count,
      </if>
      <if test="suffix != null">
        suffix,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=CHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="wordCount != null">
        #{wordCount,jdbcType=INTEGER},
      </if>
      <if test="suffix != null">
        #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=CHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="wordCount != null">
        word_count = #{wordCount,jdbcType=INTEGER},
      </if>
      <if test="suffix != null">
        suffix = #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
</mapper>