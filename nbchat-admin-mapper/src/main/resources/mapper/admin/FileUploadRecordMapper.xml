<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.FileUploadRecordMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.FileUploadRecord">
    <result column="file_no" jdbcType="VARCHAR" property="fileNo" />
    <result column="file_type" jdbcType="CHAR" property="fileType" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="upload_type" jdbcType="VARCHAR" property="uploadType" />
    <result column="upload_user" jdbcType="VARCHAR" property="uploadUser" />
    <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="busi_code" jdbcType="VARCHAR" property="busiCode" />
    <result column="access_url" jdbcType="VARCHAR" property="accessUrl" />
    <result column="file_size" jdbcType="INTEGER" property="fileSize" />
  </resultMap>

  <sql id="base_sql">
    file_no,file_type,file_name,file_path,upload_type,upload_user,
    upload_time,tenant_code,busi_code,access_url,file_size
  </sql>

  <update id="updateByFileNo" parameterType="com.tydic.nbchat.admin.mapper.po.FileUploadRecord">
    update file_upload_record
    <set>
        <if test="fileType != null">
            file_type = #{fileType},
        </if>
        <if test="fileName != null">
            file_name = #{fileName},
        </if>
        <if test="uploadTime != null">
            upload_time = #{uploadTime},
        </if>
        <if test="uploadType != null">
            upload_type = #{uploadType},
        </if>
    </set>
    where file_no = #{fileNo}
  </update>

  <select id="selectByFileNo" parameterType="string" resultMap="BaseResultMap">
    select <include refid="base_sql" /> from file_upload_record where file_no = #{fileNo}
  </select>

  <select id="selectByBusiCode" parameterType="string" resultMap="BaseResultMap">
    select <include refid="base_sql" /> from file_upload_record where busi_code = #{busiCode}
  </select>

  <delete id="deleteByFileNo">
      update file_upload_record set is_valid = '0',update_time=NOW() where file_no = #{fileNo}
  </delete>

  <delete id="deleteByPath">
     update file_upload_record set is_valid = '0',update_time=NOW() where file_path = #{filePath}
  </delete>

  <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.FileUploadRecord">
    insert into file_upload_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileNo != null">
        file_no,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="uploadType != null">
        upload_type,
      </if>
      <if test="uploadUser != null">
        upload_user,
      </if>
      <if test="uploadTime != null">
        upload_time,
      </if>
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="busiCode != null">
        busi_code,
      </if>
      <if test="accessUrl != null">
        access_url,
      </if>
      <if test="clientIp != null">
        client_ip,
      </if>
      <if test="serverIp != null">
        server_ip,
      </if>
      <if test="fileSize != null">
        file_size,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileNo != null">
        #{fileNo,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=CHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="uploadType != null">
        #{uploadType,jdbcType=VARCHAR},
      </if>
      <if test="uploadUser != null">
        #{uploadUser,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="busiCode != null">
        #{busiCode,jdbcType=VARCHAR},
      </if>
      <if test="accessUrl != null">
        #{accessUrl,jdbcType=VARCHAR},
      </if>
      <if test="clientIp != null">
        #{clientIp,jdbcType=VARCHAR},
      </if>
      <if test="serverIp != null">
        #{serverIp,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>