<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.NbchatSysJobNoMapper">

    <resultMap type="com.tydic.nbchat.admin.mapper.po.NbchatSysJobNo" id="NbchatSysJobBoMap">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="job_no" property="jobNo" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="bind_status" property="bindStatus" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id, user_id,tenant_code, job_no, phone, `name`, create_time, update_time, bind_status
    </sql>

    <select id="selectByUserId" resultMap="NbchatSysJobBoMap">
        select
        <include refid="Base_Column_List" />
        from nbchat_sys_job_no
        where user_id = #{userId}
    </select>

    <!--查询单个-->
    <select id="selectJobNo" resultMap="NbchatSysJobBoMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_sys_job_no
        where job_no = #{jobNo} and `name` = #{name} limit 1
    </select>

    <update id="bindJobNo">
        update nbchat_sys_job_no set bind_status = '1',user_id = #{userId} where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_sys_job_no where id = #{id}
    </delete>

</mapper>

