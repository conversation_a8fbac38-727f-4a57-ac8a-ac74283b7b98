<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpUserGrowMapper">
    <select id="queryVipUser" resultType="java.lang.Integer">
        select
            count(user_id)
        from op_rp_user_detail
        where vip_status = 1 and vip_start_time between #{param1} and #{param2}
    </select>
    <select id="queryTotalUser" resultType="java.lang.Integer">
        select
            count(user_id)
        from op_rp_user_detail
        where tenant_code ='00000000' and reg_time  between #{param1} and #{param2}
    </select>
    <select id="queryNewFreeUser" resultType="java.lang.Integer">
        select count(distinct user_id)
        from sys_login_log
        where auth_type in (1, 4) and vip_status='2'
          and reg_time between #{param1} and #{param2}
          and login_time between #{param1} and  #{param2};
    </select>
</mapper>