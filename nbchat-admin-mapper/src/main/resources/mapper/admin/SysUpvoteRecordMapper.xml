<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysUpvoteRecordMapper">

    <resultMap type="com.tydic.nbchat.admin.mapper.po.SysUpvoteRecord" id="SysUpvoteRecordMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="busiCode" column="busi_code" jdbcType="VARCHAR"/>
        <result property="busiId" column="busi_id" jdbcType="VARCHAR"/>
        <result property="likeStatus" column="like_status" jdbcType="VARCHAR"/>
        <result property="likesCount" column="likes_count" jdbcType="INTEGER"/>
        <result property="dislikesCount" column="dislikes_count" jdbcType="INTEGER"/>
        <result property="unlikeReason" column="unlike_reason" jdbcType="VARCHAR"/>
        <result property="unlikeInput" column="unlike_input" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="rating" column="rating" jdbcType="INTEGER"/>
        <result property="productModule" column="product_module" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id
        , user_id, tenant_code, busi_code, busi_id, like_status,
unlike_reason, unlike_input, create_time, rating, product_module
    </sql>

    <!--查询单个-->
    <select id="queryById" resultMap="SysUpvoteRecordMap">
        select
        SUM(CASE WHEN like_status = 1 THEN 1 ELSE 0 END) AS likes_count,
        SUM(CASE WHEN like_status = 0 THEN 1 ELSE 0 END) AS dislikes_count,
        <include refid="Base_Column_List"/>
        from sys_upvote_record
        where id = #{id}
    </select>


    <select id="selectAll" resultMap="SysUpvoteRecordMap"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysUpvoteRecord">
        select
        SUM(CASE WHEN like_status = 1 THEN 1 ELSE 0 END) AS likes_count,
        SUM(CASE WHEN like_status = 0 THEN 1 ELSE 0 END) AS dislikes_count,
        <include refid="Base_Column_List"/>
        from sys_upvote_record
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="busiCode != null and busiCode != ''">
                and busi_code = #{busiCode}
            </if>
            <if test="busiId != null and busiId != ''">
                and busi_id = #{busiId}
            </if>
            <if test="likeStatus != null and likeStatus != ''">
                and like_status =#{likeStatus}

            </if>
            <if test="unlikeReason != null and unlikeReason != ''">
                and unlike_reason = CONCAT('%',#{unlikeReason},'%')

            </if>
            <if test="unlikeInput != null and unlikeInput != ''">
                and unlike_input = CONCAT('%',#{unlikeInput},'%')

            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
        group by busi_id
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysUpvoteRecord">
        insert into sys_upvote_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="busiCode != null and busiCode != ''">
                busi_code,
            </if>
            <if test="busiId != null and busiId != ''">
                busi_id,
            </if>
            <if test="likeStatus != null and likeStatus != ''">
                like_status,
            </if>
            <if test="unlikeReason != null and unlikeReason != ''">
                unlike_reason,
            </if>
            <if test="unlikeInput != null and unlikeInput != ''">
                unlike_input,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="rating != null">
                rating,
            </if>
            <if test="productModule != null and productModule != ''">
                product_module,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="busiCode != null and busiCode != ''">
                #{busiCode},
            </if>
            <if test="busiId != null and busiId != ''">
                #{busiId},
            </if>
            <if test="likeStatus != null and likeStatus != ''">
                #{likeStatus},
            </if>
            <if test="unlikeReason != null and unlikeReason != ''">
                #{unlikeReason},
            </if>
            <if test="unlikeInput != null and unlikeInput != ''">
                #{unlikeInput},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="rating != null">
                #{rating},
            </if>
            <if test="productModule != null and productModule != ''">
                #{productModule},
            </if>
        </trim>
    </insert>


    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_upvote_record(user_id, tenant_code, busi_code, busi_id, like_status,
                                      unlike_reason, unlike_input, create_time, rating, product_module)
        values (#{userId}, #{tenantCode}, #{busiCode}, #{busiId}, #{likeStatus},
                #{unlikeReason}, #{unlikeInput}, #{createTime}, #{rating}, #{productModule})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_upvote_record
        <set>
            unlike_reason = #{unlikeReason},
            unlike_input = #{unlikeInput},
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="busiCode != null and busiCode != ''">
                busi_code = #{busiCode},
            </if>
            <if test="busiId != null and busiId != ''">
                busi_id = #{busiId},
            </if>
            <if test="likeStatus != null and likeStatus != ''">
                like_status = #{likeStatus},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="rating != null">
                rating = #{rating},
            </if>
            <if test="productModule != null and productModule != ''">
                product_module = #{productModule},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from sys_upvote_record
        where id = #{id}
    </delete>

</mapper>

