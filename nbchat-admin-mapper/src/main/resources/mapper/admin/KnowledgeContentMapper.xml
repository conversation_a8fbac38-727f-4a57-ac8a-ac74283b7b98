<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.KnowledgeContentMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo">
        <id column="sentence_id" jdbcType="VARCHAR" property="sentenceId"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="type_id" jdbcType="BIGINT" property="typeId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="sort_id" jdbcType="INTEGER" property="sortId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="hit_count" jdbcType="INTEGER" property="hitCount"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="content_title" jdbcType="VARCHAR" property="contentTitle"/>
        <result column="classes" jdbcType="VARCHAR" property="classes"/>
        <result column="is_valid" jdbcType="VARCHAR" property="isValid"/>

    </resultMap>
    <sql id="Base_Column_List">
        sentence_id,
        tenant_code,
        type_id,
        content,
        sort_id,
        create_time,
        create_user_id,
        create_user_name,
        update_time,
        update_user_id,
        update_user_name,
        hit_count,
        content_type,
        content_title,
        classes,
        is_valid
    </sql>

    <sql id="Knowledge_content">
        sentence_id,
        type_id,
        content,
        create_time,
        create_user_id,
        create_user_name,
        update_time,
        update_user_id,
        update_user_name,
        content_type,
        content_title,
        tenant_code,
        classes,
        is_valid
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Knowledge_content"/>
        from nbchat_knowledge_content
        where sentence_id = #{sentenceId,jdbcType=VARCHAR} and is_valid = '1'
    </select>
    <select id="selectByContent" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo"
            resultType="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo">
        select
            csc.sentence_id sentenceId,
            csc.content     content,
            csc.hit_count   hitCount
        from nbchat_knowledge_content csc
        where csc.tenant_code = #{tenantCode,jdbcType=VARCHAR}
          and csc.content like CONCAT('%', CONCAT(#{content,jdbcType=VARCHAR}, '%'))
          and csc.is_valid = '1'
        group by csc.content
        limit 5
    </select>
    <update id="deleteByPrimaryKey" parameterType="java.lang.String">
        update nbchat_knowledge_content set is_valid = '0'
        where sentence_id = #{sentenceId,jdbcType=VARCHAR}
    </update>
    <insert id="insert" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo" useGeneratedKeys="true"
            keyProperty="sentenceId">
        insert into nbchat_knowledge_content (sentence_id, tenant_code, type_id,
                                           content, sort_id, create_time,
                                           create_user_id, create_user_name, update_time,
                                           update_user_id, update_user_name, hit_count)
        values (#{sentenceId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{typeId,jdbcType=BIGINT},
                #{content,jdbcType=VARCHAR}, #{sortId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
                #{createUserId,jdbcType=BIGINT}, #{createUserName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
                #{updateUserId,jdbcType=BIGINT}, #{updateUserName,jdbcType=VARCHAR}, #{hitCount,jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo">
        insert into nbchat_knowledge_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sentenceId != null">
                sentence_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="typeId != null">
                type_id,
            </if>
            <if test="content != null">
                content,
            </if>
            <if test="sortId != null">
                sort_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createUserName != null">
                create_user_name,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateUserId != null">
                update_user_id,
            </if>
            <if test="updateUserName != null">
                update_user_name,
            </if>
            <if test="hitCount != null">
                hit_count,
            </if>
            <if test="contentTitle != null">
                content_title,
            </if>
            <if test="classes != null">
                classes,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sentenceId != null">
                #{sentenceId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="typeId != null">
                #{typeId,jdbcType=BIGINT},
            </if>
            <if test="content != null">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="sortId != null">
                #{sortId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=BIGINT},
            </if>
            <if test="createUserName != null">
                #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                #{updateUserId,jdbcType=BIGINT},
            </if>
            <if test="updateUserName != null">
                #{updateUserName,jdbcType=VARCHAR},
            </if>
            <if test="hitCount != null">
                #{hitCount,jdbcType=INTEGER},
            </if>
            <if test="contentTitle != null">
                #{contentTitle,jdbcType=INTEGER},
            </if>
            <if test="classes != null">
                #{classes,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo">
        update nbchat_knowledge_content
        <set>
            <if test="content != null">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                update_user_id = #{updateUserId,jdbcType=BIGINT},
            </if>
            <if test="updateUserName != null">
                update_user_name = #{updateUserName,jdbcType=VARCHAR},
            </if>
            <if test="contentTitle != null and contentTitle != ''">
                content_title = #{contentTitle,jdbcType=VARCHAR},
            </if>
        </set>
        where sentence_id = #{sentenceId,jdbcType=BIGINT} and is_valid = '1';
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo">
        update nbchat_knowledge_content
        set tenant_code      = #{tenantCode,jdbcType=VARCHAR},
            type_id          = #{typeId,jdbcType=BIGINT},
            content          = #{content,jdbcType=VARCHAR},
            sort_id          = #{sortId,jdbcType=INTEGER},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            create_user_id   = #{createUserId,jdbcType=BIGINT},
            create_user_name = #{createUserName,jdbcType=VARCHAR},
            update_time      = #{updateTime,jdbcType=TIMESTAMP},
            update_user_id   = #{updateUserId,jdbcType=BIGINT},
            update_user_name = #{updateUserName,jdbcType=VARCHAR},
            content_title    = #{contentTitle,jdbcType=VARCHAR},
            hit_count        = #{hitCount,jdbcType=INTEGER}
        where sentence_id = #{sentenceId,jdbcType=BIGINT} and is_valid = '1';
    </update>
    <select id="selectByPo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_knowledge_content
        where
        create_user_id = #{createUserId,jdbcType=BIGINT} and is_valid = '1'
        <if test="classes!=null">
            and classes =#{classes,jdbcType=VARCHAR}
        </if>
        <if test="typeId != null">
            and type_id = #{typeId,jdbcType=BIGINT}
        </if>
        <if test="content != null">
            and content like concat('%',concat(#{content,jdbcType=VARCHAR},'%'))
        </if>
        <if test="sortId != null">
            and sort_id = #{sortId,jdbcType=INTEGER}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="tenantCode != null">
            and tenant_code = #{tenantCode,jdbcType=VARCHAR}
        </if>
        <if test="createUserName != null">
            and create_user_name = #{createUserName,jdbcType=VARCHAR}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateUserId != null">
            and update_user_id = #{updateUserId,jdbcType=BIGINT}
        </if>
        <if test="updateUserName != null">
            and update_user_name = #{updateUserName,jdbcType=VARCHAR}
        </if>
        <if test="updateUserName != null">
            and update_user_name = #{updateUserName,jdbcType=VARCHAR}
        </if>
        <if test="contentTitle != null">
            and content_title = #{contentTitle,jdbcType=VARCHAR}
        </if>
        order by sort_id asc
    </select>

    <update id="deleteByTypeIds">
        update nbchat_knowledge_content
        set is_valid = '0'
        where create_user_id = #{createUserId} and type_id in
        <foreach collection="typeIds" item="typeId" index="index" separator="," open="(" close=")">
            #{typeId}
        </foreach>
    </update>


</mapper>
