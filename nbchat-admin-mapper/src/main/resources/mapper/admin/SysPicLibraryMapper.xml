<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysPicLibraryMapper">
    <resultMap type="com.tydic.nbchat.admin.mapper.po.SysPicLibraryPO" id="SysPicLibraryMap">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="pic_code" property="picCode" jdbcType="VARCHAR"/>
        <result column="pic_url" property="picUrl" jdbcType="VARCHAR"/>
        <result column="label_en" property="labelEn" jdbcType="VARCHAR"/>
        <result column="label_zh" property="labelZh" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectByKeyword" resultMap="SysPicLibraryMap">
        SELECT id, tenant_code, pic_code, pic_url, label_en, label_zh
        FROM sys_picture_library
        <where>
            <if test="labelEn != null and labelEn != ''">
                MATCH(label_en) AGAINST(#{keyword} IN NATURAL LANGUAGE MODE)
            </if>
            <if test="labelZh != null and labelZh != ''">
                MATCH(label_zh) AGAINST(#{keyword} IN NATURAL LANGUAGE MODE)
            </if>
            <if test="phrase != null and phrase != ''">
                label_zh LIKE CONCAT('%',#{keyword},'%')
            </if>
        </where>
        ORDER BY RAND()
    </select>
</mapper>

