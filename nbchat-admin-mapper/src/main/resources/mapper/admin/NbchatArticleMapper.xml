<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.admin.mapper.NbchatArticleMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.NbchatArticle">
        <id column="article_id" property="articleId" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="link_url" property="linkUrl" jdbcType="VARCHAR"/>
        <result column="category" property="category" jdbcType="VARCHAR"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="img_avatar" property="imgAvatar" jdbcType="VARCHAR"/>
        <result column="hit_count" property="hitCount" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_hot" property="isHot" jdbcType="CHAR"/>
        <result column="is_valid" property="isValid" jdbcType="CHAR"/>
        <result column="order_index" property="orderIndex" jdbcType="SMALLINT"/>
    </resultMap>
    <resultMap id="ResultMapWithBLOBs" type="com.tydic.nbchat.admin.mapper.po.NbchatArticle" extends="BaseResultMap">
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        article_id
        , user_id, title, link_url, category, tenant_code, img_avatar, hit_count,
    create_time, update_time, is_hot, is_valid, order_index
    </sql>
    <sql id="Blob_Column_List">
        content
    </sql>
    <select id="selectByPrimaryKey" resultMap="ResultMapWithBLOBs" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from nbchat_article
        where article_id = #{articleId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from nbchat_article
        where article_id = #{articleId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatArticle">
        insert into nbchat_article (article_id, user_id, title,
                                    link_url, category, tenant_code,
                                    img_avatar, hit_count, create_time,
                                    update_time, is_hot, is_valid,
                                    order_index, content)
        values (#{articleId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR},
                #{linkUrl,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR},
                #{imgAvatar,jdbcType=VARCHAR}, #{hitCount,jdbcType=SMALLINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP}, #{isHot,jdbcType=CHAR}, #{isValid,jdbcType=CHAR},
                #{orderIndex,jdbcType=SMALLINT}, #{content,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatArticle">
        insert into nbchat_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="articleId != null">
                article_id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="linkUrl != null">
                link_url,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="imgAvatar != null">
                img_avatar,
            </if>
            <if test="hitCount != null">
                hit_count,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isHot != null">
                is_hot,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="content != null">
                content,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="articleId != null">
                #{articleId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="linkUrl != null">
                #{linkUrl,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="imgAvatar != null">
                #{imgAvatar,jdbcType=VARCHAR},
            </if>
            <if test="hitCount != null">
                #{hitCount,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isHot != null">
                #{isHot,jdbcType=CHAR},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=CHAR},
            </if>
            <if test="orderIndex != null">
                #{orderIndex,jdbcType=SMALLINT},
            </if>
            <if test="content != null">
                #{content,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatArticle">
        update nbchat_article
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="linkUrl != null">
                link_url = #{linkUrl,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="imgAvatar != null">
                img_avatar = #{imgAvatar,jdbcType=VARCHAR},
            </if>
            <if test="hitCount != null">
                hit_count = #{hitCount,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isHot != null">
                is_hot = #{isHot,jdbcType=CHAR},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex,jdbcType=SMALLINT},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where article_id = #{articleId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatArticle">
        update nbchat_article
        set user_id     = #{userId,jdbcType=VARCHAR},
            title       = #{title,jdbcType=VARCHAR},
            link_url    = #{linkUrl,jdbcType=VARCHAR},
            category    = #{category,jdbcType=VARCHAR},
            tenant_code = #{tenantCode,jdbcType=VARCHAR},
            img_avatar  = #{imgAvatar,jdbcType=VARCHAR},
            hit_count   = #{hitCount,jdbcType=SMALLINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            is_hot      = #{isHot,jdbcType=CHAR},
            is_valid    = #{isValid,jdbcType=CHAR},
            order_index = #{orderIndex,jdbcType=SMALLINT},
            content     = #{content,jdbcType=LONGVARCHAR}
        where article_id = #{articleId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatArticle">
        update nbchat_article
        set user_id     = #{userId,jdbcType=VARCHAR},
            title       = #{title,jdbcType=VARCHAR},
            link_url    = #{linkUrl,jdbcType=VARCHAR},
            category    = #{category,jdbcType=VARCHAR},
            tenant_code = #{tenantCode,jdbcType=VARCHAR},
            img_avatar  = #{imgAvatar,jdbcType=VARCHAR},
            hit_count   = #{hitCount,jdbcType=SMALLINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            is_hot      = #{isHot,jdbcType=CHAR},
            is_valid    = #{isValid,jdbcType=CHAR},
            order_index = #{orderIndex,jdbcType=SMALLINT}
        where article_id = #{articleId,jdbcType=VARCHAR}
    </update>

    <select id="selectByCondition"
            parameterType="com.tydic.nbchat.admin.mapper.po.NbchatArticleSelectCondition"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        nbchat_article
        <where>
            <if test="isValid != null">
                is_valid = #{isValid}
            </if>
            <if test="isHot != null">
                is_hot = #{isHot}
            </if>
            <if test="articleId != null">
                article_id = #{articleId}
            </if>
        </where>
        order by order_index asc,create_time desc
    </select>

</mapper>