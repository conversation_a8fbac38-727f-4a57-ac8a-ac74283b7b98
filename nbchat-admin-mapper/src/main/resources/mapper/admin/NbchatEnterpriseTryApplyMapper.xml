<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.NbchatEnterpriseTryApplyMapper">

    <resultMap type="com.tydic.nbchat.admin.mapper.po.NbchatEnterpriseTryApply" id="NbchatEnterpriseTryApplyMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="companyName" column="company_name" jdbcType="VARCHAR"/>
        <result property="businessSector" column="business_sector" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="position" column="position" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="specificRequirements" column="specific_requirements" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="tryInviteKey" column="try_invite_key" jdbcType="VARCHAR"/>
        <result property="fileUrl" column="file_url" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        id, user_id, company_name, business_sector, tenant_code, position, name, phone, email, specific_requirements,
            status, created_time, updated_time, try_invite_key, file_url,file_name</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="NbchatEnterpriseTryApplyMap">
        select
          <include refid="Base_Column_List" />
        from nbchat_enterprise_try_apply
        where id = #{id}
    </select>
    
    
    <select id="selectAll" resultMap="NbchatEnterpriseTryApplyMap" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatEnterpriseTryApply">
        select
          <include refid="Base_Column_List" />
        from nbchat_enterprise_try_apply
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="companyName != null and companyName != ''">
                and instr(company_name,#{companyName}) > 0
            </if>
            <if test="businessSector != null and businessSector != ''">
                and business_sector = #{businessSector}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="position != null and position != ''">
                and position = #{position}
            </if>
            <if test="name != null and name != ''">
                and name = #{name}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone}
            </if>
            <if test="email != null and email != ''">
                and email = #{email}
            </if>
            <if test="specificRequirements != null and specificRequirements != ''">
                and specific_requirements = #{specificRequirements}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="createdTime != null">
                and DATE_FORMAT(created_time,'%Y%m%d') =  DATE_FORMAT(#{createdTime},'%Y%m%d')
            </if>
            <if test="updatedTime != null">
                and updated_time = #{updatedTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatEnterpriseTryApply">
        insert into nbchat_enterprise_try_apply
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="companyName != null and companyName != ''">
                company_name,
            </if>
            <if test="businessSector != null and businessSector != ''">
                business_sector,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="position != null and position != ''">
                position,
            </if>
            <if test="name != null and name != ''">
                name,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="email != null and email != ''">
                email,
            </if>
            <if test="specificRequirements != null and specificRequirements != ''">
                specific_requirements,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
            <if test="tryInviteKey != null and tryInviteKey != ''">
                try_invite_key,
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url,
            </if>
            <if test="fileName != null and fileName != ''">
                file_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="companyName != null and companyName != ''">
                #{companyName},
            </if>
            <if test="businessSector != null and businessSector != ''">
                #{businessSector},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="position != null and position != ''">
                #{position},
            </if>
            <if test="name != null and name != ''">
                #{name},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="email != null and email != ''">
                #{email},
            </if>
            <if test="specificRequirements != null and specificRequirements != ''">
                #{specificRequirements},
            </if>
            <if test="status != null and status != ''">
                #{status},
            </if>
            <if test="createdTime != null">
                #{createdTime},
            </if>
            <if test="updatedTime != null">
                #{updatedTime},
            </if>
            <if test="tryInviteKey != null and tryInviteKey != ''">
                #{tryInviteKey},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                #{fileUrl},
            </if>
            <if test="fileName != null and fileName != ''">
                #{fileName},
            </if>
        </trim>
    </insert>




    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into nbchat_enterprise_try_apply(user_id, company_name, business_sector, tenant_code, position, name, phone, email, specific_requirements, status, created_time, updated_time)
        values (#{userId}, #{companyName}, #{businessSector}, #{tenantCode}, #{position}, #{name}, #{phone}, #{email}, #{specificRequirements}, #{status}, #{createdTime}, #{updatedTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update nbchat_enterprise_try_apply
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="companyName != null and companyName != ''">
                company_name = #{companyName},
            </if>
            <if test="businessSector != null and businessSector != ''">
                business_sector = #{businessSector},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="position != null and position != ''">
                position = #{position},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="specificRequirements != null and specificRequirements != ''">
                specific_requirements = #{specificRequirements},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime},
            </if>
            <if test="tryInviteKey != null and tryInviteKey != ''">
                try_invite_key = #{tryInviteKey},
            </if>
            <if test="fileUrl != null and fileUrl != ''">
                file_url = #{fileUrl},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from nbchat_enterprise_try_apply where id = #{id}
    </delete>

</mapper>

