<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.admin.mapper.NbchatSysTenantMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.NbchatSysTenant">
        <id column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="tenant_desc" property="tenantDesc" jdbcType="VARCHAR"/>
        <result column="domain" property="domain" jdbcType="VARCHAR"/>
        <result column="linkman" property="linkman" jdbcType="VARCHAR"/>
        <result column="contact_number" property="contactNumber" jdbcType="VARCHAR"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="create_user" property="createUser" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_user" property="updateUser" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="is_valid" property="isValid" jdbcType="CHAR"/>
        <result column="img_avatar" property="imgAvatar" jdbcType="VARCHAR"/>
        <result column="ext_info" property="extInfo" jdbcType="VARCHAR"/>
        <result column="auth_api" property="authApi" jdbcType="VARCHAR"/>
        <result column="auth_config" property="authConfig" jdbcType="VARCHAR"/>
        <result column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="tenant_config" property="tenantConfig" jdbcType="VARCHAR"/>
        <result column="custom_config" property="customConfig" jdbcType="VARCHAR"/>
        <result column="user_limit" property="userLimit" jdbcType="INTEGER"/>
        <result column="pay_type" property="payType" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="UserInTenantMap"  type="com.tydic.nbchat.admin.mapper.po.SysUserInTenant" extends="BaseResultMap">
        <id column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="tenant_name" property="tenantName" jdbcType="VARCHAR"/>
        <result column="img_avatar" property="imgAvatar" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="user_reality_name" property="userRealityName" jdbcType="VARCHAR"/>
        <result column="avatar" property="userAvatar" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        tenant_code, tenant_name, tenant_desc, domain, linkman, contact_number, address,tenant_config,
    create_user, create_time, update_user, update_time, status, is_valid, img_avatar, auth_api, auth_config,
    ext_info, tpl_code, order_index,user_limit, custom_config, pay_type
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from nbchat_sys_tenant
        where tenant_code = #{tenantCode,jdbcType=VARCHAR} and is_valid = '1'
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from nbchat_sys_tenant
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatSysTenant">
        insert into nbchat_sys_tenant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="tenantName != null">
                tenant_name,
            </if>
            <if test="tenantDesc != null">
                tenant_desc,
            </if>
            <if test="domain != null">
                domain,
            </if>
            <if test="linkman != null">
                linkman,
            </if>
            <if test="contactNumber != null">
                contact_number,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="createUser != null">
                create_user,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateUser != null">
                update_user,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="isValid != null">
                is_valid,
            </if>
            <if test="imgAvatar != null">
                img_avatar,
            </if>
            <if test="extInfo != null">
                ext_info,
            </if>
            <if test="authApi != null">
                auth_api,
            </if>
            <if test="authConfig != null">
                auth_config,
            </if>
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="tenantConfig != null and tenantConfig != ''">
                tenant_config,
            </if>
            <if test="userLimit != null">
                user_limit,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="tenantName != null">
                #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="tenantDesc != null">
                #{tenantDesc,jdbcType=VARCHAR},
            </if>
            <if test="domain != null">
                #{domain,jdbcType=VARCHAR},
            </if>
            <if test="linkman != null">
                #{linkman,jdbcType=VARCHAR},
            </if>
            <if test="contactNumber != null">
                #{contactNumber,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null">
                #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null">
                #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="isValid != null">
                #{isValid,jdbcType=CHAR},
            </if>
            <if test="imgAvatar != null">
                #{imgAvatar,jdbcType=VARCHAR},
            </if>
            <if test="extInfo != null">
                #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="authApi != null">
                #{authApi,jdbcType=VARCHAR},
            </if>
            <if test="authConfig != null">
                #{authConfig,jdbcType=VARCHAR},
            </if>
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="tenantConfig != null and tenantConfig != ''">
                #{tenantConfig,jdbcType=VARCHAR},
            </if>
            <if test="userLimit != null">
                #{userLimit,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatSysTenant">
        update nbchat_sys_tenant
        <set>
            <if test="tenantName != null and tenantName != ''">
                tenant_name = #{tenantName,jdbcType=VARCHAR},
            </if>
            <if test="tenantDesc != null and tenantDesc != ''">
                tenant_desc = #{tenantDesc,jdbcType=VARCHAR},
            </if>
            <if test="domain != null and domain != ''">
                domain = #{domain,jdbcType=VARCHAR},
            </if>
            <if test="linkman != null and linkman != ''">
                linkman = #{linkman,jdbcType=VARCHAR},
            </if>
            <if test="contactNumber != null and contactNumber != ''">
                contact_number = #{contactNumber,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != ''">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="createUser != null and createUser != ''">
                create_user = #{createUser,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUser != null and updateUser != ''">
                update_user = #{updateUser,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid,jdbcType=CHAR},
            </if>
            <if test="imgAvatar != null and imgAvatar != ''">
                img_avatar = #{imgAvatar,jdbcType=VARCHAR},
            </if>
            <if test="extInfo != null and extInfo != ''">
                ext_info = #{extInfo,jdbcType=VARCHAR},
            </if>
            <if test="authApi != null and authApi != ''">
                auth_api = #{authApi,jdbcType=VARCHAR},
            </if>
            <if test="authConfig != null and authConfig != ''">
                auth_config = #{authConfig,jdbcType=VARCHAR},
            </if>
            <if test="tplCode != null and tplCode != ''">
                tpl_code = #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="tenantConfig != null and tenantConfig != ''">
                tenant_config = #{tenantConfig,jdbcType=VARCHAR},
            </if>
            <if test="userLimit != null">
                user_limit = #{userLimit,jdbcType=INTEGER},
            </if>
            <if test="customConfig != null and customConfig != ''">
                custom_config = #{customConfig,jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != ''">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
        </set>
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
    </update>


    <select id="selectByCondition"
            parameterType="com.tydic.nbchat.admin.mapper.po.NbchatSysTenantSelectCondition"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_sys_tenant
        <where>
            <if test="tenantCode != null and tenantCode!=''">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="tenantName != null">
                and tenant_name LIKE CONCAT('%', #{tenantName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="status != null">
                and status = #{status,jdbcType=INTEGER}
            </if>
            <if test="isValid != null">
                and is_valid = #{isValid,jdbcType=CHAR}
            </if>
        </where>
        order by order_index,create_time desc
    </select>


    <select id="getUserRole" resultType="java.lang.String">
        select current_setting
        from nbchat_user_settings
        where user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="selectTenantList" resultMap="BaseResultMap">
        SELECT * FROM (
        SELECT tenant_code, tenant_name, create_time FROM nbchat_sys_tenant WHERE tenant_code NOT
        IN('00000000','000TYDIC','00000TMO')
        UNION ALL
        SELECT '00000000' AS tenant_code, company_name AS tenant_name, create_time FROM nbchat_user_enterprise
        GROUP BY company_name, create_time
        ) AS combined_results
        <where>
            <if test="tenantName != null and tenantName != ''">
                AND tenant_name LIKE '%'#{tenantName}'%'
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                AND tenant_code = #{tenantCode}
            </if>
        </where>
    </select>

    <select id="selectTenantListByUseRole" resultMap="BaseResultMap">
        select * from nbchat_sys_tenant where tenant_code in
        ( select DISTINCT subsystem from sys_user_role_rel
                                    where user_id = #{userId,jdbcType=VARCHAR} and role != 'user')
         and is_valid = '1'
        order by order_index,create_time desc
    </select>

    <select id="selectUserTenantList" resultMap="UserInTenantMap">
        select a.tenant_code, a.tenant_name, a.create_time, a.img_avatar,b.user_reality_name,b.avatar
        from nbchat_sys_tenant a join nbchat_sys_user_tenant b
            on a.tenant_code = b.tenant_code AND b.user_id = #{userId,jdbcType=VARCHAR} AND b.user_status = 1
        WHERE a.is_valid = '1' order by a.order_index,a.create_time desc
    </select>

    <select id="checkUserInTenant" resultType="int">
        select count(1) from nbchat_sys_user_tenant where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </select>
</mapper>