<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.admin.mapper.SysMenuTplRelMenuMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysMenuTplRelMenu">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="tpl_code" property="tplCode" jdbcType="VARCHAR"/>
        <result column="menu_code" property="menuCode" jdbcType="VARCHAR"/>
        <result column="button_codes" property="buttonCodes" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, tpl_code, menu_code, button_codes, create_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from sys_menu_tpl_rel
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from sys_menu_tpl_rel
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuTplRelMenu">
        insert into sys_menu_tpl_rel (id, tpl_code, menu_code,
                                           button_codes, create_time)
        values (#{id,jdbcType=INTEGER}, #{tplCode,jdbcType=VARCHAR}, #{menuCode,jdbcType=VARCHAR},
                #{buttonCodes,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuTplRelMenu">
        insert into sys_menu_tpl_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tplCode != null">
                tpl_code,
            </if>
            <if test="menuCode != null">
                menu_code,
            </if>
            <if test="buttonCodes != null">
                button_codes,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="tplCode != null">
                #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="menuCode != null">
                #{menuCode,jdbcType=VARCHAR},
            </if>
            <if test="buttonCodes != null">
                #{buttonCodes,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuTplRelMenu">
        update sys_menu_tpl_rel
        <set>
            <if test="tplCode != null">
                tpl_code = #{tplCode,jdbcType=VARCHAR},
            </if>
            <if test="menuCode != null">
                menu_code = #{menuCode,jdbcType=VARCHAR},
            </if>
            <if test="buttonCodes != null">
                button_codes = #{buttonCodes,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuTplRelMenu">
        update sys_menu_tpl_rel
        set tpl_code     = #{tplCode,jdbcType=VARCHAR},
            menu_code    = #{menuCode,jdbcType=VARCHAR},
            button_codes = #{buttonCodes,jdbcType=VARCHAR},
            create_time  = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <insert id="insertBatch" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuTplRelMenu">
        insert into sys_menu_tpl_rel (id, tpl_code, menu_code,
        button_codes, create_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=INTEGER}, #{item.tplCode,jdbcType=VARCHAR}, #{item.menuCode,jdbcType=VARCHAR},
            #{item.buttonCodes,jdbcType=VARCHAR}, NOW())
        </foreach>
    </insert>

    <delete id="deleteByTplCode" parameterType="string">
        delete
        from sys_menu_tpl_rel
        where tpl_code = #{tplCode,jdbcType=VARCHAR}
    </delete>

    <select id="selectAllByTplCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_menu_tpl_rel
        where tpl_code = #{tplCode,jdbcType=VARCHAR}
    </select>

</mapper>