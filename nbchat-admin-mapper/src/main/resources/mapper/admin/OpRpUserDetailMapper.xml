<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.OpRpUserDetailMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.OpRpUserDetail">
        <!--@mbg.generated-->
        <!--@Table op_rp_user_detail-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="user_name" jdbcType="VARCHAR" property="userName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="user_type" jdbcType="CHAR" property="userType"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="reg_time" jdbcType="TIMESTAMP" property="regTime"/>
        <result column="reg_channel" jdbcType="VARCHAR" property="regChannel"/>
        <result column="vip_status" jdbcType="CHAR" property="vipStatus"/>
        <result column="vip_type" jdbcType="CHAR" property="vipType"/>
        <result column="vip_start_time" jdbcType="TIMESTAMP" property="vipStartTime"/>
        <result column="vip_end_time" jdbcType="TIMESTAMP" property="vipEndTime"/>
        <result column="buy_version" jdbcType="VARCHAR" property="buyVersion"/>
        <result column="login_time" jdbcType="TIMESTAMP" property="loginTime"/>
        <result column="score_balance" jdbcType="INTEGER" property="scoreBalance"/>
        <result column="login_count" jdbcType="INTEGER" property="loginCount"/>
        <result column="video_make_count" jdbcType="INTEGER" property="videoMakeCount"/>
        <result column="video_success_count" jdbcType="INTEGER" property="videoSuccessCount"/>
        <result column="ppt_make_count" jdbcType="INTEGER" property="pptMakeCount"/>
        <result column="ppt_success_count" jdbcType="INTEGER" property="pptSuccessCount"/>
        <result column="exam_make_count" jdbcType="INTEGER" property="examMakeCount"/>
        <result column="exam_success_count" jdbcType="INTEGER" property="examSuccessCount"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="vip_first_time" jdbcType="TIMESTAMP" property="vipFirstTime"/>
        <result column="vip_buy_count" jdbcType="INTEGER" property="vipBuyCount"/>
        <result column="reg_source" jdbcType="VARCHAR" property="regSource"/>
        <result column="prom_key" jdbcType="VARCHAR" property="promKey"/>
        <result column="prom_id" jdbcType="VARCHAR" property="promId"/>
        <result column="prom_channel" jdbcType="VARCHAR" property="promChannel"/>
        <result column="pay_time" jdbcType="TIMESTAMP" property="payTime"/>
        <result column="last_pay_time" jdbcType="TIMESTAMP" property="lastPayTime"/>
        <result column="last_pay_amount" jdbcType="INTEGER" property="lastPayAmount"/>
        <result column="total_pay_amount" jdbcType="INTEGER" property="totalPayAmount"/>
        <result column="score_buy_count" jdbcType="INTEGER" property="scoreBuyCount"/>
        <result column="score_recharge_total" jdbcType="INTEGER" property="scoreRechargeTotal"/>
        <result column="last_goods_name" jdbcType="VARCHAR" property="lastGoodsName"/>
        <result column="score_consume_total" jdbcType="INTEGER" property="scoreConsumeTotal"/>
        <result column="is_bind_wx" jdbcType="CHAR" property="isBindWx"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, user_id, user_name, phone, user_type, tenant_code, company_name, reg_time, reg_channel,
        vip_status, vip_type, vip_start_time, vip_end_time, vip_buy_count, vip_first_time,
        buy_version, login_time, is_bind_wx, score_balance, login_count, video_make_count,
        video_success_count, ppt_make_count, ppt_success_count, exam_make_count, exam_success_count,
        update_time, reg_source, prom_key, prom_id, prom_channel, last_pay_time, last_pay_amount,
        total_pay_amount, score_buy_count, score_recharge_total, score_consume_total, last_goods_name
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from op_rp_user_detail
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from op_rp_user_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <update id="updateUserScore">
        update op_rp_user_detail set score_balance = #{scoreBalance},score_recharge_total =
        (SELECT IFNULL(SUM(score),0) FROM nbchat_user_score_task WHERE tenant_code =
        #{tenantCode} AND user_id = #{userId} AND task_status = '1'),
        score_consume_total = ( SELECT IFNULL(ABS(SUM(score)),0) FROM nbchat_user_bill_record
        WHERE tenant_code = #{tenantCode}
        AND user_id = #{userId} AND (score &lt; 0 or refund_id != '') )
        where tenant_code = #{tenantCode} AND user_id = #{userId}
    </update>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.tydic.nbchat.admin.mapper.po.OpRpUserDetail" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into op_rp_user_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="userName != null and userName != ''">
                user_name,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="userType != null and userType != ''">
                user_type,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="companyName != null and companyName != ''">
                company_name,
            </if>
            <if test="regTime != null">
                reg_time,
            </if>
            <if test="regChannel != null and regChannel != ''">
                reg_channel,
            </if>
            <if test="vipStatus != null and vipStatus != ''">
                vip_status,
            </if>
            <if test="vipType != null and vipType != ''">
                vip_type,
            </if>
            <if test="vipStartTime != null">
                vip_start_time,
            </if>
            <if test="vipEndTime != null">
                vip_end_time,
            </if>
            <if test="vipBuyCount != null">
                vip_buy_count,
            </if>
            <if test="vipFirstTime != null">
                vip_first_time,
            </if>
            <if test="buyVersion != null and buyVersion != ''">
                buy_version,
            </if>
            <if test="loginTime != null">
                login_time,
            </if>
            <if test="isBindWx != null and isBindWx != ''">
                is_bind_wx,
            </if>
            <if test="scoreBalance != null">
                score_balance,
            </if>
            <if test="loginCount != null">
                login_count,
            </if>
            <if test="videoMakeCount != null">
                video_make_count,
            </if>
            <if test="videoSuccessCount != null">
                video_success_count,
            </if>
            <if test="pptMakeCount != null">
                ppt_make_count,
            </if>
            <if test="pptSuccessCount != null">
                ppt_success_count,
            </if>
            <if test="examMakeCount != null">
                exam_make_count,
            </if>
            <if test="examSuccessCount != null">
                exam_success_count,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="regSource != null and regSource != ''">
                reg_source,
            </if>
            <if test="promKey != null and promKey != ''">
                prom_key,
            </if>
            <if test="promId != null and promId != ''">
                prom_id,
            </if>
            <if test="promChannel != null and promChannel != ''">
                prom_channel,
            </if>
            <if test="lastPayTime != null">
                last_pay_time,
            </if>
            <if test="lastPayAmount != null">
                last_pay_amount,
            </if>
            <if test="totalPayAmount != null">
                total_pay_amount,
            </if>
            <if test="scoreBuyCount != null">
                score_buy_count,
            </if>
            <if test="scoreRechargeTotal != null">
                score_recharge_total,
            </if>
            <if test="scoreConsumeTotal != null">
                score_consume_total,
            </if>
            <if test="lastGoodsName != null and lastGoodsName != ''">
                last_goods_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != ''">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != ''">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="userType != null and userType != ''">
                #{userType,jdbcType=CHAR},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null and companyName != ''">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="regTime != null">
                #{regTime,jdbcType=TIMESTAMP},
            </if>
            <if test="regChannel != null and regChannel != ''">
                #{regChannel,jdbcType=VARCHAR},
            </if>
            <if test="vipStatus != null and vipStatus != ''">
                #{vipStatus,jdbcType=CHAR},
            </if>
            <if test="vipType != null and vipType != ''">
                #{vipType,jdbcType=CHAR},
            </if>
            <if test="vipStartTime != null">
                #{vipStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="vipEndTime != null">
                #{vipEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="vipBuyCount != null">
                #{vipBuyCount,jdbcType=INTEGER},
            </if>
            <if test="vipFirstTime != null">
                #{vipFirstTime,jdbcType=TIMESTAMP},
            </if>
            <if test="buyVersion != null and buyVersion != ''">
                #{buyVersion,jdbcType=VARCHAR},
            </if>
            <if test="loginTime != null">
                #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isBindWx != null and isBindWx != ''">
                #{isBindWx,jdbcType=CHAR},
            </if>
            <if test="scoreBalance != null">
                #{scoreBalance,jdbcType=INTEGER},
            </if>
            <if test="loginCount != null">
                #{loginCount,jdbcType=INTEGER},
            </if>
            <if test="videoMakeCount != null">
                #{videoMakeCount,jdbcType=INTEGER},
            </if>
            <if test="videoSuccessCount != null">
                #{videoSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="pptMakeCount != null">
                #{pptMakeCount,jdbcType=INTEGER},
            </if>
            <if test="pptSuccessCount != null">
                #{pptSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="examMakeCount != null">
                #{examMakeCount,jdbcType=INTEGER},
            </if>
            <if test="examSuccessCount != null">
                #{examSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="regSource != null and regSource != ''">
                #{regSource,jdbcType=VARCHAR},
            </if>
            <if test="promKey != null and promKey != ''">
                #{promKey,jdbcType=VARCHAR},
            </if>
            <if test="promId != null and promId != ''">
                #{promId,jdbcType=VARCHAR},
            </if>
            <if test="promChannel != null and promChannel != ''">
                #{promChannel,jdbcType=VARCHAR},
            </if>
            <if test="lastPayTime != null">
                #{lastPayTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastPayAmount != null">
                #{lastPayAmount,jdbcType=INTEGER},
            </if>
            <if test="totalPayAmount != null">
                #{totalPayAmount,jdbcType=INTEGER},
            </if>
            <if test="scoreBuyCount != null">
                #{scoreBuyCount,jdbcType=INTEGER},
            </if>
            <if test="scoreRechargeTotal != null">
                #{scoreRechargeTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreConsumeTotal != null">
                #{scoreConsumeTotal,jdbcType=INTEGER},
            </if>
            <if test="lastGoodsName != null and lastGoodsName != ''">
                #{lastGoodsName,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.OpRpUserDetail">
        <!--@mbg.generated-->
        update op_rp_user_detail
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="userType != null and userType != ''">
                user_type = #{userType,jdbcType=CHAR},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null and companyName != ''">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="regTime != null">
                reg_time = #{regTime,jdbcType=TIMESTAMP},
            </if>
            <if test="regChannel != null and regChannel != ''">
                reg_channel = #{regChannel,jdbcType=VARCHAR},
            </if>
            <if test="vipStatus != null and vipStatus != ''">
                vip_status = #{vipStatus,jdbcType=CHAR},
            </if>
            <if test="vipType != null and vipType != ''">
                vip_type = #{vipType,jdbcType=CHAR},
            </if>
            <if test="vipStartTime != null">
                vip_start_time = #{vipStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="vipEndTime != null">
                vip_end_time = #{vipEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="vipBuyCount != null">
                vip_buy_count = #{vipBuyCount,jdbcType=INTEGER},
            </if>
            <if test="vipFirstTime != null">
                vip_first_time = #{vipFirstTime,jdbcType=TIMESTAMP},
            </if>
            <if test="buyVersion != null and buyVersion != ''">
                buy_version = #{buyVersion,jdbcType=VARCHAR},
            </if>
            <if test="loginTime != null">
                login_time = #{loginTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isBindWx != null and isBindWx != ''">
                is_bind_wx = #{isBindWx,jdbcType=CHAR},
            </if>
            <if test="scoreBalance != null">
                score_balance = #{scoreBalance,jdbcType=INTEGER},
            </if>
            <if test="loginCount != null">
                login_count = #{loginCount,jdbcType=INTEGER},
            </if>
            <if test="videoMakeCount != null">
                video_make_count = #{videoMakeCount,jdbcType=INTEGER},
            </if>
            <if test="videoSuccessCount != null">
                video_success_count = #{videoSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="pptMakeCount != null">
                ppt_make_count = #{pptMakeCount,jdbcType=INTEGER},
            </if>
            <if test="pptSuccessCount != null">
                ppt_success_count = #{pptSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="examMakeCount != null">
                exam_make_count = #{examMakeCount,jdbcType=INTEGER},
            </if>
            <if test="examSuccessCount != null">
                exam_success_count = #{examSuccessCount,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="regSource != null and regSource != ''">
                reg_source = #{regSource,jdbcType=VARCHAR},
            </if>
            <if test="promKey != null and promKey != ''">
                prom_key = #{promKey,jdbcType=VARCHAR},
            </if>
            <if test="promId != null and promId != ''">
                prom_id = #{promId,jdbcType=VARCHAR},
            </if>
            <if test="promChannel != null and promChannel != ''">
                prom_channel = #{promChannel,jdbcType=VARCHAR},
            </if>
            <if test="lastPayTime != null">
                last_pay_time = #{lastPayTime,jdbcType=TIMESTAMP},
            </if>
            <if test="lastPayAmount != null">
                last_pay_amount = #{lastPayAmount,jdbcType=INTEGER},
            </if>
            <if test="totalPayAmount != null">
                total_pay_amount = #{totalPayAmount,jdbcType=INTEGER},
            </if>
            <if test="scoreBuyCount != null">
                score_buy_count = #{scoreBuyCount,jdbcType=INTEGER},
            </if>
            <if test="scoreRechargeTotal != null">
                score_recharge_total = #{scoreRechargeTotal,jdbcType=INTEGER},
            </if>
            <if test="scoreConsumeTotal != null">
                score_consume_total = #{scoreConsumeTotal,jdbcType=INTEGER},
            </if>
            <if test="lastGoodsName != null and lastGoodsName != ''">
                last_goods_name = #{lastGoodsName,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByCondition" parameterType="com.tydic.nbchat.admin.mapper.po.OpRpUserDetailSelectCondition"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from op_rp_user_detail
        <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="isPay == '1'.toString()">
                and total_pay_amount &gt; 0
            </if>
            <if test="isPay == '0'.toString()">
                and total_pay_amount = 0
            </if>
            <if test="userType != null and userType != ''">
                and user_type = #{userType}
            </if>
            <if test="phone != null and phone != ''">
                and phone = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="regChannel != null and regChannel != ''">
                and reg_channel = #{regChannel,jdbcType=CHAR}
            </if>
            <if test="vipStatus != null and vipStatus != ''">
                and vip_status = #{vipStatus,jdbcType=CHAR}
            </if>
            <if test="vipType != null and vipType != ''">
                and vip_type = #{vipType,jdbcType=CHAR}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="keyword != null and keyword != ''">
                and (user_name like concat('%',#{keyword,jdbcType=VARCHAR},'%') or company_name like
                concat('%',#{keyword,jdbcType=VARCHAR},'%'))
            </if>
            <if test="regStartTime != null and regEndTime != null">
                and reg_time between #{regStartTime} and #{regEndTime}
            </if>
            <if test="payStartTime != null or payEndTime != null">
                and last_pay_time between #{payStartTime} and #{payEndTime}
            </if>
            <if test="videoMakeCountFilter != null and videoMakeCountFilter != ''">
                AND video_make_count ${videoMakeCountFilter}
            </if>
            <if test="pptMakeCountFilter != null and pptMakeCountFilter != ''">
                AND ppt_make_count ${pptMakeCountFilter}
            </if>
            <if test="examMakeCountFilter != null and examMakeCountFilter != ''">
                AND exam_make_count ${examMakeCountFilter}
            </if>
        </where>
        order by reg_time desc,id desc
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from op_rp_user_detail
        where tenant_code = #{tenantCode} and user_id = #{userId,jdbcType=VARCHAR} limit 1
    </select>

    <select id="countTdhTotal" resultType="int">
        select count(1) from tdh_creation_task where tenant_code = #{tenantCode}
        and user_id = #{userId,jdbcType=VARCHAR} and task_state != 'q'
    </select>

    <select id="countTdhSuccess" resultType="int">
        select count(1) from tdh_creation_task where tenant_code = #{tenantCode}
        and user_id = #{userId,jdbcType=VARCHAR} and task_state = '1'
    </select>

    <select id="selectScoreConsumeList" resultType="java.util.HashMap">
        select user_id, ifnull(sum(score),0) as score from nbchat_user_bill_detail
        <foreach close=") group by user_id,tenant_code;" collection="list" item="item" open=" where user_id in( "
                 separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="selectScoreRecharge" resultType="java.lang.Integer">
        select ifnull(sum(score),0) as score from nbchat_user_bill_record where tenant_code = #{param1} and user_id =
        #{param2,jdbcType=VARCHAR} and type = '1'
    </select>

    <update id="updateJoinType">
        update op_rp_user_detail set reg_channel = #{param3} , reg_source = #{param3} where user_id = #{param1} and
        tenant_code = #{param2};
    </update>

    <select id="selectByPayTimeCondition" resultMap="BaseResultMap">
        SELECT a.pay_time AS pay_time,
        b.id AS id,
        b.user_id AS user_id,
        b.user_name AS user_name,
        b.phone AS phone,
        b.user_type AS user_type,
        b.tenant_code AS tenant_code,
        b.company_name AS company_name,
        b.reg_time AS reg_time,
        b.reg_channel AS reg_channel,
        b.vip_status AS vip_status,
        b.vip_type AS vip_type,
        b.vip_start_time AS vip_start_time,
        b.vip_end_time AS vip_end_time,
        b.buy_version AS buy_version,
        b.login_time AS login_time,
        b.score_balance AS score_balance,
        b.login_count AS login_count,
        b.video_make_count AS video_make_count,
        b.video_success_count AS video_success_count,
        b.ppt_make_count AS ppt_make_count,
        b.ppt_success_count AS ppt_success_count,
        b.exam_make_count AS exam_make_count,
        b.exam_success_count AS exam_success_count,
        b.update_time AS update_time,
        b.vip_first_time AS vip_first_time,
        b.vip_buy_count AS vip_buy_count,
        b.reg_source AS reg_source,
        b.prom_key AS prom_key,
        b.prom_id AS prom_id,
        b.prom_channel AS prom_channel
        FROM (
        SELECT a.user_id, a.pay_time, a.pay_status,a.tenant_code,
        ROW_NUMBER() OVER (PARTITION BY a.user_id ORDER BY a.pay_time DESC) AS rn
        FROM pay_trade_record a
        WHERE a.pay_status = 'SUCCESS'
        <if test="payStartTime != null or payEndTime != null">
            AND a.pay_time BETWEEN #{payStartTime,jdbcType=TIMESTAMP} AND #{payEndTime,jdbcType=TIMESTAMP}
        </if>
        ) a
        LEFT JOIN op_rp_user_detail b ON a.user_id = b.user_id AND a.tenant_code = b.tenant_code
        <where>
            a.rn = 1
            <if test="id != null">
                AND b.id = #{id}
            </if>
            <if test="userId != null and userId != ''">
                AND b.user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="userType != null and userType != ''">
                AND b.user_type = #{userType}
            </if>
            <if test="phone != null and phone != ''">
                AND b.phone = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="regChannel != null and regChannel != ''">
                AND b.reg_channel = #{regChannel,jdbcType=CHAR}
            </if>
            <if test="vipStatus != null and vipStatus != ''">
                AND b.vip_status = #{vipStatus,jdbcType=CHAR}
            </if>
            <if test="vipType != null and vipType != ''">
                AND b.vip_type = #{vipType,jdbcType=CHAR}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                AND b.tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (b.user_name LIKE CONCAT('%',#{keyword,jdbcType=VARCHAR},'%') OR b.company_name LIKE
                CONCAT('%',#{keyword,jdbcType=VARCHAR},'%'))
            </if>
            <if test="regStartTime != null and regEndTime != null">
                AND b.reg_time BETWEEN #{regStartTime,jdbcType=TIMESTAMP} AND #{regEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
    <select id="selectRpByTenantCodeAndUserId" resultType="com.tydic.nbchat.admin.mapper.po.UserRpDetailPO">
        select user.tenant_code,
        user.user_id,
        user_name,
        phone,
        user_type,
        company_name,
        reg_channel,
        reg_time,
        is_bind_wx,
        prom_channel,
        prom_id,
        prom_key,
        total_num,
        assist_num,
        fission_num,
        is_subscriber,
        is_refund,
        payment.total_pay_amount,
        pay_num,
        first_pay_time,
        payment.last_pay_time,
        first_vip_time,
        last_vip_time,
        total_vip_amount,
        payment.score_recharge_total,
        payment.score_consume_total,
        payment.score_balance,
        score_total_count,
        score_total_amount,
        score_total_num,
        score_500_count,
        score_500_amount,
        score_2000_count,
        score_2000_amount,
        score_5000_count,
        score_5000_amount,
        launch_times,
        total_amount,
        invoice_title,
        access_times,
        first_access_time,
        last_access_time,
        ppt_make_times,
        ppt_download_times,
        video_make_times,
        video_build_times,
        video_total_duration,
        video_avg_duration,
        video_total_size,
        video_download_times,
        video_ppt_count,
        video_ppt_upload_count,
        video_ppt_platform_count,
        video_word_speech_count,
        video_ai_count,
        video_word_generate_count,
        video_template_count,
        video_custom_count,
        video_cartoon_count,
        video_25d_count,
        video_2d_count,
        video_none_count,
        video_custom_25d_count,
        video_custom_2d_count,
        video_custom_cartoon_count,
        video_audio_count,
        exam_make_times,
        exam_chick_times,
        exam_download_times
        from op_rp_user_detail user
        left join rp_user_promotion_info promotion
        on promotion.tenant_code = user.tenant_code
        and promotion.user_id = user.user_id
        left join rp_user_payment_info payment
        on payment.tenant_code = user.tenant_code
        and payment.user_id = user.user_id
        left join rp_user_pay_invoice_info payInvoice
        on payInvoice.tenant_code = user.tenant_code
        and payInvoice.user_id = user.user_id
        left join rp_user_activity_info activity
        on activity.tenant_code = user.tenant_code
        and activity.user_id = user.user_id
        left join rp_user_ppt_video_info pptVideo
        on pptVideo.tenant_code = user.tenant_code
        and pptVideo.user_id = user.user_id
        left join rp_user_exam_info exam
        on exam.tenant_code = user.tenant_code
        and exam.user_id = user.user_id
        where user.tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user.user_id = #{userId,jdbcType=VARCHAR}
        limit 1
    </select>
</mapper>
