<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysPostUserRelMapper">

    <resultMap type="com.tydic.nbchat.admin.mapper.po.SysPostUserRel" id="SysPostUserRelMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="postId" column="post_id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <!-- 基本字段 -->
    <sql id="Base_Column_List">
id, tenant_code, post_id, user_id, create_time</sql>

    <!--查询单个-->
    <select id="queryById" resultMap="SysPostUserRelMap">
        select
          <include refid="Base_Column_List" />
        from sys_post_user_rel
        where id = #{id}
    </select>
    
    
    <select id="selectAll" resultMap="SysPostUserRelMap" parameterType="com.tydic.nbchat.admin.mapper.po.SysPostUserRel">
        select
          <include refid="Base_Column_List" />
        from sys_post_user_rel
         <where>
            <if test="id != null">
                and id = #{id}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="postId != null">
                and post_id = #{postId}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime}
            </if>
        </where>
    </select>


    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.tydic.nbchat.admin.mapper.po.SysPostUserRel">
        insert into sys_post_user_rel
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null">
                id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="postId != null">
                post_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null">
                #{id},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="postId != null">
                #{postId},
            </if>
            <if test="userId != null and userId != ''">
                #{userId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
        </trim>
    </insert>



    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into sys_post_user_rel(tenant_codepost_iduser_idcreate_time)
        values (#{tenantCode}#{postId}#{userId}#{createTime})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update sys_post_user_rel
        <set>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode},
            </if>
            <if test="postId != null">
                post_id = #{postId},
            </if>
            <if test="userId != null and userId != ''">
                user_id = #{userId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete from sys_post_user_rel where id = #{id}
    </delete>

    <select id="selectByUserId" resultMap="SysPostUserRelMap">
        select
          <include refid="Base_Column_List" />
        from sys_post_user_rel where tenant_code = #{tenantCode} and user_id = #{userId}
    </select>

    <delete id="deleteByUserId">
        delete from sys_post_user_rel where tenant_code = #{tenantCode} and user_id = #{userId}
    </delete>

    <select id="selectUserPostList" resultType="com.tydic.nbchat.admin.mapper.po.SysPost">
        select
          a.post_id postId,a.post_name postName,a.post_desc postDesc,b.create_time createTime
        from sys_post a inner join sys_post_user_rel b on a.post_id = b.post_id
        where b.tenant_code = #{tenantCode} and b.user_id = #{userId}  and a.is_valid = '1'
        order by a.order_index,b.id
    </select>
</mapper>

