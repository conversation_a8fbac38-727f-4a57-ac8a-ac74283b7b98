<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysDeptMapper">

    <resultMap type="com.tydic.nbchat.admin.mapper.po.SysDept" id="sysDeptMap">
        <result property="deptId" column="dept_id" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="deptName" column="dept_name" jdbcType="VARCHAR"/>
        <result property="deptDesc" column="dept_desc" jdbcType="VARCHAR"/>
        <result property="tenantCode" column="tenant_code" jdbcType="VARCHAR"/>
        <result property="subsystem" column="subsystem" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="deptDesc" column="dept_desc" jdbcType="VARCHAR"/>
        <result property="isValid" column="is_valid" jdbcType="VARCHAR"/>
        <result property="deptType" column="dept_type" jdbcType="VARCHAR"/>
        <result property="ancestors" column="ancestors" jdbcType="VARCHAR"/>
        <result property="orderIndex" column="order_index" jdbcType="INTEGER"/>
        <result property="leader" column="leader" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="email" column="email" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基本字段 -->
    <sql id="Base_Column_List">
        dept_id
        ,parent_id,level,dept_name,dept_desc,tenant_code,subsystem,create_time,is_valid,
        dept_type,ancestors,order_index,leader,phone,email,status,create_by,update_by,update_time
    </sql>

    <update id="updateAncestors">
        UPDATE sys_dept a
            INNER JOIN (WITH RECURSIVE dept_cte AS (
            -- Base case
            SELECT dept_id,
            parent_id,
            CAST(dept_id AS CHAR (255)) AS ancestors
            FROM sys_dept
            WHERE parent_id = '0'
            AND tenant_code = #{tenantCode}
            UNION ALL
            -- Recursive step
            SELECT d.dept_id,
            d.parent_id,
            CONCAT(c.ancestors, ',', d.dept_id)
            FROM sys_dept d
            INNER JOIN dept_cte c ON d.parent_id = c.dept_id
            WHERE d.tenant_code = #{tenantCode})
            SELECT *
            FROM dept_cte) b
        ON a.dept_id = b.dept_id
            SET a.ancestors = b.ancestors;
    </update>

    <!--查询单个-->
    <select id="queryById" resultMap="sysDeptMap">
        select
        <include refid="Base_Column_List"/>
        from sys_dept
        where dept_id = #{deptId}
    </select>

    <select id="queryDeptFullPath" resultType="java.lang.String">
        SELECT GROUP_CONCAT(t2.dept_name SEPARATOR '-') AS dept_names
        FROM sys_dept t1
                 JOIN sys_dept t2
                      ON FIND_IN_SET(t2.dept_id, t1.ancestors) > 0
        WHERE t1.dept_id = #{deptId};
    </select>

    <select id="selectByCondition"
            resultMap="sysDeptMap"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysDeptCondition">
        select
        <include refid="Base_Column_List"/>
        from sys_dept
        <where>
            <if test="deptId != null and deptId != ''">
                and dept_id = #{deptId}
            </if>
            <if test="filterId != null and filterId != ''">
                and ancestors like concat('%',#{filterId},'%')
            </if>
            <if test="parentId != null and parentId != ''">
                and parent_id = #{parentId}
            </if>
            <if test="subsystem != null and subsystem != ''">
                and subsystem = #{subsystem}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="keyword != null and keyword != ''">
                and (dept_name like concat('%', #{keyword}, '%') or dept_desc like concat('%', #{keyword}, '%'))
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="deptType != null and deptType != ''">
                and dept_type = #{deptType}
            </if>
            <if test="maxLevel != null and maxLevel != ''">
                and level &lt; #{maxLevel}
            </if>
        </where>
        order by order_index,create_time desc
    </select>

    <select id="selectDeptTree"
            resultMap="sysDeptMap"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysDeptCondition">
        WITH RECURSIVE
        parent_depts AS (
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_dept
        <where>
            <if test="filterId != null and filterId != ''">
                and dept_id = #{filterId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="keyword != null and keyword != ''">
                and (dept_name like concat('%', #{keyword}, '%') or dept_desc like concat('%', #{keyword}, '%'))
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="deptType != null and deptType != ''">
                and dept_type = #{deptType}
            </if>
            <if test="maxLevel != null and maxLevel != ''">
                and level &lt; #{maxLevel}
            </if>
        </where>
        UNION ALL
        SELECT d.*
        FROM sys_dept d
        INNER JOIN parent_depts pd ON d.dept_id = pd.parent_id),
        child_depts AS (
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_dept
        <where>
            <if test="filterId != null and filterId != ''">
                and parent_id = #{filterId}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode}
            </if>
            <if test="keyword != null and keyword != ''">
                and (dept_name like concat('%', #{keyword}, '%') or dept_desc like concat('%', #{keyword}, '%'))
            </if>
            <if test="isValid != null and isValid != ''">
                and is_valid = #{isValid}
            </if>
            <if test="status != null and status != ''">
                and status = #{status}
            </if>
            <if test="deptType != null and deptType != ''">
                and dept_type = #{deptType}
            </if>
            <if test="maxLevel != null and maxLevel != ''">
                and level &lt; #{maxLevel}
            </if>
        </where>
        UNION ALL
        SELECT d.*
        FROM sys_dept d
        INNER JOIN child_depts cd ON d.parent_id = cd.dept_id and d.is_valid='1')
        SELECT
        <include refid="Base_Column_List"/>
        FROM parent_depts
        UNION ALL
        SELECT
        <include refid="Base_Column_List"/>
        FROM child_depts;
    </select>

    <select id="selectAll" resultMap="sysDeptMap" parameterType="com.tydic.nbchat.admin.mapper.po.SysDept">
        select
        <include refid="Base_Column_List"/>
        from sys_dept
        <where>
            <if test="deptId != null and deptId != ''">
                and dept_id = #{deptId}
            </if>
            <if test="parentId != null and parentId != ''">
                and parent_id = #{parentId}
            </if>
            <if test="level != null">
                and level = #{level}
            </if>
            <if test="deptName != null and deptName != ''">
                and dept_name = #{deptName}
            </if>
            <if test="deptDesc != null and deptDesc != ''">
                and dept_desc = #{deptDesc}
            </if>
            <if test="subsystem != null and subsystem != ''">
                and subsystem = #{subsystem}
            </if>
            and is_valid = '1'
        </where>
    </select>


    <insert id="insertSelective"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysDept">
        insert into sys_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null and deptId != ''">
                dept_id,
            </if>
            <if test="parentId != null and parentId != ''">
                parent_id,
            </if>
            <if test="level != null">
                `level`,
            </if>
            <if test="deptName != null and deptName != ''">
                dept_name,
            </if>
            <if test="deptDesc != null and deptDesc != ''">
                dept_desc,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="subsystem != null and subsystem != ''">
                subsystem,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="deptType != null and deptType != ''">
                dept_type,
            </if>
            <if test="ancestors != null and ancestors != ''">
                ancestors,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="leader != null and leader != ''">
                leader,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="email != null and email != ''">
                email,
            </if>
            <if test="status !=null and status !=''">
                `status`,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null and deptId != ''">
                #{deptId},
            </if>
            <if test="parentId != null and parentId != ''">
                #{parentId},
            </if>
            <if test="level != null">
                #{level},
            </if>
            <if test="deptName != null and deptName != ''">
                #{deptName},
            </if>
            <if test="deptDesc != null and deptDesc != ''">
                #{deptDesc},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="subsystem != null and subsystem != ''">
                #{subsystem},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="deptType != null and deptType != ''">
                #{deptType},
            </if>
            <if test="ancestors != null and ancestors != ''">
                #{ancestors},
            </if>
            <if test="orderIndex != null">
                #{orderIndex},
            </if>
            <if test="leader != null and leader != ''">
                #{leader},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="email != null and email != ''">
                #{email},
            </if>
            <if test="status !=null and status !=''">
                #{status},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
        </trim>
    </insert>


    <!--通过主键修改数据-->
    <update id="updateSelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysDept">
        update sys_dept
        <set>
            <if test="parentId != null and parentId != ''">
                parent_id = #{parentId},
            </if>
            <if test="level != null">
                `level` = #{level},
            </if>
            <if test="deptName != null and deptName != ''">
                dept_name = #{deptName},
            </if>
            <if test="deptDesc != null">
                dept_desc = #{deptDesc},
            </if>
            <if test="subsystem != null and subsystem != ''">
                subsystem = #{subsystem},
            </if>
            <if test="deptType != null and deptType != ''">
                dept_type = #{deptType},
            </if>
            <if test="ancestors != null and ancestors != ''">
                ancestors = #{ancestors},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex},
            </if>
            <if test="leader != null and leader != ''">
                leader = #{leader},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="status !=null and status !=''">
                status = #{status},
            </if>
            <if test="updateBy != null and updateBy !=''">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid = #{isValid},
            </if>
        </set>
        where dept_id = #{deptId}
    </update>

    <!--通过主键删除-->
    <delete id="deleteById">
        delete
        from sys_dept
        where dept_id = #{deptId}
    </delete>


    <insert id="insertDeptUserRel" parameterType="com.tydic.nbchat.admin.mapper.po.SysDeptUser">
        insert into sys_dept_user_rel
            (user_id, dept_id, tenant_code, create_time)
        values (#{userId}, #{deptId}, #{tenantCode}, now())
    </insert>

    <select id="selectDeptUserRid" resultType="long">
        select id
        from sys_dept_user_rel
        where user_id = #{userId}
          and tenant_code = #{tenantCode} limit 1
    </select>

    <update id="updateDeptUserRel">
        update sys_dept_user_rel
        set dept_id = #{deptId}
        where id = #{id}
    </update>

    <select id="selectByUserId" resultMap="sysDeptMap">
        select a.dept_id, a.dept_name, a.parent_id, a.create_time, a.tenant_code, a.ancestors, a.dept_desc
        from sys_dept a
                 left join sys_dept_user_rel b on a.dept_id = b.dept_id
            and a.tenant_code = b.tenant_code
        where b.tenant_code = #{tenantCode}
          and b.user_id = #{userId}
    </select>

    <select id="selectDeptChildList" resultMap="sysDeptMap">
        select *
        from sys_dept
        where tenant_code = #{tenantCode}
          and find_in_set(#{deptId}, ancestors)
    </select>

    <select id="queryOrganizeName" resultType="java.lang.String">
        SELECT GROUP_CONCAT(t2.dept_name order by t2.ancestors SEPARATOR '-') AS dept_names
        FROM sys_dept t1
                 JOIN sys_dept t2 ON FIND_IN_SET(t2.dept_id, t1.ancestors) > 0
        WHERE t1.dept_id = #{deptId}
    </select>

    <delete id="deleteDeptUserRel">
        delete
        from sys_dept_user_rel
        where tenant_code = #{tenantCode}
          and user_id = #{userId}
    </delete>

</mapper>

