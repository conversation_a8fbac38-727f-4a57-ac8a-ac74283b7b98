<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpActivateMapper">

    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysLoginLog">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="auth_type" property="authType" jdbcType="CHAR"/>
        <result column="login_time" property="loginTime" jdbcType="TIMESTAMP"/>
        <result column="login_ip" property="loginIp" jdbcType="VARCHAR"/>
        <result column="login_type" property="loginType" jdbcType="CHAR"/>
        <result column="login_msg" property="loginMsg" jdbcType="VARCHAR"/>
        <result column="login_client" property="loginClient" jdbcType="VARCHAR"/>
        <result column="switch_tenant" property="switchTenant" jdbcType="VARCHAR"/>
        <result column="vip_type" property="vipType" jdbcType="CHAR"/>
        <result column="vip_status" property="vipStatus" jdbcType="CHAR"/>
        <result column="reg_time" property="regTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, tenant_code, user_id, user_name, auth_type, login_time, login_ip, login_type,
    login_msg, login_client, switch_tenant, vip_type, vip_status, reg_time
    </sql>

    <select id="selectBaseData" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from sys_login_log
        where tenant_code = '00000000' and login_time between #{beginDate} and #{endDate} and reg_time is not null
    </select>

    <!-- 基于sys_login_log，auth_type= 扫码公众号登录 ， 手机号，登录时间在统计时间内，用户求和 -->
    <select id="activateUserCount" resultType="java.lang.Integer">
        select
            count(distinct user_id)
        from sys_login_log
        where tenant_code = '00000000' and auth_type in (1, 4) and login_time between #{beginDate} and #{endDate}
    </select>

    <!-- 基于sys_login_log，auth_type= 扫码公众号登录 ，手机号，vip_status=0,2 , 登录时间在统计时间内，用户求和 -->
    <select id="activateFreeUserCount" resultType="java.lang.Integer">
        select
            count(distinct user_id)
        from sys_login_log
        where tenant_code = '00000000' and auth_type in (1, 4) and login_time between #{beginDate} and #{endDate} and vip_status != 1
    </select>

    <!-- 基于sys_login_log，auth_type= 扫码公众号登录 ，手机号，注册时间到注册时间+7，次数>=2 ，用户求和 -->
    <select id="activateNewUserCount" resultType="java.lang.Integer">

    </select>

    <!-- 基于sys_login_log，auth_type= 扫码公众号登录 ，手机号，统计时间，付费状态，用户求和 -->
    <select id="activateVipUserCount" resultType="java.lang.Integer">
        select
            count(distinct user_id)
        from sys_login_log
        where tenant_code = '00000000' and auth_type in (1, 4) and login_time between #{beginDate} and #{endDate} and vip_status = 1
    </select>


</mapper>