<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.admin.mapper.NbchatSysUserTenantMapper">

    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.NbchatSysUserTenant">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_reality_name" property="userRealityName" jdbcType="VARCHAR"/>
        <result column="join_type" property="joinType" jdbcType="INTEGER"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="id_card" property="idCard" jdbcType="VARCHAR"/>
        <result column="birthday" property="birthday" jdbcType="TIMESTAMP"/>
        <result column="gender" property="gender" jdbcType="VARCHAR"/>
        <result column="user_status" property="userStatus" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <resultMap id="SysDeptUserResultMap" type="com.tydic.nbchat.admin.mapper.po.SysDeptUser">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="user_reality_name" property="userRealityName" jdbcType="VARCHAR"/>
        <result column="join_type" property="joinType" jdbcType="INTEGER"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="name" property="name" jdbcType="VARCHAR"/>
        <result column="user_name" property="userName" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="score" property="balanceScore" jdbcType="INTEGER"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="id_card" property="idCard" jdbcType="VARCHAR"/>
        <result column="birthday" property="birthday" jdbcType="TIMESTAMP"/>
        <result column="gender" property="gender" jdbcType="VARCHAR"/>
        <result column="user_status" property="userStatus" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, user_reality_name, join_type, tenant_code, create_time,
        avatar, id_card, birthday, gender, update_time, user_status
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from nbchat_sys_user_tenant
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_sys_user_tenant
        <where>
            user_status = '1'
            <trim prefix="and (" suffix=")" prefixOverrides="or">
                <if test="id != null and id != '' ">
                    OR id = #{id,jdbcType=INTEGER}
                </if>
                <if test="userId != null and userId != '' ">
                    OR user_id = #{userId,jdbcType=VARCHAR}
                </if>
                <if test="joinType != null and joinType != '' ">
                    OR join_type = #{joinType,jdbcType=INTEGER}
                </if>
                <if test="userRealityName != null and userRealityName != '' ">
                    OR user_reality_name = #{userRealityName,jdbcType=VARCHAR}
                </if>
            </trim>
            <if test="tenantCode != null and tenantCode != '' ">
                AND tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="(userId == null or userId == '') and (tenantCode == null or tenantCode == '') and (id == null or id == '') and
                (joinType == null or joinType == '') and (userRealityName == null or userRealityName == '')">
                AND tenant_code = '0'
            </if>
        </where>
        ORDER BY ASCII(tenant_code), create_time DESC
    </select>

    <select id="selectByUserIdAndTenantCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_sys_user_tenant
        where user_id = #{userId,jdbcType=VARCHAR}
        and tenant_code = #{tenantCode,jdbcType=VARCHAR}
    </select>

    <select id="selectDeptUserByCondition" resultMap="SysDeptUserResultMap">
        SELECT
            sys.id,
            sys.user_id,
            sys.user_reality_name,
            sys.join_type,
            sys.tenant_code,
            sys.create_time,
            u.user_name,
            u.name,
            u.phone,
            balance.score,
            sys.avatar,
            sys.id_card,
            sys.birthday,
            sys.gender,
            sys.update_time,
            sys.user_status
        FROM nbchat_user u
        JOIN nbchat_sys_user_tenant sys
        JOIN nbchat_user_balance balance
        ON u.user_id = sys.user_id
        and sys.user_id = balance.user_id
        and sys.tenant_code = balance.tenant_code
        <where>
            AND sys.tenant_code = #{tenantCode,jdbcType=VARCHAR}
            <if test="keyWords != null and keyWords != ''">
                AND (sys.user_reality_name like CONCAT('%', #{keyWords, jdbcType=VARCHAR}, '%')
                OR sys.id like CONCAT('%', #{keyWords, jdbcType=INTEGER}, '%')
                OR u.phone like CONCAT('%', #{keyWords, jdbcType=VARCHAR}, '%'))
            </if>
            <if test="(keyWords == null or keyWords == '') and (tenantCode == null or tenantCode == '') ">
                AND sys.tenant_code = '0'
            </if>
            <if test="deptId != null and deptId != ''">
                <if test='deptScope != "1"'>
                    <!-- 查询当前部门的数据 -->
                    AND sys.user_id in (select user_id from sys_dept_user_rel where dept_id = #{deptId})
                </if>
                <if test='deptScope == "1" and deptId != tenantCode'>
                    <!-- 查询当前部门及下级部门的数据 -->
                    AND sys.user_id in (
                    select user_id from sys_dept_user_rel where dept_id in (
                    select dept_id from sys_dept where tenant_code = #{tenantCode} and find_in_set(#{deptId},ancestors)
                    )
                    )
                </if>
            </if>
        </where>
        ORDER BY sys.create_time
        <if test="sort==1">ASC</if>
        <if test="sort!=1">DESC</if>,sys.id
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from nbchat_sys_user_tenant
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatSysUserTenant">
        insert into nbchat_sys_user_tenant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="userRealityName != null">
                user_reality_name,
            </if>
            <if test="joinType != null">
                join_type,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="avatar != null and avatar != ''">
                avatar,
            </if>
            <if test="idCard != null and idCard != ''">
                id_card,
            </if>
            <if test="birthday != null">
                birthday,
            </if>
            <if test="gender != null and gender != ''">
                gender,
            </if>
            <if test="userStatus != null and userStatus != '' ">
                user_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="userRealityName != null">
                #{userRealityName,jdbcType=VARCHAR},
            </if>
            <if test="joinType != null">
                #{joinType},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="avatar != null and avatar != ''">
                #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="idCard != null and idCard != ''">
                #{idCard,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="gender != null and gender != ''">
                #{gender,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null and userStatus != '' ">
                #{userStatus,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatSysUserTenant">
        update nbchat_sys_user_tenant
        <set>
            <if test="userRealityName != null">
                user_reality_name = #{userRealityName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="avatar != null">
                avatar = #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="idCard != null">
                id_card = #{idCard,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="gender != null">
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null and userStatus != '' ">
                user_status = #{userStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="getPhone" resultType="java.lang.String">
        select phone from nbchat_user where user_id = #{userId,jdbcType=VARCHAR}
    </select>

    <select id="checkIfExists" resultType="java.lang.Boolean">
        SELECT
            CASE
                WHEN EXISTS (SELECT 1 FROM sys_user_role WHERE name = #{param1})
                    AND EXISTS (SELECT 1 FROM sys_dept WHERE dept_id = #{param2})
                    AND EXISTS (SELECT 1 FROM sys_post WHERE post_id = #{param3})
                    THEN TRUE
                ELSE FALSE
                END AS exists_flag
    </select>

    <select id="selectNotSyncUserInfo" resultMap="BaseResultMap">
        SELECT ut.*
        FROM nbchat_sys_user_tenant ut
                 LEFT JOIN op_rp_user_detail ud
                           ON ut.tenant_code = ud.tenant_code AND ut.user_id = ud.user_id
                 LEFT JOIN nbchat_user u on  ut.user_id = u.user_id
        WHERE ud.tenant_code IS NULL AND ud.user_id IS NULL and ut.tenant_code != '00000000'  and u.is_deleted ='0';
    </select>

    <select id="selectUserInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from nbchat_sys_user_tenant
        where user_id = #{param1,jdbcType=VARCHAR} and tenant_code = #{param2,jdbcType=VARCHAR}
    </select>

    <update id="updateUserInfo">
        update nbchat_sys_user_tenant
        <set>
            <if test="userRealityName != null">
                user_reality_name = #{userRealityName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="avatar != null and avatar != ''">
                avatar = #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="idCard != null and idCard != ''">
                id_card = #{idCard,jdbcType=VARCHAR},
            </if>
            <if test="birthday != null">
                birthday = #{birthday,jdbcType=TIMESTAMP},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="userStatus != null and userStatus != '' ">
                user_status = #{userStatus,jdbcType=VARCHAR},
            </if>
        </set>
        where user_id = #{userId,jdbcType=VARCHAR} and tenant_code = #{tenantCode,jdbcType=VARCHAR}
    </update>

    <select id="selectUserCount" resultType="int">
        select count(*)  from nbchat_sys_user_tenant where tenant_code = #{tenantCode,jdbcType=VARCHAR}
    </select>
</mapper>