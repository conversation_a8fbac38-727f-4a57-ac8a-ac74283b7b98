<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpPptUsageMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpPptUsage">
        <!--@mbg.generated-->
        <!--@Table rp_ppt_usage-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ppt_id" jdbcType="VARCHAR" property="pptId"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="is_free" jdbcType="CHAR" property="isFree"/>
        <result column="theme_id" jdbcType="VARCHAR" property="themeId"/>
        <result column="ai_scene" jdbcType="VARCHAR" property="aiScene"/>
        <result column="ai_style" jdbcType="VARCHAR" property="aiStyle"/>
        <result column="ai_color" jdbcType="VARCHAR" property="aiColor"/>
        <result column="use_theme_id" jdbcType="VARCHAR" property="useThemeId"/>
        <result column="user_type" jdbcType="CHAR" property="userType"/>
        <result column="is_changed" jdbcType="CHAR" property="isChanged"/>
        <result column="total_page" jdbcType="INTEGER" property="totalPage"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, ppt_id, tenant_code, user_id, is_free, theme_id, ai_scene, ai_style, ai_color,
        use_theme_id, user_type, is_changed, total_page, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rp_ppt_usage
        where id = #{id,jdbcType=BIGINT}
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.RpPptUsage"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_ppt_usage (ppt_id, tenant_code, user_id,
        is_free, theme_id, ai_scene,
        ai_style, ai_color, use_theme_id,
        user_type, is_changed, total_page,
        create_time, update_time)
        values (#{pptId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR},
        #{isFree,jdbcType=CHAR}, #{themeId,jdbcType=VARCHAR}, #{aiScene,jdbcType=VARCHAR},
        #{aiStyle,jdbcType=VARCHAR}, #{aiColor,jdbcType=VARCHAR}, #{useThemeId,jdbcType=VARCHAR},
        #{userType,jdbcType=CHAR}, #{isChanged,jdbcType=CHAR}, #{totalPage,jdbcType=SMALLINT},
        #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.tydic.nbchat.admin.mapper.po.RpPptUsage" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_ppt_usage
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="pptId != null">
                ppt_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="isFree != null">
                is_free,
            </if>
            <if test="themeId != null">
                theme_id,
            </if>
            <if test="aiScene != null">
                ai_scene,
            </if>
            <if test="aiStyle != null">
                ai_style,
            </if>
            <if test="aiColor != null">
                ai_color,
            </if>
            <if test="useThemeId != null">
                use_theme_id,
            </if>
            <if test="userType != null">
                user_type,
            </if>
            <if test="isChanged != null">
                is_changed,
            </if>
            <if test="totalPage != null">
                total_page,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="pptId != null">
                #{pptId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="isFree != null">
                #{isFree,jdbcType=CHAR},
            </if>
            <if test="themeId != null">
                #{themeId,jdbcType=VARCHAR},
            </if>
            <if test="aiScene != null">
                #{aiScene,jdbcType=VARCHAR},
            </if>
            <if test="aiStyle != null">
                #{aiStyle,jdbcType=VARCHAR},
            </if>
            <if test="aiColor != null">
                #{aiColor,jdbcType=VARCHAR},
            </if>
            <if test="useThemeId != null">
                #{useThemeId,jdbcType=VARCHAR},
            </if>
            <if test="userType != null">
                #{userType,jdbcType=CHAR},
            </if>
            <if test="isChanged != null">
                #{isChanged,jdbcType=CHAR},
            </if>
            <if test="totalPage != null">
                #{totalPage,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.RpPptUsage">
        <!--@mbg.generated-->
        update rp_ppt_usage
        <set>
            <if test="pptId != null">
                ppt_id = #{pptId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="isFree != null">
                is_free = #{isFree,jdbcType=CHAR},
            </if>
            <if test="themeId != null">
                theme_id = #{themeId,jdbcType=VARCHAR},
            </if>
            <if test="aiScene != null">
                ai_scene = #{aiScene,jdbcType=VARCHAR},
            </if>
            <if test="aiStyle != null">
                ai_style = #{aiStyle,jdbcType=VARCHAR},
            </if>
            <if test="aiColor != null">
                ai_color = #{aiColor,jdbcType=VARCHAR},
            </if>
            <if test="useThemeId != null">
                use_theme_id = #{useThemeId,jdbcType=VARCHAR},
            </if>
            <if test="userType != null">
                user_type = #{userType,jdbcType=CHAR},
            </if>
            <if test="isChanged != null">
                is_changed = #{isChanged,jdbcType=CHAR},
            </if>
            <if test="totalPage != null">
                total_page = #{totalPage,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.RpPptUsage">
        <!--@mbg.generated-->
        update rp_ppt_usage
        set ppt_id = #{pptId,jdbcType=VARCHAR},
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
        user_id = #{userId,jdbcType=VARCHAR},
        is_free = #{isFree,jdbcType=CHAR},
        theme_id = #{themeId,jdbcType=VARCHAR},
        ai_scene = #{aiScene,jdbcType=VARCHAR},
        ai_style = #{aiStyle,jdbcType=VARCHAR},
        ai_color = #{aiColor,jdbcType=VARCHAR},
        use_theme_id = #{useThemeId,jdbcType=VARCHAR},
        user_type = #{userType,jdbcType=CHAR},
        is_changed = #{isChanged,jdbcType=CHAR},
        total_page = #{totalPage,jdbcType=SMALLINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from rp_ppt_usage
        <where>
            <if test="id != null">
                and id=#{id,jdbcType=BIGINT}
            </if>
            <if test="pptId != null">
                and ppt_id=#{pptId,jdbcType=VARCHAR}
            </if>
            <if test="tenantCode != null">
                and tenant_code=#{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and user_id=#{userId,jdbcType=VARCHAR}
            </if>
            <if test="isFree != null">
                and is_free=#{isFree,jdbcType=CHAR}
            </if>
            <if test="themeId != null">
                and theme_id=#{themeId,jdbcType=VARCHAR}
            </if>
            <if test="aiScene != null">
                and ai_scene=#{aiScene,jdbcType=VARCHAR}
            </if>
            <if test="aiStyle != null">
                and ai_style=#{aiStyle,jdbcType=VARCHAR}
            </if>
            <if test="aiColor != null">
                and ai_color=#{aiColor,jdbcType=VARCHAR}
            </if>
            <if test="useThemeId != null">
                and use_theme_id=#{useThemeId,jdbcType=VARCHAR}
            </if>
            <if test="userType != null">
                and user_type=#{userType,jdbcType=CHAR}
            </if>
            <if test="isChanged != null">
                and is_changed=#{isChanged,jdbcType=CHAR}
            </if>
            <if test="totalPage != null">
                and total_page=#{totalPage,jdbcType=SMALLINT}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update rp_ppt_usage
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ppt_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.pptId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.pptId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_free = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isFree != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.isFree,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="theme_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.themeId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.themeId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ai_scene = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.aiScene != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.aiScene,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ai_style = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.aiStyle != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.aiStyle,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ai_color = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.aiColor != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.aiColor,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="use_theme_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.useThemeId != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.useThemeId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="user_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userType != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.userType,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_changed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isChanged != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.isChanged,jdbcType=CHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="total_page = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.totalPage != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.totalPage,jdbcType=SMALLINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_ppt_usage
        (ppt_id, tenant_code, user_id, is_free, theme_id, ai_scene, ai_style, ai_color, use_theme_id,
        user_type, is_changed, total_page, create_time, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.pptId,jdbcType=VARCHAR}, #{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR},
            #{item.isFree,jdbcType=CHAR}, #{item.themeId,jdbcType=VARCHAR}, #{item.aiScene,jdbcType=VARCHAR},
            #{item.aiStyle,jdbcType=VARCHAR}, #{item.aiColor,jdbcType=VARCHAR}, #{item.useThemeId,jdbcType=VARCHAR},
            #{item.userType,jdbcType=CHAR}, #{item.isChanged,jdbcType=CHAR}, #{item.totalPage,jdbcType=SMALLINT},
            #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <delete id="deleteByPrimaryKeyIn">
        <!--@mbg.generated-->
        delete from rp_ppt_usage where id in
        <foreach close=")" collection="list" item="id" open="(" separator=", ">
            #{id,jdbcType=BIGINT}
        </foreach>
    </delete>
</mapper>
