<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.CommonMapper">

    <select id="queryBeautPPTCount" resultType="java.lang.Integer">
        select count(1) from ppt_creation_record where user_id = #{userId} and tenant_code = #{tenantCode} and ppt_type= '1'
    </select>

    <select id="queryStanderPrice" resultType="String">
        select json_object(
                       '2d', max(case when sku_id = '414884854704734211' then sale_price end),
                       'audio', max(case when sku_id = '414884854704734212' then sale_price end),
                       '2.5d_mtk', max(case when sku_id = '414884854704734214' then sale_price end)
               ) as result
        from pay_goods_sku
        where sku_id in ('414884854704734211', '414884854704734212', '414884854704734214');
    </select>

</mapper>

