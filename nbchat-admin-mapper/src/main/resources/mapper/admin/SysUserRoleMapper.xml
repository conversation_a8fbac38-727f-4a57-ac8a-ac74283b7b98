<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.admin.mapper.SysUserRoleMapper" >
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysUserRole" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="subsystem" property="subsystem" jdbcType="VARCHAR" />
    <result column="role" property="role" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="api_permission" property="apiPermission" jdbcType="VARCHAR" />
    <result column="menu_permission" property="menuPermission" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, subsystem, role, name, api_permission, menu_permission, create_time
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from sys_user_role
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from sys_user_role
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserRole" >
    insert into sys_user_role
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="subsystem != null" >
        subsystem,
      </if>
      <if test="role != null" >
        role,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="apiPermission != null" >
        api_permission,
      </if>
      <if test="menuPermission != null" >
        menu_permission,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="subsystem != null" >
        #{subsystem,jdbcType=VARCHAR},
      </if>
      <if test="role != null" >
        #{role,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="apiPermission != null" >
        #{apiPermission,jdbcType=VARCHAR},
      </if>
      <if test="menuPermission != null" >
        #{menuPermission,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserRole" >
    update sys_user_role
    <set >
      <if test="subsystem != null" >
        subsystem = #{subsystem,jdbcType=VARCHAR},
      </if>
      <if test="role != null" >
        role = #{role,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="apiPermission != null" >
        api_permission = #{apiPermission,jdbcType=VARCHAR},
      </if>
      <if test="menuPermission != null" >
        menu_permission = #{menuPermission,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>


  <select id="selectBySubsystem" resultMap="BaseResultMap">
    select
      id,subsystem,role,name,api_permission,menu_permission,create_time
    from sys_user_role
    where subsystem = #{subsystem,jdbcType=VARCHAR}
  </select>

  <select id="selectUserRoles" resultType="com.tydic.nbchat.admin.mapper.po.SysUserRoleRel" >
    select
      a.subsystem,a.user_id as userId,a.role,a.create_time as createTime,b.name as name
    from sys_user_role_rel a left join sys_user_role b
        on a.role = b.role where (a.subsystem = #{subsystem,jdbcType=VARCHAR} or a.subsystem = '')
                             and a.user_id = #{userId,jdbcType=VARCHAR}
        order by a.id
  </select>

  <delete id="deleteUserRoles">
    delete from sys_user_role_rel
    where subsystem = #{subsystem,jdbcType=VARCHAR}
    and user_id = #{userId,jdbcType=VARCHAR}
    <if test="role != null">
      and `role` = #{role,jdbcType=VARCHAR}
    </if>
  </delete>


  <insert id="addUserRoles" parameterType="list">
    insert into sys_user_role_rel
    (subsystem,user_id,role,create_time)
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (#{item.subsystem,jdbcType=VARCHAR},
       #{item.userId,jdbcType=VARCHAR},
       #{item.role,jdbcType=VARCHAR},
       NOW()
      )
    </foreach>
  </insert>

  <select id="selectAllUserSetting" resultType="com.tydic.nbchat.admin.mapper.po.UserSettingPO">
    SELECT a.user_id userId,
           a.tenant_code tenantCode,
           a.current_setting currentSetting FROM nbchat_user_settings a
        INNER JOIN nbchat_user b on a.user_id = b.user_id WHERE b.is_deleted = 0 and b.phone is not null;
  </select>

  <select id="selectUserRole" resultType="com.tydic.nbchat.admin.mapper.po.SysUserRoleRel">
    select
    a.subsystem,a.user_id as userId,a.role,a.create_time as createTime,b.name as name
    from sys_user_role_rel a left join sys_user_role b
    on a.role = b.role where (a.subsystem = #{subsystem,jdbcType=VARCHAR} or a.subsystem = '')
    and a.user_id = #{userId,jdbcType=VARCHAR} and a.role = #{role,jdbcType=VARCHAR} limit 1
  </select>

</mapper>