<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpPptLayoutUsageMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpPptLayoutUsage">
    <!--@mbg.generated-->
    <!--@Table rp_ppt_layout_usage-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="ppt_id" jdbcType="VARCHAR" property="pptId" />
    <result column="layout_id" jdbcType="VARCHAR" property="layoutId" />
    <result column="page_num" jdbcType="SMALLINT" property="pageNum" />
    <result column="use_layout_id" jdbcType="VARCHAR" property="useLayoutId" />
    <result column="is_changed" jdbcType="CHAR" property="isChanged" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="layId" jdbcType="VARCHAR" property="layid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, tenant_code, user_id, ppt_id, layout_id, page_num, use_layout_id, is_changed, 
    create_time, update_time, layId
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from rp_ppt_layout_usage
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from rp_ppt_layout_usage
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.RpPptLayoutUsage" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into rp_ppt_layout_usage (tenant_code, user_id, ppt_id, 
      layout_id, page_num, use_layout_id, 
      is_changed, create_time, update_time, 
      layId)
    values (#{tenantCode,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{pptId,jdbcType=VARCHAR}, 
      #{layoutId,jdbcType=VARCHAR}, #{pageNum,jdbcType=SMALLINT}, #{useLayoutId,jdbcType=VARCHAR}, 
      #{isChanged,jdbcType=CHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{layid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.RpPptLayoutUsage" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into rp_ppt_layout_usage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantCode != null">
        tenant_code,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="pptId != null">
        ppt_id,
      </if>
      <if test="layoutId != null">
        layout_id,
      </if>
      <if test="pageNum != null">
        page_num,
      </if>
      <if test="useLayoutId != null">
        use_layout_id,
      </if>
      <if test="isChanged != null">
        is_changed,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="layid != null">
        layId,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="pptId != null">
        #{pptId,jdbcType=VARCHAR},
      </if>
      <if test="layoutId != null">
        #{layoutId,jdbcType=VARCHAR},
      </if>
      <if test="pageNum != null">
        #{pageNum,jdbcType=SMALLINT},
      </if>
      <if test="useLayoutId != null">
        #{useLayoutId,jdbcType=VARCHAR},
      </if>
      <if test="isChanged != null">
        #{isChanged,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="layid != null">
        #{layid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.RpPptLayoutUsage">
    <!--@mbg.generated-->
    update rp_ppt_layout_usage
    <set>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="pptId != null">
        ppt_id = #{pptId,jdbcType=VARCHAR},
      </if>
      <if test="layoutId != null">
        layout_id = #{layoutId,jdbcType=VARCHAR},
      </if>
      <if test="pageNum != null">
        page_num = #{pageNum,jdbcType=SMALLINT},
      </if>
      <if test="useLayoutId != null">
        use_layout_id = #{useLayoutId,jdbcType=VARCHAR},
      </if>
      <if test="isChanged != null">
        is_changed = #{isChanged,jdbcType=CHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="layid != null">
        layId = #{layid,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.RpPptLayoutUsage">
    <!--@mbg.generated-->
    update rp_ppt_layout_usage
    set tenant_code = #{tenantCode,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      ppt_id = #{pptId,jdbcType=VARCHAR},
      layout_id = #{layoutId,jdbcType=VARCHAR},
      page_num = #{pageNum,jdbcType=SMALLINT},
      use_layout_id = #{useLayoutId,jdbcType=VARCHAR},
      is_changed = #{isChanged,jdbcType=CHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      layId = #{layid,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from rp_ppt_layout_usage
        <where>
            <if test="id != null">
                and id=#{id,jdbcType=BIGINT}
            </if>
            <if test="tenantCode != null">
                and tenant_code=#{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and user_id=#{userId,jdbcType=VARCHAR}
            </if>
            <if test="pptId != null">
                and ppt_id=#{pptId,jdbcType=VARCHAR}
            </if>
            <if test="layoutId != null">
                and layout_id=#{layoutId,jdbcType=VARCHAR}
            </if>
            <if test="pageNum != null">
                and page_num=#{pageNum,jdbcType=SMALLINT}
            </if>
            <if test="useLayoutId != null">
                and use_layout_id=#{useLayoutId,jdbcType=VARCHAR}
            </if>
            <if test="isChanged != null">
                and is_changed=#{isChanged,jdbcType=CHAR}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="layid != null">
                and layId=#{layid,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update rp_ppt_layout_usage
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="tenant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tenantCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ppt_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pptId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.pptId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="layout_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.layoutId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.layoutId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="page_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pageNum != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.pageNum,jdbcType=SMALLINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="use_layout_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.useLayoutId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.useLayoutId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_changed = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isChanged != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.isChanged,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="layId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.layid != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.layid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into rp_ppt_layout_usage
    (tenant_code, user_id, ppt_id, layout_id, page_num, use_layout_id, is_changed, create_time, 
      update_time, layId)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.pptId,jdbcType=VARCHAR}, 
        #{item.layoutId,jdbcType=VARCHAR}, #{item.pageNum,jdbcType=SMALLINT}, #{item.useLayoutId,jdbcType=VARCHAR}, 
        #{item.isChanged,jdbcType=CHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, 
        #{item.layid,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from rp_ppt_layout_usage where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>
</mapper>