<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpUserPayInvoiceInfoMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpUserPayInvoiceInfo">
        <!--@mbg.generated-->
        <!--@Table rp_user_pay_invoice_info-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="launch_times" jdbcType="INTEGER" property="launchTimes"/>
        <result column="total_amount" jdbcType="INTEGER" property="totalAmount"/>
        <result column="invoice_title" jdbcType="VARCHAR" property="invoiceTitle"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_code, user_id, launch_times, total_amount, invoice_title
    </sql>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.tydic.nbchat.admin.mapper.po.RpUserPayInvoiceInfo" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_user_pay_invoice_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="launchTimes != null">
                launch_times,
            </if>
            <if test="totalAmount != null">
                total_amount,
            </if>
            <if test="invoiceTitle != null and invoiceTitle != ''">
                invoice_title,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="userId != null and userId != ''">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="launchTimes != null">
                #{launchTimes,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=INTEGER},
            </if>
            <if test="invoiceTitle != null and invoiceTitle != ''">
                #{invoiceTitle,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateBySelective" parameterType="com.tydic.nbchat.admin.mapper.po.RpUserPayInvoiceInfo">
        <!--@mbg.generated-->
        update rp_user_pay_invoice_info
        <set>
            <if test="launchTimes != null">
                launch_times = launch_times + #{launchTimes,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                total_amount = total_amount + #{totalAmount,jdbcType=INTEGER},
            </if>
            <if test="invoiceTitle != null and invoiceTitle != ''">
                invoice_title = #{invoiceTitle,jdbcType=VARCHAR},
            </if>
        </set>
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </update>
    <select id="findOneByTenantCodeAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rp_user_pay_invoice_info
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
        limit 1
    </select>
    <insert id="insertForAll">
        insert into rp_user_pay_invoice_info (tenant_code, user_id, launch_times, total_amount, invoice_title)
        select tenant_code,
               user_id,
               count(0),
               sum(if(invoice_status in ('1', '2'), invoice_amount, 0)),
               case
                   when row_number() over (partition by tenant_code, user_id order by invoice_time) = 1
                       then invoice_title end
        from pay_invoice_record
        group by tenant_code, user_id
    </insert>
    <delete id="deleteAll">
        delete
        from rp_user_pay_invoice_info
    </delete>
</mapper>
