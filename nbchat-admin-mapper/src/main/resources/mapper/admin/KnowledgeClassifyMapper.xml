<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.KnowledgeClassifyMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.KnowledgeClassifyPo">
        <id column="type_id" jdbcType="BIGINT" property="typeId"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="type_group" jdbcType="SMALLINT" property="typeGroup"/>
        <result column="cust_code" jdbcType="VARCHAR" property="custCode"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="type_desc" jdbcType="VARCHAR" property="typeDesc"/>
        <result column="sort_id" jdbcType="INTEGER" property="sortId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="classes" jdbcType="VARCHAR" property="classes"/>
    </resultMap>
    <sql id="Base_Column_List">
        type_id,
        tenant_code,
        type_group,
        cust_code,
        type_name,
        type_desc,
        sort_id,
        create_time,
        create_user_id,
        create_user_name,
        update_time,
        update_user_id,
        update_user_name,
        parent_id,
        classes
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_knowledge_classify
        where type_id = #{typeId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nbchat_knowledge_classify
        where type_id = #{typeId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByParentId">
        delete
        from nbchat_knowledge_classify
        where parent_id = #{parentId,jdbcType=BIGINT} and create_user_id = #{createUserId}
    </delete>
    <insert id="insert" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeClassifyPo">
        insert into nbchat_knowledge_classify (type_id, tenant_code, type_group,
                                           cust_code, type_name, type_desc,
                                           sort_id, create_time, create_user_id,
                                           create_user_name, update_time, update_user_id,
                                           update_user_name)
        values (#{typeId,jdbcType=BIGINT}, #{tenantCode,jdbcType=VARCHAR}, #{typeGroup,jdbcType=SMALLINT},
                #{custCode,jdbcType=VARCHAR}, #{typeName,jdbcType=VARCHAR}, #{typeDesc,jdbcType=VARCHAR},
                #{sortId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=BIGINT},
                #{createUserName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=BIGINT},
                #{updateUserName,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeClassifyPo">
        insert into nbchat_knowledge_classify
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeId != null">
                type_id,
            </if>
            <if test="tenantCode != null">
                tenant_code,
            </if>
            <if test="typeGroup != null">
                type_group,
            </if>
            <if test="custCode != null">
                cust_code,
            </if>
            <if test="typeName != null">
                type_name,
            </if>
            <if test="typeDesc != null">
                type_desc,
            </if>
            <if test="sortId != null">
                sort_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createUserName != null">
                create_user_name,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateUserId != null">
                update_user_id,
            </if>
            <if test="updateUserName != null">
                update_user_name,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="classes != null">
                classes,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeId != null">
                #{typeId,jdbcType=BIGINT},
            </if>
            <if test="tenantCode != null">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="typeGroup != null">
                #{typeGroup,jdbcType=SMALLINT},
            </if>
            <if test="custCode != null">
                #{custCode,jdbcType=VARCHAR},
            </if>
            <if test="typeName != null">
                #{typeName,jdbcType=VARCHAR},
            </if>
            <if test="typeDesc != null">
                #{typeDesc,jdbcType=VARCHAR},
            </if>
            <if test="sortId != null">
                #{sortId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createUserId != null">
                #{createUserId,jdbcType=VARCHAR},
            </if>
            <if test="createUserName != null">
                #{createUserName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                #{updateUserId,jdbcType=VARCHAR},
            </if>
            <if test="updateUserName != null">
                #{updateUserName,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="classes != null">
                #{classes,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeClassifyPo">
        update nbchat_knowledge_classify
        <set>
            <if test="typeName != null">
                type_name = #{typeName,jdbcType=VARCHAR},
            </if>
            <if test="typeDesc != null">
                type_desc = #{typeDesc,jdbcType=VARCHAR},
            </if>
            <if test="sortId != null">
                sort_id = #{sortId,jdbcType=INTEGER},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateUserId != null">
                update_user_id = #{updateUserId,jdbcType=BIGINT},
            </if>
            <if test="updateUserName != null">
                update_user_name = #{updateUserName,jdbcType=VARCHAR},
            </if>
        </set>
        where type_id = #{typeId,jdbcType=BIGINT} and create_user_id =#{updateUserId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeClassifyPo">
        update nbchat_knowledge_classify
        set tenant_code      = #{tenantCode,jdbcType=VARCHAR},
            type_group       = #{typeGroup,jdbcType=SMALLINT},
            cust_code        = #{custCode,jdbcType=VARCHAR},
            type_name        = #{typeName,jdbcType=VARCHAR},
            type_desc        = #{typeDesc,jdbcType=VARCHAR},
            sort_id          = #{sortId,jdbcType=INTEGER},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            create_user_id   = #{createUserId,jdbcType=BIGINT},
            create_user_name = #{createUserName,jdbcType=VARCHAR},
            update_time      = #{updateTime,jdbcType=TIMESTAMP},
            update_user_id   = #{updateUserId,jdbcType=BIGINT},
            update_user_name = #{updateUserName,jdbcType=VARCHAR}
        where type_id = #{typeId,jdbcType=BIGINT}
    </update>
    <select id="selectByPo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_knowledge_classify
        where
        create_user_id = #{createUserId,jdbcType=BIGINT}
        <if test="classes!=null">
            and classes =#{classes,jdbcType=VARCHAR}
        </if>
        <if test="typeId!=null">
            and type_id = #{typeId,jdbcType=BIGINT}
        </if>
        <if test="typeGroup != null">
            and type_group = #{typeGroup,jdbcType=SMALLINT}
        </if>
        <if test="custCode != null">
            and cust_code = #{custCode,jdbcType=VARCHAR}
        </if>
        <if test="typeName != null">
            and type_name like CONCAT('%',#{typeName,jdbcType=VARCHAR},'%')
        </if>
        <if test="typeDesc != null">
            and type_desc = #{typeDesc,jdbcType=VARCHAR}
        </if>
        <if test="sortId != null">
            and sort_id = #{sortId,jdbcType=INTEGER}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="tenantCode != null">
            and tenant_code = #{tenantCode,jdbcType=VARCHAR}
        </if>
        <if test="createUserName != null">
            and create_user_name = #{createUserName,jdbcType=VARCHAR}
        </if>
        <if test="updateTime != null">
            and update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateUserId != null">
            and update_user_id = #{updateUserId,jdbcType=BIGINT}
        </if>
        <if test="updateUserName != null">
            and update_user_name = #{updateUserName,jdbcType=VARCHAR}
        </if>
        <if test="parentId != null">
            and parent_id = #{parentId,jdbcType=BIGINT}
        </if>
        order by sort_id asc , create_time desc
    </select>

    <select id="selectByCondition" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_knowledge_classify
        <where>
            <if test="classes!=null">
                and classes = #{classes,jdbcType=VARCHAR}
            </if>
            <if test="typeId!=null">
                and type_id = #{typeId,jdbcType=BIGINT}
            </if>
            <if test="typeGroup != null">
                and type_group = #{typeGroup,jdbcType=SMALLINT}
            </if>
            <if test="custCode != null">
                and cust_code = #{custCode,jdbcType=VARCHAR}
            </if>
            <if test="typeName != null">
                and type_name = #{typeName,jdbcType=VARCHAR}
            </if>
            <if test="typeDesc != null">
                and type_desc = #{typeDesc,jdbcType=VARCHAR}
            </if>
            <if test="sortId != null">
                and sort_id = #{sortId,jdbcType=INTEGER}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="tenantCode != null">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="createUserName != null">
                and create_user_name = #{createUserName,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateUserId != null">
                and update_user_id = #{updateUserId,jdbcType=BIGINT}
            </if>
            <if test="updateUserName != null">
                and update_user_name = #{updateUserName,jdbcType=VARCHAR}
            </if>
            <if test="parentId != null">
                and parent_id = #{parentId,jdbcType=BIGINT}
            </if>
            and create_user_id = #{createUserId,jdbcType=BIGINT}
        </where>
        order by sort_id asc , create_time desc
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        select COUNT(*)
        from nbchat_knowledge_classify
        where
        create_user_id = #{createUserId,jdbcType=BIGINT}
        <if test="classes!=null">
            and classes = #{classes,jdbcType=VARCHAR}
        </if>
        <if test="typeName != null and typeName != ''">
            and type_name = #{typeName,jdbcType=VARCHAR}
        </if>
        <if test="typeGroup!=null">
            and type_group = #{typeGroup,jdbcType=SMALLINT}
        </if>
        <if test="custCode != null">
            and cust_code = #{custCode,jdbcType=VARCHAR}
        </if>
        <if test="parentId != null">
            and parent_id = #{parentId}
        </if>
        <if test="typeId != null ">
            and type_id = #{typeId}
        </if>
    </select>
    <select id="selectTypePo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_knowledge_classify
        where
        create_user_id = #{createUserId,jdbcType=BIGINT}
        <if test="classes!=null">
            and classes = #{classes,jdbcType=VARCHAR}
        </if>
        <if test="typeName!=null">
            and type_name = #{typeName,jdbcType=VARCHAR}
        </if>
        <if test="typeGroup!=null">
            and type_group = #{typeGroup,jdbcType=SMALLINT}
        </if>
        <if test="custCode != null">
            and create_user_id = #{custCode,jdbcType=VARCHAR}
        </if>
        <if test="parentId != null">
            and parent_id = #{parentId}
        </if>
        order by create_time desc limit 1
    </select>
    <select id="selectId" resultType="java.lang.Long">
        select
        type_id
        from nbchat_knowledge_classify
        where
        classes =#{classes,jdbcType=VARCJAR}
        <if test="typeName!=null">
            and type_name = #{typeName,jdbcType=VARCHAR}
        </if>
        <if test="typeGroup!=null">
            and type_group = #{typeGroup,jdbcType=SMALLINT}
        </if>
        order by create_time desc limit 1
    </select>

    <select id="queryCommSentenceTypeByIds" resultMap="BaseResultMap">
        select type_id,type_name from nbchat_knowledge_classify
        <where>
            <if test="tenantCode != null and tenantCode  != ''">
                tenant_code = #{tenantCode}
            </if>
            <if test="ids != null and ids.size() > 0">
                and type_id in
                <foreach collection="ids" separator="," index="index" item="typeId" open="(" close=")">
                    #{typeId}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectDefaultType" resultType="java.lang.String" resultMap="BaseResultMap">
        select * from nbchat_knowledge_classify where tenant_code = #{inner} and classes = '2';
    </select>

</mapper>
