<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.KnowledgeMapper">
    <resultMap id="CommSentenceRowMap" type="com.tydic.nbchat.admin.mapper.po.CommSentenceRowPO">
        <result column="type_id" jdbcType="VARCHAR" property="typeId"/>
        <result column="type_name" jdbcType="VARCHAR" property="typeName"/>
        <result column="sentence_id" jdbcType="VARCHAR" property="sentenceId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="sort_id" jdbcType="INTEGER" property="sortId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="create_user_id" jdbcType="VARCHAR" property="createUserId"/>
        <result column="create_user_name" jdbcType="VARCHAR" property="createUserName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="type_group" jdbcType="VARCHAR" property="typeGroup"/>
        <result column="content_title" jdbcType="VARCHAR" property="contentTitle"/>
        <result column="is_valid" jdbcType="VARCHAR" property="isValid"/>
        <result column="classes" jdbcType="VARCHAR" property="classes"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
    </resultMap>
    <insert id="insert" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo">
        insert into nbchat_knowledge_content (sentence_id, type_id,
        content, create_time, create_user_id, create_user_name, update_time,
        update_user_id, update_user_name,content_type,content_title,classes)
        select #{sentenceId,jdbcType=VARCHAR}, #{typeId,jdbcType=BIGINT},
        #{content,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{createUserId,jdbcType=VARCHAR}, #{createUserName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
        #{updateUserId,jdbcType=VARCHAR}, #{updateUserName,jdbcType=VARCHAR}, #{contentType,jdbcType=VARCHAR},
        #{contentTitle,jdbcType=VARCHAR},#{classes,jdbcType=VARCHAR}
    </insert>


    <insert id="insertSelective" keyProperty="sentenceId" useGeneratedKeys="true" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo">
        insert into nbchat_knowledge_content
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="sentenceId != null and sentenceId != ''">
                sentence_id,
            </if>
            <if test="contentTitle != null and contentTitle != ''">
                content_title,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="typeId != null">
                type_id,
            </if>
            <if test="content != null and content != ''">
                content,
            </if>
            <if test="sortId != null">
                sort_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="createUserName != null and createUserName != ''">
                create_user_name,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateUserId != null">
                update_user_id,
            </if>
            <if test="updateUserName != null and updateUserName != ''">
                update_user_name,
            </if>
            <if test="hitCount != null">
                hit_count,
            </if>
            <if test="contentType != null and contentType != ''">
                content_type,
            </if>
            <if test="classes != null and classes != ''">
                classes,
            </if>
            <if test="isValid != null and isValid != ''">
                is_valid,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="sentenceId != null and sentenceId != ''">
                #{sentenceId},
            </if>
            <if test="contentTitle != null and contentTitle != ''">
                #{contentTitle},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode},
            </if>
            <if test="typeId != null">
                #{typeId},
            </if>
            <if test="content != null and content != ''">
                #{content},
            </if>
            <if test="sortId != null">
                #{sortId},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="createUserId != null">
                #{createUserId},
            </if>
            <if test="createUserName != null and createUserName != ''">
                #{createUserName},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="updateUserId != null">
                #{updateUserId},
            </if>
            <if test="updateUserName != null and updateUserName != ''">
                #{updateUserName},
            </if>
            <if test="hitCount != null">
                #{hitCount},
            </if>
            <if test="contentType != null and contentType != ''">
                #{contentType},
            </if>
            <if test="classes != null and classes != ''">
                #{classes},
            </if>
            <if test="isValid != null and isValid != ''">
                #{isValid},
            </if>
        </trim>
    </insert>

    <select id="getExportList" parameterType="com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo"
            resultType="com.tydic.nbchat.admin.mapper.po.CommSentExcelBo">
        select
        t2.type_name as typeName,
        t1.content as content,
        t1.content_title as contentTitle,
        t1.create_time as createTime,
        t1.update_time as updateTime,
        t2.parent_id  as parentTypeId
        from nbchat_knowledge_content t1
        left join nbchat_knowledge_classify t2
        on t1.type_id = t2.type_id
        <where>
                and t1.create_user_id =#{createUserId,jdbcType=BIGINT}
            <if test="classes!=null">
                and t1.classes =#{classes,jdbcType=VARCHAR}
            </if>
            <if test="typeId != null and typeId != ''">
                and t1.type_id = #{typeId}
            </if>
            <if test="content != null and content != ''">
                and t1.content like concat('%',concat(#{content},'%'))
            </if>
            <if test="sentenceIdList != null and sentenceIdList.size()>0">
                and t1.sentence_id in
                <foreach collection="sentenceIdList" index="index" item="sentenceId" separator="," open="(" close=")">
                    #{sentenceId}
                </foreach>
            </if>
            and t1.is_valid = '1'
        </where>
        order by t1.update_time desc, t1.create_time desc
    </select>
    <!--知识列表分页查询-->
    <select id="getCommSentencePageList" parameterType="java.util.Map" resultMap="CommSentenceRowMap">
        select
        ct.type_id,
        ct.sentence_id,ct.content,ct.sort_id,ct.create_time,ct.create_user_id,ct.create_user_name,ct.classes,ct.tenant_code,
        ct.update_time,ct.update_user_id,ct.update_user_name,ct.content_type,ct.content_title,ct.is_valid,cf.type_name
        from nbchat_knowledge_content ct left join nbchat_knowledge_classify cf on ct.type_id = cf.type_id
        WHERE (ct.tenant_code = #{inner} OR ct.create_user_id = #{userId})
        <if test="typeId != null">
            AND ct.type_id = #{typeId}
        </if>
        <if test="classes != null">
            and ct.classes = #{classes}
        </if>
        <if test="contentTitle != null and contentTitle != ''">
            AND ct.content_title like concat('%',concat(#{contentTitle},'%'))
        </if>
        AND ct.is_valid = '1'
        order by ct.tenant_code asc,ct.update_time desc
    </select>

    <!--知识分类下拉列表查询-->
    <select id="getCommSentenceTypeList" parameterType="java.util.Map" resultType="java.util.Map">
        select
        cast(type_id as char) code,type_name value
        from nbchat_knowledge_classify
        where 1=1
        and tenant_code = #{tenantCode}
        <if test="typeGroup != null">
            and type_group = #{typeGroup}
        </if>
        <if test="custCode != null">
            and cust_code = #{custCode}
        </if>
        order by type_group, sort_id, update_time desc, create_time desc
    </select>

</mapper>
