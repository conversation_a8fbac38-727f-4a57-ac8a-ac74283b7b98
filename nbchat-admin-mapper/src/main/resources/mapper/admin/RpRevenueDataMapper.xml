<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpRevenueDataMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.PayOrderPO">
        <id column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="tenant_code" property="tenantCode" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="channel" property="channel" jdbcType="VARCHAR"/>
        <result column="total_price" property="totalPrice" jdbcType="INTEGER"/>
        <result column="discount_price" property="discountPrice" jdbcType="INTEGER"/>
        <result column="order_status" property="orderStatus" jdbcType="CHAR"/>
        <result column="order_time" property="orderTime" jdbcType="TIMESTAMP"/>
        <result column="pay_time" property="payTime" jdbcType="TIMESTAMP"/>
        <result column="pay_type" property="payType" jdbcType="CHAR"/>
        <result column="trade_no" property="tradeNo" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="is_valid" property="isValid" jdbcType="CHAR"/>
    </resultMap>
    <sql id="Base_Column_List">
        order_no, tenant_code, user_id, channel, total_price, discount_price, order_status,
    order_time, pay_time, pay_type, trade_no, update_time, is_valid
    </sql>
    <select id="queryOrderList" parameterType="com.tydic.nbchat.admin.mapper.po.PayOrderPO" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from pay_order
        <where>
            <if test="orderNo != null">
                and order_no = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="tenantCode != null">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="channel != null">
                and channel = #{channel,jdbcType=VARCHAR}
            </if>
            <if test="totalPrice != null">
                and total_price = #{totalPrice,jdbcType=INTEGER}
            </if>
            <if test="discountPrice != null">
                and discount_price = #{discountPrice,jdbcType=INTEGER}
            </if>
            <if test="orderStatus != null">
                and order_status = #{orderStatus,jdbcType=CHAR}
            </if>
            <if test="orderTime != null">
                and order_time = #{orderTime,jdbcType=TIMESTAMP}
            </if>
            <if test="payTime != null">
                and pay_time between #{beginDate} and #{endDate}
            </if>
            <if test="payType != null">
                and pay_type = #{payType,jdbcType=CHAR}
            </if>
            <if test="tradeNo != null">
                and trade_no = #{tradeNo,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                and update_time = #{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="isValid != null">
                and is_valid = #{isValid,jdbcType=CHAR}
            </if>
        </where>
    </select>
    <select id="querySkuIdList" parameterType="list" resultType="java.lang.String">
        SELECT sku_id FROM pay_order_item
        WHERE order_no IN
        <foreach item="orderNo" index="index" collection="list" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>
    <select id="queryRenewalUser" parameterType="list" resultType="java.lang.Integer">
        SELECT count(user_id) FROM op_rp_user_detail
        WHERE tenant_code = '00000000' and vip_buy_count >= 2 and user_id IN
        <foreach item="userId" index="index" collection="list"
                 open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>
    <select id="queryExpireUser" resultType="java.lang.Integer">
        SELECT count(user_id)
        FROM op_rp_user_detail
        WHERE tenant_code = '00000000'
          and vip_end_time between #{param1} and #{param2}
    </select>
    <select id="querySpuName" resultType="java.lang.String">
        SELECT spu_name
        FROM pay_goods_spu
        WHERE spu_id = (select spu_id from pay_goods_sku where sku_id = #{param1})
    </select>

    <select id="querySpuNameByOrderNo" resultType="java.lang.String">
        SELECT spu_name
        FROM pay_goods_spu
        WHERE spu_id = (select spu_id
                        from pay_goods_sku
                        where sku_id = (select sku_id from pay_order_item where order_no = #{param1})
                          and is_valid = '1')
          and is_valid = '1'
    </select>

    <select id="selectSkuNameByOrderNo" resultType="java.lang.String">
        SELECT sku_name FROM pay_order_item where order_no = #{orderNo} LIMIT 1
    </select>
</mapper>