<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysDataPermissionMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysDataPermission">
    <!--@mbg.generated-->
    <!--@Table sys_data_permission-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="busi_type" jdbcType="VARCHAR" property="busiType" />
    <result column="busi_id" jdbcType="VARCHAR" property="busiId" />
    <result column="permission" jdbcType="VARCHAR" property="permission" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="post_id" jdbcType="VARCHAR" property="postId" />
    <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, busi_type, busi_id, permission, user_id, dept_id, role_id, create_time, create_by, 
    post_id, tenant_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from sys_data_permission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from sys_data_permission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysDataPermission" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_data_permission (busi_type, busi_id, permission, 
      user_id, dept_id, role_id, 
      create_time, create_by, post_id, 
      tenant_code)
    values (#{busiType,jdbcType=VARCHAR}, #{busiId,jdbcType=VARCHAR}, #{permission,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{deptId,jdbcType=VARCHAR}, #{roleId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{postId,jdbcType=VARCHAR}, 
      #{tenantCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysDataPermission" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_data_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="busiType != null">
        busi_type,
      </if>
      <if test="busiId != null">
        busi_id,
      </if>
      <if test="permission != null">
        permission,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="postId != null">
        post_id,
      </if>
      <if test="tenantCode != null">
        tenant_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="busiType != null">
        #{busiType,jdbcType=VARCHAR},
      </if>
      <if test="busiId != null">
        #{busiId,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        #{permission,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="postId != null">
        #{postId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysDataPermission">
    <!--@mbg.generated-->
    update sys_data_permission
    <set>
      <if test="busiType != null">
        busi_type = #{busiType,jdbcType=VARCHAR},
      </if>
      <if test="busiId != null">
        busi_id = #{busiId,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        permission = #{permission,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="postId != null">
        post_id = #{postId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysDataPermission">
    <!--@mbg.generated-->
    update sys_data_permission
    set busi_type = #{busiType,jdbcType=VARCHAR},
      busi_id = #{busiId,jdbcType=VARCHAR},
      permission = #{permission,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      post_id = #{postId,jdbcType=VARCHAR},
      tenant_code = #{tenantCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="findByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from sys_data_permission
        <where>
            <if test="id != null">
                and id=#{id,jdbcType=BIGINT}
            </if>
            <if test="busiType != null">
                and busi_type=#{busiType,jdbcType=VARCHAR}
            </if>
            <if test="busiId != null">
                and busi_id=#{busiId,jdbcType=VARCHAR}
            </if>
            <if test="permission != null">
                and permission=#{permission,jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                and user_id=#{userId,jdbcType=VARCHAR}
            </if>
            <if test="deptId != null">
                and dept_id=#{deptId,jdbcType=VARCHAR}
            </if>
            <if test="roleId != null">
                and role_id=#{roleId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createBy != null">
                and create_by=#{createBy,jdbcType=VARCHAR}
            </if>
            <if test="postId != null">
                and post_id=#{postId,jdbcType=VARCHAR}
            </if>
            <if test="tenantCode != null">
                and tenant_code=#{tenantCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
  <select id="queryById" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from sys_data_permission
        where id=#{id,jdbcType=BIGINT}
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update sys_data_permission
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="busi_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.busiType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.busiType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="busi_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.busiId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.busiId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="permission = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.permission != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.permission,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.userId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deptId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="role_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.roleId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.roleId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_by = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createBy != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createBy,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="post_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.postId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.postId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="tenant_code = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tenantCode != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.tenantCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_data_permission
    (busi_type, busi_id, permission, user_id, dept_id, role_id, create_time, create_by, 
      post_id, tenant_code)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.busiType,jdbcType=VARCHAR}, #{item.busiId,jdbcType=VARCHAR}, #{item.permission,jdbcType=VARCHAR}, 
        #{item.userId,jdbcType=VARCHAR}, #{item.deptId,jdbcType=VARCHAR}, #{item.roleId,jdbcType=VARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.postId,jdbcType=VARCHAR}, 
        #{item.tenantCode,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from sys_data_permission where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=BIGINT}
    </foreach>
  </delete>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    <foreach collection="list" item="item" separator=";">
      update sys_data_permission
      <set>
        <if test="item.busiType != null">
          busi_type = #{item.busiType,jdbcType=VARCHAR},
        </if>
        <if test="item.busiId != null">
          busi_id = #{item.busiId,jdbcType=VARCHAR},
        </if>
        <if test="item.permission != null">
          permission = #{item.permission,jdbcType=VARCHAR},
        </if>
        <if test="item.userId != null">
          user_id = #{item.userId,jdbcType=VARCHAR},
        </if>
        <if test="item.deptId != null">
          dept_id = #{item.deptId,jdbcType=VARCHAR},
        </if>
        <if test="item.roleId != null">
          role_id = #{item.roleId,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null">
          create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.createBy != null">
          create_by = #{item.createBy,jdbcType=VARCHAR},
        </if>
        <if test="item.postId != null">
          post_id = #{item.postId,jdbcType=VARCHAR},
        </if>
        <if test="item.tenantCode != null">
          tenant_code = #{item.tenantCode,jdbcType=VARCHAR},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsertSelectiveUseDefaultForNull" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_data_permission
    (busi_type, busi_id, permission, user_id, dept_id, role_id, create_time, create_by, 
      post_id, tenant_code)
    values
    <foreach collection="list" item="item" separator=",">
      (
      <choose>
        <when test="item.busiType != null">
          #{item.busiType,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.busiId != null">
          #{item.busiId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.permission != null">
          #{item.permission,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.userId != null">
          #{item.userId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.deptId != null">
          #{item.deptId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.roleId != null">
          #{item.roleId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createTime != null">
          #{item.createTime,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createBy != null">
          #{item.createBy,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.postId != null">
          #{item.postId,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.tenantCode != null">
          #{item.tenantCode,jdbcType=VARCHAR}
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysDataPermission" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_data_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      busi_type,
      busi_id,
      permission,
      user_id,
      dept_id,
      role_id,
      create_time,
      create_by,
      post_id,
      tenant_code,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{busiType,jdbcType=VARCHAR},
      #{busiId,jdbcType=VARCHAR},
      #{permission,jdbcType=VARCHAR},
      #{userId,jdbcType=VARCHAR},
      #{deptId,jdbcType=VARCHAR},
      #{roleId,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP},
      #{createBy,jdbcType=VARCHAR},
      #{postId,jdbcType=VARCHAR},
      #{tenantCode,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      busi_type = #{busiType,jdbcType=VARCHAR},
      busi_id = #{busiId,jdbcType=VARCHAR},
      permission = #{permission,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=VARCHAR},
      role_id = #{roleId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      post_id = #{postId,jdbcType=VARCHAR},
      tenant_code = #{tenantCode,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysDataPermission" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_data_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="busiType != null">
        busi_type,
      </if>
      <if test="busiId != null">
        busi_id,
      </if>
      <if test="permission != null">
        permission,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="postId != null">
        post_id,
      </if>
      <if test="tenantCode != null">
        tenant_code,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="busiType != null">
        #{busiType,jdbcType=VARCHAR},
      </if>
      <if test="busiId != null">
        #{busiId,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        #{permission,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="postId != null">
        #{postId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        #{tenantCode,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="busiType != null">
        busi_type = #{busiType,jdbcType=VARCHAR},
      </if>
      <if test="busiId != null">
        busi_id = #{busiId,jdbcType=VARCHAR},
      </if>
      <if test="permission != null">
        permission = #{permission,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="postId != null">
        post_id = #{postId,jdbcType=VARCHAR},
      </if>
      <if test="tenantCode != null">
        tenant_code = #{tenantCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <delete id="deleteByBusiIdAndType">
    delete from sys_data_permission where busi_id=#{busiId,jdbcType=VARCHAR} and busi_type=#{busiType,jdbcType=VARCHAR}
  </delete>

  <select id="selectPermissionByBusiIdAndType" resultType="string">
    select distinct permission from sys_data_permission where busi_id = #{busiId,jdbcType=VARCHAR}
                                                          and busi_type = #{busiType,jdbcType=VARCHAR}
    and (
    user_id = #{object.userId,jdbcType=VARCHAR}
    or dept_id = #{object.deptId,jdbcType=VARCHAR}
    <if test="object.roleIds != null and object.roleIds.size() > 0">
      or role_id in
      <foreach collection="object.roleIds" item="roleId" open="(" close=")" separator=",">#{roleId}</foreach>
    </if>
    <if test="object.postIds != null and object.postIds.size() > 0">
      or post_id in
      <foreach collection="object.postIds" item="postId" open="(" close=")" separator=",">#{postId}</foreach>
    </if>
    )
  </select>

  <select id="selectByBusiIdAndType" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from sys_data_permission
                where busi_id = #{busiId,jdbcType=VARCHAR}  and busi_type = #{busiType,jdbcType=VARCHAR}
  </select>

  <select id="queryDataPermission" resultType="com.tydic.nbchat.admin.mapper.po.PermissionObj">
    select a.user_id objId, b.user_reality_name objName, a.permission permission, 'user' as 'type'
    from sys_data_permission a
           JOIN nbchat_sys_user_tenant b ON a.user_id = b.user_id and a.tenant_code = b.tenant_code
      AND b.tenant_code = #{tenantCode} AND a.busi_type = #{busiType} and a.busi_id = #{busiId} AND a.user_id is not NULL and a.user_id != ''
    UNION
    select b.dept_id objId, b.dept_name objName, a.permission permission, 'dept' as 'type'
    from sys_data_permission a
           JOIN sys_dept b ON a.dept_id = b.dept_id
      AND a.tenant_code = #{tenantCode} AND a.busi_type = #{busiType} and a.busi_id = #{busiId} AND a.dept_id is not NULL and a.dept_id != ''
    UNION
    select b.post_id objId, b.post_name objName,  a.permission permission,'post' as 'type'
    from sys_data_permission a
           JOIN sys_post b ON a.post_id = b.post_id
      AND a.tenant_code = #{tenantCode} AND a.busi_type = #{busiType} and a.busi_id = #{busiId} AND a.post_id is not NULL and a.post_id != ''
    UNION
    select b.role objId, b.name objName, a.permission permission, 'role' as 'type'
    from sys_data_permission a
           JOIN sys_user_role b ON a.role_id = b.role
      AND a.tenant_code = #{tenantCode} AND a.busi_type = #{busiType} and a.busi_id = #{busiId} AND a.role_id is not NULL and a.role_id != ''
  </select>

</mapper>