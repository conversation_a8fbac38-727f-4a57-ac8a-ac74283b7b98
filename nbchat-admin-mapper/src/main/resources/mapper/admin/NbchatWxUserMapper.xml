<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.NbchatWxUserMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.NbchatWxUser">
        <!--@mbg.generated-->
        <!--@Table nbchat_wx_user-->
        <id column="open_id" jdbcType="VARCHAR" property="openId"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
        <result column="app_id" jdbcType="VARCHAR" property="appId"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="avatar" jdbcType="VARCHAR" property="avatar"/>
        <result column="gender" jdbcType="VARCHAR" property="gender"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="created_by" jdbcType="VARCHAR" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="is_deleted" jdbcType="BIT" property="isDeleted"/>
        <result column="channel" jdbcType="VARCHAR" property="channel"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        open_id,
        user_id,
        tenant_code,
        union_id,
        app_id,
        phone,
        nick_name,
        avatar,
        gender,
        `status`,
        created_by,
        created_time,
        updated_by,
        updated_time,
        is_deleted,
        channel
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from nbchat_wx_user
        where open_id = #{openId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete
        from nbchat_wx_user
        where open_id = #{openId,jdbcType=VARCHAR}
    </delete>
    <insert id="insertSelective" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatWxUser">
        <!--@mbg.generated-->
        insert into nbchat_wx_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">
                open_id,
            </if>
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="unionId != null and unionId != ''">
                union_id,
            </if>
            <if test="appId != null and appId != ''">
                app_id,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name,
            </if>
            <if test="avatar != null and avatar != ''">
                avatar,
            </if>
            <if test="gender != null and gender != ''">
                gender,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="channel != null and channel != ''">
                channel,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">
                #{openId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null and userId != ''">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="unionId != null and unionId != ''">
                #{unionId,jdbcType=VARCHAR},
            </if>
            <if test="appId != null and appId != ''">
                #{appId,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != ''">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="nickName != null and nickName != ''">
                #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null and avatar != ''">
                #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="gender != null and gender != ''">
                #{gender,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="createdBy != null and createdBy != ''">
                #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=BIT},
            </if>
            <if test="channel != null and channel != ''">
                #{channel,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.NbchatWxUser">
        <!--@mbg.generated-->
        update nbchat_wx_user
        <set>
            <if test="userId != null and userId != ''">
                user_id = #{userId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code = #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="unionId != null and unionId != ''">
                union_id = #{unionId,jdbcType=VARCHAR},
            </if>
            <if test="appId != null and appId != ''">
                app_id = #{appId,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name = #{nickName,jdbcType=VARCHAR},
            </if>
            <if test="avatar != null and avatar != ''">
                avatar = #{avatar,jdbcType=VARCHAR},
            </if>
            <if test="gender != null and gender != ''">
                gender = #{gender,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=TINYINT},
            </if>
            <if test="createdBy != null and createdBy != ''">
                created_by = #{createdBy,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedBy != null and updatedBy != ''">
                updated_by = #{updatedBy,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=BIT},
            </if>
            <if test="channel != null and channel != ''">
                channel = #{channel,jdbcType=VARCHAR},
            </if>
        </set>
        where open_id = #{openId,jdbcType=VARCHAR}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        <!--@mbg.generated-->
        update nbchat_wx_user
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.userId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="union_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.unionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.appId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.phone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="nick_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.nickName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="avatar = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.avatar,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="gender = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.gender,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.status,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="created_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.createdBy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="created_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.createdTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="updated_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.updatedBy,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="updated_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.updatedTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.isDeleted,jdbcType=BIT}
                </foreach>
            </trim>
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.channel,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where open_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.openId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update nbchat_wx_user
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="user_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.userId != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.userId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="tenant_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.tenantCode != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.tenantCode,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="union_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.unionId != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.unionId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="app_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.appId != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.appId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.phone != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.phone,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="nick_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.nickName != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.nickName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="avatar = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.avatar != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.avatar,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="gender = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.gender != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.gender,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`status` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.status != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.status,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="created_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createdBy != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.createdBy,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="created_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createdTime != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.createdTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updated_by = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updatedBy != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.updatedBy,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="updated_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updatedTime != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.updatedTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="is_deleted = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.isDeleted,jdbcType=BIT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="channel = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.channel != null">
                        when open_id = #{item.openId,jdbcType=VARCHAR} then #{item.channel,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where open_id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.openId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into nbchat_wx_user
        (open_id, user_id, tenant_code, union_id, app_id, phone, nick_name, avatar, gender,
         `status`, created_by, created_time, updated_by, updated_time, is_deleted, channel)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.openId,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.tenantCode,jdbcType=VARCHAR},
             #{item.unionId,jdbcType=VARCHAR}, #{item.appId,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR},
             #{item.nickName,jdbcType=VARCHAR}, #{item.avatar,jdbcType=VARCHAR}, #{item.gender,jdbcType=VARCHAR},
             #{item.status,jdbcType=TINYINT}, #{item.createdBy,jdbcType=VARCHAR},
             #{item.createdTime,jdbcType=TIMESTAMP},
             #{item.updatedBy,jdbcType=VARCHAR}, #{item.updatedTime,jdbcType=TIMESTAMP}, #{item.isDeleted,jdbcType=BIT},
             #{item.channel,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <select id="findOneByTenantCodeAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nbchat_wx_user
        where is_deleted = '0'
          and tenant_code = #{tenantCode,jdbcType=VARCHAR}
          and user_id = #{userId,jdbcType=VARCHAR}
    </select>
</mapper>
