<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysUserStarMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysUserStar">
        <!--@mbg.generated-->
        <!--@Table sys_user_star-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="busi_type" jdbcType="VARCHAR" property="busiType"/>
        <result column="busi_id" jdbcType="VARCHAR" property="busiId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id,
        user_id,
        tenant_code,
        busi_type,
        busi_id,
        create_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from sys_user_star
        where id = #{id,jdbcType=BIGINT}
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserStar"
            useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sys_user_star (user_id, tenant_code, busi_type,
                                   busi_id, create_time)
        values (#{userId,jdbcType=VARCHAR}, #{tenantCode,jdbcType=VARCHAR}, #{busiType,jdbcType=VARCHAR},
                #{busiId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysUserStar" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sys_user_star
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                user_id,
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                tenant_code,
            </if>
            <if test="busiType != null and busiType != ''">
                busi_type,
            </if>
            <if test="busiId != null and busiId != ''">
                busi_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                #{tenantCode,jdbcType=VARCHAR},
            </if>
            <if test="busiType != null and busiType != ''">
                #{busiType,jdbcType=VARCHAR},
            </if>
            <if test="busiId != null and busiId != ''">
                #{busiId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserStar">
        <!--@mbg.generated-->
        update sys_user_star
        <set>
            <if test="busiType != null and busiType != ''">
                busi_type = #{busiType,jdbcType=VARCHAR},
            </if>
            <if test="busiId != null and busiId != ''">
                busi_id = #{busiId,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysUserStar">
        <!--@mbg.generated-->
        update sys_user_star
        set user_id     = #{userId,jdbcType=VARCHAR},
            tenant_code = #{tenantCode,jdbcType=VARCHAR},
            busi_type   = #{busiType,jdbcType=VARCHAR},
            busi_id     = #{busiId,jdbcType=VARCHAR},
            create_time = #{createTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from sys_user_star
        <where>
            <if test="id != null">
                and id = #{id,jdbcType=BIGINT}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId,jdbcType=VARCHAR}
            </if>
            <if test="tenantCode != null and tenantCode != ''">
                and tenant_code = #{tenantCode,jdbcType=VARCHAR}
            </if>
            <if test="busiType != null and busiType != ''">
                and busi_type = #{busiType,jdbcType=VARCHAR}
            </if>
            <if test="busiId != null and busiId != ''">
                and busi_id = #{busiId,jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                and create_time = #{createTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </select>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update sys_user_star
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="busi_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.busiType != null and item.busiType != ''">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.busiType,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="busi_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.busiId != null and item.busiId != ''">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.busiId,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into sys_user_star
            (user_id, tenant_code, busi_type, busi_id, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId,jdbcType=VARCHAR}, #{item.tenantCode,jdbcType=VARCHAR}, #{item.busiType,jdbcType=VARCHAR},
             #{item.busiId,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <delete id="deleteById">
        delete
        from sys_user_star
        where id = #{id,jdbcType=BIGINT}
    </delete>

<!--auto generated by MybatisCodeHelper on 2024-12-24-->
    <select id="selectOne" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_user_star
        where user_id = #{userId,jdbcType=VARCHAR}
        and tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and busi_id = #{busiId,jdbcType=VARCHAR}
    </select>
</mapper>