<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.tydic.nbchat.admin.mapper.SysMenuButtonMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysMenuButton">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="menu_code" property="menuCode" jdbcType="VARCHAR"/>
        <result column="button_code" property="buttonCode" jdbcType="VARCHAR"/>
        <result column="button_name" property="buttonName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="order_index" property="orderIndex" jdbcType="SMALLINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, menu_code, button_code, button_name, remark, order_index, create_time, update_time
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from sys_menu_button
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from sys_menu_button
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuButton">
        insert into sys_menu_button (id, menu_code, button_code,
                                     button_name, remark, order_index,
                                     create_time, update_time)
        values (#{id,jdbcType=INTEGER}, #{menuCode,jdbcType=VARCHAR}, #{buttonCode,jdbcType=VARCHAR},
                #{buttonName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{orderIndex,jdbcType=SMALLINT},
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true"
            parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuButton">
        insert into sys_menu_button
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="menuCode != null">
                menu_code,
            </if>
            <if test="buttonCode != null">
                button_code,
            </if>
            <if test="buttonName != null">
                button_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="orderIndex != null">
                order_index,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="menuCode != null">
                #{menuCode,jdbcType=VARCHAR},
            </if>
            <if test="buttonCode != null">
                #{buttonCode,jdbcType=VARCHAR},
            </if>
            <if test="buttonName != null">
                #{buttonName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="orderIndex != null">
                #{orderIndex,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuButton">
        update sys_menu_button
        <set>
            <if test="menuCode != null">
                menu_code = #{menuCode,jdbcType=VARCHAR},
            </if>
            <if test="buttonCode != null">
                button_code = #{buttonCode,jdbcType=VARCHAR},
            </if>
            <if test="buttonName != null">
                button_name = #{buttonName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="orderIndex != null">
                order_index = #{orderIndex,jdbcType=SMALLINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysMenuButton">
        update sys_menu_button
        set menu_code   = #{menuCode,jdbcType=VARCHAR},
            button_code = #{buttonCode,jdbcType=VARCHAR},
            button_name = #{buttonName,jdbcType=VARCHAR},
            remark      = #{remark,jdbcType=VARCHAR},
            order_index = #{orderIndex,jdbcType=SMALLINT},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="existButtonCode" parameterType="string" resultType="int">
        select count(1)
        from sys_menu_button
        where button_code = #{buttonCode}
    </select>

    <select id="selectByMenuCode" resultMap="BaseResultMap" parameterType="string">
        select
        <include refid="Base_Column_List"/>
        from sys_menu_button
        where menu_code = #{menuCode} order by order_index,id
    </select>

    <delete id="deleteByCode" parameterType="string">
        delete
        from sys_menu_button
        where menu_code = #{menuCode}
          and button_code = #{buttonCode}
    </delete>

</mapper>