<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.SysUpdateLogMapper">
  <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.SysUpdateLog">
    <!--@mbg.generated-->
    <!--@Table sys_update_log-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="click_count" jdbcType="INTEGER" property="clickCount" />
    <result column="view_count" jdbcType="INTEGER" property="viewCount" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="status" jdbcType="CHAR" property="status" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, version, remark, publish_time, click_count, view_count, content, create_time, 
    update_time, create_user, `status`, is_valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from sys_update_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysUpdateLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_update_log (version, remark, publish_time, 
      click_count, view_count, content, 
      create_time, update_time, create_user, 
      `status`, is_valid)
    values (#{version,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{publishTime,jdbcType=TIMESTAMP}, 
      #{clickCount,jdbcType=INTEGER}, #{viewCount,jdbcType=INTEGER}, #{content,jdbcType=LONGVARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{createUser,jdbcType=VARCHAR}, 
      #{status,jdbcType=CHAR}, #{isValid,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.tydic.nbchat.admin.mapper.po.SysUpdateLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_update_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="version != null">
        version,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="clickCount != null">
        click_count,
      </if>
      <if test="viewCount != null">
        view_count,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clickCount != null">
        #{clickCount,jdbcType=INTEGER},
      </if>
      <if test="viewCount != null">
        #{viewCount,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=CHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.tydic.nbchat.admin.mapper.po.SysUpdateLog">
    <!--@mbg.generated-->
    update sys_update_log
    <set>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="clickCount != null">
        click_count = #{clickCount,jdbcType=INTEGER},
      </if>
      <if test="viewCount != null">
        view_count = #{viewCount,jdbcType=INTEGER},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=CHAR},
      </if>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.tydic.nbchat.admin.mapper.po.SysUpdateLog">
    <!--@mbg.generated-->
    update sys_update_log
    set version = #{version,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      click_count = #{clickCount,jdbcType=INTEGER},
      view_count = #{viewCount,jdbcType=INTEGER},
      content = #{content,jdbcType=LONGVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_user = #{createUser,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=CHAR},
      is_valid = #{isValid,jdbcType=CHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectByAll" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from sys_update_log
        <where>
            <if test="id != null">
                and id=#{id,jdbcType=INTEGER}
            </if>
            <if test="version != null">
                and version=#{version,jdbcType=VARCHAR}
            </if>
            <if test="remark != null">
                and remark=#{remark,jdbcType=VARCHAR}
            </if>
            <if test="publishTime != null">
                and publish_time=#{publishTime,jdbcType=TIMESTAMP}
            </if>
            <if test="clickCount != null">
                and click_count=#{clickCount,jdbcType=INTEGER}
            </if>
            <if test="viewCount != null">
                and view_count=#{viewCount,jdbcType=INTEGER}
            </if>
            <if test="content != null">
                and content=#{content,jdbcType=LONGVARCHAR}
            </if>
            <if test="createTime != null">
                and create_time=#{createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                and update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createUser != null">
                and create_user=#{createUser,jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                and `status`=#{status,jdbcType=CHAR}
            </if>
            <if test="isValid != null">
                and is_valid=#{isValid,jdbcType=CHAR}
            </if>
        </where>
    </select>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update sys_update_log
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.version != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.version,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="publish_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.publishTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.publishTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="click_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.clickCount != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.clickCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="view_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.viewCount != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.viewCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.content != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.content,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_user = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createUser != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createUser,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.status,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_valid = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isValid != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.isValid,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into sys_update_log
    (version, remark, publish_time, click_count, view_count, content, create_time, update_time, 
      create_user, `status`, is_valid)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.version,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.publishTime,jdbcType=TIMESTAMP}, 
        #{item.clickCount,jdbcType=INTEGER}, #{item.viewCount,jdbcType=INTEGER}, #{item.content,jdbcType=LONGVARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createUser,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=CHAR}, #{item.isValid,jdbcType=CHAR})
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from sys_update_log where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=INTEGER}
    </foreach>
  </delete>
</mapper>