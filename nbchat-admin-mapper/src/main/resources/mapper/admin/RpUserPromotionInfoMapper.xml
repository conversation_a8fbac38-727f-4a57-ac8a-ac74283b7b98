<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tydic.nbchat.admin.mapper.RpUserPromotionInfoMapper">
    <resultMap id="BaseResultMap" type="com.tydic.nbchat.admin.mapper.po.RpUserPromotionInfo">
        <!--@mbg.generated-->
        <!--@Table rp_user_promotion_info-->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="tenant_code" jdbcType="VARCHAR" property="tenantCode"/>
        <result column="user_id" jdbcType="VARCHAR" property="userId"/>
        <result column="total_num" jdbcType="INTEGER" property="totalNum"/>
        <result column="assist_num" jdbcType="INTEGER" property="assistNum"/>
        <result column="fission_num" jdbcType="INTEGER" property="fissionNum"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, tenant_code, user_id, total_num, assist_num, fission_num, update_time
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into rp_user_promotion_info
        (tenant_code, user_id, total_num, assist_num, fission_num, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.tenantCode,jdbcType=VARCHAR}, #{item.userId,jdbcType=VARCHAR}, #{item.totalNum,jdbcType=INTEGER},
            #{item.assistNum,jdbcType=INTEGER}, #{item.fissionNum,jdbcType=INTEGER},
            current_timestamp
            )
        </foreach>
    </insert>
    <update id="batchUpdate">
        update rp_user_promotion_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="total_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.totalNum != null">
                        when id = #{item.id,jdbcType=INTEGER} then
                        <if test="incrementFlag">
                            total_num +
                        </if>
                        #{item.totalNum,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="assist_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.assistNum != null">
                        when id = #{item.id,jdbcType=INTEGER} then
                        <if test="incrementFlag">
                            assist_num +
                        </if>
                        #{item.assistNum,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="fission_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.fissionNum != null">
                        when id = #{item.id,jdbcType=INTEGER} then
                        <if test="incrementFlag">
                            fission_num +
                        </if>
                        #{item.fissionNum,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=INTEGER} then current_timestamp
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <select id="findOneByTenantCodeAndUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rp_user_promotion_info
        where tenant_code = #{tenantCode,jdbcType=VARCHAR}
        and user_id = #{userId,jdbcType=VARCHAR}
    </select>
    <insert id="insertForAll">
        insert into rp_user_promotion_info (tenant_code, user_id, assist_num, update_time)
        select tenant_code,
               assist_id,
               count(0) as assist_num,
               current_timestamp
        from nbchat_user_assist_detail
        group by tenant_code, assist_id
    </insert>
    <delete id="deleteAll">
        delete
        from rp_user_promotion_info
    </delete>
</mapper>
