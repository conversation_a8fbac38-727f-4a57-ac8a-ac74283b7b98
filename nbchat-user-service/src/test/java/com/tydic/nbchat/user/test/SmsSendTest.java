package com.tydic.nbchat.user.test;

import com.tydic.nbchat.user.api.bo.SendSmsRequest;
import com.tydic.nbchat.user.api.bo.eums.SmsHelperType;
import com.tydic.nbchat.user.core.utils.NbchatSmsProxyHelper;
import com.tydic.nbchat.user.core.busi.VipSmsNoticeService;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@SpringBootTest
public class SmsSendTest {

    @Autowired
    private NbchatSmsProxyHelper nbchatSmsProxyHelper;
    @Autowired
    private VipSmsNoticeService vipSmsNoticeService;

    @Test
    public void test(){
        Map<String,String> paramMap = new HashMap<>();
        paramMap.put("name","体验会员");
        paramMap.put("time","2024-08-10");
        SendSmsRequest request = SendSmsRequest.builder().
                signName("课件帮").templateCode("SMS_470775014").
                phone("17611258360").templateParam(paramMap).
                build();
        Rsp rsp = nbchatSmsProxyHelper.send(SmsHelperType.ALI,request);
        log.info("短信发送: {}|{}",request,rsp);
    }

    @Test
    public void test2(){
        vipSmsNoticeService.sendVipOpenSms("254285439858831360","体验会员",new Date());
    }
}
