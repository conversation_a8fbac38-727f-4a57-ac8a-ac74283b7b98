
# 配置日志
logging:
  config: classpath:logback-spring.xml

# nacos 配置中心
nacos:
  config:
    # 命名空间,区分不同环境
    namespace: nbchat-dev
    # nacos服务地址,本机配置host
    server-addr: **************:8848
    remote-first: true
    # 配置data-id
    data-id: nbchat-user
    # 分组
    group: NBCHAT
    # 配置类型
    type: yaml
    # 自动刷新配置
    auto-refresh: true
    max-retry: 10
    config-retry-time: 2333
    config-long-poll-timeout: 46000
    enable-remote-sync-config: true
    username: nacos
    password: nacos
    #access-key:
    #secret-key:
    bootstrap:
      enable: true
    ext-config: # 导入公共配置
      - data-id: nbchat-public
        group: NBCHAT
        type: yaml
        auto-refresh: true
        username: ${nacos.config.username:nacos}
        password: ${nacos.config.password:nacos}
