
## 版本变更记录

### v1.1.0
- 聊天机器人支持fastchat、文心一言
- 机器人token支持配置url、机器人类型等
- 重构文档解析实现，支持配置不同方式
- 新增工具 内容优化、文档摘要提取(配置预设模板)
- 新增bert解析向量(需要新增如下向量字段):
```shell
post /nbchat_research_part/_mapping
post /nbchat_research_major_part/_mapping
{ 
  "properties": { 
    "partVectorBert": {
        "type": "dense_vector",
        "dims": 768
    }
  } 
} 
```
- 更改配置参数：
```yaml
nbchat-robot:
  config:
    robot-type-default: chatgpt
    robot-model-default: gpt-3.5-turbo # 默认机器人类型和默认模型需要配合
    research-analysis: # 解析工具配置
      default-parser: chatgpt
      qa-parser: chatgpt # chatgpt/baidu/fastchat/auto 配置auto之后 ，所使用解析工具跟随用户设定机器人，否则使用指定
      sub-parser: chatgpt
      optimize-parser: auto 
      vector-parser: chatgpt
      bert-embedding-api: http://192.168.10.248:8000/encode # bert做向量解析


nbchat-user:
  config:
    # 用户默认设置项， robotType需要与 nbchat-robot.config.robot-type-default 保持一致
    default-user-settings: { "robotType":"chatgpt"  }

```

- 表结构变更
```mysql

-- 新增字典表
DROP TABLE IF EXISTS `sys_dict_config`;
CREATE TABLE `sys_dict_config` (
  `dict_id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `tenant_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '' COMMENT '租户',
  `channel_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '渠道',
  `dict_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '编码',
  `dict_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '对应值',
  `dict_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `dict_desc` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '描述',
  `order_index` smallint unsigned DEFAULT '0' COMMENT '顺序字段',
  `crt_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `is_valid` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT '1' COMMENT '是否有效',
  PRIMARY KEY (`dict_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin;

-- ----------------------------
-- Records of sys_dict_config
-- ----------------------------
BEGIN;
INSERT INTO `sys_dict_config` (`dict_id`, `tenant_code`, `channel_code`, `dict_code`, `dict_name`, `dict_value`, `dict_desc`, `order_index`, `crt_time`, `is_valid`) VALUES (1, '', '', 'robotType', '通用GPT', 'chatgpt', 'ChatGPT', 0, '2023-05-12 17:00:52', '1');
INSERT INTO `sys_dict_config` (`dict_id`, `tenant_code`, `channel_code`, `dict_code`, `dict_name`, `dict_value`, `dict_desc`, `order_index`, `crt_time`, `is_valid`) VALUES (2, '', '', 'robotType', '私有GPT', 'fastchat', 'FastChat', 1, '2023-05-12 17:00:59', '1');
INSERT INTO `sys_dict_config` (`dict_id`, `tenant_code`, `channel_code`, `dict_code`, `dict_name`, `dict_value`, `dict_desc`, `order_index`, `crt_time`, `is_valid`) VALUES (3, '', '', 'robotType', '文心一言', 'baidu', '文心一言', 2, '2023-05-10 18:33:39', '1');
COMMIT;

-- 新增用户设置表
DROP TABLE IF EXISTS `nbchat_user_settings`;
CREATE TABLE `nbchat_user_settings` (
    `setting_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
    `tenant_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '00000000',
    `user_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
    `current_setting` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '当前设置',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `last_setting` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '上次设置',
    `is_valid` char(2) COLLATE utf8mb4_general_ci DEFAULT '1',
    PRIMARY KEY (`setting_id`),
    KEY `idx_uni_uid` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 新增用户建议表
DROP TABLE IF EXISTS `nbchat_user_suggestion`;
CREATE TABLE `nbchat_user_suggestion` (
  `suggest_id` int unsigned NOT NULL AUTO_INCREMENT,
  `tenant_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户',
  `user_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户',
  `username` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '称呼',
  `content` varchar(300) COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `phone` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '电话',
  `email` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '邮箱',
  `page_source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '页面来源',
  `return_visit` char(2) COLLATE utf8mb4_general_ci DEFAULT '0' COMMENT '允许回访',
  `create_time` datetime DEFAULT NULL,
  PRIMARY KEY (`suggest_id`),
  KEY `idx_uid` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户反馈建议';

-- 新增预设提示模板表
CREATE TABLE `nbchat_preset_prompt` (
    `preset_id` varchar(30) COLLATE utf8mb4_general_ci NOT NULL,
    `tenant_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
    `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '-1',
    `template` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
    `preset_name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL,
    `preset_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
    `create_time` datetime DEFAULT NULL,
    `preset_img` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '',
    `order_index` smallint unsigned DEFAULT '0',
    `is_valid` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1',
    PRIMARY KEY (`preset_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Records of nbchat_preset_prompt
-- ----------------------------
BEGIN;
INSERT INTO `nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('1000', '000000', '-1', '请你充当一名专业作者，帮我优化这篇文章。下面是具体要求：\n\n1.修正文章中的错别字和病句，使文章流畅 \n2.字数限制在2000字以下 \n3.请根据输入的语言自动识别返回的语言类型 \n4.直接返回你修改后的内容，不需要增加批注 \n下文是文章内容，请你修改它:\n----------------\n\n#ARG0', '文本优化', '文本内容优化', '2023-05-15 11:21:21', '', 0, '1');
INSERT INTO `nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('1001', '000000', '-1', 'please, write a detailed summary of this article in chinese:\n\n#ARG0', '摘要提取', '摘要提取', '2023-05-15 11:23:30', '', 0, '1');
COMMIT;
```

### v1.1.1

- 专属文档管理功能
- 会话消息列表新增robotType字段用于判断头像

```mysql
-- 专属文档知识表变更
ALTER TABLE nbchat_research_major ADD COLUMN user_id VARCHAR(50) DEFAULT '' COMMENT '用户id';
ALTER TABLE nbchat_research_major ADD COLUMN major_source SMALLINT(2) DEFAULT 1 COMMENT '来源：0 系统内定 1 用户导入 ';
ALTER TABLE nbchat_research_major ADD COLUMN major_avatar VARCHAR(200) DEFAULT '' COMMENT '头像地址';
ALTER TABLE nbchat_research_major ADD COLUMN robot_type VARCHAR(20) DEFAULT '' COMMENT '指定问答机器人: 默认不填跟随用户设定';
ALTER TABLE nbchat_research_major ADD COLUMN embed_type VARCHAR(20) DEFAULT 'chatgpt' COMMENT '指定文档embedd工具: 默认跟随系统设定';
ALTER TABLE nbchat_research_major ADD COLUMN update_time datetime DEFAULT null COMMENT '更新时间';

-- 专属机器人分享
DROP TABLE IF EXISTS `nbchat_research_share`;
CREATE TABLE `nbchat_research_share` (
     `share_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '共享id',
     `tenant_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户编码',
     `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
     `object_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分享对象',
     `object_type` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '对象类型',
     `share_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '密码',
     `expired_date` datetime DEFAULT NULL COMMENT '过期时间',
     `created_at` datetime NOT NULL COMMENT '创建时间',
     `share_url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '共享链接',
     `permission` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT '权限 1 读写 2 只读',
     `is_valid` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '是否删除',
     PRIMARY KEY (`share_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
```

### v1.2.0-20230526

- 对接知识库-新增解析文本文档接口
- 对接知识库-新增知识库题库生成
- 新增解析任务创建/查询接口（nbchat_research_task/nbchat_research_task_result）
- 更新会话列表查询/会话消息查询/文档检索消息查询接口 支持分页加载
- 新增向量解析工具（ali_nlp解析）
- robot模块代码优化
- 去除邀请码限制
- 配置更新：
```yaml
nbchat-robot:
  config:
      ali-config: # 阿里ak/sk配置
        access-key: LTAI5tJ542cAJW4YdxMabuix
        secret-key: ******************************
        text-type: query
```

- SQL 变更：

```mysql

-- 任务结果表
CREATE TABLE `nbchat_research_task_result` (
    `data_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键自增',
    `tenant_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '租户编码',
    `major_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '所属机器人id',
    `file_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源文档',
    `request_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求id',
    `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '用户id',
    `preset_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '预设提示词id',
    `robot_type` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '机器人类型',
    `content` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '回复内容',
    `part_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '文档段落',
    `data_time` datetime DEFAULT NULL COMMENT '时间',
    PRIMARY KEY (`data_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 任务表
CREATE TABLE `nbchat_research_task` (
    `request_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键自增',
    `tenant_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '租户编码',
    `major_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '专属机器人',
    `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户id',
    `preset_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '预设提示词id',
    `file_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源文档',
    `robot_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '机器人类型',
    `start_time` datetime NOT NULL COMMENT '时间',
    `end_time` datetime DEFAULT NULL,
    `task_state` char(2) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '0 : 任务创建  1 任务执行中 2 已完成 3 出错',
    `err_msg` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '异常信息',
    `preset_param` varchar(500) COLLATE utf8mb4_general_ci DEFAULT NULL,
    PRIMARY KEY (`request_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 新增提示词模板
ALTER TABLE nbchat_preset_prompt ADD COLUMN system_role text COMMENT '系统角色设定';

INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('2001', '00000000', '-1', '', '请根据最下面【素材】中的内容，帮我制作 #ARG0 道单选题，请按以下模板，反回1个json数组保存这些选题（最多有4个答案选项），不需要返回任何多余的信息：\n----------\n[\n { \n  \"question\":\"$YOUR_QUESTION_HERE\",\n  \"answers\":[$YOUR_ANSWER_INDEX]  //答案序号,单选只有1个\n   \"items\":[\n      \"$YOUR_ANSWER_ITEM_A\",\"$YOUR_ANSWER_ITEM_B\",\"$YOUR_ANSWER_ITEM_C\",\"$YOUR_ANSWER_ITEM_D\" \n   ]\n }\n ...\n]\n----------\n【素材】：\n#ARG1', '单选题', '题库生成', '2023-05-19 16:45:19', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('2002', '00000000', '-1', '', '请根据最下面【素材】中的内容，帮我制作 #ARG0 道多选题，请按以下模板，只反回1个json数组保存这些选题（多选题最多有4个答案选项），不需要返回任何多余的信息：\n----------\n[\n { \n  \"question\":\"$YOUR_QUESTION_HERE\",\n  \"answers\":[$YOUR_ANSWER_INDEXS...]  //答案序号,多选可能有多个\n   \"items\":[\n      \"$YOUR_ANSWER_ITEM_A\",\"$YOUR_ANSWER_ITEM_B\",\"$YOUR_ANSWER_ITEM_C\",\"$YOUR_ANSWER_ITEM_D\" \n   ]\n }\n ...\n]\n----------\n【素材】：\n#ARG1', '多选题', '题库生成', '2023-05-19 16:45:19', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('2003', '00000000', '-1', '', '请根据最下面【素材】中的内容，帮我制作填空题，请按以下模板，只反回1个json数组保存 #ARG0 道填空题（填空题空白处用\"____\"替代，填空题中的\"____\"可以有多个，但是必须同答案数量相对应）不需要返回任何多余的信息：\n这是【模板】\n----------\n[\n  {\n    \"question\":\"$YOUR_QUESTION_HERE\",\n    \"answers\":[\"$YOUR_ANSWER_HER\"]\n  }\n  ...\n]\n----------\n这是【素材】：\n#ARG1', '填空题', '题库生成', '2023-05-19 16:45:19', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('2004', '00000000', '-1', '', '请根据最下面【素材】中的内容，帮我制作 #ARG0 道判断题，请按以下模板，只反回1个json数组保存这些题（答案为yes或no），不需要返回任何多余的信息：\n----------\n[\n  {\n    \"question\":\"$YOUR_QUESTION_HERE\",\n    \"answer\":\"$YOUR_ANSWER_HERE\" //yes/no\n  }\n  ...\n]\n----------\n【素材】：\n#ARG1', '判断题', '题库生成', '2023-05-19 16:45:19', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('2005', '00000000', '-1', '', '请根据最下面【素材】中的内容，帮我制作 #ARG0 道简答题，请按以下模板，只反回1个json数组保存这些题，不需要返回任何多余的信息：\n----------\n[\n  {\n    \"question\":\"$YOUR_QUESTION_HERE\",\n    \"answer\":\"$YOUR_ANSWER_HERE\"\n  }\n  ...\n]\n----------\n【素材】：\n#ARG1', '简答题', '题库生成', '2023-05-19 16:45:19', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('2006', '00000000', '-1', '', '请根据最下面【素材】中的内容，帮我制作 #ARG0 道论述题，请按以下模板，只反回1个json数组保存这些题，不需要返回任何多余的信息：\n----------\n[\n  {\n    \"question\":\"$YOUR_QUESTION_HERE\",\n    \"answer\":\"$YOUR_ANSWER_HERE\"\n  }\n  ...\n]\n----------\n【素材】：\n#ARG1', '论述题', '题库生成', '2023-05-19 16:45:19', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('system_part_sub', '00000000', '-1', '', 'please, write a short summary of this paragraph (please using chinese):\n---------\n#ARG0\n', '段落摘要', '段落摘要', '2023-05-22 18:16:31', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('system_qa', '00000000', '-1', '', 'You are a smart assistant designed to help high school teachers come up with reading comprehension questions.\nGiven a piece of text, you must come up with one or more question and answer pair that can be used to test a student\'s reading comprehension abilities.\nWhen coming up with this question/answer pair, you must respond in the following format (please using chinese):\n[{\n    \"question\": \"$YOUR_QUESTION_HERE\",\n    \"answer\": \"$THE_ANSWER_HERE\"\n}]\n\nPlease come up with a question/answer pair, in the specified JSON format, for the following text:\n----------------\n#ARG0', 'qa提取', 'qa提取', '2023-05-22 18:10:42', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('system_research', '00000000', '-1', 'whose expertise is reading and summarizing scientific papers. You are given a query, \n        a series of text embeddings and the title from a paper in order of their cosine similarity to the query. \n        You must take the given embeddings and return a very detailed summary of the paper in the languange of the query: \n        Here is the question: #ARG0 \n        and here are the embeddings: \n#ARG1', 'Given the question: #ARG0 . Return a detailed answer based on the paper(请默认用中文回复,只返回你总结的内容,不需要加任何多余提示):', '文档检索', '文档检索', '2023-05-24 11:01:53', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('system_sub', '00000000', '-1', '', 'please, write a detailed summary of this article in chinese:\n---------\n#ARG0\n', '摘要', '摘要', '2023-05-24 11:01:55', '', 0, '1');


```


### v1.2.1-20230531

- 新增文档列表查询
- 新增小工具

```mysql
-- 文档列表
CREATE TABLE `nbchat_article` (
      `article_id` varchar(50) COLLATE utf8mb4_general_ci NOT NULL,
      `user_id` varchar(50) COLLATE utf8mb4_general_ci DEFAULT NULL,
      `title` varchar(200) COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
      `content` text COLLATE utf8mb4_general_ci COMMENT '文章内容，富文本',
      `link_url` varchar(300) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '链接地址',
      `category` varchar(20) COLLATE utf8mb4_general_ci DEFAULT '',
      `tenant_code` varchar(50) COLLATE utf8mb4_general_ci DEFAULT '',
      `img_avatar` varchar(300) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '缩略图头像',
      `hit_count` smallint DEFAULT '0' COMMENT '点击量',
      `create_time` datetime NOT NULL COMMENT '创建时间',
      `update_time` datetime DEFAULT NULL COMMENT '更新时间',
      `is_hot` char(1) COLLATE utf8mb4_general_ci DEFAULT '0',
      `is_valid` char(1) COLLATE utf8mb4_general_ci DEFAULT '1',
      `order_index` smallint unsigned DEFAULT '1' COMMENT '排序字段',
      PRIMARY KEY (`article_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- 新增提示词语
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('1002', '00000000', '-1', '', '请帮我整理成周报：#ARG0', '', '', '2023-05-15 11:23:30', '', 0, '1');
INSERT INTO `nbchat`.`nbchat_preset_prompt` (`preset_id`, `tenant_code`, `category`, `system_role`, `template`, `preset_name`, `preset_desc`, `create_time`, `preset_img`, `order_index`, `is_valid`) VALUES ('1003', '00000000', '-1', '', '请帮我翻译以下内容，如果是中文则翻译为英文，如果是英文翻译为中文：#ARG0', '', '', '2023-05-15 11:23:30', '', 0, '1');

```

### v1.2.2-20230623

- 支持milvus向量检索和存储
- 配置文件变更

```yaml
nbchat-robot:
  config-milvus:
    enable: true
    host: localhost
    port: 19530
    user: root
    password: 1qaz@WSX
    auto-create: true # 自动创建索引
```

### v1.2.3-20230630

- 文档检索显示知识来源段落
- 百度文心一言token刷新功能
- 移除partVectorBert向量字段
- 配置文件变更
- sql脚本变更

```shell
# 移除操作过程
# 1. 移除 partVectorBert
curl -H "Content-Type:application/json" -X POST -d '{"script": {"lang": "painless","inline":
 "ctx._source.remove(\"partVectorBert\")"}, "query": {"match_all": {}}}' 
  http://127.0.0.1:19200/nbchat_research_major/_update_by_query
# 2. 创建新索引（不带partVectorBert字段）
# 3. _reindex索引，此时新索引 nbchat_research_major_prod 数据不带partVectorBert
curl -H "Content-Type:application/json" -X POST -d '{"source":{"index":"nbchat_research_major"},
"dest":{"index":"nbchat_research_major_prod"}}' http://127.0.0.1:19200/_reindex
```

```yaml
nbchat-robot:
  config:
    baidu-config:
      access-key: test # 百度token刷新用
      secret-key: test
```

```sql
-- 保存知识来源
 ALTER TABLE nbchat_research_msg ADD COLUMN doc_parts TEXT DEFAULT NULL COMMENT '保留知识来源:数组格式';
-- 去除非空校验
ALTER TABLE nbchat_research_files MODIFY COLUMN access_url VARCHAR(300) DEFAULT '' COMMENT '文件访问地址';
```


### 1.2.4
- 对接知识库的问答接口
- 专属机器人支持解析qa
- qa分页查询支持majorId
- 专属机器人es索引创建支持配置vector-dimension向量长度参数
- 专属机器人向量对接api更新
- 更新部分提示词
- 新增NbchatHelper工具

```yaml
nbchat-robot:
  config-es:
    vector-dimension: 768 # 新增向量长度配置，创建索引时设置向量长度
  config-helper:
    api-url: http://nbchat-helper:5000/ # 对接helper模块
    pdf-ocr-enable: false # 是否开启pdf ocr
```

```mysql
-- 新增专属机器人字段
ALTER TABLE nbchat_research_qa ADD COLUMN major_id VARCHAR(50) DEFAULT '' COMMENT '专属机器人id';
-- 更新qa提取提示词
UPDATE nbchat_preset_prompt SET `tenant_code` = '00000000', `category` = '-1', `system_role` = '', 
                                           `template` = '资料内容: {{ #ARG0 }}\n----------\n请阅读上面资料的内容，根据资料的长度帮我提取不超过10个问答对，以下为返回的格式，请务必返回json格式的数据：\n[{\n    \"question\": \"$YOUR_QUESTION_HERE\",\n    \"answer\": \"$THE_ANSWER_HERE\"\n}]\n', 
                                           `preset_name` = 'qa提取', `preset_desc` = 'qa提取', `create_time` = '2023-05-22 18:10:42', `preset_img` = '', `order_index` = 0, `is_valid` = '1' WHERE `preset_id` = 'system_qa';

```

### 1.4.2
- 优化刷新token逻辑，新增nbchat_robot_token表config字段

```mysql
-- 新增robot_tokens表config字段
ALTER TABLE nbchat_robot_token ADD COLUMN config varchar(500) DEFAULT '' COMMENT '账号配置';
```
### 1.4.9
```mysql
-- 修改提示词表
ALTER TABLE nbchat_preset_prompt ADD COLUMN version int DEFAULT 0 COMMENT '版本',
add column history_version json COMMENT '历史版本';

-- 新增ai应用配置表

CREATE TABLE ai_tool_app(
    `app_id` varchar(50) NOT NULL PRIMARY KEY,
    `app_name` varchar(50) NOT NULL COMMENT '应用名称',
    `app_desc` varchar(50) DEFAULT '' COMMENT '描述',
    `app_type` varchar(2) DEFAULT '1' COMMENT '类型',
    `avatar` varchar(200) DEFAULT '' COMMENT '头像',
    `app_config` JSON COMMENT '应用配置',
    `tenant_code` varchar(50) NOT NULL COMMENT '租户',
    `user_id` varchar(50) NOT NULL COMMENT '用户ID',
    `robot_type` varchar(50) DEFAULT '' COMMENT '机器人',
    `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `share_link` VARCHAR(300) DEFAULT '' COMMENT '分享链接',
    `share_state` CHAR(1) DEFAULT '0' COMMENT '1 分享',
    `is_valid` CHAR(1) DEFAULT '1' COMMENT '0 删除 1 有效'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='ai应用配置表';


CREATE TABLE ai_tool_app_user (
      `id` BIGINT(11) unsigned NOT NULL AUTO_INCREMENT PRIMARY KEY,
      `tenant_code` varchar(50) DEFAULT '' COMMENT '租户',
      `user_id` varchar(50) DEFAULT '' COMMENT '用户ID',
      `app_id` varchar(50) NOT NULL,
      `name` varchar(20) DEFAULT '' COMMENT '姓名',
      `phone` varchar(20) DEFAULT '' COMMENT '手机号',
      `email` varchar(50) DEFAULT '' COMMENT '邮箱',
      `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `session_id` varchar(30) DEFAULT '' COMMENT '关联会话',
      `is_valid` CHAR(1) DEFAULT '1' COMMENT '0 删除 1 有效'
)

```