package com.tydic.nbchat.admin.core.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.admin.api.BillRecordApi;
import com.tydic.nbchat.admin.api.bo.constants.ExcelConstants;
import com.tydic.nbchat.admin.api.bo.file.FileUploadRequest;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.admin.core.export.UserBillRecordDTO;
import com.tydic.nbchat.admin.core.utils.ExamQuestionExportUtil;
import com.tydic.nbchat.admin.api.bo.ExcelRspBO;
import com.tydic.nbchat.user.api.bo.trade.UserBillRecordBO;
import com.tydic.nbchat.user.api.bo.trade.UserBillRecordQueryReqBO;
import com.tydic.nbchat.user.api.trade.TradeBillRecordApi;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.FileManagerHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class BillRecordServiceImpl implements BillRecordApi {
    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private TradeBillRecordApi tradeBillRecordApi;

    private final FileManageService fileManageService;

    public BillRecordServiceImpl(FileManageService fileManageService) {
        this.fileManageService = fileManageService;
    }

    @Override
    public Rsp exportBillRecord(UserBillRecordQueryReqBO queryReqBO) {
        log.info("导出用户账单记录:{}", queryReqBO);
        RspList<UserBillRecordBO> rspList = tradeBillRecordApi.getBillRecord(queryReqBO);
        List<UserBillRecordDTO> export = new ArrayList<>();
        if (!rspList.isSuccess()) {
            return BaseRspUtils.createErrorRsp("查询用户账单记录失败");
        }
        if (org.springframework.util.CollectionUtils.isEmpty(rspList.getRows())) {
            log.warn("用户账单记录导出-导出数据为空");
        } else {
            NiccCommonUtil.copyList(rspList.getRows(), export, UserBillRecordDTO.class);
        }
        return export(export, queryReqBO);
    }

    private Rsp export(List<UserBillRecordDTO> data, UserBillRecordQueryReqBO request) {
        ExcelRspBO rspBO = new ExcelRspBO();
        try {

            // 创建临时文件
            String fileName = ExamQuestionExportUtil.generateFileName(ExcelConstants.BILL_RECORD_SHEET_NAME, ExcelConstants.EXCEL_FILE_SUFFIX);
            File dirFile = ExamQuestionExportUtil.createTempFile(fileName);

            // 写出Excel文件
            writeExcelFile(dirFile, data);

            // 上传文件
            RspList<FileManageSaveBO> fileManageSaveBOS = uploadFile(dirFile, request);
            if (!fileManageSaveBOS.isSuccess()) {
                return BaseRspUtils.createErrorRsp("上传文件失败");
            }

            rspBO.setFileManageSaveBO(fileManageSaveBOS.getRows());
            log.info("用户账单记录导出-导出成功");
            return BaseRspUtils.createSuccessRsp(rspBO, "导出成功");
        } catch (Exception e) {
            log.error("用户账单记录导出失败", e);
            return BaseRspUtils.createErrorRsp("导出失败：" + e.getMessage());
        }
    }

    /**
     * 写出Excel文件
     *
     * @param outputFile
     * @param data
     */
    private void writeExcelFile(File outputFile, List<UserBillRecordDTO> data) {
        HorizontalCellStyleStrategy styleStrategy = ExamQuestionExportUtil.setExcelStyle();
        EasyExcel.write(outputFile, UserBillRecordDTO.class)
                .sheet(ExcelConstants.BILL_RECORD_SHEET_NAME)
                .registerWriteHandler(styleStrategy)
                .useDefaultStyle(true)
                .doWrite(data);

        log.info("用户账单记录导出成功，文件路径：{}", outputFile.getAbsolutePath());
    }


    /**
     * 上传文件
     *
     * @param file
     * @param request
     * @return
     * @throws IOException
     */
    private RspList<FileManageSaveBO> uploadFile(File file, UserBillRecordQueryReqBO request) throws IOException {
        if (file == null || !file.exists()) {
            throw new FileNotFoundException("待上传的文件不存在");
        }
        MultipartFile multipartFile = FileManagerHelper.parseToMultipartFile(file);
        FileUploadRequest uploadRequest = new FileUploadRequest();
        uploadRequest.setTenantCode(request.getTenantCode());
        uploadRequest.setUploadUser(request.getUserId());
        uploadRequest.setFileName(file.getName());
        uploadRequest.setFile(multipartFile.getBytes());
        uploadRequest.setUseOriginalName(true);

        RspList<FileManageSaveBO> rsp = fileManageService.fileUploadRequest(uploadRequest);
        log.info("文件上传结束, 返回信息：{}", JSON.toJSONString(rsp));
        return rsp;
    }
}
