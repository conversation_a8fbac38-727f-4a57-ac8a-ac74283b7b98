package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysUpvoteRecordApi;
import com.tydic.nbchat.admin.api.bo.SysUpvoteRecordBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @datetime：2024/9/18 18:26
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/admin/upvote")
public class SysUpvoteRecordController {
    @Resource
    private SysUpvoteRecordApi sysUpvoteRecordApi;

    @PostMapping("/save")
    public Rsp addNewUpvoteRecord(@RequestBody SysUpvoteRecordBO recordBO) {
        return sysUpvoteRecordApi.addNewUpvoteRecord(recordBO);
    }

    @PostMapping("/report/list")
    public RspList getUpvoteRecordList(@RequestBody SysUpvoteRecordBO recordBO) {
        return sysUpvoteRecordApi.getUpvoteRecordList(recordBO);
    }
}
