package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.TenantApplyApi;
import com.tydic.nbchat.admin.api.bo.TenantApplyBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@RestController
@RequestMapping("/admin/tenant/apply")
public class TenantApplyController {

    TenantApplyApi tenantApplyApi;

    TenantApplyController(TenantApplyApi tenantApplyApi){
        this.tenantApplyApi = tenantApplyApi;
    }

    @PostMapping("save")
    public Rsp save(@RequestBody TenantApplyBO request){
        return tenantApplyApi.save(request);
    }

    @PostMapping("query")
    public RspList query(@RequestBody TenantApplyBO request){
        return tenantApplyApi.query(request);
    }
}
