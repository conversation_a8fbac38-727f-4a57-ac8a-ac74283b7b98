package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.bo.menu.*;
import com.tydic.nbchat.admin.api.menu.SysMenuTemplateApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/menu")
public class SysMenuTemplateController {
    private final SysMenuTemplateApi sysMenuTemplateApi;

    public SysMenuTemplateController(SysMenuTemplateApi sysMenuTemplateApi) {
        this.sysMenuTemplateApi = sysMenuTemplateApi;
    }

    @PostMapping("/templates")
    public RspList templates(@RequestBody SysMenuTplQueryReqBO queryReqBO) {
        return sysMenuTemplateApi.getTemplates(queryReqBO);
    }

    @PostMapping("/template")
    public Rsp getTemplate(@RequestBody SysMenuTplQueryReqBO queryReqBO) {
        return sysMenuTemplateApi.getTemplate(queryReqBO);
    }

    @PostMapping("/template/save")
    public Rsp saveTpl(@RequestBody SysMenuTplSaveReqBO saveReqBO) {
        return sysMenuTemplateApi.saveTemplate(saveReqBO);
    }

    @PostMapping("/template/delete")
    public Rsp deleteTpl(@RequestBody SysMenuTplSaveReqBO saveReqBO) {
        return sysMenuTemplateApi.deleteTemplate(saveReqBO.getTplCode());
    }



}
