package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.tydic.nbchat.admin.api.NbchatSysTenantApi;
import com.tydic.nbchat.admin.api.bo.*;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryReqBO;
import com.tydic.nbchat.admin.api.bo.eum.TenantUserStatusType;
import com.tydic.nbchat.admin.core.busi.SysMenuBusiService;
import com.tydic.nbchat.admin.core.busi.SysTenantUserBuisService;
import com.tydic.nbchat.admin.mapper.NbchatSysTenantMapper;
import com.tydic.nbchat.admin.mapper.NbchatSysUserTenantMapper;
import com.tydic.nbchat.admin.mapper.SysTenantSubsystemMapper;
import com.tydic.nbchat.admin.mapper.po.*;
import com.tydic.nbchat.user.api.UserService;
import com.tydic.nbchat.user.api.UserSettingsApi;
import com.tydic.nbchat.user.api.bo.UserBO;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.SysPlatformDefine;
import com.tydic.nbchat.user.api.bo.setting.UserSettingFlashReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserBalanceRechargeReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserBillRecordQueryReqBO;
import com.tydic.nbchat.user.api.bo.trade.UserTradeResult;
import com.tydic.nbchat.user.api.trade.TradeBalanceApi;
import com.tydic.nbchat.user.api.trade.TradeBillRecordApi;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class NbchatSysTenantServiceImpl implements NbchatSysTenantApi {

    private final NbchatSysTenantMapper nbchatSysTenantMapper;
    private final NbchatSysUserTenantMapper nbchatSysUserTenantMapper;
    private final SysMenuBusiService sysMenuBusiService;
    private final SysTenantUserBuisService sysTenantUserBuisService;
    private final SysTenantSubsystemMapper sysTenantSubsystemMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
    private UserService userService;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private TradeBalanceApi tradeBalanceApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
    private TradeBillRecordApi tradeBillRecordApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 100000)
    private UserSettingsApi userSettingsApi;

    public NbchatSysTenantServiceImpl(NbchatSysTenantMapper nbchatSysTenantMapper,
                                      NbchatSysUserTenantMapper nbchatSysUserTenantMapper,
                                      SysMenuBusiService sysMenuBusiService,
                                      SysTenantUserBuisService sysTenantUserBuisService,
                                      SysTenantSubsystemMapper sysTenantSubsystemMapper) {
        this.nbchatSysTenantMapper = nbchatSysTenantMapper;
        this.nbchatSysUserTenantMapper = nbchatSysUserTenantMapper;
        this.sysMenuBusiService = sysMenuBusiService;
        this.sysTenantUserBuisService = sysTenantUserBuisService;
        this.sysTenantSubsystemMapper = sysTenantSubsystemMapper;
    }

    @Override
    public Rsp<SysTenantBO> getTenant(BaseInfo reqBO) {
        NbchatSysTenant tenant = nbchatSysTenantMapper.selectByPrimaryKey(reqBO.getTenantCode());
        if (tenant != null && EntityValidType.NORMAL.getCode().equals(tenant.getIsValid())) {
            SysTenantBO sysTenantBO = new SysTenantBO();
            BeanUtils.copyProperties(tenant, sysTenantBO);

            // 获取租户关联的子系统
            List<SysTenantSubsystemRspBo> subsystemRspList = new ArrayList<>();
            String tenantCode = reqBO.getTenantCode();
            String platformCode = reqBO.getPlatformCode();
            List<SysTenantSubsystem> subsystemList = sysTenantSubsystemMapper
                    .selectByTenantCodeAndSubsystem(tenantCode, platformCode);
            for (SysTenantSubsystem subsystem : subsystemList) {
                SysTenantSubsystemRspBo subsystemRspBo = new SysTenantSubsystemRspBo();
                BeanUtils.copyProperties(subsystem, subsystemRspBo);
                subsystemRspList.add(subsystemRspBo);
            }
            sysTenantBO.setSubsystems(subsystemRspList);
            // 检查子系统中的tplCode
            String tplCodeToUse = tenant.getTplCode();
            boolean tplCodeFoundInSubsystems = false;
            for (SysTenantSubsystem subsystem : subsystemList) {
                if (subsystem.getTplCode() != null && !subsystem.getTplCode().isEmpty()) {
                    tplCodeToUse = subsystem.getTplCode();
                    tplCodeFoundInSubsystems = true;
                    break;
                }
            }
            // 获取子系统菜单或租户菜单
            if (tplCodeFoundInSubsystems) {
                sysTenantBO.setMenus(sysMenuBusiService.getMenusByTplCode(tplCodeToUse));
            } else {
                sysTenantBO.setMenus(sysMenuBusiService.getMenusByTplCode(tenant.getTplCode()));
            }
            return BaseRspUtils.createSuccessRsp(sysTenantBO);
        }
        return BaseRspUtils.createErrorRsp("租户不存在");
    }

    /**
     * 将用户加入到租户下
     *
     * @param @param bindUserReqBO 结合用户要求博
     * @return @return {@link Rsp }
     */
    @Override
    @MethodParamVerifyEnable
    public Rsp addUsers(SysTenantOptUserReqBO bindUserReqBO) {
        try {
            return sysTenantUserBuisService.addUsers(bindUserReqBO);
        } catch (Exception e) {
            log.error("用户加入租户失败: {}", bindUserReqBO, e);
            return BaseRspUtils.createErrorRsp("用户加入租户失败");
        }
    }


    @Override
    public Rsp userRecharge(SysTenantUserRechargeReqBO rechargeReqBO) {
        log.info("后台充值: {}", rechargeReqBO);
        if (rechargeReqBO.getScore() <= 0) {
            return BaseRspUtils.createErrorRsp("充值数额不能小于0");
        }
        UserBalanceRechargeReqBO recharge = new UserBalanceRechargeReqBO();
        recharge.setUserId(rechargeReqBO.getTargetUid());
        recharge.setTenantCode(rechargeReqBO.getTargetTenant());
        recharge.setScore(rechargeReqBO.getScore());
        recharge.setExpireTime(rechargeReqBO.getExpireTime());
        recharge.setRemark(rechargeReqBO.getRemark());
        if (StringUtils.isBlank(recharge.getRemark())) {
            recharge.setRemark("后台充值");
        }
        if (rechargeReqBO.getDays() != null && rechargeReqBO.getDays() > 0) {
            Date date = DateTimeUtil.DateAddDayOfYear(rechargeReqBO.getDays());
            Date exp = DateTimeUtil.createTime(date, 23, 59, 59);
            recharge.setExpireTime(exp);
        }
        Rsp<UserTradeResult> rsp = tradeBalanceApi.recharge(recharge);
        if (rsp.isSuccess()) {
            return BaseRspUtils.createSuccessRsp("充值成功");
        }
        return BaseRspUtils.createErrorRsp("充值失败: " + rsp.getRspDesc());
    }


    @Override
    public Rsp userBalance(SysTenantUserBalanceReqBO balanceReqBO) {
        //判断用户是否为平台管理员权限
        log.info("用户余额查询: {}|{}", balanceReqBO, balanceReqBO.getRoleCodes());
        return tradeBalanceApi.getBalance(balanceReqBO.getTargetTenant(), balanceReqBO.getTargetUid());
    }


    @Override
    public RspList userBillList(SysTenantUserBillReqBO balanceReqBO) {
        //判断用户是否为平台管理员权限
        log.info("用户账单查询: {}|{}", balanceReqBO, balanceReqBO.getRoleCodes());
        UserBillRecordQueryReqBO queryReqBO = new UserBillRecordQueryReqBO();
        queryReqBO.setTenantCode(balanceReqBO.getTargetTenant());
        queryReqBO.setUserId(balanceReqBO.getTargetUid());
        queryReqBO.setPage(balanceReqBO.getPage());
        queryReqBO.setPayType(balanceReqBO.getPayType());
        queryReqBO.setLimit(balanceReqBO.getLimit());
        return tradeBillRecordApi.getBillRecord(queryReqBO);
    }

    /**
     * 将用户从租户移除
     *
     * @param @param removeUserReqBO 删除用户要求博
     * @return @return {@link Rsp }
     */
    @Override
    @MethodParamVerifyEnable
    public Rsp removeUsers(SysTenantOptUserReqBO removeUserReqBO) {
        return sysTenantUserBuisService.removeUsers(removeUserReqBO);
    }

    /**
     * 查询租户列表
     *
     * @param @param reqBO 要求博
     * @return @return {@link RspList }
     */
    @Override
    public RspList getTenants(SysTenantQueryReqBO reqBO) {
        log.info("查询租户-分页查询租户列表: {}", reqBO);
        Page<NbchatSysTenant> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        List<SysTenantQueryRspBO> result = new ArrayList<>();
        nbchatSysTenantMapper.selectByCondition(packageCondition(reqBO));
        NiccCommonUtil.copyList(page.getResult(), result, SysTenantQueryRspBO.class);
        log.info("查询租户-分页查询租户列表-查询结果: {}", result);
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }


    private NbchatSysTenantSelectCondition packageCondition(SysTenantQueryReqBO reqBO) {
        NbchatSysTenantSelectCondition condition = new NbchatSysTenantSelectCondition();
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        condition.setTenantCode(reqBO.getTargetTenant());
        condition.setTenantName(reqBO.getTenantName());
        return condition;
    }

    /**
     * 查询租户下的所有用户
     * @param @param reqBO
     * @return @return {@link RspList }
     */
    @Override
    public RspList getTenantUsers(SysDeptUserQueryReqBO reqBO) {
        return sysTenantUserBuisService.getTenantUsers(reqBO);
    }

    /**
     * 查询用户所属的租户
     *
     * @param @param reqBO 要求博
     * @return @return {@link RspList }
     */
    @Override
    public RspList getUserTenants(SysTenantQueryReqBO reqBO) {
        List<SysTenantQueryRspBO> result = new ArrayList<>();
        log.info("查询租户-查询用户所属的租户: {}", reqBO);
        List<SysUserInTenant> list = nbchatSysTenantMapper.selectUserTenantList(reqBO.getUserId());
        NiccCommonUtil.copyList(list, result, SysTenantQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(result);
    }

    @Override
    public RspList<SysTenantQueryRspBO> selectTenantList(SysTenantQueryReqBO reqBO) {
        List<SysTenantQueryRspBO> result = new ArrayList<>();
        log.info("查询租户-分页查询租户列表：{}", reqBO);
        Page<NbchatSysTenant> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        nbchatSysTenantMapper.selectTenantList(packageCondition(reqBO));
        NiccCommonUtil.copyList(page.getResult(), result, SysTenantQueryRspBO.class);
        log.info("查询租户-分页查询租户列表-查询结果：{}", result);
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    @Override
    public Rsp getUserInfo(SysTenantOptUserReqBO reqBo) {
        NbchatSysUserTenant userTenant = nbchatSysUserTenantMapper.selectUserInfo(reqBo.getUserId(), reqBo.getTenantCode());
        return BaseRspUtils.createSuccessRsp(userTenant,"查询用户信息成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp updateUser(SysTenantUserInfoBO reqBo) {
        NbchatSysUserTenant userTenant = new NbchatSysUserTenant();
        BeanUtils.copyProperties(reqBo, userTenant);
        //更新用户信息
        int result = nbchatSysUserTenantMapper.updateUserInfo(userTenant);
        if (result > 0) {
            return BaseRspUtils.createSuccessRsp("更新用户信息成功");
        }
        return BaseRspUtils.createErrorRsp("更新用户信息失败");
    }

    @Override
    public Rsp disableUser(SysTenantUserInfoBO reqBo) {
        NbchatSysUserTenant sysUserTenant = nbchatSysUserTenantMapper.selectByPrimaryKey(reqBo.getId());
        if (sysUserTenant == null) {
            return BaseRspUtils.createErrorRsp("用户不存在");
        }
        NbchatSysUserTenant userTenant = new NbchatSysUserTenant();
        BeanUtils.copyProperties(reqBo, userTenant);
        String targetTenant = UserAttributeConstants.DEFAULT_TENANT_CODE;
        if (TenantUserStatusType.NORMAL.getCode().equals(reqBo.getUserStatus())){
            //在用户状态为禁用时，将用户切换到默认租户
            targetTenant = reqBo.getTenantCode();
        } else if (TenantUserStatusType.FROZEN.getCode().equals(reqBo.getUserStatus())) {
            log.info("用户状态为禁用，将用户切换到默认租户: {}", reqBo);
        }else {
            return BaseRspUtils.createErrorRsp("用户状态不合法");
        }
        nbchatSysUserTenantMapper.updateByPrimaryKeySelective(userTenant);
        //切换租户
        UserBO updateUser = new UserBO();
        updateUser.setTargetTenant(targetTenant);
        updateUser.setUserId(sysUserTenant.getUserId());
        updateUser.setUpdatedTime(new Date());
        userService.updateById(updateUser);

        //移除平台设置
        UserSettingFlashReqBO reqBO = new UserSettingFlashReqBO();
        reqBO.setUserId(sysUserTenant.getUserId());
        reqBO.setTenantCode(reqBo.getTenantCode());
        reqBO.setUserIds(Collections.singletonList(sysUserTenant.getUserId()));
        reqBO.setRemoveKeys(SysPlatformDefine.platformSet());
        userSettingsApi.flashSettings(reqBO);

        log.info("更新用户信息成功: {}", reqBo);
        return BaseRspUtils.createSuccessRsp("更新用户信息成功");
    }

    @Override
    public Rsp checkTenantUserLimitReached(SysDeptUserQueryReqBO reqBo) {
        //查询租户是否存在
        NbchatSysTenant nbchatSysTenant = nbchatSysTenantMapper.selectByPrimaryKey(reqBo.getTargetTenantCode());
        if (nbchatSysTenant == null) {
            log.error("将用户加入到租户下-租户不存在: {}", reqBo.getTargetTenantCode());
            return BaseRspUtils.createSuccessRsp(true);
        }
        int userCount = nbchatSysUserTenantMapper.selectUserCount(reqBo.getTargetTenantCode());
        //判断是否达到租户人数上限
        if (nbchatSysTenant.getUserLimit() != -1 && userCount >= nbchatSysTenant.getUserLimit()) {
            log.warn("将用户加入到租户下-企业用户人数已达上限: {}", reqBo.getTargetTenantCode());
            return BaseRspUtils.createSuccessRsp(true);
        }
        return BaseRspUtils.createSuccessRsp(false);
    }


}
