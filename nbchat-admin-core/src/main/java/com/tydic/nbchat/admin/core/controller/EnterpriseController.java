package com.tydic.nbchat.admin.core.controller;

import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.admin.core.config.properties.QichachaConfigProperties;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.http.RestApiHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/admin")
public class EnterpriseController {

    @Resource
    RestApiHelper restApiHelper;

    @Resource
    QichachaConfigProperties qichachaConfigProperties;

    @GetMapping("enterprise/list")
    public Rsp queryList(@RequestParam("name") String name) {
        String url = qichachaConfigProperties.getUrl();
        String key = qichachaConfigProperties.getKey();
        String secretKey = qichachaConfigProperties.getSecretKey();
        String timestamp = String.valueOf(new Date().getTime() / 1000);

        String token = this.getMD5(key, timestamp, secretKey);
        url = url.replace("NAME", name).replace("KEY", key);
        Map<String, String> params = new HashMap<>();
        HttpHeaders headers = new HttpHeaders();
        headers.add("Token", token);
        headers.add("Timespan", timestamp);
        String res = restApiHelper.get(url, "", null, headers, MediaType.APPLICATION_JSON);

        return BaseRspUtils.createSuccessRsp(JSON.parseObject(res));
    }

    public static void main(String[] args) {
        String key = "da40963b60bb443a9b6d2e832c28f221";
        String SecretKey = "24BCD6E4952D676842D9992A10E6DCA9";
        String timestamp = String.valueOf(new Date().getTime() / 1000);

        System.out.println(timestamp);
        System.out.println(getMD5(key, timestamp, SecretKey));

    }

    public static String getMD5(String key, String timestamp, String secretKey) {
        try {
            String input = key + timestamp + secretKey;
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : messageDigest) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString().toUpperCase();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }
}
