package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.SysPermissionOptionApi;
import com.tydic.nbchat.admin.api.bo.eum.PermissionRoleType;
import com.tydic.nbchat.admin.api.bo.permission.*;
import com.tydic.nbchat.admin.core.busi.SysDataPermissionBusiService;
import com.tydic.nbchat.admin.core.busi.SysTrainPermissionBusiService;
import com.tydic.nbchat.user.api.UserBaseInfoApi;
import com.tydic.nbchat.user.api.bo.user.UserBaseInfo;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
public class SysPermissionOptionServiceImpl implements SysPermissionOptionApi {

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
    private UserBaseInfoApi userBaseInfoApi;

    private final SysDataPermissionBusiService sysDataPermissionBusiService;
    private final SysTrainPermissionBusiService sysTrainPermissionBusiService;

    public SysPermissionOptionServiceImpl(SysDataPermissionBusiService sysDataPermissionBusiService,
                                          SysTrainPermissionBusiService sysTrainPermissionBusiService) {
        this.sysDataPermissionBusiService = sysDataPermissionBusiService;
        this.sysTrainPermissionBusiService = sysTrainPermissionBusiService;
    }


    @Override
    public Rsp<Integer> deleteDataPermission(String busiType, String busiId) {
        try {
            log.info("删除数据权限: {}|{}", busiType, busiId);
            int i = sysDataPermissionBusiService.deletePermission(busiType, busiId);
            return BaseRspUtils.createSuccessRsp(i);
        } catch (Exception e) {
            log.error("删除数据权限-异常: {}|{}", busiType, busiId, e);
            return BaseRspUtils.createErrorRsp(e.getMessage());
        }
    }


    @Override
    public Rsp<Integer> updateDataPermission(PermissionObjectUpdateBO update) {
        try {
            log.info("更新数据权限: {}", update);
            int i = sysDataPermissionBusiService.updatePermission(update);
            return BaseRspUtils.createSuccessRsp(i);
        } catch (Exception e) {
            log.error("更新数据权限-异常: {}", update, e);
            return BaseRspUtils.createErrorRsp(e.getMessage());
        }
    }


    @Override
    public Rsp<Integer> deleteTrainPermission(String busiType, String busiId) {
        try {
            log.info("删除业务权限: {}|{}", busiType, busiId);
            int i = sysTrainPermissionBusiService.deletePermission(busiType, busiId);
            return BaseRspUtils.createSuccessRsp(i);
        } catch (Exception e) {
            log.error("删除业务权限-异常: {}|{}", busiType, busiId, e);
            return BaseRspUtils.createErrorRsp(e.getMessage());
        }
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<Integer> updateTrainPermission(PermissionObjectUpdateBO update) {
        try {
            log.info("更新业务权限: {}", update);
            int i = sysTrainPermissionBusiService.updatePermission(update);
            return BaseRspUtils.createSuccessRsp(i);
        } catch (Exception e) {
            log.error("更新业务权限-异常: {}", update, e);
            return BaseRspUtils.createErrorRsp(e.getMessage());
        }
    }

    @Override
    public RspList<PermissionObject> getTrainPermObjects(String tenantCode, String busiType, String busiId) {
        List<PermissionObject> objects = sysTrainPermissionBusiService.queryTrainPermission(tenantCode, busiType, busiId);
        for (PermissionObject object : objects) {
            if (PermissionRoleType.user.name().equals(object.getType())) {
                try {
                    Rsp<UserBaseInfo> rsp = userBaseInfoApi.getByUserId(tenantCode, object.getObjId());
                    if (rsp.isSuccess()) {
                        object.setUserBaseInfo(rsp.getData());
                    }
                } catch (Exception e) {
                    log.error("查询用户信息异常: {}", object.getObjId(), e);
                }
            }
        }
        return BaseRspUtils.createSuccessRspList(objects);
    }

    @Override
    public RspList<PermissionObject> getDataPermObjects(String tenantCode, String busiType, String busiId) {
        List<PermissionObject> objects = sysDataPermissionBusiService.queryDataPermission(tenantCode, busiType, busiId);
        for (PermissionObject object : objects) {
            if (PermissionRoleType.user.name().equals(object.getType())) {
                try {
                    Rsp<UserBaseInfo> rsp = userBaseInfoApi.getByUserId(tenantCode, object.getObjId());
                    if (rsp.isSuccess()) {
                        object.setUserBaseInfo(rsp.getData());
                    }
                } catch (Exception e) {
                    log.error("查询用户信息异常: {}", object.getObjId(), e);
                }
            }
        }
        return BaseRspUtils.createSuccessRspList(objects);
    }

    @Override
    public Rsp<Set<String>> getUserDataPermission(@Validated UserPermissionQueryRequest request) {
        if (StringUtils.isAnyBlank(request.getTenantCode(), request.getBusiType(),request.getBusiId())) {
            return BaseRspUtils.createErrorRsp("参数错误");
        }
        List<PermissionObject> objects = sysDataPermissionBusiService.queryDataPermission(
                request.getTenantCode(),
                request.getBusiType(),
                request.getBusiId());
        return BaseRspUtils.createSuccessRsp(processPermission(request, objects));
    }

    @Override
    public Rsp<Set<String>> getUserTrainPermission(@Validated UserPermissionQueryRequest request) {
        if (StringUtils.isAnyBlank(request.getTenantCode(), request.getBusiType(),request.getBusiId())) {
            return BaseRspUtils.createErrorRsp("参数错误");
        }
        List<PermissionObject> objects = sysTrainPermissionBusiService.queryTrainPermission(
                request.getTenantCode(),
                request.getBusiType(),
                request.getBusiId());
        return BaseRspUtils.createSuccessRsp(processPermission(request, objects));
    }


    private Set<String> processPermission(UserPermissionQueryRequest request, List<PermissionObject> objects) {
        Set<String> permissions = new LinkedHashSet<>();
        if (objects != null && !objects.isEmpty()) {
            //判断有权限就加入到集合
            for (PermissionObject object : objects) {
                if (
                        (request.getUserId() != null && request.getUserId().equals(object.getObjId()))
                                ||
                                (request.getDeptId() != null && request.getDeptId().equals(object.getObjId()))
                                ||
                                (request.getRoleIds() != null && request.getRoleIds().contains(object.getObjId()))
                                ||
                                (request.getPostIds() != null && request.getPostIds().contains(object.getObjId()))

                ) {
                    permissions.add(object.getPermission());
                }
            }
        }
        return permissions;
    }

}
