package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.bo.menu.*;
import com.tydic.nbchat.admin.api.menu.SysMenuApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/menu")
public class SysMenuButtonController {
    private final SysMenuApi sysMenuApi;

    public SysMenuButtonController(SysMenuApi sysMenuApi) {
        this.sysMenuApi = sysMenuApi;
    }

    @PostMapping("/list")
    public RspList list(@RequestBody SysMenuQueryReqBO queryReqBO) {
        return sysMenuApi.getMenus(queryReqBO);
    }


    @PostMapping("/save")
    public Rsp save(@RequestBody SysMenuSaveReqBO reqBO) {
        return sysMenuApi.saveMenu(reqBO);
    }

    @PostMapping("/delete")
    public Rsp delete(@RequestBody SysButtonDeleteReqBO reqBO) {
        return sysMenuApi.deleteMenu(reqBO.getMenuCode());
    }

    @PostMapping("/button/save")
    public Rsp saveButton(@RequestBody SysButtonSaveReqBO reqBO) {
        return sysMenuApi.saveButton(reqBO);
    }

    @PostMapping("/button/delete")
    public Rsp deleteButton(@RequestBody SysButtonDeleteReqBO reqBO) {
        return sysMenuApi.deleteButton(reqBO);
    }


}
