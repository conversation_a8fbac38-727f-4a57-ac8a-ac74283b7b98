package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.NbchatArticleApi;
import com.tydic.nbchat.admin.api.bo.ArticleQueryReqBO;
import com.tydic.nbchat.admin.api.bo.SysDictConfigQueryReqBO;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/article")
public class ArticleController {
    final NbchatArticleApi nbchatArticleApi;

    public ArticleController(NbchatArticleApi nbchatArticleApi) {
        this.nbchatArticleApi = nbchatArticleApi;
    }


    @PostMapping("/list")
    public RspList getArticles(@RequestBody ArticleQueryReqBO reqBo) {
        return nbchatArticleApi.getArticles(reqBo);
    }
}
