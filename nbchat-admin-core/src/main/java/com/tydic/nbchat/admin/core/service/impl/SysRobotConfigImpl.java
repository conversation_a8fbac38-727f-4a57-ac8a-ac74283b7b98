package com.tydic.nbchat.admin.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.admin.api.SysRobotConfigApi;
import com.tydic.nbchat.admin.api.bo.eum.RobotConfigType;
import com.tydic.nbchat.admin.api.bo.robot.*;
import com.tydic.nbchat.admin.core.config.properties.NbchatAdminConfigProperties;
import com.tydic.nbchat.admin.mapper.SysTenantRobotConfigMapper;
import com.tydic.nbchat.admin.mapper.po.SysTenantRobotConfig;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
public class SysRobotConfigImpl implements SysRobotConfigApi {

    private final SysTenantRobotConfigMapper sysTenantRobotConfigMapper;
    private final NbchatAdminConfigProperties nbchatAdminConfigProperties;

    public SysRobotConfigImpl(SysTenantRobotConfigMapper sysTenantRobotConfigMapper,
                              NbchatAdminConfigProperties nbchatAdminConfigProperties) {
        this.sysTenantRobotConfigMapper = sysTenantRobotConfigMapper;
        this.nbchatAdminConfigProperties = nbchatAdminConfigProperties;
    }

    @Override
    public String getRobotValue(String tenantCode, String userId) {
        /**
         *  获取默认机器人配置
         * 1. 优先获取用户配置
         * 2. 用户配置不存在获取机器人配置
         * 3. 都不存在使用默认配置
         */
        SysTenantRobotConfig config = null;
        if (StringUtils.isNotBlank(userId)) {
            config = sysTenantRobotConfigMapper.selectConfig(RobotConfigType.USER.getCode(), userId);
            if (config != null) {
                return config.getConfigValue();
            }
        }
        if (StringUtils.isNotBlank(tenantCode)) {
            config = sysTenantRobotConfigMapper.selectConfig(RobotConfigType.TENANT.getCode(), tenantCode);
            if (config != null) {
                return config.getConfigValue();
            }
        }
        return nbchatAdminConfigProperties.getDefaultRobot();
    }


    @Override
    @MethodParamVerifyEnable
    public Rsp getRobotConfigList(SysRobotConfigQueryReqBO queryReqBO) {
        log.info("查询机器人配置:{}", queryReqBO);
        String targetUserId = queryReqBO.getTargetUserId();
        String targetTenantCode = queryReqBO.getTargetTenantCode();
        AtomicReference<SysRobotConfigQueryRspBO> rspBO = new AtomicReference<>(new SysRobotConfigQueryRspBO());
        if (StringUtils.isAllBlank(targetTenantCode, targetUserId) ||
                (queryReqBO.getUserId().equals(targetUserId) && queryReqBO.getTenantCode().equals(targetTenantCode))) {
            //1. 查询并集(使用当前登录用户注入参数查询)
            SysTenantRobotConfig userConfig = sysTenantRobotConfigMapper.selectConfig(RobotConfigType.USER.getCode(), queryReqBO.getUserId());
            SysTenantRobotConfig tenantConfig = sysTenantRobotConfigMapper.selectConfig(RobotConfigType.TENANT.getCode(), queryReqBO.getTenantCode());
            if (ObjectUtils.allNull(userConfig, tenantConfig)) {
                rspBO.get().setConfigValue(nbchatAdminConfigProperties.getDefaultRobot());
            } else {
                Set<SysRobotKvConfigBO> configSet = configList(userConfig, tenantConfig);
                rspBO.set(SysRobotConfigQueryRspBO.builder().
                        configList(configSet).build());
                String val = getRobotValue(queryReqBO.getTenantCode(), queryReqBO.getUserId());
                rspBO.get().setConfigValue(val);
            }
        } else {
            SysTenantRobotConfig trainRobotConfig;
            if (StringUtils.isNotBlank(targetUserId) && StringUtils.isBlank(targetTenantCode)) {
                //查询用户配置列表
                //校验是否管理员 TODO
                trainRobotConfig = sysTenantRobotConfigMapper.selectConfig(RobotConfigType.USER.getCode(), targetUserId);
            } else if (StringUtils.isNotBlank(targetTenantCode) && StringUtils.isBlank(targetUserId)) {
                //查询租户配置列表
                //校验是否管理员 TODO
                trainRobotConfig = sysTenantRobotConfigMapper.selectConfig(RobotConfigType.TENANT.getCode(), targetTenantCode);
                String val = getRobotValue(targetTenantCode, targetUserId);
            } else {
                return BaseRspUtils.createErrorRsp("参数异常!");
            }
            Optional.ofNullable(trainRobotConfig).ifPresent(obj -> {
                Set<SysRobotKvConfigBO> configSet = configList(trainRobotConfig);
                rspBO.set(SysRobotConfigQueryRspBO.builder().
                        configList(configSet).build());
                rspBO.get().setConfigValue(trainRobotConfig.getConfigValue());
            });
        }
        rspBO.get().setConfigName(sysTenantRobotConfigMapper.queryRobotName(rspBO.get().getConfigValue()));
        return BaseRspUtils.createSuccessRsp(rspBO.get());
    }

    @Override
    @MethodParamVerifyEnable
    public Rsp setRobotConfigByAdmin(SysRobotConfigAdminSetReqBO setReqBO) {
        log.info("设置机器人配置[管理员]:{}", setReqBO);
        if (StringUtils.isEmpty(setReqBO.getConfigValue()) && setReqBO.getConfigList().size() > 0) {
            setReqBO.setConfigValue(setReqBO.getConfigList().get(0));
        }
        SysTenantRobotConfig config = null;
        if (setReqBO.getConfigType().equals(RobotConfigType.USER.getCode())) {
            config = sysTenantRobotConfigMapper.selectConfig(RobotConfigType.USER.getCode(),
                    setReqBO.getConfigId());
        } else if (setReqBO.getConfigType().equals(RobotConfigType.TENANT.getCode())) {
            config = sysTenantRobotConfigMapper.selectConfig(RobotConfigType.TENANT.getCode(),
                    setReqBO.getConfigId());
        } else {
            return BaseRspUtils.createErrorRsp("参数异常!");
        }
        SysTenantRobotConfig SysTenantRobotConfig = new SysTenantRobotConfig();
        BeanUtils.copyProperties(setReqBO, SysTenantRobotConfig);
        SysTenantRobotConfig.setConfigList(JSONObject.toJSONString(setReqBO.getConfigList()));
        if (config != null) {
            SysTenantRobotConfig.setId(config.getId());
            SysTenantRobotConfig.setUpdateTime(new Date());
            sysTenantRobotConfigMapper.update(SysTenantRobotConfig);
        } else {
            if (StringUtils.isNotBlank(setReqBO.getConfigValue())){
                SysTenantRobotConfig.setCreateTime(new Date());
                sysTenantRobotConfigMapper.insertSelective(SysTenantRobotConfig);
            }
        }
        return BaseRspUtils.createSuccessRsp("配置成功");
    }

    @Override
    @MethodParamVerifyEnable
    public Rsp setRobotConfig(SysRobotConfigUserSetReqBO setReqBO) {
        log.info("设置机器人配置[用户]:{}", setReqBO);
        String userId = setReqBO.getUserId();
        if (RobotConfigType.USER.getCode().equals(setReqBO.getConfigType())) {
            SysTenantRobotConfig config = sysTenantRobotConfigMapper.selectConfig(RobotConfigType.USER.getCode(), userId);
            SysTenantRobotConfig SysTenantRobotConfig = new SysTenantRobotConfig();
            if (config != null) {
                SysTenantRobotConfig.setConfigValue(setReqBO.getConfigValue());
                SysTenantRobotConfig.setId(config.getId());
                SysTenantRobotConfig.setUpdateTime(new Date());
                sysTenantRobotConfigMapper.update(SysTenantRobotConfig);
            } else {
                SysTenantRobotConfig.setConfigList(JSONObject.toJSONString(setReqBO.getConfigList()));
                SysTenantRobotConfig.setConfigId(userId);
                SysTenantRobotConfig.setCreateTime(new Date());
                SysTenantRobotConfig.setConfigType(RobotConfigType.USER.getCode());
                SysTenantRobotConfig.setConfigValue(setReqBO.getConfigValue());
                sysTenantRobotConfigMapper.insertSelective(SysTenantRobotConfig);
            }
            return BaseRspUtils.createSuccessRsp("配置成功");
        }
        return BaseRspUtils.createErrorRsp("请勿非法操作");
    }

    /**
     * 机器人配置列表转换
     *
     * @param configArgs
     * @return
     */
    private Set<SysRobotKvConfigBO> configList(SysTenantRobotConfig... configArgs) {
        Set<SysRobotKvConfigBO> configSet = new LinkedHashSet<>();
        for (SysTenantRobotConfig config : configArgs) {
            Optional.ofNullable(config).ifPresent(obj -> {
                final List<String> list = JSONObject.parseArray(obj.getConfigList(), String.class);
                for (String val : list) {
                    SysRobotKvConfigBO trainRobotKvConfigBO = new SysRobotKvConfigBO();
                    trainRobotKvConfigBO.setRobotName(sysTenantRobotConfigMapper.queryRobotName(val));
                    trainRobotKvConfigBO.setRobotValue(val);
                    trainRobotKvConfigBO.setMaxChars(sysTenantRobotConfigMapper.queryRobotMaxChars(val));
                    configSet.add(trainRobotKvConfigBO);
                }
            });
        }
        return configSet;
    }

}
