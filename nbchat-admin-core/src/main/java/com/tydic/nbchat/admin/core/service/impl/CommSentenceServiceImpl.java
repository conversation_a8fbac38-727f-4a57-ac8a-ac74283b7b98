package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.bo.sentence.*;
import com.tydic.nbchat.admin.api.sentence.CommSentenceApi;
import com.tydic.nbchat.admin.core.busi.CommSentenceBusiService;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Service
public class CommSentenceServiceImpl implements CommSentenceApi {

    @Resource
    private CommSentenceBusiService commSentenceBusiService;

    @MethodParamVerifyEnable
    @Override
    public Rsp saveCommSentence(SaveCommSentenceReq req) {
        CommSentence entity = CommSentence.builder()
                .tenantCode(req.getTenantCode())
                .typeId(parseLong(req.getTypeId()))
                .content(req.getContent())
                .sortId(parseInt(req.getSortId()))
                .createUserId(req.get_userId())
                .contentType(req.getContentType())
                .contentTitle(req.getContentTitle())
                .classes(req.getClasses())
                .build();
        return commSentenceBusiService.saveCommSentence(entity);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp updateCommSentence(UpdateCommSentenceReq req) {
        CommSentence entity = CommSentence.builder()
                .sentenceId(req.getSentenceId())
                .content(req.getContent())
                .createUserId(req.get_userId())
                .contentTitle(req.getContentTitle())
                .build();
        return commSentenceBusiService.updateCommSentence(entity);

    }

    @MethodParamVerifyEnable
    @Override
    public RspList deleteCommSentence(DelCommSentenceReqBO req) {
        log.info("知识删除：{}", req);
        List<String> errorList = new ArrayList<>();
        for (String id : req.getSentenceIdList()) {
            CommSentence entity = CommSentence.builder()
                    .sentenceId(id)
                    .createUserId(req.get_userId())
                    .build();
            Rsp rsp = commSentenceBusiService.deleteCommSentence(entity);
            errorList.add("知识id:" + id + "删除" + (rsp.isSuccess() ? "成功" : "失败"));
            if(!rsp.isSuccess()){
                return BaseRspUtils.createErrorRspList(rsp.getRspDesc());
            }
        }
        return BaseRspUtils.createSuccessRspList(errorList);
    }

    @MethodParamVerifyEnable
    @Override
    public RspList getCommSentencePageList(QueryCommSentencePageListReqBO req) {
        return commSentenceBusiService.getCommSentencePageList(req);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp getKnowledgeContentById(QueryKnowledgeConReq req) {
        return commSentenceBusiService.getKnowledgeContentById(req);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp getShareUrl(ShareKnowledgeReqBO req) {
        return commSentenceBusiService.getShareUrl(req);
    }

    @Override
    public Rsp queryShare(ShareKnowledgeReqBO reqBO) {
        return commSentenceBusiService.queryShare(reqBO);
    }

    @Override
    public Rsp checkShareKey(ShareKnowledgeReqBO reqBO) {
        return commSentenceBusiService.checkShareKey(reqBO);
    }


    private Short parseShort(String value) {
        return StringUtils.isNotEmpty(value) ? Short.parseShort(value) : null;
    }

    private Integer parseInt(String value) {
        return StringUtils.isNotEmpty(value) ? Integer.parseInt(value) : null;
    }

    private Long parseLong(String value) {
        return StringUtils.isNotEmpty(value) ? Long.parseLong(value) : null;
    }
}
