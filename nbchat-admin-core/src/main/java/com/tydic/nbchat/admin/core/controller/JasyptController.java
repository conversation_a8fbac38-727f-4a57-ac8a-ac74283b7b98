package com.tydic.nbchat.admin.core.controller;

import lombok.extern.slf4j.Slf4j;
import org.jasypt.encryption.StringEncryptor;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.jasypt.iv.NoIvGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 加密
 */
@Slf4j
@RestController
@RequestMapping("/admin")
public class JasyptController {

    @Autowired
    private StringEncryptor encryptor;

    public void foo() {
        String ai1aAuZNcFxY = encryptor.encrypt("HFDhhjgrh7264");
        System.out.println("加密后密码： " + ai1aAuZNcFxY);
    }

    private static final String ENCRYPTION_PASSWORD = "nicc-salt_!@#";
    private static final String ALGORITHM = "PBEWithMD5AndDES";

    @GetMapping("/encrypt/{password}")
    public void authUser(@PathVariable("password") String password, HttpServletResponse response) {

        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
        encryptor.setPassword(ENCRYPTION_PASSWORD);
        encryptor.setAlgorithm(ALGORITHM);
        encryptor.setIvGenerator(new NoIvGenerator());

        String encryptedPassword = encryptor.encrypt(password);

        String body = "加密后密码：" + encryptedPassword + "<br/><br/><br/>" +
                "加密算法：" + ALGORITHM + "<br/>" +
                "加密盐：" + ENCRYPTION_PASSWORD + "<br/>" +
                "向量算法：" + new NoIvGenerator() +
                "<br/>" ;
        response.setContentType("text/html; charset=UTF-8");
        try {
            response.getWriter().write(body);
        } catch (IOException e) {
            log.error("Error writing response", e);
        }
    }
}
