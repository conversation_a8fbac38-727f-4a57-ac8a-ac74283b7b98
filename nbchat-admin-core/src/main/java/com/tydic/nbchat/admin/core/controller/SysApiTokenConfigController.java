package com.tydic.nbchat.admin.core.controller;


import com.tydic.nbchat.admin.api.SysTokenConfigApi;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenConfigBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenCreateReqBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenRefreshReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/token")
public class SysApiTokenConfigController {

    private final SysTokenConfigApi sysTokenConfigApi;


    @PostMapping("/config/create")
    public Rsp flashApiTokenConfig(@RequestBody SysApiTokenCreateReqBO reqBO) {
        return sysTokenConfigApi.createTokenConfig(reqBO);
    }

    @PostMapping("/refresh")
    public Rsp flashApiTokenConfig(@RequestBody SysApiTokenRefreshReqBO refreshReqBO) {
        return sysTokenConfigApi.refreshToken(refreshReqBO);
    }

    @PostMapping("/config")
    public Rsp<SysApiTokenConfigBO> getApiTokenConfig(@RequestBody Map<String,Object> params) {
        String appId = (String) params.get("appId");
        String accessKey = (String) params.get("accessKey");
        if (StringUtils.isNotBlank(appId)) {
            return sysTokenConfigApi.getApiTokenConfig(Integer.valueOf(appId));
        }
        if (accessKey != null) {
            return sysTokenConfigApi.getApiTokenConfig(accessKey);
        }
        return BaseRspUtils.createErrorRsp("参数错误");
    }

}
