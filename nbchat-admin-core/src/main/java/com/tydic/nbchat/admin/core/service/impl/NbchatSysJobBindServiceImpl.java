package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.NbchatSysJobBindApi;
import com.tydic.nbchat.admin.api.NbchatSysTenantApi;
import com.tydic.nbchat.admin.api.bo.NbchatSysJobNoBindInfo;
import com.tydic.nbchat.admin.api.bo.SysBindJobReqBO;
import com.tydic.nbchat.admin.api.bo.SysTenantOptUserReqBO;
import com.tydic.nbchat.admin.api.bo.SysTenantUserInfoBO;
import com.tydic.nbchat.admin.mapper.NbchatSysJobNoMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatSysJobNo;
import com.tydic.nbchat.user.api.UserService;
import com.tydic.nbchat.user.api.bo.UserBO;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NbchatSysJobBindServiceImpl implements NbchatSysJobBindApi {
    @Resource
    private NbchatSysJobNoMapper nbchatSysJobNoMapper;
    private final NbchatSysTenantApi nbchatSysTenantApi;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
    private UserService userService;

    public NbchatSysJobBindServiceImpl(NbchatSysTenantApi nbchatSysTenantApi) {
        this.nbchatSysTenantApi = nbchatSysTenantApi;
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp bindJobNo(SysBindJobReqBO reqBO) {
        log.info("绑定工号-请求参数：{}", reqBO);
        NbchatSysJobNo jobNo = nbchatSysJobNoMapper.selectJobNo(reqBO.getJobNo(), reqBO.getName());
        if (jobNo == null) {
            return BaseRspUtils.createErrorRsp("绑定失败：该工号不存在!");
        }
        if (EntityValidType.NORMAL.getCode().equals(jobNo.getBindStatus())) {
            log.warn("绑定工号-该工号已被绑定: {}|{}", reqBO.get_userId(), reqBO.getJobNo());
            return BaseRspUtils.createErrorRsp("绑定失败：该工号已被绑定!");
        }
        if (StringUtils.isBlank(jobNo.getTenantCode())) {
            return BaseRspUtils.createErrorRsp("绑定失败：该工号未绑定租户!");
        }
        int result = nbchatSysJobNoMapper.bindJobNo(jobNo.getId(), reqBO.get_userId());
        SysTenantOptUserReqBO bindUserReqBO = new SysTenantOptUserReqBO();
        bindUserReqBO.setTargetTenant(jobNo.getTenantCode());
        bindUserReqBO.setJoinType(JoinTenantType.ARTIFICIAL.getCode());
        List<SysTenantUserInfoBO> userInfos = Lists.newArrayList();
        SysTenantUserInfoBO userInfoBO = new SysTenantUserInfoBO();
        userInfoBO.setUserId(reqBO.get_userId());
        userInfoBO.setUserRealityName(reqBO.getName());
        userInfos.add(userInfoBO);
        bindUserReqBO.setUserInfos(userInfos);
        Rsp rsp = nbchatSysTenantApi.addUsers(bindUserReqBO);
        log.info("绑定工号-绑定租户: {}", rsp);
        if (!rsp.isSuccess()){
            return BaseRspUtils.createErrorRsp("绑定失败：切换租户失败!");
        }
        if(!reqBO.getTenantCode().equals(jobNo.getTenantCode())){
            //切换当前租户
            UserBO updateUser = new UserBO();
            updateUser.setTargetTenant(jobNo.getTenantCode());
            updateUser.setUserId(reqBO.get_userId());
            updateUser.setUpdatedTime(new Date());
            Rsp changeTenantRsp = userService.updateById(updateUser);
            log.info("绑定工号-切换租户: {}", changeTenantRsp);
        }
        return BaseRspUtils.createSuccessRsp(jobNo.getTenantCode(),"绑定成功");
    }

    @Override
    public Rsp bindJobNoStatus(BaseInfo reqBO) {
        /**
         * { "0000":"1", "0001":"0"}
         */
        List<NbchatSysJobNo> list = nbchatSysJobNoMapper.selectByUserId(reqBO.get_userId());
        //转换为map格式，每个租户对应的绑定状态
        Map<String, NbchatSysJobNoBindInfo> bindStatusMap = list.stream().collect(Collectors.toMap(NbchatSysJobNo::getTenantCode, item -> {
            NbchatSysJobNoBindInfo bindInfo = new NbchatSysJobNoBindInfo();
            bindInfo.setJobNo(item.getJobNo());
            bindInfo.setName(item.getName());
            bindInfo.setBindStatus(item.getBindStatus());
            return bindInfo;
        }));
        log.info("绑定工号状态-请求参数：{}，返回结果：{}", reqBO, bindStatusMap);
        return BaseRspUtils.createSuccessRsp(bindStatusMap);
    }
}
