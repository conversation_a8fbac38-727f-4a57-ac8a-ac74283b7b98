package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.bo.file.*;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.config.FileManageConfigPropertiesBean;
import com.tydic.nicc.framework.utils.MultipartFileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/")
public class FileUploadController {
    @Resource
    FileManageConfigPropertiesBean fileManageConfigProperties;

    private final FileManageService fileManageService;

    public FileUploadController(FileManageService fileManageService) {
        this.fileManageService = fileManageService;
    }

    /**
     * 创建下载链接
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/file/pre_get/create")
    public Rsp createGetUrl(@RequestBody @Validated PreDownloadUrlRequest request) {
        return fileManageService.createGetUrl(request);
    }

    @PostMapping(value = "/file/pre_put/create")
    public Rsp createPrePutUrl(@RequestBody PrePutUrlCreateRequest request) {
        return fileManageService.createPrePutUrl(request);
    }

    @PostMapping(value = "/file/pre_put/ok")
    public Rsp createPrePutUrl(@RequestBody PrePutUrlOkRequest request) {
        return fileManageService.createPrePutOk(request);
    }

    @PostMapping(value = "/file/delete")
    public Rsp delete(@RequestBody FileDeleteRequest request) {
        return fileManageService.fileDelete(request);
    }

    @GetMapping(value = "/file/proxy/{fileId}")
    public Rsp fileProxy(@PathVariable("fileId") String fileId, HttpServletResponse response) {
        Rsp<FileSaveBO> file = fileManageService.getFile(fileId);
        if (file.isSuccess()) {
            FileSaveBO data = file.getData();
            if (data != null) {
                String url = data.getAccessUrl();
                if (StringUtils.isNotBlank(url)) {
                    try {
                        URL urlObj = new URL(url);
                        HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
                        conn.setDoInput(true);
                        conn.setRequestMethod("GET");
                        conn.setConnectTimeout(5000);
                        conn.setReadTimeout(5000);
                        conn.connect();
                        BufferedInputStream bis = new BufferedInputStream(conn.getInputStream());
                        byte[] bytes = new byte[1024];
                        int len;
                        while ((len = bis.read(bytes)) != -1) {
                            response.getOutputStream().write(bytes, 0, len);
                        }
                        bis.close();
                        conn.disconnect();
                    } catch (Exception e) {
                        log.error("文件代理异常:{}", fileId, e);
                    }
                }
            }
        }
        return file;
    }


    @PostMapping(value = "/file/proxy/load")
    public Rsp fileProxyLoad(@RequestBody FileRemoteLoadRequest request) {
        try {
            return fileManageService.fileProxyLoad(request);
        } catch (IOException e) {
            log.error("文件导入-解析异常:{}|{}", request.getTenantCode(), request.getUserId(), e);
            return BaseRspUtils.createErrorRsp("文件加载失败:" + e.getMessage());
        }
    }

    @PostMapping(value = "/file/multipart/create")
    public Rsp<ChunkUploadCreateResponse> createMultipartUpload(@RequestBody ChunkUploadCreateRequest createRequest) {
        return fileManageService.createMultipartUpload(createRequest);
    }

    @PostMapping(value = "/file/multipart/complete")
    public Rsp<ChunkUploadCreateResponse> createMultipartUpload(@RequestBody ChunkUploadCompleteRequest completeRequest) {
        return fileManageService.completeMultipartUpload(completeRequest);
    }

    @PostMapping(value = "/file/upload")
    public RspList fileUpload(HttpServletRequest request, @RequestParam(value = "files") MultipartFile[] files) {
        if (files == null || files.length == 0) {
            return BaseRspUtils.createErrorRspList("请选择要上传的文件!");
        }
        String tenantCode = request.getParameter("tenantCode");
        String userId = request.getParameter("userId");
        String compress = request.getParameter("compress");
        String targetDir = request.getParameter("targetDir");
        String useOriginalName = request.getParameter("useOriginalName");
        String autoDir = request.getParameter("autoDir");
        String busiCode = request.getParameter("busiCode");
        //不用 cdn 地址，返回 oss原始地址
        String disableCdn = request.getParameter("disableCdn");
        try {
            FileUploadReqBO uploadReqBO = new FileUploadReqBO();
            uploadReqBO.setTenantCode(tenantCode);
            uploadReqBO.setUploadUser(userId);
            uploadReqBO.setFiles(files);
            uploadReqBO.setTargetDir(targetDir);
            uploadReqBO.setBusiCode(busiCode);
            if(StringUtils.isNotBlank(compress)){
                uploadReqBO.setCompress(Boolean.parseBoolean(compress));
            }
            if(StringUtils.isNotBlank(autoDir)){
                uploadReqBO.setAutoDir(Boolean.parseBoolean(autoDir));
            }
            if(StringUtils.isNotBlank(disableCdn)){
                uploadReqBO.setDisableCdn(Boolean.parseBoolean(disableCdn));
            }
            if(StringUtils.isNotBlank(useOriginalName)){
                uploadReqBO.setUseOriginalName(Boolean.parseBoolean(useOriginalName));
            }
            RspList<FileManageSaveBO> rspList =  fileManageService.fileUpload(uploadReqBO);
            if (uploadReqBO.isDisableCdn()) {
                String cdnUrl = fileManageConfigProperties.getAccessUrlPrefix();
                String gatewayUrl = fileManageConfigProperties.getGatewayUrlPrefix();
                for (FileManageSaveBO row : rspList.getRows()) {
                    row.setAccessUrl(row.getAccessUrl().replace(cdnUrl, gatewayUrl));
                }
            }
            return rspList;
        } catch (Exception e) {
            log.error("文件导入-解析异常:{}|{}",tenantCode,userId,e);
            return BaseRspUtils.createErrorRspList("文件上传失败:"+e.getMessage());
        }
    }


    @PostMapping(value = "/file/svg/upload")
    public RspList svgFileUpload(@RequestBody SvgSaveRequest request) {
        if (CollectionUtils.isEmpty(request.getSvgObjs())) {
            return BaseRspUtils.createErrorRspList("请选择要上传的文件!");
        }
        String tenantCode = request.getTenantCode();
        String userId = request.get_userId();
        String targetDir = "/tdh/creation";
        try {
            List<MultipartFile> fileList = new ArrayList<>();
            for (SvgSaveRequest svg : request.getSvgObjs()) {
                String content = svg.getContent();
                MultipartFile file = MultipartFileUtil.toMultipartFile(content.getBytes(StandardCharsets.UTF_8), svg.getSvgId() + ".svg");
                fileList.add(file);
            }
            MultipartFile[] files = fileList.toArray(new MultipartFile[0]);
            FileUploadReqBO uploadReqBO = new FileUploadReqBO();
            uploadReqBO.setTenantCode(tenantCode);
            uploadReqBO.setUploadUser(userId);
            uploadReqBO.setFiles(files);
            uploadReqBO.setTargetDir(targetDir);
            uploadReqBO.setBusiCode("svg_save");
            uploadReqBO.setAutoDir(false);
            uploadReqBO.setUseOriginalName(true);
            RspList<FileManageSaveBO> rspList = fileManageService.fileUpload(uploadReqBO);
            //TODO
            String cdnUrl = fileManageConfigProperties.getAccessUrlPrefix();
            String gatewayUrl = fileManageConfigProperties.getGatewayUrlPrefix();
            for (FileManageSaveBO row : rspList.getRows()) {
                row.setAccessUrl(row.getAccessUrl().replace(cdnUrl, gatewayUrl));
            }
            return rspList;
        } catch (Exception e) {
            log.error("svg文件保存异常:{}|{}", tenantCode, userId, e);
            return BaseRspUtils.createErrorRspList("文件上传失败:" + e.getMessage());
        }
    }


}
