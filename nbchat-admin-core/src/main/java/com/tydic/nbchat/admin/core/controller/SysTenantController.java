package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.NbchatSysTenantApi;
import com.tydic.nbchat.admin.api.NbchatSysTenantInviteApi;
import com.tydic.nbchat.admin.api.bo.*;
import com.tydic.nbchat.admin.api.bo.SysTenantOptUserReqBO;
import com.tydic.nbchat.admin.api.bo.SysTenantQueryReqBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryReqBO;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/tenant")
public class SysTenantController {
    private final NbchatSysTenantApi nbchatSysTenantApi;
    private final NbchatSysTenantInviteApi nbchatSysTenantInviteApi;

    public SysTenantController(NbchatSysTenantApi nbchatSysTenantApi,
                               NbchatSysTenantInviteApi nbchatSysTenantInviteApi) {
        this.nbchatSysTenantApi = nbchatSysTenantApi;
        this.nbchatSysTenantInviteApi = nbchatSysTenantInviteApi;
    }

    @PostMapping("/info")
    public Rsp tenantInfo(@RequestBody BaseInfo reqBo) {
        return nbchatSysTenantApi.getTenant(reqBo);
    }


    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin}, logical = Logical.OR)
    @PostMapping("/invite/try/create")
    public Rsp createTryInvite(@RequestBody SysTenantCreateInviteReqBO reqBo) {
        return nbchatSysTenantInviteApi.createTryInvite(reqBo);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin,
            UserRoleConstants.orgAdmin,
            UserRoleConstants.opUser}, logical = Logical.OR)
    @PostMapping("/invite/create")
    public Rsp createInvite(@RequestBody SysTenantCreateInviteReqBO reqBo) {
        return nbchatSysTenantInviteApi.createInvite(reqBo);
    }

    @PostMapping("/invite/user/add")
    public Rsp addUserInvite(@RequestBody SysTenantAddInviteReqBO reqBo) {
        return nbchatSysTenantInviteApi.addUserInvite(reqBo);
    }


    /**
     * 将用户加入到租户下
     *
     * @param @param reqBo 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/user/add")
    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin,
            UserRoleConstants.orgAdmin,
            UserRoleConstants.opUser}, logical = Logical.OR)
    public Rsp addUsers(@RequestBody SysTenantOptUserReqBO reqBo) {
        return nbchatSysTenantApi.addUsers(reqBo);
    }


    @PostMapping("/user/info")
    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin,
            UserRoleConstants.orgAdmin,
            UserRoleConstants.opUser}, logical = Logical.OR)
    public Rsp getUserInfo(@RequestBody SysTenantOptUserReqBO reqBo){
        return nbchatSysTenantApi.getUserInfo(reqBo);
    }


    @PostMapping("/user/update")
    public Rsp updateUser(@RequestBody SysTenantUserInfoBO reqBo){
        return nbchatSysTenantApi.updateUser(reqBo);
    }

    @PostMapping("/disable/user")
    @RequiresRole(value = {UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin,
            UserRoleConstants.orgAdmin}, logical = Logical.OR)
    public Rsp disableUser(@RequestBody SysTenantUserInfoBO reqBo){
        return nbchatSysTenantApi.disableUser(reqBo);
    }

    /**
     * 充值
     *
     * @param reqBo
     * @return
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin}, logical = Logical.OR)
    @PostMapping("/user/recharge")
    public Rsp recharge(@Validated @RequestBody SysTenantUserRechargeReqBO reqBo) {
        return nbchatSysTenantApi.userRecharge(reqBo);
    }

    /**
     * 平台查询余额
     *
     * @param reqBo
     * @return
     */
    @RequiresRole(value = {
            UserRoleConstants.sysAdmin,
            UserRoleConstants.tenantAdmin,
            UserRoleConstants.orgAdmin})
    @PostMapping("/user/balance")
    public Rsp userBalance(@RequestBody SysTenantUserBalanceReqBO reqBo) {
        return nbchatSysTenantApi.userBalance(reqBo);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin}, logical = Logical.OR)
    @PostMapping("/user/bill_list")
    public RspList billList(@RequestBody SysTenantUserBillReqBO reqBo) {
        return nbchatSysTenantApi.userBillList(reqBo);
    }

    /**
     * 将用户从租户移除
     *
     * @param @param reqBo 要求博
     * @return @return {@link Rsp }
     */
    @PostMapping("/user/remove")
//    @RequiresRole(value = {
//            UserRoleConstants.sysAdmin,
//            UserRoleConstants.tenantAdmin,
//            UserRoleConstants.orgAdmin,
//            UserRoleConstants.opUser}, logical = Logical.OR)
    public Rsp removeUsers(@RequestBody SysTenantOptUserReqBO reqBo) {
        return nbchatSysTenantApi.removeUsers(reqBo);
    }

    /**
     * 查询租户列表
     *
     * @param @param reqBo 要求博
     * @return @return {@link RspList }
     */
    @PostMapping("/list")
    public RspList getTenants(@RequestBody SysTenantQueryReqBO reqBo) {
        return nbchatSysTenantApi.getTenants(reqBo);
    }

    /**
     * 查询租户下的所有用户
     *
     * @param @param reqBo 要求博
     * @return @return {@link RspList }
     */
    @PostMapping("/users")
    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    public RspList getTenantUsers(@RequestBody SysDeptUserQueryReqBO reqBo) {
        return nbchatSysTenantApi.getTenantUsers(reqBo);
    }

    /**
     * 查询用户所属的租户
     *
     * @param @param reqBo 要求博
     * @return @return {@link RspList }
     */
    @PostMapping("/user-tenants")
    public RspList getUserTenants(@RequestBody SysTenantQueryReqBO reqBo) {
        return nbchatSysTenantApi.getUserTenants(reqBo);
    }

    /**
     * 检查租户用户数量是否达到上限
     * @param reqBo
     * @return
     */
    @PostMapping("/user/limit")
    public Rsp checkTenantUserLimitReached(@Validated @RequestBody SysDeptUserQueryReqBO reqBo){
        return nbchatSysTenantApi.checkTenantUserLimitReached(reqBo);
    }

}
