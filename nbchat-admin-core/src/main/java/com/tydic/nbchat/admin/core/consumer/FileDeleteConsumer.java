package com.tydic.nbchat.admin.core.consumer;

import com.tydic.nicc.framework.utils.FileManagerHelper;
import com.tydic.nicc.mq.starter.annotation.KKMqConsumer;
import com.tydic.nicc.mq.starter.api.KKMqConsumerListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@KKMqConsumer(
        consumerGroup = "NBCHAT_FILE_DELETE_CID",
        topic = "NBCHAT_FILE_DELETE_TOPIC")
@Component
public class FileDeleteConsumer implements KKMqConsumerListener<String> {

    private final FileManagerHelper fileManagerHelper;

    public FileDeleteConsumer(FileManagerHelper fileManagerHelper) {
        this.fileManagerHelper = fileManagerHelper;
    }

    @Override
    public void onMessage(String filePath) {
        log.info("文件删除通知: {}", filePath);
        try {
            //TODO
            //fileManagerHelper.delete(filePath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
