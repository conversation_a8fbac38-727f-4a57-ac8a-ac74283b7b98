package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysTreeCategoryApi;
import com.tydic.nbchat.admin.api.bo.category.SysTreeCategoryReqBO;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @datetime：2024/10/16 16:36
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/admin/sys")
public class SysTreeCategoryController {
    @Resource
    private SysTreeCategoryApi sysTreeCategoryApi;

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    @PostMapping("/category/tree")
    public RspList getCategoryTree(@RequestBody SysTreeCategoryReqBO reqBO) {
        return sysTreeCategoryApi.getCategoryTree(reqBO);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    @PostMapping("/category/list")
    public RspList getCategoryList(@RequestBody SysTreeCategoryReqBO reqBO) {
        return sysTreeCategoryApi.getCategoryList(reqBO);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    @PostMapping("/category/add")
    public Rsp addTreeCategory(@RequestBody SysTreeCategoryReqBO reqBO) {
        return sysTreeCategoryApi.addTreeCategory(reqBO);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    @PostMapping("/category/update")
    public Rsp updateTreeCategory(@RequestBody SysTreeCategoryReqBO reqBO) {
        return sysTreeCategoryApi.updateTreeCategory(reqBO);
    }

    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin, UserRoleConstants.opUser})
    @PostMapping("/category/delete")
    public Rsp deleteTreeCategory(@RequestBody SysTreeCategoryReqBO reqBO) {
        return sysTreeCategoryApi.deleteTreeCategory(reqBO.getCateId());
    }


}
