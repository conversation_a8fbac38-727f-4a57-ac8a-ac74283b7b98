package com.tydic.nbchat.admin.core.controller;

import com.tydic.nicc.framework.utils.FileManagerHelper;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

@RestController
@RequestMapping("/admin/")
public class FileProxyController {

    private final FileManagerHelper fileManagerHelper;

    public FileProxyController(FileManagerHelper fileManagerHelper) {
        this.fileManagerHelper = fileManagerHelper;
    }

    @GetMapping("/proxy/**")
    public ResponseEntity<InputStreamResource> proxyFile(HttpServletRequest request) {
        try {
            String requestURI = request.getRequestURI();
            String filePath = requestURI.substring(requestURI.indexOf("/proxy/") + 7);
            String decodedFileName = URLDecoder.decode(filePath, StandardCharsets.UTF_8);
            InputStream inputStream = fileManagerHelper.downloadFile(decodedFileName);
            InputStreamResource resource = new InputStreamResource(inputStream);
            HttpHeaders headers = new HttpHeaders();
            headers.add(HttpHeaders.CONTENT_DISPOSITION,
                    "inline; filename=" + Paths.get(decodedFileName).getFileName().toString());
            MediaType contentType = determineContentType(decodedFileName);
            return ResponseEntity.ok()
                    .headers(headers)
                    .contentType(contentType)
                    .body(resource);
        } catch (Exception e) {
            //提示404
            return ResponseEntity.notFound().build();
        }
    }

    private MediaType determineContentType(String fileName) {
        try {
            String mimeType = Files.probeContentType(Paths.get(fileName));
            return mimeType != null ? MediaType.parseMediaType(mimeType) : MediaType.APPLICATION_OCTET_STREAM;
        } catch (Exception e) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }
    }

}
