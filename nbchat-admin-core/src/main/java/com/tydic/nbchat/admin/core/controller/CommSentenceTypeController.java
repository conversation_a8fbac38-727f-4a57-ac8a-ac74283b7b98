package com.tydic.nbchat.admin.core.controller;


import com.tydic.nbchat.admin.api.bo.sentence.DelCommSentenceTypeReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.QueryCommSentenceTreeReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.SaveCommSentenceTypeReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.UpdateCommSentenceTypeReqBO;
import com.tydic.nbchat.admin.api.sentence.CommSentenceTypeApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin")
public class CommSentenceTypeController {

    private final CommSentenceTypeApi commSentenceTypeApi;

    public CommSentenceTypeController(CommSentenceTypeApi commSentenceTypeApi) {
        this.commSentenceTypeApi = commSentenceTypeApi;
    }

    @PostMapping("/knowledge/type/save")
    public Rsp saveCommSentenceType(@RequestBody SaveCommSentenceTypeReqBO reqBo) {
        return commSentenceTypeApi.saveCommSentenceType(reqBo);
    }
    @PostMapping("/knowledge/type/update")
    public Rsp updateCommSentenceType(@RequestBody UpdateCommSentenceTypeReqBO reqBo) {
        return commSentenceTypeApi.updateCommSentenceType(reqBo);
    }

    @PostMapping("/knowledge/type/del")
    public Rsp deleteCommSentenceType(@RequestBody DelCommSentenceTypeReqBO reqBO) {
        return commSentenceTypeApi.deleteCommSentenceType(reqBO);
    }

    @PostMapping("/knowledge/type/qryTreeData")
    public RspList queryCommSentenceTreeData(@RequestBody QueryCommSentenceTreeReqBO req) {
        return commSentenceTypeApi.queryCommSentenceTreeData(req);
    }

}
