package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.SysUserCooperateApi;
import com.tydic.nbchat.admin.api.bo.SysUserCooperateBO;
import com.tydic.nbchat.admin.core.utils.DingtalkUtil;
import com.tydic.nbchat.admin.mapper.SysUserCooperateMapper;
import com.tydic.nbchat.admin.mapper.po.SysUserCooperate;
import com.tydic.nbchat.user.api.bo.constants.RedisConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Slf4j
@Service
public class SysUserCooperateServiceImpl implements SysUserCooperateApi {

    @Resource
    private SysUserCooperateMapper sysUserCooperateMapper;
    @Resource
    private RedisHelper redisHelper;

    private static final DateTimeFormatter DATE_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp addNewUserCooperate(SysUserCooperateBO reqBo) {
        String phone = reqBo.getPhone();
        String smsCode = reqBo.getSmsCode();
        String key = RedisConstants.LOGIN_VERIFYCODE_PREFIX + phone;
        String savedCode = (String) redisHelper.get(key);

        if (StringUtils.isBlank(smsCode)) {
            return BaseRspUtils.createErrorRsp("请输入验证码");
        }
        if (savedCode == null || !savedCode.equals(smsCode)) {
            return BaseRspUtils.createErrorRsp("验证码错误或已过期");
        }

        try {
            SysUserCooperate request = buildRequest(reqBo, phone, smsCode);
            sysUserCooperateMapper.insert(request);
            sendDingNotification(reqBo, phone);
            return BaseRspUtils.createSuccessRsp("操作成功");
        } finally {
            redisHelper.del(key);
        }
    }

    private SysUserCooperate buildRequest(SysUserCooperateBO reqBo, String phone, String smsCode) {
        SysUserCooperate request = new SysUserCooperate();
        request.setPhone(phone);
        request.setName(reqBo.getName());
        request.setSmsCode(smsCode);
        request.setCreateTime(new Date());
        request.setStatus("0");
        request.setIsValid("1");
        return request;
    }

    private void sendDingNotification(SysUserCooperateBO reqBo, String phone) {
        try {
            StringBuilder message = new StringBuilder()
                    .append("用户合作代理数据\n")
                    .append("申请人: ").append(reqBo.getName()).append("\n")
                    .append("手机号: ").append(phone).append("\n")
                    .append("申请时间: ").append(LocalDateTime.now().format(DATE_FORMATTER));
            DingtalkUtil.sendMessageWebhook(message.toString(), "tdh-portal");
        } catch (Exception e) {
            log.error("钉钉推送失败，手机号: {}", phone, e);
            throw new RuntimeException("消息推送失败", e); // 触发事务回滚
        }
    }
}
