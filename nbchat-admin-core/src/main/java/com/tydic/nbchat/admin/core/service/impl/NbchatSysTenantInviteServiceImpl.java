package com.tydic.nbchat.admin.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.admin.api.NbchatSysTenantApi;
import com.tydic.nbchat.admin.api.NbchatSysTenantInviteApi;
import com.tydic.nbchat.admin.api.bo.*;
import com.tydic.nbchat.admin.api.bo.eum.TenantUserStatusType;
import com.tydic.nbchat.admin.api.bo.redis.TenantInviteRedisEntity;
import com.tydic.nbchat.admin.core.busi.SysTenantUserBuisService;
import com.tydic.nbchat.admin.core.config.properties.NbchatAdminConfigProperties;
import com.tydic.nbchat.admin.mapper.NbchatSysTenantMapper;
import com.tydic.nbchat.admin.mapper.NbchatSysUserTenantMapper;
import com.tydic.nbchat.admin.mapper.SysDeptMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatSysTenant;
import com.tydic.nbchat.admin.mapper.po.NbchatSysUserTenant;
import com.tydic.nbchat.admin.mapper.po.SysDept;
import com.tydic.nbchat.user.api.UserService;
import com.tydic.nbchat.user.api.bo.UserBO;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.net.URI;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class NbchatSysTenantInviteServiceImpl implements NbchatSysTenantInviteApi {

    private final NbchatAdminConfigProperties nbchatAdminConfigProperties;
    private final RedisHelper redisHelper;
    private final NbchatSysTenantApi nbchatSysTenantApi;
    private final SysTenantUserBuisService sysTenantUserBuisService;
    private final NbchatSysUserTenantMapper nbchatSysUserTenantMapper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
    private UserService userService;

    @Resource
    private NbchatSysTenantMapper nbchatSysTenantMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;

    public final static String NBCHAT_ADMIN_TENANT_INVITE_KEY = "nbchat-admin:tenant:invite:";


    public NbchatSysTenantInviteServiceImpl(NbchatAdminConfigProperties nbchatAdminConfigProperties,
                                            RedisHelper redisHelper,
                                            NbchatSysTenantApi nbchatSysTenantApi,
                                            SysTenantUserBuisService sysTenantUserBuisService,
                                            NbchatSysUserTenantMapper nbchatSysUserTenantMapper) {
        this.nbchatAdminConfigProperties = nbchatAdminConfigProperties;
        this.redisHelper = redisHelper;
        this.nbchatSysTenantApi = nbchatSysTenantApi;
        this.sysTenantUserBuisService = sysTenantUserBuisService;
        this.nbchatSysUserTenantMapper = nbchatSysUserTenantMapper;
    }

    @Override
    public Rsp createTryInvite(SysTenantCreateInviteReqBO createInviteReqBO) {
        log.info("创建试用邀请链接，请求参数：{}", createInviteReqBO);
        NbchatSysTenant tenant = nbchatSysTenantMapper.selectByPrimaryKey(createInviteReqBO.getTenantCode());
        if (tenant != null) {
            String inviteKey = NiccCommonUtil.createImUserId(true);
            String key = NBCHAT_ADMIN_TENANT_INVITE_KEY + inviteKey;
            TenantInviteRedisEntity cache = TenantInviteRedisEntity.builder().
                    targetTenant(createInviteReqBO.getTenantCode())
                    .deptId(createInviteReqBO.getDeptId()).build();
            redisHelper.set(key, cache, nbchatAdminConfigProperties.getTenantInviteExpireTime());
            SysTenantCreateInviteRspBO inviteRspBO = SysTenantCreateInviteRspBO.builder().build();
            inviteRspBO.setInviteKey(inviteKey);
            inviteRspBO.setTenantCode(tenant.getTenantCode());
            inviteRspBO.setTenantName(tenant.getTenantName());
            URI uri = UriComponentsBuilder.fromUriString(nbchatAdminConfigProperties.getTenantInviteTryUrl()).build(inviteKey);
            inviteRspBO.setInviteUrl(uri.toString());
            log.info("创建试用邀请链接，结果：{}", inviteRspBO);
            return BaseRspUtils.createSuccessRsp(inviteRspBO);
        }
        return BaseRspUtils.createErrorRsp("租户不存在");
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp createInvite(SysTenantCreateInviteReqBO createInviteReqBO) {
        /**
         * 1. 创建邀请token 写入redis： key:token value:teantCode
         * 2. 生成邀请链接
         */
        log.info("创建邀请链接，请求参数：{}", createInviteReqBO);
        String targetTenant = createInviteReqBO.getTenantCode();
        if (StringUtils.isNotBlank(createInviteReqBO.getTargetTenant())) {
            targetTenant = createInviteReqBO.getTargetTenant();
        }
        NbchatSysTenant tenant = nbchatSysTenantMapper.selectByPrimaryKey(targetTenant);
        if (tenant != null) {
            //校验是否管理员权限
            String inviteKey = NiccCommonUtil.createImUserId(true);
            String key = NBCHAT_ADMIN_TENANT_INVITE_KEY + inviteKey;
            TenantInviteRedisEntity cache = TenantInviteRedisEntity.builder().targetTenant(targetTenant)
                    .deptId(createInviteReqBO.getDeptId()).build();
            redisHelper.set(key, cache, nbchatAdminConfigProperties.getTenantInviteExpireTime());
            SysTenantCreateInviteRspBO inviteRspBO = SysTenantCreateInviteRspBO.builder().build();
            inviteRspBO.setInviteKey(inviteKey);
            inviteRspBO.setTenantCode(tenant.getTenantCode());
            inviteRspBO.setTenantName(tenant.getTenantName());
            inviteRspBO.setImgAvatar(tenant.getImgAvatar());
            URI uri = UriComponentsBuilder.fromUriString(nbchatAdminConfigProperties.getTenantInviteUrl()).build(inviteKey,tenant.getTenantName(),tenant.getTenantCode());
            inviteRspBO.setInviteUrl(uri.toString());

            String deptFullPath = sysDeptMapper.queryDeptFullPath(createInviteReqBO.getDeptId());
            Integer expireTime = nbchatAdminConfigProperties.getTenantInviteExpireTime();
            Date date = DateTimeUtil.DateAddSecond(expireTime);
            inviteRspBO.setExpireTime(date);
            inviteRspBO.setDeptName(deptFullPath);
            log.info("创建邀请链接，结果：{}", inviteRspBO);
            return BaseRspUtils.createSuccessRsp(inviteRspBO);

        }
        return BaseRspUtils.createErrorRsp("租户不存在");
    }


    @MethodParamVerifyEnable
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp addUserInvite(SysTenantAddInviteReqBO request) {
        /**
         * 1. 校验邀请token是否有效
         * 2. 将用户添加到租户
         * 3. 如果用户是非公共租户，将用户切换到该租户
         */
        log.info("邀请码添加用户，请求参数：{}", request);
        String inviteKey = request.getInviteKey();
        String key = NBCHAT_ADMIN_TENANT_INVITE_KEY + inviteKey;
        TenantInviteRedisEntity cache = (TenantInviteRedisEntity) redisHelper.get(key);
        if (ObjectUtils.isEmpty(cache)) {
            return BaseRspUtils.createErrorRsp("该邀请码已失效！");
        }
        String targetTenant = cache.getTargetTenant();
        NbchatSysTenant tenant = nbchatSysTenantMapper.selectByPrimaryKey(targetTenant);
        if (request.getTenantCode().equals(targetTenant)) {
            if (request.get_deptId().equals(cache.getDeptId())) {
                return BaseRspUtils.createSuccessRsp(targetTenant, "绑定成功: 未更新租户");
            }
            //确认切换租户、部门
            if ("1".equals(request.getIsChangeDept())) {
                this.addUser(request, targetTenant, cache.getDeptId());
            } else {
                SysDept sysDept = sysDeptMapper.queryById(cache.getDeptId());
                JSONObject object = new JSONObject();
                object.put("inviteDeptId",cache.getDeptId());
                object.put("deptName",sysDept.getDeptName());
                object.put("sameDept",false); //与邀请部门不一致
                return BaseRspUtils.createSuccessRsp(object, "与邀请部门不一致，确认是否切换");
            }
        } else if (!sysTenantUserBuisService.existInTenant(request.getUserId(), targetTenant)) {
            //新用户拉入租户、部门
            this.addUser(request, targetTenant, cache.getDeptId());
        }
        NbchatSysUserTenant userTenant = nbchatSysUserTenantMapper.selectByUserIdAndTenantCode(request.getUserId(), targetTenant);
        //切换当前租户
        if (!UserAttributeConstants.DEFAULT_TENANT_CODE.equals(targetTenant) &&
                TenantUserStatusType.NORMAL.getCode().equals(userTenant.getUserStatus())) {
            UserBO updateUser = new UserBO();
            updateUser.setTargetTenant(targetTenant);
            updateUser.setUserId(request.getUserId());
            updateUser.setUpdatedTime(new Date());
            userService.updateById(updateUser);
            return BaseRspUtils.createSuccessRsp(targetTenant, "绑定成功！");
        }
        log.info("邀请码添加用户，绑定不成功，用户已被禁用或已在租户中，用户状态：{}", userTenant.getUserStatus());
        return BaseRspUtils.createErrorRsp(tenant.getTenantName() + "租户下不存在该学员，无法使用学员端。");
    }

    public void addUser(SysTenantAddInviteReqBO request,String targetTenant,String deptId){
        SysTenantOptUserReqBO bindUserReqBO = new SysTenantOptUserReqBO();
        bindUserReqBO.setTargetTenant(targetTenant);
        bindUserReqBO.setJoinType(JoinTenantType.INVITATION_CODE.getCode());
        bindUserReqBO.setDeptId(deptId);
        List<SysTenantUserInfoBO> userInfos = Lists.newArrayList();
        SysTenantUserInfoBO userInfoBO = new SysTenantUserInfoBO();
        userInfoBO.setUserId(request.getUserId());
        userInfoBO.setUserRealityName(request.getUserRealityName());
        userInfoBO.setAvatar(request.getAvatar());
        userInfoBO.setGender(request.getGender());
        userInfoBO.setIdCard(request.getIdCard());
        userInfoBO.setPostIds(request.getPostIds());
        userInfoBO.setBirthday(request.getBirthday());
        userInfos.add(userInfoBO);
        bindUserReqBO.setUserInfos(userInfos);
        Rsp rsp = sysTenantUserBuisService.addUsers(bindUserReqBO);
        log.info("邀请码添加用户，结果：{}", rsp);
    }

}
