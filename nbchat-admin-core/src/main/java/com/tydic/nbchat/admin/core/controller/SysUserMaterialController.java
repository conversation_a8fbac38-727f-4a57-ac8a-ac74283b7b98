package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysUserMaterialApi;
import com.tydic.nbchat.admin.api.bo.SysUserMaterialReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/user/material")
public class SysUserMaterialController {
    private final SysUserMaterialApi sysUserMaterialApi;

    public SysUserMaterialController(SysUserMaterialApi sysUserMaterialApi) {
        this.sysUserMaterialApi = sysUserMaterialApi;
    }

    @PostMapping("/save")
    public Rsp saveUserMaterial(@RequestBody SysUserMaterialReqBO reqBO) {
        return sysUserMaterialApi.save(reqBO);
    }

    @PostMapping("/list")
    public RspList list(@RequestBody SysUserMaterialReqBO reqBO) {
        return sysUserMaterialApi.list(reqBO);
    }

    @PostMapping("/delete")
    public Rsp deleteUserMaterial(@RequestBody SysUserMaterialReqBO reqBO) {
        return sysUserMaterialApi.delete(reqBO);
    }
}
