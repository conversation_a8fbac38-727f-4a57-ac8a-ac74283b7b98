package com.tydic.nbchat.admin.core.utils;

public class StringUtil {

    /**
     * 计算字符串的字数。
     * 对于英文，按空格拆分为单词计数；
     * 对于中文，统计汉字的数量，不包括标点和空格。
     *
     * @param input 输入的字符串
     * @return 字数统计
     */
    public static int getWordCount(String input) {
        if (input == null || input.trim().isEmpty()) {
            return 0;
        }

        // 判断是否包含中文字符
        if (input.matches(".*\\p{IsHan}.*")) {
            // 中文环境下，统计汉字数量
            int count = 0;
            for (char c : input.toCharArray()) {
                if (Character.toString(c).matches("\\p{IsHan}")) {
                    count++;
                }
            }
            return count;
        } else {
            // 英文环境下，按空格拆分单词计数
            String[] words = input.trim().split("\\s+");
            return words.length;
        }
    }
}
