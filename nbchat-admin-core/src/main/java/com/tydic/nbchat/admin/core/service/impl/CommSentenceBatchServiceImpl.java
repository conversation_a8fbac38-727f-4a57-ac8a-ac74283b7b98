package com.tydic.nbchat.admin.core.service.impl;


import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.admin.api.bo.file.FileUploadReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.CommSentence;
import com.tydic.nbchat.admin.api.bo.sentence.constants.OlConstants;
import com.tydic.nbchat.admin.api.bo.sentence.xlsx.ExportCommSentReqBo;
import com.tydic.nbchat.admin.api.bo.sentence.xlsx.ImportCommSentReqBO;
import com.tydic.nbchat.admin.api.fileMannager.FileManageService;
import com.tydic.nbchat.admin.api.sentence.CommSentenceBatchApi;
import com.tydic.nbchat.admin.core.busi.CommSentenceBusiService;
import com.tydic.nbchat.admin.core.config.properties.NbchatAdminConfigProperties;
import com.tydic.nbchat.admin.core.config.utils.excel.ImportCommErrorRsp;
import com.tydic.nbchat.admin.core.config.utils.excel.ImportCommentSentError;
import com.tydic.nbchat.admin.core.config.utils.excel.ImportSentenceDTO;
import com.tydic.nbchat.admin.core.config.utils.excel.listener.ImportCommSentListener;
import com.tydic.nbchat.admin.mapper.KnowledgeClassifyMapper;
import com.tydic.nbchat.admin.mapper.KnowledgeMapper;
import com.tydic.nbchat.admin.mapper.po.CommSentExcelBo;
import com.tydic.nbchat.admin.mapper.po.KnowledgeClassifyPo;
import com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo;
import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.FileManagerHelper;
import com.tydic.nicc.framework.utils.MultipartFileUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @version: 0.0.1
 * @Author: Jason
 * @Date 2021/10/24
 **/
@Slf4j
@Service
public class CommSentenceBatchServiceImpl implements CommSentenceBatchApi {

    private final CommSentenceBusiService commSentenceBusiService;
    private final FileManagerHelper fileManagerHelper;
    private final FileManageService fileManageService;

    @Resource
    private KnowledgeClassifyMapper knowledgeClassifyMapper;
    @Resource
    private KnowledgeMapper knowledgeMapper;
    @Resource
    private NbchatAdminConfigProperties nbchatAdminConfigProperties;

    public CommSentenceBatchServiceImpl(CommSentenceBusiService commSentenceBusiService,
                                        FileManagerHelper fileManagerHelper,
                                        FileManageService fileManageService) {
        this.commSentenceBusiService = commSentenceBusiService;
        this.fileManagerHelper = fileManagerHelper;
        this.fileManageService = fileManageService;
    }

    @MethodParamVerifyEnable
    @Override
    public RspList exportCommSentList(ExportCommSentReqBo req) {
        KnowledgeContentPo po = new KnowledgeContentPo();
        BeanUtils.copyProperties(req, po);
        po.setCreateUserId(req.get_userId());
        if(!CollectionUtils.isEmpty(po.getSentenceIdList()) && !CollectionUtils.isEmpty(po.getTypeIdList())){
            po.getTypeIdList().clear();
        }
        List<CommSentExcelBo> exportList = knowledgeMapper.getExportList(po);
        if (CollectionUtils.isEmpty(exportList)) {
            return BaseRspUtils.createSuccessRspList(null, 0);
        }

        // 获取所有父级id
        List<Long> parentIdList = exportList.stream().map(CommSentExcelBo::getParentTypeId).distinct().collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(parentIdList)) {
            List<KnowledgeClassifyPo> classifyPos = knowledgeClassifyMapper.queryCommSentenceTypeByIds(req.getTenantCode(), parentIdList);
            if (!CollectionUtils.isEmpty(classifyPos)) {
                Map<Long, String> parentNameMap = classifyPos.stream().collect(Collectors.toMap(KnowledgeClassifyPo::getTypeId, KnowledgeClassifyPo::getTypeName));
                for (CommSentExcelBo commSentExcelBo : exportList) {
                    //如果有父id，则typeName为父名称+'->'+子名称
                    commSentExcelBo.setTypeName(StringUtils.isEmpty(parentNameMap.get(commSentExcelBo.getParentTypeId())) ? commSentExcelBo.getTypeName() : parentNameMap.get(commSentExcelBo.getParentTypeId()) + OlConstants.COMM_SENTENCE_NAME_DELIMITER + commSentExcelBo.getTypeName());
                }
            }
        }

        log.info("返回结果为={}", JSON.toJSONString(exportList));
        return createUploadExcel(exportList, CommSentExcelBo.class);
    }

    private RspList createUploadExcel(List<?> exportBOList, Class clazz) {
        try {
            String fileName = "知识列表" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
            String tempPath = System.getProperty("java.io.tmpdir");
            File dirFile = new File(tempPath + "/" + fileName);
            EasyExcel.write(dirFile, clazz).sheet("知识导出").doWrite(exportBOList);
            MultipartFile multipartFile = MultipartFileUtil.toMultipartFile(dirFile);
            MultipartFile[] files = {multipartFile};
            String month = DateTimeUtil.getTimeShortString(new Date(), DateTimeUtil.MONTH_FORMAT);
            List<FileManageSaveBO> fileManageSaveBOS = fileManagerHelper.saveFiles(files, month);
            log.info("文件上传完毕，信息：{}", JSON.toJSONString(fileManageSaveBOS));
            if (CollectionUtils.isEmpty(fileManageSaveBOS)) {
                return BaseRspUtils.createErrorRspList("上传文件失败");
            }
            FileUploadReqBO fileUploadReqBO = new FileUploadReqBO();
            fileUploadReqBO.setServerIp(NiccCommonUtil.getIpAddress());
            fileUploadReqBO.setSaveResults(fileManageSaveBOS);
            fileManageService.fileUpload(fileUploadReqBO);
            return BaseRspUtils.createSuccessRspList(fileManageSaveBOS);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return BaseRspUtils.createErrorRspList("创建表格失败，请查看日志");
        }
    }


    @MethodParamVerifyEnable
    @Override
    public RspList importCommSentList(ImportCommSentReqBO reqBO) throws Exception {
        log.info("导入知识入参: {}", JSON.toJSONString(reqBO));
        String accessUrl = reqBO.getAccessUrl();
        String router = nbchatAdminConfigProperties.getDownRouter();
        String fileName = accessUrl.substring(accessUrl.indexOf(router) + router.length());
        //根据文件名称从文件服务器下载文件，然后对读取文件，存储到数据库
        InputStream inputStream = fileManagerHelper.downloadFile(fileName);
        ImportCommSentListener sentListener = new ImportCommSentListener();

        EasyExcel.read(inputStream, ImportSentenceDTO.class, sentListener).sheet().doRead();
        List<ImportSentenceDTO> record = sentListener.getRecord();
        if (CollectionUtils.isEmpty(record)) {
            return BaseRspUtils.createErrorRspList("读取文件内容为空");
        }

        Integer successNum = 0;
        List<ImportCommentSentError> errorList = new ArrayList<>();

        for (ImportSentenceDTO data : record) {
            ImportCommentSentError errorRecord = new ImportCommentSentError();
            errorRecord.setCommSortId(data.getCommSortId());
            errorRecord.setContent(data.getContent());
            errorRecord.setTypeId(data.getTypeId());
            errorRecord.setContentTitle(data.getContentTitle());
            if (ruleCheck(data, errorRecord, errorList)) {
                continue;
            }

            if(StringUtils.isNotBlank(data.getTypeId())) {
                // 判断是否为两级分级
                String[] typeNameSplit = data.getTypeId().split(OlConstants.COMM_SENTENCE_NAME_DELIMITER);
                Long parentId = null;
                String typeName = data.getTypeId();
                log.info("typeNameSplit size={}", typeNameSplit.length);
                if (typeNameSplit.length > 1) {
                    // 查询父级分类id
                    KnowledgeClassifyPo po = getTypeId(reqBO.get_userId(), typeNameSplit[0], parentId);
                    if (po == null) {
                        errorRecord.setErrorReason("所属分类[" + typeNameSplit[0] + "]不存在");
                        errorList.add(errorRecord);
                        continue;
                    }
                    parentId = po.getTypeId();
                    typeName = typeNameSplit[1];
                }
                KnowledgeClassifyPo typePo = getTypeId(reqBO.get_userId(), typeName, parentId);
                if (null == typePo || typePo.getTypeId() == null) {
                    errorRecord.setErrorReason("所属分类[" + typeName + "]不存在");
                    errorList.add(errorRecord);
                    continue;
                }
                data.setTypeId(typePo.getTypeId() + "");
            }
            saveCommSentence(reqBO, data);
            successNum++;

        }

        int errorNum = errorList.size();
        ImportCommErrorRsp rsp = new ImportCommErrorRsp();
        rsp.setTips("导入总条数[" + (successNum + errorNum) + "]条,成功[" + successNum + "]条");

        if (errorNum > 0) {
            RspList<FileManageSaveBO> res = createUploadExcel(errorList, ImportCommentSentError.class);
            if (!res.isSuccess()) {
                return BaseRspUtils.createErrorRspList("导入失败,请确定所属类目是否存在");
            }
            rsp.setFilePath(res.getRows().get(0).getAccessUrl());
            rsp.setTips("导入成功[" + successNum + "]条; 失败[" + errorNum + "]条。");
        }
        return BaseRspUtils.createSuccessRspList(Arrays.asList(rsp));
    }

    /**
     * @return boolean
     * <AUTHOR>
     * @description 校验
     **/
    public boolean ruleCheck(ImportSentenceDTO data, ImportCommentSentError errorRecord, List<ImportCommentSentError> errorList) {
        if (StringUtils.isEmpty(data.getContent())) {
            errorRecord.setErrorReason("内容不能为空");
            errorList.add(errorRecord);
            return true;
        }
        if (StringUtils.isEmpty(data.getContentTitle())) {
            errorRecord.setErrorReason("内容名称不能为空");
            errorList.add(errorRecord);
            return true;
        }
        if (data.getContentTitle().length() > 500) {
            errorRecord.setErrorReason("内容名称不能超过500字符");
            errorList.add(errorRecord);
            return true;
        }
        if (data.getContent().length() > 500) {
            errorRecord.setErrorReason("内容不能超过500字符");
            errorList.add(errorRecord);
            return true;
        }
//        if (StringUtils.isEmpty(data.getTypeId())) {
//            errorRecord.setErrorReason("所属分类不能为空");
//            errorList.add(errorRecord);
//            return true;
//        }
//        try {
//            if (StringUtils.isEmpty(data.getCommSortId())) {
//                errorRecord.setErrorReason("排序标识不能为空");
//                errorList.add(errorRecord);
//                return true;
//            }
//            Integer num = Integer.valueOf(data.getCommSortId());
//            if (num <= 0 || num > 10000) {
//                errorRecord.setErrorReason("排序标识超出[1-10000]取值范围");
//                errorList.add(errorRecord);
//                return true;
//            }
//        } catch (Exception e) {
//            errorRecord.setErrorReason("排序标识必须为阿拉伯数字");
//            errorList.add(errorRecord);
//            return true;
//        }
        //通过
        return false;
    }

    public KnowledgeClassifyPo getTypeId(String createUserId, String typeName, Long parentId) {
        KnowledgeClassifyPo knowledgeClassifyPo = new KnowledgeClassifyPo();
        knowledgeClassifyPo.setParentId(parentId);
        knowledgeClassifyPo.setTypeName(typeName);
        knowledgeClassifyPo.setCreateUserId(createUserId);
        knowledgeClassifyPo.setClasses("2");

        log.info("getTypeId--->{}", JSON.toJSONString(knowledgeClassifyPo));
        return knowledgeClassifyMapper.selectTypePo(knowledgeClassifyPo);
    }

    public void saveCommSentence(ImportCommSentReqBO reqBO, ImportSentenceDTO data) {
        CommSentence sentence = new CommSentence();
        Integer commSortId = data.getCommSortId() == null ? null : Integer.valueOf(data.getCommSortId());
        Long typeId = StringUtils.isBlank(data.getTypeId()) ? 0L : Long.parseLong(data.getTypeId());
        sentence.setSortId(commSortId);
        sentence.setContent(data.getContent());
        sentence.setContentTitle(data.getContentTitle());
        sentence.setTypeId(typeId);
        sentence.setCreateUserId(reqBO.get_userId());
        sentence.setCreateTime(new Date());
        sentence.setClasses("2");
        sentence.setTenantCode(reqBO.getTenantCode());

        commSentenceBusiService.saveCommSentence(sentence);
    }


}
