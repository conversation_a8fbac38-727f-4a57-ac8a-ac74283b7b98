package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysPermissionOptionApi;
import com.tydic.nbchat.admin.api.bo.permission.PermissionObjectUpdateBO;
import com.tydic.nbchat.admin.api.bo.permission.UserPermissionQueryRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 仅仅用于调试，前端无对接
 */
@Slf4j
@RestController
@RequestMapping("/admin")
public class SysDataPermissionController {

    private final SysPermissionOptionApi sysPermissionOptionApi;

    public SysDataPermissionController(SysPermissionOptionApi sysPermissionOptionApi) {
        this.sysPermissionOptionApi = sysPermissionOptionApi;
    }


    @PostMapping("data/permission/delete")
    public Rsp delete(@RequestBody PermissionObjectUpdateBO request) {
        return sysPermissionOptionApi.deleteDataPermission(request.getBusiType(), request.getBusiId());
    }

    @PostMapping("data/permission/update")
    public Rsp update(@RequestBody PermissionObjectUpdateBO request) {
        return sysPermissionOptionApi.updateDataPermission(request);
    }

    @PostMapping("train/permission/delete")
    public Rsp deleteTrainPermission(@RequestBody PermissionObjectUpdateBO request) {
        return sysPermissionOptionApi.deleteTrainPermission(request.getBusiType(), request.getBusiId());
    }

    @PostMapping("train/permission/update")
    public Rsp updateTrainPermission(@RequestBody PermissionObjectUpdateBO request) {
        return sysPermissionOptionApi.updateTrainPermission(request);
    }

    @PostMapping("data/user/permissions")
    public Rsp getUserDataPermission(@RequestBody UserPermissionQueryRequest request) {
        return sysPermissionOptionApi.getUserDataPermission(request);
    }

    @PostMapping("train/user/permissions")
    public Rsp getUserTrainPermission(@RequestBody UserPermissionQueryRequest request) {
        return sysPermissionOptionApi.getUserTrainPermission(request);
    }


}
