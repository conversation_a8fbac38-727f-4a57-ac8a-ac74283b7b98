package com.tydic.nbchat.admin.core.utils;

import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.tydic.nbchat.admin.api.bo.constants.ExcelConstants;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class ExamQuestionExportUtil {
    /**
     * 设置excel格式
     *
     * @return
     */
    public static HorizontalCellStyleStrategy setExcelStyle() {
        //设置内容格式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints(ExcelConstants.CONTENT_FONT_HEIGHT);
        contentWriteFont.setFontName(ExcelConstants.FONT_NAME);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        // 设置水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);

        //设置表头格式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName(ExcelConstants.FONT_NAME);
        headWriteFont.setFontHeightInPoints(ExcelConstants.HEAD_FONT_HEIGHT);
        headWriteFont.setBold(false);
        headWriteCellStyle.setWriteFont(headWriteFont);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        return horizontalCellStyleStrategy;
    }

    /**
     * 生成文件名
     *
     * @param baseName
     * @param suffix
     * @return
     */
    public static String generateFileName(String baseName, String suffix) {
        String timeStamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern(ExcelConstants.EXCEL_SIMPLE_DATE_FORMAT));
        return baseName + timeStamp + suffix;
    }

    /**
     * 创建临时文件
     *
     * @param fileName
     * @return
     * @throws IOException
     */
    public static File createTempFile(String fileName) throws IOException {
        Path tempDir = Files.createTempDirectory(ExcelConstants.EXCEL_TMP_DIRECTORY);
        Path filePath = tempDir.resolve(fileName);
        return filePath.toFile();
    }
}
