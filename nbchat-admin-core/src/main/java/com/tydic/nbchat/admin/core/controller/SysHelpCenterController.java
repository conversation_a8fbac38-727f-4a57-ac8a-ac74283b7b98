package com.tydic.nbchat.admin.core.controller;


import com.tydic.nbchat.admin.api.SysHelpCenterApi;
import com.tydic.nbchat.admin.api.bo.SysHelpCenterQueryReqBo;
import com.tydic.nbchat.admin.api.bo.SysHelpCenterRspBO;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/sys")
public class SysHelpCenterController {

    private final SysHelpCenterApi sysHelpCenterApi;

    public SysHelpCenterController(SysHelpCenterApi sysHelpCenterApi) {
        this.sysHelpCenterApi = sysHelpCenterApi;
    }

    /**
     * 管理帮助中心查询
     */
    @PostMapping("/hc/list")
    public RspList<SysHelpCenterRspBO> getHelpCenterList(@RequestBody SysHelpCenterQueryReqBo reqBo) {
        return sysHelpCenterApi.getHelpCenterList(reqBo);
    }
    /**
     * 管理帮助中心保存,新增或修改
     */
    @PostMapping("/hc/save")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp<SysHelpCenterRspBO> save(@RequestBody SysHelpCenterQueryReqBo reqBo) {
        return sysHelpCenterApi.save(reqBo);
    }
    /**
     * 管理帮助中心删除
     */
    @PostMapping("/hc/delete")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp delete(@RequestBody SysHelpCenterQueryReqBo reqBo) {
        return sysHelpCenterApi.delete(reqBo);
    }
    /**
     * 管理中心配置上下架
     */
    @PostMapping("/hc/updateStatus")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp updateStatus(@RequestBody SysHelpCenterQueryReqBo reqBo) {
        return sysHelpCenterApi.updateStatus(reqBo);
    }
    /**
     * 用户查询配置
     */
    @PostMapping("/hc/helps")
    public RspList<SysHelpCenterRspBO> getUserHelpCenterList(@RequestBody SysHelpCenterQueryReqBo reqBo) {
        return sysHelpCenterApi.getUserHelpCenterList(reqBo);
    }
    /**
     * 根据id列表排序
     */
    @PostMapping("/hc/sort")
    @RequiresRole(value = {UserRoleConstants.sysAdmin})
    public Rsp sort(@RequestBody SysHelpCenterQueryReqBo reqBo) {
        return sysHelpCenterApi.updateSort(reqBo);
    }
    /**
     * 根据id查询
     */
    @PostMapping("/hc/getById")
    public Rsp<SysHelpCenterRspBO> getHelpCenterById(@RequestBody SysHelpCenterQueryReqBo reqBo) {
        return sysHelpCenterApi.getHelpCenterById(reqBo);
    }

}
