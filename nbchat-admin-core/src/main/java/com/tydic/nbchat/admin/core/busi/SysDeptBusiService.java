package com.tydic.nbchat.admin.core.busi;

import com.tydic.nbchat.admin.mapper.SysDeptMapper;
import com.tydic.nbchat.admin.mapper.po.SysDept;
import com.tydic.nbchat.admin.mapper.po.SysDeptUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class SysDeptBusiService {

    private final SysDeptMapper sysDeptMapper;

    public String getDeptName(String deptId) {
        SysDept sysDept = sysDeptMapper.queryById(deptId);
        return sysDept == null ? "" : sysDept.getDeptName();
    }
}
