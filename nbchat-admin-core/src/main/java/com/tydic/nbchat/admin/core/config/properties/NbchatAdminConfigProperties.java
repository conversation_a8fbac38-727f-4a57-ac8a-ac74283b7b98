package com.tydic.nbchat.admin.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-admin.config")
public class NbchatAdminConfigProperties {
    //邀请加入租户
    private String tenantInviteUrl = "https://chat-test.tydiczt.com/chat/?inviteKey={inviteKey}&tenantName={tenantName}&tenantCode={tenantCode}#/enterpriseInvitation";
    //试用邀请
    private String tenantInviteTryUrl = "https://chat-test.tydiczt.com/chat/?tryInviteKey={inviteKey}#/comInformation";
    private Integer tenantInviteExpireTime = 3600 * 24 * 3;
    //导入功能路由
    private String downRouter = "im-files/";
    //知识长度
    private int knowledgeLength = 1000;
    private HttpProxyConfigProperties httpProxy;
    //默认菜单模板
    private String defaultMenuTpl = "410166397947727872";
    private String dingtalkTitle = "测试环境";
    private Boolean dingtalkEnable = true;
    //默认密码前缀 Kjb#+手机号后6位
    private String defaultPasswordPrefix = "Kjb#";
    private String defaultRobot = "doubao";
}
