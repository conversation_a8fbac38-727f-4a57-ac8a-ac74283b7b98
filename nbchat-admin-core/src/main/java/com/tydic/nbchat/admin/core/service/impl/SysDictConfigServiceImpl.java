package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.tydic.nbchat.admin.api.bo.SysDictInsertReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.SysDictConfigQueryBo;
import com.tydic.nbchat.admin.mapper.po.SysDictConfigResult;
import com.tydic.nbchat.admin.mapper.po.SysDictConfigSelectCondition;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import com.tydic.nbchat.admin.api.SysDictConfigService;
import com.tydic.nbchat.admin.api.bo.SysDictConfigRspBO;
import com.tydic.nbchat.admin.api.bo.SysDictConfigQueryReqBO;
import com.tydic.nbchat.admin.mapper.SysDictConfigMapper;
import com.tydic.nbchat.admin.mapper.po.SysDictConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Classname SysDictConfigServiceImpl
 * @Description 字典配置相关
 * @Date 2022/7/13 15:08
 * @Created by kangkang
 */
@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
public class SysDictConfigServiceImpl implements SysDictConfigService {

    @Resource
    private SysDictConfigMapper sysDictConfigMapper;

    @MethodParamVerifyEnable
    @Override
    public RspList getSysDictValues(SysDictConfigQueryReqBO configReqBO) {
        log.info("查询系统字典:{}",configReqBO);
        List<SysDictConfigRspBO> list = Lists.newArrayList();
        List<SysDictConfig> dictConfigs = sysDictConfigMapper.selectByCode(configReqBO.getTenantCode(),configReqBO.getDictCode());
        NiccCommonUtil.copyList(dictConfigs,list, SysDictConfigRspBO.class);
        return BaseRspUtils.createSuccessRspList(list);
    }

    /**
     * 根据字典名称和字典描述对dict_code分组查询
     * @param reqBO
     * @return
     */
    @Override
    public RspList getSysDictValuesByGroup(SysDictConfigQueryBo reqBO) {
        log.info("根据字典名称和字典描述对dict_code分组查询:{}",reqBO);
        SysDictConfigSelectCondition condition = new SysDictConfigSelectCondition();
        BeanUtils.copyProperties(reqBO, condition);
        Page<SysDictConfigResult> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        List<SysDictConfigResult> result = Lists.newArrayList();
        sysDictConfigMapper.selectByGroup(condition);
        NiccCommonUtil.copyList(page.getResult(), result, SysDictConfigResult.class);
        return BaseRspUtils.createSuccessRspList(result, page.getTotal());
    }

    /**
     * 根据dict_code对字典表进行配置
     * @param reqBO
     * @return
     */
    @Override
    @Transactional
    public RspList insertDictCode(SysDictInsertReqBO reqBO) {
        log.info("插入字典配置: dictCode={}, valueList={}", reqBO.getDictCode(), reqBO.getValueList());
        String dictCode = reqBO.getDictCode();
        String tenantCode = reqBO.getTenantCode();
        List<Map<String, String>> valueList = reqBO.getValueList();
        // 查询现在的数据
        List<SysDictConfig> oldList = sysDictConfigMapper.selectByCode(tenantCode, dictCode);
        // 如果有数据，先删除现有的列表数据
        if (!oldList.isEmpty()) {
            sysDictConfigMapper.deleteByDictCode(dictCode);
        }
        // 插入新的数据
        List<SysDictConfig> list = Lists.newArrayList();
        for (int i = 0; i < valueList.size(); i++) {
            Map<String, String> map = valueList.get(i);
            SysDictConfig sysDictConfig = new SysDictConfig();
            sysDictConfig.setDictCode(dictCode);
            sysDictConfig.setDictDesc(map.get("dictDesc"));
            sysDictConfig.setDictName(map.get("dictName"));
            sysDictConfig.setDictValue(map.get("dictValue"));
            sysDictConfig.setTenantCode(""); // 设置 tenantCode
            sysDictConfig.setChannelCode(map.get("channelCode"));
            sysDictConfig.setOrderIndex(i + 1);
            sysDictConfig.setCrtTime(new Date());
            sysDictConfig.setIsValid("1");
            list.add(sysDictConfig);
        }
        if (!list.isEmpty()) {
            sysDictConfigMapper.insertDictCode(list);
        }
        return BaseRspUtils.createSuccessRspList(list);
    }

    /**
     * 批量更新字典状态
     * @param reqBO
     * @return
     */
    @Override
    public RspList updateDictCode(SysDictConfigRspBO reqBO) {
        log.info("批量更新字典状态:{}",reqBO);
        SysDictConfig sysDictConfig = new SysDictConfig();
        sysDictConfig.setDictCode(reqBO.getDictCode());
        sysDictConfig.setIsValid("0");
        List<SysDictConfig> list = Lists.newArrayList(sysDictConfig);
        sysDictConfigMapper.updateDictStatus(list);
        return BaseRspUtils.createSuccessRspList(list);
    }
}
