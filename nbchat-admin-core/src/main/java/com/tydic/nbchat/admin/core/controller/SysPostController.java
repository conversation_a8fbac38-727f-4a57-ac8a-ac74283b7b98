package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysPostApi;
import com.tydic.nbchat.admin.api.bo.post.SysPostBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/post")
public class SysPostController {


    private final SysPostApi sysPostApi;

    public SysPostController(SysPostApi sysPostApi) {
        this.sysPostApi = sysPostApi;
    }

    @PostMapping("list")
    public RspList list(@RequestBody SysPostBO request){
        return sysPostApi.query(request);
    }
    @PostMapping("save")
    public Rsp save(@Validated @RequestBody SysPostBO request){
        return sysPostApi.save(request);
    }
    /**
     * 删除岗位信息
     */
    @PostMapping("delete")
    public Rsp delete(@RequestBody SysPostBO request){
        return sysPostApi.delete(request);
    }

}
