package com.tydic.nbchat.admin.core.config.utils.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

@Data
@ContentRowHeight(13)
@HeadRowHeight(20)
@ColumnWidth(25)
public class ImportCommentSentError {
    @ExcelProperty("所属分类")
    private String typeId;

    @ExcelProperty("排序标识")
    private String commSortId;

    @ExcelProperty("知识内容")
    private String content;

    @ExcelProperty("内容名称")
    private String contentTitle;

    @ExcelProperty("失败原因")
    private String errorReason;
}
