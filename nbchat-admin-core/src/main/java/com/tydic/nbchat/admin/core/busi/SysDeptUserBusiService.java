package com.tydic.nbchat.admin.core.busi;

import com.tydic.nbchat.admin.api.bo.dept.SysDeptQueryRspBO;
import com.tydic.nbchat.admin.mapper.SysDeptMapper;
import com.tydic.nbchat.admin.mapper.po.SysDept;
import com.tydic.nbchat.admin.mapper.po.SysDeptUser;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class SysDeptUserBusiService {

    private final SysDeptMapper sysDeptMapper;
    private final SysUserPostBusiService sysUserPostBusiService;
    /**
     * 获取部门下所有子部门
     * @param deptId
     * @return
     */
    public List<String> getDeptChildList(String tenantCode, String deptId) {
        List<SysDept> depts = sysDeptMapper.selectDeptChildList(tenantCode, deptId);
        return depts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
    }

    /**
     * 获取用户部门
     *
     * @param tenantCode
     * @param userId
     * @return
     */
    public SysDeptQueryRspBO getUserDept(String tenantCode, String userId) {
        SysDeptQueryRspBO rspBO = new SysDeptQueryRspBO();
        List<SysDept> depts = sysDeptMapper.selectByUserId(tenantCode, userId);
        SysDept dept;
        if (depts == null || depts.isEmpty()) {
            dept = sysDeptMapper.queryById(tenantCode);
        } else {
            dept = depts.get(0);
        }
        if (dept != null) {
            BeanUtils.copyProperties(dept, rspBO);
        }
        //查询归属岗位
        rspBO.setPostList(sysUserPostBusiService.getUserPostList(tenantCode, userId));
        return rspBO;
    }

    public void addDeptUserRel(String tenantCode, String deptId, String userId) {
        try {
            if (StringUtils.isAnyBlank(tenantCode, userId)) {
                log.warn("新增部门用户-参数错误: {}_{}_{}", tenantCode, deptId, userId);
                return;
            }
            Long rid = sysDeptMapper.selectDeptUserRid(tenantCode, userId);
            if (rid == null) {
                SysDeptUser deptUser = new SysDeptUser();
                //初始化默认部门
                if (StringUtils.isBlank(deptId)) {
                    deptId = tenantCode;
                }
                deptUser.setDeptId(deptId);
                deptUser.setUserId(userId);
                deptUser.setTenantCode(tenantCode);
                sysDeptMapper.insertDeptUserRel(deptUser);
            } else {
                log.info("新增部门用户-关系已存在: {}_{}_{}", tenantCode,deptId, userId);
                if (StringUtils.isNotBlank(deptId)) {
                    sysDeptMapper.updateDeptUserRel(rid, deptId);
                }
            }
        } catch (Exception e) {
            log.error("新增部门用户-异常: {}_{}_{}", tenantCode, deptId, userId, e);
        }
    }

    /**
     * 删除部门用户关系
     * @param tenantCode
     * @param userId
     */
    public void removeDeptUserRel(String tenantCode, String userId) {
        try {
            if (StringUtils.isAnyBlank(tenantCode, userId)) {
                log.warn("删除部门用户-参数错误: {}_{}", tenantCode, userId);
                return;
            }
            sysDeptMapper.deleteDeptUserRel(tenantCode, userId);
        } catch (Exception e) {
            log.error("删除部门用户-异常: {}_{}", tenantCode, userId, e);
        }
    }
}
