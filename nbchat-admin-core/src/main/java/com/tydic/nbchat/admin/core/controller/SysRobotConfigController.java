package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysRobotConfigApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import com.tydic.nbchat.admin.api.bo.robot.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/robot/config")
public class SysRobotConfigController {

    private final SysRobotConfigApi sysRobotConfigApi;

    public SysRobotConfigController(SysRobotConfigApi sysRobotConfigApi) {
        this.sysRobotConfigApi = sysRobotConfigApi;
    }

    /**
     * 管理员配置机器人
     * @param setReqBO
     * @return
     */
    @PostMapping("/admin/set")
    public Rsp adminSet(@RequestBody SysRobotConfigAdminSetReqBO setReqBO){
        return sysRobotConfigApi.setRobotConfigByAdmin(setReqBO);
    }

    /**
     * 普通用户切换机器人
     * @param setReqBO
     * @return
     */
    @PostMapping("/set")
    public Rsp set(@RequestBody SysRobotConfigUserSetReqBO setReqBO){
        return sysRobotConfigApi.setRobotConfig(setReqBO);
    }

    /**
     * 用户查询机器人列表
     * @param reqBO
     * @return
     */
    @PostMapping("/list")
    public Rsp list(@RequestBody SysRobotConfigQueryReqBO reqBO){
        return sysRobotConfigApi.getRobotConfigList(reqBO);
    }
}
