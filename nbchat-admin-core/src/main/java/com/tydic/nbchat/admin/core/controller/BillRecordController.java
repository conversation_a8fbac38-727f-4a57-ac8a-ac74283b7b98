package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.BillRecordApi;
import com.tydic.nbchat.user.api.bo.trade.UserBillRecordQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("/admin")
public class BillRecordController {

    private final BillRecordApi billRecordApi;

    public BillRecordController(BillRecordApi billRecordApi) {
        this.billRecordApi = billRecordApi;
    }

    @PostMapping("/trade/bill/export")
    public Rsp exportBillRecord(@RequestBody UserBillRecordQueryReqBO queryReqBO){
        return billRecordApi.exportBillRecord(queryReqBO);
    }
}
