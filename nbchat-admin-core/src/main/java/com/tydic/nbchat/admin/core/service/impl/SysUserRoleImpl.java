package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.SysUserRoleApi;
import com.tydic.nbchat.admin.api.bo.SysTenantQueryRspBO;
import com.tydic.nbchat.admin.core.busi.SysUserRoleBusiService;
import com.tydic.nbchat.admin.mapper.NbchatSysTenantMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatSysTenant;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.bo.user.SysRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.common.nbchat.emus.UserRoleType;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class SysUserRoleImpl implements SysUserRoleApi {

    @Resource
    private NbchatSysTenantMapper nbchatSysTenantMapper;
    private final SysUserRoleBusiService sysUserRoleBusiService;

    public SysUserRoleImpl(SysUserRoleBusiService sysUserRoleBusiService) {
        this.sysUserRoleBusiService = sysUserRoleBusiService;
    }


    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin,
            UserRoleConstants.orgAdmin, UserRoleConstants.opUser},
            logical = Logical.OR)
    @Override
    public RspList getSelectRoleList(BaseInfo reqBO) {
        if (UserAttributeConstants.TMO_TENANT.equals(reqBO.getTenantCode())) {
            //术语在线角色
            return BaseRspUtils.createSuccessRspList(sysUserRoleBusiService.getConfigRoles("tmo"));
        } else {
            List<SysRole> sysRoleList = new ArrayList<>();
            if (reqBO.hasRole(UserRoleConstants.sysAdmin)) {
                sysRoleList = UserRoleType.getSysRoleList(UserRoleConstants.sysAdmin);
            } else if (reqBO.hasRole(UserRoleConstants.tenantAdmin)) {
                sysRoleList = UserRoleType.getSysRoleList(UserRoleConstants.tenantAdmin);
            } else if (reqBO.hasRole(UserRoleConstants.orgAdmin)) {
                sysRoleList = UserRoleType.getSysRoleList(UserRoleConstants.orgAdmin);
            } else if (reqBO.hasRole(UserRoleConstants.opUser)) {
                sysRoleList = UserRoleType.getSysRoleList(UserRoleConstants.opUser);
            }
            return BaseRspUtils.createSuccessRspList(sysRoleList);
        }
    }

    @Override
    public RspList getRoleTenants(BaseInfo reqBO) {
        List<NbchatSysTenant> list = nbchatSysTenantMapper.selectTenantListByUseRole(reqBO.get_userId());
        List<SysTenantQueryRspBO> result = new ArrayList<>();
        NiccCommonUtil.copyList(list, result, SysTenantQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(result);
    }
}
