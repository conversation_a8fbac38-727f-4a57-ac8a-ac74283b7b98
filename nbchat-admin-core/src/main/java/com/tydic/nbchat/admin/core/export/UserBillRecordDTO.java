package com.tydic.nbchat.admin.core.export;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ContentRowHeight(30)
@HeadRowHeight(30)
@ColumnWidth(25)
public class UserBillRecordDTO implements Serializable {
    @ExcelProperty("订单ID")
    private String tradeId;

    @ExcelProperty("消费类型")
    private String bizCodeDesc;

    @ExcelProperty("内容名称")
    private String bizName;

    @ExcelProperty("算力点")
    private Integer score;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("消费人ID")
    private String userId;

    @ExcelProperty("消费人名称")
    private String realName;

    @ExcelProperty("时间")
    private Date tradeTime;
}
