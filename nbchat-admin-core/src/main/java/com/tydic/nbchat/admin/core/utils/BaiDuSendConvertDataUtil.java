package com.tydic.nbchat.admin.core.utils;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import com.tydic.nbchat.admin.api.bo.baidu.ConversionTypeBO;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.List;

public class BaiDuSendConvertDataUtil {
    private static final String BAIDU_OCPC_URL = "https://ocpc.baidu.com/ocpcapi/api/uploadConvertData";
    private static final Integer RETRY_TIMES = 3;
    private static final Logger log = LoggerFactory.getLogger(BaiDuSendConvertDataUtil.class);

    /**
     * 数据回传接口
     *
     * @param token                用户回传数据api接口token
     * @param ConversionTypeBOList 回传转化数据数组
     * @return 返回true代表成功 false代表失败
     */
    public static Boolean sendConvertData(String token, List<ConversionTypeBO> ConversionTypeBOList) {

        JsonObject data = new JsonObject();
        // 设置API接口回传Token
        data.addProperty("token", token);
        // 设置API接口回传ConversionTypeBOs
        data.add("ConversionTypes",
                new Gson().toJsonTree(ConversionTypeBOList, new TypeToken<List<ConversionTypeBO>>() {
                }.getType()).getAsJsonArray());
        // 发送的完整请求数据
        // do some log
        log.info("req data: {}", data);
        // 向百度发送数据
        return sendWithRetry(data.toString());

    }

    private static boolean sendWithRetry(String msg) {
        // 发送请求
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(BAIDU_OCPC_URL);
        post.setHeader("Content-type", "application/json; charset=UTF-8");
        StringEntity entity = new StringEntity(msg, Charset.forName("UTF-8"));
        entity.setContentEncoding("UTF-8");
        post.setEntity(entity);
        // 添加失败重试
        int retry = RETRY_TIMES;
        for (int i = 0; i < retry; i++) {
            try {
                HttpResponse response = client.execute(post);
                // 检验状态码，如果成功接收数据
                int code = response.getStatusLine().getStatusCode();
                if (code == HttpStatus.SC_OK) {
                    String res = EntityUtils.toString(response.getEntity());
                    JsonObject returnData = new JsonParser().parse(res).getAsJsonObject();
                    // 打印返回结果
                    // do some log
                    log.info("retry times :{}, res data: {}", i, res);

                    int status = returnData.getAsJsonObject("header").get("status").getAsInt();
                    // status为4，代表服务端异常，可添加重试
                    if (status != 4) {
                        return status == 0; // status == 0 代表回传数据成功，其余情况回传数据失败
                    }
                }
            } catch (IOException e) {
                // do some log
                log.error("sendWithRetry error", e);
            }
        }
        return false;
    }
}
