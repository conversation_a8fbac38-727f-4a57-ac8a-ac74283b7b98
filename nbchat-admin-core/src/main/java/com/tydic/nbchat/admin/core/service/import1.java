package com.tydic.nbchat.admin.core.service;

import com.alibaba.excel.EasyExcel;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserSaveReqBO;
import com.tydic.nbchat.admin.mapper.po.ImportUserInfo;
import com.tydic.nbchat.user.api.bo.eums.JoinTenantType;
import com.tydic.nicc.framework.utils.HttpClientHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

public class import1 {
    public static void main(String[] args) {
        String url = "https://aixunzhan.yuewen.unicomgd.com/api/admin/dept/user/add";
        File file = new File("/Users/<USER>/Downloads/导入1.xlsx");
        List<ImportUserInfo> userInfoList  = EasyExcel.read(file)
                .head(ImportUserInfo.class) // 指定数据模型
                .sheet()
                .doReadSync();

        userInfoList.forEach(userInfo -> {
            String phone = userInfo.getPhone();
            SysDeptUserSaveReqBO reqBO = new SysDeptUserSaveReqBO();
            reqBO.setPhone(phone);
            reqBO.setTargetTenantCode("000UNICOM");
            reqBO.setIdCard("");
            reqBO.setUserRealityName(userInfo.getUserRealityName());
            reqBO.setTenantCode("000UNICOM");
            reqBO.setJoinType(JoinTenantType.IMPORTS.getCode());
            reqBO.setPassword("Unicom#"+phone.substring(phone.length() - 4));
            String role = userInfo.getRole();
            String deptId = userInfo.getDeptId();
            String postIdName = userInfo.getPostId();
            Integer postId  = Integer.parseInt(postIdName) ;


            List<Integer> postIdList = new ArrayList<>();
            postIdList.add(postId);
            HashSet<String> roleSet = new HashSet<>();
            roleSet.add(role);
            reqBO.setDeptId(deptId);
            reqBO.setPostIds(postIdList);
            reqBO.setRole(roleSet);

            HashMap<String,String> map = new HashMap<>();
            map.put("auth-token","eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJqd3QuYXV0aCIsIkxPR0lOX0lORk8iOiJ7XCJhcHBJZFwiOlwiMDAwMDAwMDBcIixcImF2YXRhclwiOlwiaHR0cHM6Ly9jaGF0LnR5ZGljenQuY29tL2ZpbGVzLzIwMjMwOS8xNjcyODE2ODEwNTI5LmpwZ1wiLFwiZW1haWxcIjpcIlwiLFwiZ2VuZGVyXCI6XCIwXCIsXCJuYW1lXCI6XCIxNTkzNTg1NjEyMVwiLFwicGhvbmVcIjpcIjE1OTM1ODU2MTIxXCIsXCJyZWdUaW1lXCI6MTcyMzE3MzMyNDAwMCxcInNldHRpbmdzXCI6e1wicm9ib3RUeXBlXCI6XCJmYXN0Y2hhdFwifSxcInN0YXR1c1wiOjEsXCJzdWJTeXN0ZW1zXCI6e1wiMDAwVU5JQ09NXCI6e1wiY29kZVwiOlwiMDAwVU5JQ09NXCIsXCJkZXB0c1wiOltcIjAwMFVOSUNPTVwiXSxcInJvbGVzXCI6W3tcIm5hbWVcIjpcIuW5s-WPsOeuoeeQhuWRmFwiLFwicm9sZVwiOlwic3lzQWRtaW5cIn0se1wibmFtZVwiOlwi5YWs5Y-4566h55CG5ZGYXCIsXCJyb2xlXCI6XCJ0ZW5hbnRBZG1pblwifSx7XCJuYW1lXCI6XCLmma7pgJrnlKjmiLdcIixcInJvbGVcIjpcInVzZXJcIn1dfX0sXCJ0ZW5hbnRDb2RlXCI6XCIwMDBVTklDT01cIixcInRlbmFudENvZGVMaXN0XCI6W1wiMDAwMDAwMDBcIixcIjAwMFVOSUNPTVwiXSxcInVzZXJJZFwiOlwiNDM0MDM3OTg3NjQ0ODk5MzI4XCIsXCJ1c2VyTmFtZVwiOlwiMTU5MzU4NTYxMjFcIixcInVzZXJSZWFsaXR5TmFtZVwiOlwi5YiY5rabXCIsXCJ2aXBJbmZvXCI6e1wiaXNDb21wYW55XCI6XCIwXCIsXCJpc0V4cGVyaWVuY2VkXCI6XCIwXCIsXCJ2aXBEZXNjXCI6XCLlhY3otLnkvJrlkZhcIixcInZpcFN0YXR1c1wiOlwiMlwifX0iLCJpZCI6IjQzNDAzNzk4NzY0NDg5OTMyOCIsImV4cCI6MTczMTA0MDMwMywiaWF0IjoxNzMwNzgxMTAzfQ.Ew-PKNQ5QB9YvY6OMjJ_uJe6ytXG86E8YQnBbLUWQtM");
            String result = HttpClientHelper.doPost(url,map,reqBO);


        });

    }
}
