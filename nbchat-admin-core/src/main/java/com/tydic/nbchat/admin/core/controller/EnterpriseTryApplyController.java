package com.tydic.nbchat.admin.core.controller;


import com.tydic.nbchat.admin.api.NbchatEnterpriseTryApplyApi;
import com.tydic.nbchat.admin.api.bo.EnterpriseTryApplyReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/enterprise")
public class EnterpriseTryApplyController {
    private final NbchatEnterpriseTryApplyApi nbchatEnterpriseTryApplyApi;

    /**
     * 企业体验试用申请接口
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @PostMapping("/trialApplication")
    public Rsp enterpriseTrialApplication(@RequestBody EnterpriseTryApplyReqBO reqBO){
        Rsp rsp = nbchatEnterpriseTryApplyApi.enterpriseTrialApplication(reqBO);
        return rsp;
    }
}
