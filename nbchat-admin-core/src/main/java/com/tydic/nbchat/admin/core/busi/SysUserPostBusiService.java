package com.tydic.nbchat.admin.core.busi;

import com.tydic.nbchat.admin.api.bo.post.SysPostBO;
import com.tydic.nbchat.admin.mapper.SysPostUserRelMapper;
import com.tydic.nbchat.admin.mapper.po.SysPost;
import com.tydic.nbchat.admin.mapper.po.SysPostUserRel;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SysUserPostBusiService {

    @Resource
    private SysPostUserRelMapper sysPostUserRelMapper;

    public List<SysPostBO> getUserPostList(String tenantCode,String userId) {
        List<SysPost> list = sysPostUserRelMapper.selectUserPostList(tenantCode,userId);
        List<SysPostBO> result = new ArrayList<>();
        NiccCommonUtil.copyList(list, result, SysPostBO.class);
        return result;
    }

    public void updateUserPost(String tenantCode, String userId, List<Integer> postIds) {
        if (postIds == null) {
            return;
        }
        sysPostUserRelMapper.deleteByUserId(tenantCode, userId);
        if (!postIds.isEmpty()) {
            for (Integer postId : postIds) {
                SysPostUserRel userRel = new SysPostUserRel();
                userRel.setTenantCode(tenantCode);
                userRel.setUserId(userId);
                userRel.setPostId(postId);
                userRel.setCreateTime(new Date());
                sysPostUserRelMapper.insertSelective(userRel);
            }
        }
    }
}
