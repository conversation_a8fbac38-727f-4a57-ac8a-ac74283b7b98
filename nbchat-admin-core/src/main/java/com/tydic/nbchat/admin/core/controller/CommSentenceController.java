package com.tydic.nbchat.admin.core.controller;


import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.admin.api.bo.sentence.*;
import com.tydic.nbchat.admin.api.bo.sentence.xlsx.ExportCommSentReqBo;
import com.tydic.nbchat.admin.api.bo.sentence.xlsx.ImportCommSentReqBO;
import com.tydic.nbchat.admin.api.sentence.CommSentenceApi;
import com.tydic.nbchat.admin.api.sentence.CommSentenceBatchApi;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


@Slf4j
@RestController
@RequestMapping("/admin")
public class CommSentenceController {

    private final CommSentenceApi commSentenceApi;
    private final CommSentenceBatchApi commSentenceBatchApi;

    public CommSentenceController(CommSentenceApi commSentenceApi,
                                  CommSentenceBatchApi commSentenceBatchApi) {
        this.commSentenceApi = commSentenceApi;
        this.commSentenceBatchApi = commSentenceBatchApi;
    }


    @PostMapping("/knowledge/save")
    public Rsp saveCommSentence(@RequestBody SaveCommSentenceReq req) {
        return commSentenceApi.saveCommSentence(req);
    }

    @PostMapping("/knowledge/update")
    public Rsp updateCommSentence(@RequestBody UpdateCommSentenceReq req) {
        return commSentenceApi.updateCommSentence(req);
    }

    @PostMapping("/knowledge/del")
    public RspList deleteCommSentence(@RequestBody DelCommSentenceReqBO req) {
        return commSentenceApi.deleteCommSentence(req);
    }

    @PostMapping("/knowledge/qryPageList")
    public RspList getCommSentencePageList(@RequestBody QueryCommSentencePageListReqBO req) {
        log.info("分页查询知识: {}", JSON.toJSONString(req));
        return commSentenceApi.getCommSentencePageList(req);
    }

    @PostMapping("/knowledge/query")
    public Rsp getCommSentenceById(@RequestBody QueryKnowledgeConReq req) {
        log.info("查询知识: {}", JSON.toJSONString(req));
        return commSentenceApi.getKnowledgeContentById(req);
    }

    /**
     * 生成分享知识你url
     */
    @PostMapping("/knowledge/share/generator")
    public Rsp getCommSentenceUrl(@RequestBody ShareKnowledgeReqBO req){
        log.info("分享知识: {}", JSON.toJSONString(req));
        if(StringUtils.isBlank(req.getSentenceId()) || req.getExpiredDay() == null){
            return BaseRspUtils.createSuccessRsp("知识id和有效期不得为空");
        }
        return commSentenceApi.getShareUrl(req);
    }

    /**
     * 查询分享知识
     */
    @PostMapping("/knowledge/share/query")
    public Rsp queryShare(@RequestBody ShareKnowledgeReqBO reqBO){
        if (StringUtils.isEmpty(reqBO.getSentenceShareId())) {
            return BaseRspUtils.createSuccessRsp("分享知识id不得为空");
        }
        return commSentenceApi.queryShare(reqBO);
    }

    @PostMapping("/knowledge/share/check")
    public Rsp checkShareKey(@RequestBody ShareKnowledgeReqBO reqBO){
        if (StringUtils.isAnyBlank(reqBO.getSentenceShareId(),reqBO.getShareKey())) {
            return BaseRspUtils.createSuccessRsp("分享知识id和分享码不得为空");
        }
        return commSentenceApi.checkShareKey(reqBO);
    }


    @PostMapping("/knowledge/export")
    public RspList exportCommSentList(@RequestBody ExportCommSentReqBo req) {
        return commSentenceBatchApi.exportCommSentList(req);
    }

    @PostMapping("/knowledge/import")
    public RspList exportCommSentList(HttpServletRequest request, @RequestBody ImportCommSentReqBO req) {
        try {
            return commSentenceBatchApi.importCommSentList(req);
        } catch (Exception e) {
            log.error("error :", e);
        }
        return BaseRspUtils.createErrorRspList("导入失败");

    }

}
