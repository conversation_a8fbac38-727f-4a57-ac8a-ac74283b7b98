package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.bo.sentence.DelCommSentenceTypeReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.QueryCommSentenceTreeReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.SaveCommSentenceTypeReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.UpdateCommSentenceTypeReqBO;
import com.tydic.nbchat.admin.api.sentence.CommSentenceTypeApi;
import com.tydic.nbchat.admin.core.busi.CommSentenceTypeBusiService;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class CommSentenceTypeServiceImpl implements CommSentenceTypeApi {

    @Resource
    CommSentenceTypeBusiService commSentenceTypeBusiService;

    @MethodParamVerifyEnable
    @Override
    public Rsp saveCommSentenceType(SaveCommSentenceTypeReqBO reqBo) {
        return commSentenceTypeBusiService.saveCommSentenceType(reqBo);
    }
    @MethodParamVerifyEnable
    @Override
    public Rsp updateCommSentenceType(UpdateCommSentenceTypeReqBO reqBo) {
        return commSentenceTypeBusiService.updateCommSentenceType(reqBo);
    }
    @MethodParamVerifyEnable
    @Override
    public Rsp deleteCommSentenceType(DelCommSentenceTypeReqBO reqBO) {
        return commSentenceTypeBusiService.deleteCommSentenceType(reqBO);
    }
    @MethodParamVerifyEnable
    @Override
    public RspList queryCommSentenceTreeData(QueryCommSentenceTreeReqBO req) {
        return commSentenceTypeBusiService.queryCommSentenceTreeData(req);
    }
}
