package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysUserStarApi;
import com.tydic.nbchat.admin.api.star.UserStarRequest;
import com.tydic.nbchat.admin.api.star.UserStarSaveRequest;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/sys/user/star")
public class SysUserStarController {
    private SysUserStarApi sysUserStarService;

    /**
     * 保存收藏
     *
     * @param request
     * @return
     */
    @PostMapping("/save")
    public Rsp<String> saveStar(@RequestBody UserStarSaveRequest request) {
        return sysUserStarService.saveStar(request);
    }

    /**
     * 删除收藏
     *
     * @param request
     * @return
     */
    @PostMapping("/delete")
    public Rsp<String> deleteStar(@RequestBody UserStarRequest request) {
        return sysUserStarService.deleteStar(request);
    }

    /**
     * 获取收藏
     *
     * @param request
     * @return
     */
    @PostMapping("/get")
    public Rsp<String> getStar(@RequestBody UserStarRequest request) {
        return sysUserStarService.getStar(request);
    }
}
