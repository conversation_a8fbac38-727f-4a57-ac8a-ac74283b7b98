package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.SysDeptApi;
import com.tydic.nbchat.admin.api.bo.dept.*;
import com.tydic.nbchat.admin.core.busi.SysDeptUserBusiService;
import com.tydic.nbchat.admin.mapper.SysDeptMapper;
import com.tydic.nbchat.admin.mapper.po.SysDept;
import com.tydic.nbchat.admin.mapper.po.SysDeptCondition;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class SysDeptServiceImpl implements SysDeptApi {

    @Resource
    private SysDeptMapper sysDeptMapper;
    private final SysDeptUserBusiService sysDeptUserBusiService;

    public SysDeptServiceImpl(SysDeptUserBusiService sysDeptUserBusiService) {
        this.sysDeptUserBusiService = sysDeptUserBusiService;
    }

    @Override
    public RspList<SysDeptQueryRspBO> getDeptList(SysDeptQueryReqBO reqBO) {
        SysDeptCondition condition = new SysDeptCondition();
        BeanUtils.copyProperties(reqBO, condition);
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        List<SysDeptQueryRspBO> rspBOList = new ArrayList<>();
        List<SysDept> list = sysDeptMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            return BaseRspUtils.createSuccessRspList(rspBOList, 0L);
        }
        NiccCommonUtil.copyList(list, rspBOList, SysDeptQueryRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspBOList, (long) rspBOList.size());
    }


    @Override
    public Rsp<SysDeptQueryRspBO> getDept(SysDeptQueryReqBO reqBO) {
        if (StringUtils.isBlank(reqBO.getDeptId())) {
            return BaseRspUtils.createErrorRsp("部门ID不能为空");
        }
        SysDeptCondition condition = new SysDeptCondition();
        condition.setDeptId(reqBO.getDeptId());
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        List<SysDept> list = sysDeptMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(list)) {
            return BaseRspUtils.createErrorRsp("部门不存在");
        }
        SysDeptQueryRspBO queryRspBO = new SysDeptQueryRspBO();
        BeanUtils.copyProperties(list.get(0), queryRspBO);
        return BaseRspUtils.createSuccessRsp(queryRspBO);
    }

    @Override
    public RspList<SysDeptTreeBO> getDeptTree(SysDeptTreeQueryReqBO reqBO) {
        log.info("查询部门树-开始:{}", reqBO);
        SysDeptCondition condition = new SysDeptCondition();
        condition.setTenantCode(reqBO.getTenantCode());
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        condition.setStatus(reqBO.getStatus());
        condition.setFilterId(reqBO.getFilterId());
        if (StringUtils.isNotBlank(reqBO.getTargetTenant())) {
            condition.setTenantCode(reqBO.getTargetTenant());
        }
        List<SysDept> list = sysDeptMapper.selectByCondition(condition);
        List<SysDeptTreeBO> deptList = new ArrayList<>();
        if (StringUtils.isBlank(reqBO.getFilterId())) {
            //按parentId分组
            Map<String, List<SysDept>> map = list.stream().collect(Collectors.groupingBy(SysDept::getParentId));
            deptList = buildTree("0", map);
        } else {
            SysDept sysDept = sysDeptMapper.queryById(reqBO.getFilterId());
            if (sysDept != null) {
                Map<String, List<SysDept>> map = list.stream().collect(Collectors.groupingBy(SysDept::getParentId));
                deptList = buildTree(sysDept.getParentId(), map);
            }
        }
        return BaseRspUtils.createSuccessRspList(deptList);
    }

    @Override
    public RspList<SysDeptTreeBO> getChildDeptTree(SysDeptTreeQueryReqBO reqBO) {
        log.info("查询部门树-开始:{}", reqBO);
        SysDeptCondition condition = new SysDeptCondition();
        condition.setTenantCode(reqBO.getTenantCode());
        condition.setIsValid(EntityValidType.NORMAL.getCode());
        condition.setStatus(reqBO.getStatus());
        condition.setFilterId(reqBO.getFilterId());
        if (StringUtils.isNotBlank(reqBO.getTargetTenant())) {
            condition.setTenantCode(reqBO.getTargetTenant());
        }
        List<SysDept> list = sysDeptMapper.selectDeptTree(condition);
        List<SysDeptTreeBO> deptList = new ArrayList<>();
        //按parentId分组
        Map<String, List<SysDept>> map = list.stream().collect(Collectors.groupingBy(SysDept::getParentId));
        deptList = buildTree("0", map);

        return BaseRspUtils.createSuccessRspList(deptList);
    }


    private List<SysDeptTreeBO> filterDeptTree(String dept, List<SysDeptTreeBO> deptList) {
        List<SysDeptTreeBO> list = new ArrayList<>();
        for (SysDeptTreeBO sysDeptTreeBO : deptList) {
            if (StringUtils.isNotBlank(sysDeptTreeBO.getAncestors()) && sysDeptTreeBO.getAncestors().contains(dept)) {
                System.out.println(dept);
                list.add(sysDeptTreeBO);
            }
            if (!CollectionUtils.isEmpty(sysDeptTreeBO.getChildren())) {
                List<SysDeptTreeBO> child = filterDeptTree(dept, sysDeptTreeBO.getChildren());
                if (!CollectionUtils.isEmpty(child)) {
                    list.add(sysDeptTreeBO);
                }
            }
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp saveDept(SysDeptSaveReqBO reqBO) {
        log.info("保存部门信息-开始:{}", reqBO);
        String deptId = reqBO.getDeptId();
        if (StringUtils.isNotBlank(reqBO.getDeptId())) {
            //查询现在父部门
            SysDept nowDept = sysDeptMapper.queryById(reqBO.getDeptId());
            SysDept update = new SysDept();
            BeanUtils.copyProperties(reqBO, update);
            update.setUpdateTime(new Date());
            update.setUpdateBy(reqBO.get_userId());
            int i = sysDeptMapper.updateSelective(update);
            if (i > 0) {
                //部门发生变更
                try {
                    if (!nowDept.getParentId().equals(reqBO.getParentId())) {
                        //更新父级部门时，需要更新祖级列表
                        sysDeptMapper.updateAncestors(nowDept.getTenantCode());
                    }
                } catch (Exception e) {
                    log.error("更新祖级列表异常: {}", reqBO, e);
                }
            }
        } else {
            // 新增
            if (StringUtils.isBlank(reqBO.getParentId())) {
                reqBO.setParentId("0");
            } else {
                SysDept sysDept = sysDeptMapper.queryById(reqBO.getParentId());
                if (sysDept == null) {
                    return BaseRspUtils.createErrorRsp("父级部门不存在");
                } else {
                    reqBO.setAncestors(sysDept.getAncestors());
                }
            }
            if (StringUtils.isBlank(reqBO.getDeptName())) {
                return BaseRspUtils.createErrorRsp("部门名称不能为空");
            }
            SysDept save = new SysDept();
            deptId = IdWorker.nextAutoIdStr();
            BeanUtils.copyProperties(reqBO, save);
            save.setDeptId(deptId);
            save.setCreateBy(reqBO.get_userId());
            save.setCreateTime(new Date());
            save.setUpdateTime(new Date());
            //拼接当前祖级列表
            save.setAncestors(reqBO.getAncestors() + "," + deptId);
            save.setIsValid(EntityValidType.NORMAL.getCode());
            if (StringUtils.isNotBlank(reqBO.getDeptTenantCode())) {
                save.setTenantCode(reqBO.getDeptTenantCode());
            }
            sysDeptMapper.insertSelective(save);
        }
        return BaseRspUtils.createSuccessRsp(deptId, "操作成功");
    }

    @Override
    public RspList<String> getChildDeptIds(String tenantCode, String deptId) {
        List<String> depts = sysDeptUserBusiService.getDeptChildList(tenantCode, deptId);
        return BaseRspUtils.createSuccessRspList(depts);
    }

    private List<SysDeptTreeBO> buildTree(String parentId, Map<String, List<SysDept>> map) {
        List<SysDept> list = map.get(parentId);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<SysDeptTreeBO> deptList = new ArrayList<>();
        for (SysDept sysDept : list) {
            SysDeptTreeBO rspBO = new SysDeptTreeBO();
            BeanUtils.copyProperties(sysDept, rspBO);
            //递归执行下一级菜单
            if (!"0".equals(sysDept.getDeptId())) {
                List<SysDeptTreeBO> child = buildTree(sysDept.getDeptId(), map);
                rspBO.setChildren(child);
            }
            deptList.add(rspBO);
        }
        return deptList;
    }


}
