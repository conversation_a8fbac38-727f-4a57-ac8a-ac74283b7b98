package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysUpvoteRecordApi;
import com.tydic.nbchat.admin.api.bo.SysUpvoteRecordBO;
import com.tydic.nbchat.admin.api.bo.eum.ProductModuleEnum;
import com.tydic.nbchat.admin.mapper.SysUpvoteRecordMapper;
import com.tydic.nbchat.admin.mapper.po.SysMenuTpl;
import com.tydic.nbchat.admin.mapper.po.SysUpvoteRecord;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime：2024/9/19 10:16
 * @description:
 */
@Service
public class SysUpvoteRecordServiceImpl implements SysUpvoteRecordApi {
    @Resource
    private SysUpvoteRecordMapper sysUpvoteRecordMapper;

    @Override
    public Rsp addNewUpvoteRecord(SysUpvoteRecordBO recordBO) {
        SysUpvoteRecord record = new SysUpvoteRecord();
        BeanUtils.copyProperties(recordBO, record);
        String productModule = recordBO.getProductModule();
        if (productModule != null && !productModule.trim().isEmpty()) {
            record.setProductModule(ProductModuleEnum.getDescByCode(recordBO.getProductModule()));
        } else {
            record.setProductModule(ProductModuleEnum.OTHER.getCode());
        }
        if(record.getId() == null){
            record.setCreateTime(new Date());
            return BaseRspUtils.createSuccessRsp(sysUpvoteRecordMapper.insertSelective(record));
        }else {
            return BaseRspUtils.createSuccessRsp(sysUpvoteRecordMapper.update(record));
        }
    }

    @Override
    public Rsp getById(SysUpvoteRecordBO recordBO) {
        SysUpvoteRecord sysUpvoteRecord = sysUpvoteRecordMapper.queryById(recordBO.getId());
        SysUpvoteRecordBO bo = new SysUpvoteRecordBO();
        BeanUtils.copyProperties(sysUpvoteRecord, bo);
        return BaseRspUtils.createSuccessRsp(bo);
    }

    @Override
    public RspList getUpvoteRecordList(SysUpvoteRecordBO recordBO) {
        SysUpvoteRecord record = new SysUpvoteRecord();
        BeanUtils.copyProperties(recordBO, record);
        Page<SysMenuTpl> page = PageHelper.startPage(recordBO.getPage(), recordBO.getLimit());
        sysUpvoteRecordMapper.selectAll(record);
        List<SysUpvoteRecordBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), list, SysUpvoteRecordBO.class);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }


}
