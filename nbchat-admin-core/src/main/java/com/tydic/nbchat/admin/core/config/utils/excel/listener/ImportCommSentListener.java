package com.tydic.nbchat.admin.core.config.utils.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.tydic.nbchat.admin.core.config.utils.excel.ImportSentenceDTO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description //解析知识表格监听
 **/
@Slf4j
public class ImportCommSentListener extends AnalysisEventListener<ImportSentenceDTO> {

    private List<ImportSentenceDTO> excelRecord = new ArrayList<>();

    public List<ImportSentenceDTO> getRecord(){
        return this.excelRecord;
    }

    @Override
    @SneakyThrows
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        if (!(headMap.containsValue("所属分类") &&!(headMap.containsValue("内容名称"))&& headMap.containsValue("排序标识")
                && headMap.containsValue("知识内容"))) {
            throw new Exception("请检查模板是否正确");
        }
    }

    /**
     * 每读取一行就会调用一次
     *
     * @param data
     * @param context
     */
    @Override
    public void invoke(ImportSentenceDTO data, AnalysisContext context) {
        log.info("读取到一条数据内容为：{}", JSON.toJSONString(data));
        excelRecord.add(data);
    }

    /**
     * 所有数据解析完成了 都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        log.info("最后一次被调用");
    }


}