package com.tydic.nbchat.admin.core.config.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "nbchat-admin.config.http-proxy")
public class HttpProxyConfigProperties {
    private Boolean enable = false;
    //从 urlPrefix 替换为 targetPrefix
    private String urlPrefix = "http://images.unsplash.com/";
    private String targetPrefix = "";
}
