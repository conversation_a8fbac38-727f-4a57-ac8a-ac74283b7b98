package com.tydic.nbchat.admin.core.busi.impl;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.bo.sentence.*;
import com.tydic.nbchat.admin.core.busi.CommSentenceTypeBusiService;
import com.tydic.nbchat.admin.core.config.utils.InnerCommsentenceConstant;
import com.tydic.nbchat.admin.mapper.KnowledgeClassifyMapper;
import com.tydic.nbchat.admin.mapper.KnowledgeContentMapper;
import com.tydic.nbchat.admin.mapper.po.KnowledgeClassifyPo;
import com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CommSentenceTypeBusiServiceImpl implements CommSentenceTypeBusiService {

    @Resource
    KnowledgeClassifyMapper knowledgeClassifyMapper;
    @Resource
    private KnowledgeContentMapper knowledgeContentMapper;

    /**
     * <AUTHOR>
     * @description 保存知识类型
     **/
    @MethodParamVerifyEnable
    @Override
    public Rsp saveCommSentenceType(SaveCommSentenceTypeReqBO reqBO) {
        log.info("保存知识分类 ：{}", JSON.toJSONString(reqBO));
        ValidTypeClassesReqBO classesReqBO = new ValidTypeClassesReqBO();
        BeanUtils.copyProperties(reqBO,classesReqBO);
        if(!validTypeClasses(classesReqBO)){
            return BaseRspUtils.createErrorRsp("新增失败，父类目分类不匹配");
        }
        ValidTypeNameReqBO var = new ValidTypeNameReqBO();
        BeanUtils.copyProperties(reqBO, var);
        Rsp<ValidTypeNameRspBO> rsp = validTypeName(var);
        if (!rsp.getData().getUnique()) {
            return BaseRspUtils.createErrorRsp("知识类型名称已存在");
        }
        if (reqBO.getTypeName().length() > 100) {
            return BaseRspUtils.createErrorRsp("新增失败,类目名称不能大于100字符");
        }
        if (StringUtils.isNotEmpty(reqBO.getTypeDesc()) && reqBO.getTypeDesc().length() > 200) {
            return BaseRspUtils.createErrorRsp("新增失败，类目描述不能大于200字符");
        }
        KnowledgeClassifyPo po = new KnowledgeClassifyPo();
        BeanUtils.copyProperties(reqBO, po);
        po.setCreateUserId(reqBO.get_userId());
        po.setUpdateUserId(reqBO.get_userId());
        Date now = new Date();
        po.setCreateTime(now);
        po.setUpdateTime(now);

        int res = knowledgeClassifyMapper.insertSelective(po);
        if (res > 0) {
            log.info("新增知识分类成功");
            return BaseRspUtils.createSuccessRsp(null);
        }
        log.info("新增知识分类失败");
        return BaseRspUtils.createErrorRsp("新增知识分类失败");
    }

    private boolean validTypeClasses(ValidTypeClassesReqBO reqBo) {
        if(reqBo.getParentId() != null){
            KnowledgeClassifyPo condition = new KnowledgeClassifyPo();
            condition.setCreateUserId(reqBo.get_userId());
            condition.setTypeId(reqBo.getParentId());
            condition.setClasses(reqBo.getClasses());
            List<KnowledgeClassifyPo> res = knowledgeClassifyMapper.selectByCondition(condition);
            if(res.size() <= 0){
                return false;
            }
        }
        return true;
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp updateCommSentenceType(UpdateCommSentenceTypeReqBO reqBo) {
        log.info("更新知识分类 ：{}", JSON.toJSONString(reqBo));
        ValidTypeNameReqBO var = new ValidTypeNameReqBO();
        BeanUtils.copyProperties(reqBo, var);
        var.setTypeId(reqBo.getTypeId().toString());
        var.setUpdate(true);
        Rsp<ValidTypeNameRspBO> rsp = validTypeName(var);
        if (!rsp.getData().getUnique()) {
            return BaseRspUtils.createErrorRsp("知识类型名称已存在");
        }
        KnowledgeClassifyPo po = new KnowledgeClassifyPo();
        po.setCreateUserId(reqBo.get_userId());
        po.setTypeId(Long.valueOf(reqBo.getTypeId()));
        po.setClasses(reqBo.getClasses());
        List<KnowledgeClassifyPo> list = knowledgeClassifyMapper.selectByPo(po);
        if (!CollectionUtils.isEmpty(list)) {
            KnowledgeClassifyPo beforeUp = list.get(0);
            beforeUp.setTypeName(reqBo.getTypeName());
            beforeUp.setUpdateTime(new Date());
            beforeUp.setUpdateUserId(reqBo.get_userId());
            int res = knowledgeClassifyMapper.updateByPrimaryKeySelective(beforeUp);
            if (res > 0) {
                log.info("修改知识分类成功");
                return BaseRspUtils.createSuccessRsp(null, "修改知识分类成功");
            }
        }
        return BaseRspUtils.createErrorRsp("修改知识分类失败");
    }


    @MethodParamVerifyEnable
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Rsp deleteCommSentenceType(DelCommSentenceTypeReqBO reqBo) {
        Long typeId = Long.parseLong(reqBo.getTypeId());
        KnowledgeClassifyPo po = new KnowledgeClassifyPo();
        po.setCreateUserId(reqBo.get_userId());
        po.setTypeId(typeId);
        Page<KnowledgeClassifyPo> result = PageHelper.startPage(reqBo.getPage(), reqBo.getLimit());
        knowledgeClassifyMapper.selectByPo(po);
        List<KnowledgeClassifyPo> list = result.getResult();
        if (list != null && list.size() > 0 && list.get(0) != null) {
            KnowledgeClassifyPo beforeDelete = list.get(0);
            KnowledgeClassifyPo delPO = new KnowledgeClassifyPo();
            delPO.setCreateUserId(reqBo.get_userId());
            delPO.setParentId(beforeDelete.getTypeId());
            List<KnowledgeClassifyPo> delRes = knowledgeClassifyMapper.selectByPo(delPO);
            if (!CollectionUtils.isEmpty(delRes)) {
                return BaseRspUtils.createErrorRsp("该类型下有子类型，无法删除");
            }
            KnowledgeContentPo contPO = new KnowledgeContentPo();
            contPO.setCreateUserId(beforeDelete.getCreateUserId());
            contPO.setTypeId(beforeDelete.getTypeId());
            List<KnowledgeContentPo> conRes = knowledgeContentMapper.selectByPo(contPO);
            if (!CollectionUtils.isEmpty(conRes)) {
                return BaseRspUtils.createErrorRsp("该类型下有内容，无法删除");
            }

            int res = knowledgeClassifyMapper.deleteByPrimaryKey(typeId);
            if (res > 0) {
                // 查询子集分类
                KnowledgeClassifyPo commSentenceTypePo = new KnowledgeClassifyPo();
                commSentenceTypePo.setParentId(typeId);
                commSentenceTypePo.setCreateUserId(reqBo.get_userId());
                List<KnowledgeClassifyPo> commSentenceTypePoList = knowledgeClassifyMapper.selectByPo(commSentenceTypePo);
                //兜底策略，如果还有子类型，则全部删除
                if (!CollectionUtils.isEmpty(commSentenceTypePoList)) {
                    List<Long> typeIdList = commSentenceTypePoList.stream().map(KnowledgeClassifyPo::getTypeId).collect(Collectors.toList());
                    typeIdList.add(typeId);
                    // 删除知识
                    knowledgeContentMapper.deleteByTypeIds(reqBo.get_userId(),typeIdList);
                    // 删除子集分类
                    knowledgeClassifyMapper.deleteByParentId(reqBo.get_userId(), Long.valueOf(reqBo.getTypeId()));
                }
                log.info("删除知识分类成功");
                return BaseRspUtils.createSuccessRsp(null, "删除知识分类成功");
            }
        }

        return BaseRspUtils.createErrorRsp("删除知识分类失败");
    }


    @MethodParamVerifyEnable
    public Rsp<ValidTypeNameRspBO> validTypeName(ValidTypeNameReqBO reqBo) {
        ValidTypeNameRspBO validTypeNameRspBo = new ValidTypeNameRspBO();
        Long typeId = null;
        if (reqBo.getTypeId() != null) {
            typeId = Long.valueOf(reqBo.getTypeId());
        }

        KnowledgeClassifyPo condition = new KnowledgeClassifyPo();
//        Long parentId = reqBo.getParentId() == null ? 0 : reqBo.getParentId();
        condition.setCreateUserId(reqBo.get_userId());
        condition.setTypeName(reqBo.getTypeName());
        condition.setClasses(reqBo.getClasses());
//        condition.setParentId(parentId);
        List<KnowledgeClassifyPo> res = knowledgeClassifyMapper.selectByCondition(condition);

        //能查询到返回重复；否则返回不重复
        if (res.size() > 0) {

            if (reqBo.isUpdate() && res.get(0).getTypeId().equals(typeId)) {
                validTypeNameRspBo.setUnique(true);
                return BaseRspUtils.createSuccessRsp(validTypeNameRspBo);
            }

            validTypeNameRspBo.setUnique(false);
            return BaseRspUtils.createSuccessRsp(validTypeNameRspBo);
        }

        validTypeNameRspBo.setUnique(true);
        return BaseRspUtils.createSuccessRsp(validTypeNameRspBo);
    }

    @Override
    public RspList queryCommSentenceTreeData(QueryCommSentenceTreeReqBO req) {
        log.info("查询知识树形列表 ：{}", JSON.toJSONString(req));
        KnowledgeClassifyPo po = new KnowledgeClassifyPo();
        BeanUtils.copyProperties(req, po);
        po.setCreateUserId(req.get_userId());
        List<KnowledgeClassifyPo> commSentenceTypePoList = knowledgeClassifyMapper.selectByPo(po);

        if(req.getClasses().equals("2")){
            List<KnowledgeClassifyPo> defaultTypes = knowledgeClassifyMapper.selectDefaultType(InnerCommsentenceConstant.TENANTCODE);
            commSentenceTypePoList.addAll(defaultTypes);
        }

        if (CollectionUtils.isEmpty(commSentenceTypePoList)) {
            return BaseRspUtils.createSuccessRspList(new ArrayList<>());
        }

        List<CommSentenceTypeBO> rows = new ArrayList<>();
        for (KnowledgeClassifyPo res : commSentenceTypePoList) {
            if (!res.getParentId().equals(0L)) {
                continue;
            }
            CommSentenceTypeBO row = new CommSentenceTypeBO();
            BeanUtils.copyProperties(res, row);
            row.setLevel(1);
            handleChildrenCommSentence(commSentenceTypePoList, row);
            rows.add(row);
        }
        return BaseRspUtils.createSuccessRspList(rows, rows.size());
    }

    private void handleChildrenCommSentence
            (List<KnowledgeClassifyPo> list, CommSentenceTypeBO currRow) {
        List<CommSentenceTypeBO> childList = new ArrayList<>();
        for (KnowledgeClassifyPo var : list) {
            if (var.getParentId().equals(currRow.getTypeId())) {
                CommSentenceTypeBO row = new CommSentenceTypeBO();
                BeanUtils.copyProperties(var, row);
                row.setLevel(currRow.getLevel() + 1);
                childList.add(row);
                handleChildrenCommSentence(list, row);
            }
        }
        currRow.setChildrenCommSentenceTypeList(childList);
    }


}
