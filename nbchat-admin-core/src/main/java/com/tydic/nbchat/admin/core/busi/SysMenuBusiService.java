package com.tydic.nbchat.admin.core.busi;

import com.tydic.nbchat.admin.api.bo.menu.SysMenuBO;
import com.tydic.nbchat.admin.api.bo.menu.SysMenuButtonBO;
import com.tydic.nbchat.admin.api.bo.menu.SysMenuTplRelBO;
import com.tydic.nbchat.admin.mapper.SysMenuButtonMapper;
import com.tydic.nbchat.admin.mapper.SysMenuMapper;
import com.tydic.nbchat.admin.mapper.SysMenuTplRelMenuMapper;
import com.tydic.nbchat.admin.mapper.po.SysMenu;
import com.tydic.nbchat.admin.mapper.po.SysMenuButton;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SysMenuBusiService {

    @Resource
    private SysMenuMapper sysMenuMapper;
    @Resource
    private SysMenuButtonMapper sysMenuButtonMapper;
    @Resource
    private SysMenuTplRelMenuMapper sysMenuTplRelMenuMapper;


    public List<SysMenuBO> getMenusByTplCode(String tplCode) {
        if (StringUtils.isBlank(tplCode)) {
            return new ArrayList<>();
        }
        List<SysMenuTplRelBO> menuRels = new ArrayList<>();
        sysMenuTplRelMenuMapper.selectAllByTplCode(tplCode).forEach(rel -> {
            SysMenuTplRelBO relBO = new SysMenuTplRelBO();
            BeanUtils.copyProperties(rel, relBO);
            menuRels.add(relBO);
        });
        Map<String, SysMenuTplRelBO> menuRelMap = menuRels.stream().
                collect(Collectors.toMap(SysMenuTplRelBO::getMenuCode, Function.identity()));
        return getMenus("", menuRelMap);
    }


    public List<SysMenuBO> getMenus(String menuCode) {
        return getMenus(menuCode, null);
    }

    public List<SysMenuBO> getMenus(String menuCode, Map<String, SysMenuTplRelBO> filterMap) {
        List<SysMenu> menus = sysMenuMapper.selectByCode(menuCode);
        log.debug("查询菜单树: {}|{}", menuCode,menus.size());
        Map<String, List<SysMenu>> menuMap = menus.stream().collect(Collectors.groupingBy(SysMenu::getParentCode));
        List<SysMenuBO> list = getChildMenu("0",menuMap, filterMap);
        log.info("查询菜单树: {}|{}", menuCode,menus.size());
        return list;
    }

    /**
     * 递归遍历，按照parent_code=menu_code的方式组装菜单树形结构
     *
     * @param parentCode 父菜单编码
     * @param menuMap  菜单map
     * @param filterMap 过滤的菜单
     * @return
     */
    private List<SysMenuBO> getChildMenu(String parentCode,
                                         Map<String, List<SysMenu>> menuMap,
                                         Map<String, SysMenuTplRelBO> filterMap) {
        List<SysMenuBO> menuBOS = Lists.newArrayList();
        //根据parent_code=menu_code的方式组装菜单树形结构,不一定会有根目录
        List<SysMenu> menus = menuMap.get(parentCode);
        if (menus != null && !menus.isEmpty()) {
            for (SysMenu menu : menus) {
                SysMenuBO menuBO = new SysMenuBO();
                BeanUtils.copyProperties(menu, menuBO);
                if (filterMap != null) {
                    if (filterMap.containsKey(menu.getMenuCode())) {
                        List<SysMenuButtonBO> buttons = getButtons(menu.getMenuCode());
                        String buttonCodes = filterMap.get(menu.getMenuCode()).getButtonCodes();
                        if (StringUtils.isNotBlank(buttonCodes)) {
                            //过滤按钮(只显示配置的按钮
                            String[] filterButtons = filterMap.get(menu.getMenuCode()).
                                    getButtonCodes().split(",");
                            List<String> buttonCodeList = com.google.common.collect.Lists.newArrayList(filterButtons);
                            List<SysMenuButtonBO> filterButtonList = buttons.stream().
                                    filter(button -> buttonCodeList.contains(button.getButtonCode())).collect(Collectors.toList());
                            menuBO.setButtons(filterButtonList);
                        }
                        menuBOS.add(menuBO);
                    } else {
                        continue;
                    }
                } else {
                    menuBO.setButtons(getButtons(menu.getMenuCode()));
                    menuBOS.add(menuBO);
                }
                //递归执行下一级菜单
                if (!"0".equals(menu.getMenuCode())) {
                    List<SysMenuBO> child = getChildMenu(menu.getMenuCode(), menuMap, filterMap);
                    menuBO.setChildren(child);
                }
            }
        }
        return menuBOS;
    }


    private List<SysMenuButtonBO> getButtons(String menuCode) {
        List<SysMenuButtonBO> list = new ArrayList<>();
        List<SysMenuButton> buttons = sysMenuButtonMapper.selectByMenuCode(menuCode);
        NiccCommonUtil.copyList(buttons, list, SysMenuButtonBO.class);
        return list;
    }

}
