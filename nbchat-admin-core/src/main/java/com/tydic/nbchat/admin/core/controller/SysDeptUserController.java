package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.StudentManagementApi;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryReqBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserSaveReqBO;
import com.tydic.nicc.common.bo.premission.Logical;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/dept/user")
public class SysDeptUserController {

    private final StudentManagementApi studentManagementApi;

    /**
     * 新增学员
     * @param @param studentManagementReqBO 学生管理要求博
     * @return @return {@link Rsp }
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin,
            UserRoleConstants.opUser},
            logical = Logical.OR)
    @MethodParamVerifyEnable
    @PostMapping("/add")
    public Rsp addDeptUser(@RequestBody SysDeptUserSaveReqBO studentManagementReqBO){
        return studentManagementApi.addNewStudent(studentManagementReqBO);
    }

    @PostMapping("/update")
    public Rsp updateDeptUser(@RequestBody SysDeptUserSaveReqBO studentManagementReqBO){
        return studentManagementApi.addNewStudent(studentManagementReqBO);
    }

    /**
     * 查询学员信息
     * @param @param studentManagementReqBO 学生管理要求博
     * @return @return {@link Rsp }
     */
    @RequiresRole(value = {UserRoleConstants.sysAdmin, UserRoleConstants.tenantAdmin, UserRoleConstants.orgAdmin,
            UserRoleConstants.opUser},
            logical = Logical.OR)
    @PostMapping("/info")
    public Rsp getUserInfo(@RequestBody SysDeptUserQueryReqBO queryReqBO){
        return studentManagementApi.getUserInfoQR(queryReqBO);
    }

    /**
     * 二维码用户信息查询
     * @param queryReqBO
     * @return
     */
    @PostMapping("/qr/info")
    public Rsp getUserInfoQR(@RequestBody SysDeptUserQueryReqBO queryReqBO){
        return studentManagementApi.getUserInfoQR(queryReqBO.getTenantCode(), queryReqBO.getUserId());
    }

    /**
     * 导入学员
     * @param file
     * @return
     */
    @PostMapping("/import")
    public Rsp importUsers(@RequestParam("file") MultipartFile file, @RequestParam("tenantCode") String tenantCode){
        return studentManagementApi.importUsers(file, tenantCode);
    }

    /**
     * 同步用户详情
     * @return
     */
    @PostMapping("/sync")
    public Rsp syncUserDetail(){
        return studentManagementApi.syncUserDetail();
    }
}
