package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.mapper.UpdateUserIdMapper;
import com.tydic.nbchat.admin.mapper.po.UpdateUserIdPO;
import com.tydic.nbchat.admin.mapper.po.UpdateUserRecord;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class UpdateUserIdServiceImpl {

    private final UpdateUserIdMapper updateUserIdMapper;

    private final TableColumn tableColumn;
    public UpdateUserIdServiceImpl(UpdateUserIdMapper updateUserIdMapper) {
        this.updateUserIdMapper = updateUserIdMapper;
        tableColumn = new TableColumn();
    }

    @Transactional(rollbackFor = Exception.class , propagation = Propagation.REQUIRED)
    public Rsp update(String oldUserId, String newUserId){
        for (Map.Entry<String, String> entry : tableColumn.map.entrySet()) {
            String table = entry.getKey();
            String column = entry.getValue();
            UpdateUserIdPO updateUserIdPO = new UpdateUserIdPO(table, column, oldUserId, newUserId);
            try {
                int selected = updateUserIdMapper.selectByUserId(updateUserIdPO);
                if (selected > 0) {
                    updateUserIdMapper.updateByUserId(updateUserIdPO);
                    updateUserIdMapper.insertRecord(new UpdateUserRecord(table, column, oldUserId, newUserId, new Date()));
                }
            } catch (Exception e) {
                log.error("更新{}表 的{}列出现错误",table,column);
                throw new RuntimeException(e);
            }
        }
        return BaseRspUtils.createSuccessRsp("更新成功");
    }

    private class TableColumn{
        private final Map<String, String> map = new HashMap<>();

        public TableColumn(){
            map.put("file_upload_record","upload_user");
            map.put("nbchat_knowledge_classify","create_user_id");
            map.put("nbchat_knowledge_content","create_user_id");
            map.put("nbchat_research_category","create_user_id");
            map.put("nbchat_research_files","user_id");
            map.put("nbchat_research_msg","user_id");
            map.put("nbchat_research_part","user_id");
            map.put("nbchat_research_qa","user_id");
            map.put("nbchat_research_view_his","user_id");
            map.put("nbchat_session","user_id");
            map.put("nbchat_session_action_log","user_id");
            map.put("nbchat_session_msg","user_id");
            map.put("nbchat_session_share","user_id");
            map.put("nbchat_session_share_record","user_id");
            map.put("nbchat_wx_user","user_id");
        }
    }

}
