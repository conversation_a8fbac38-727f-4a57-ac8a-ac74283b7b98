package com.tydic.nbchat.admin.core.busi.impl;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import com.tydic.nbchat.admin.api.bo.sentence.*;
import com.tydic.nbchat.admin.core.busi.CommSentenceBusiService;
import com.tydic.nbchat.admin.core.config.properties.KnowledgeConfig;
import com.tydic.nbchat.admin.core.config.utils.InnerCommsentenceConstant;
import com.tydic.nbchat.admin.core.config.utils.RandomStringGenerator;
import com.tydic.nbchat.admin.mapper.KnowledgeClassifyMapper;
import com.tydic.nbchat.admin.mapper.KnowledgeMapper;
import com.tydic.nbchat.admin.mapper.KnowledgeContentMapper;
import com.tydic.nbchat.admin.mapper.KnowledgeShareMapper;
import com.tydic.nbchat.admin.mapper.po.CommSentenceRowPO;
import com.tydic.nbchat.admin.mapper.po.KnowledgeClassifyPo;
import com.tydic.nbchat.admin.mapper.po.KnowledgeContentPo;
import com.tydic.nbchat.admin.mapper.po.KnowledgeSharePo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.DateTimeUtil;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class CommSentenceBusiServiceImpl implements CommSentenceBusiService {

    @Resource
    private KnowledgeContentMapper knowledgeContentMapper;
    @Resource
    private KnowledgeMapper knowledgeMapper;
    @Resource
    private KnowledgeClassifyMapper knowledgeClassifyMapper;
    @Resource
    private KnowledgeShareMapper knowledgeShareMapper;
    @Resource
    private KnowledgeConfig knowledgeConfig;


    @Transactional
    @Override
    public Rsp saveCommSentence(CommSentence req) {
        log.info("保存知识 : {}", JSON.toJSONString(req));
        if(StringUtils.isBlank(req.getCreateUserId())) {
            return BaseRspUtils.createErrorRsp("用户参数异常");
        }
        if(req.getTypeId() != null && req.getTypeId() != 0L){
            KnowledgeClassifyPo classifyPo = knowledgeClassifyMapper.selectByPrimaryKey(req.getTypeId());
            if(classifyPo == null || StringUtils.isBlank(classifyPo.getClasses()) || !classifyPo.getClasses().equals(req.getClasses()) ){
                return BaseRspUtils.createErrorRsp("目录分类无效");
            }
        }
        Date nowTime = new Date();
        KnowledgeContentPo record = new KnowledgeContentPo();
        BeanUtils.copyProperties(req, record);
        record.setUpdateUserId(req.getCreateUserId());
        record.setUpdateUserId(req.getCreateUserId());
        record.setUpdateTime(nowTime);
        record.setCreateTime(nowTime);
        String sentenceId = NiccCommonUtil.createImUserId(true);
        record.setSentenceId(sentenceId);
        Long typeId = record.getTypeId() != null ? record.getTypeId() : 0;
        record.setTypeId(typeId);
        int res = knowledgeMapper.insertSelective(record);
        if (res > 0) {
            KnowledgeContentPo ret = knowledgeContentMapper.selectByPrimaryKey(sentenceId);
            return BaseRspUtils.createSuccessRsp(ret, "保存知识成功");
        }
        return BaseRspUtils.createErrorRsp("保存知识失败");
    }

    @Transactional
    @Override
    public Rsp updateCommSentence(CommSentence req) {
        log.info("更新知识 : {}", JSON.toJSONString(req));
        String sentenceId = req.getSentenceId();
        KnowledgeContentPo record = knowledgeContentMapper.selectByPrimaryKey(sentenceId);
        if (ObjectUtils.isEmpty(record)) {
            return BaseRspUtils.createErrorRsp("知识数据已失效，请勿重复操作");
        }
        if(InnerCommsentenceConstant.TENANTCODE.equals(record.getTenantCode())){
            return BaseRspUtils.createErrorRsp("该知识不支持操作");
        }
        if (req.getContent()== null) {
            return BaseRspUtils.createErrorRsp("知识内容不能为空");
        }
        if(req.getCreateUserId()==null || !req.getCreateUserId().equals(record.getCreateUserId())){
            return BaseRspUtils.createErrorRsp("无法修改他人内容");
        }
        Date nowTime = new Date();
        BeanUtils.copyProperties(req, record);
        record.setUpdateUserId(record.getCreateUserId());
        record.setUpdateTime(nowTime);
        knowledgeContentMapper.updateByPrimaryKeySelective(record);
        log.debug("更新知识成功, sentenceId={}", sentenceId);
        record = knowledgeContentMapper.selectByPrimaryKey(sentenceId);
        return BaseRspUtils.createSuccessRsp(record, "修改成功");
    }

    @Transactional
    @Override
    public Rsp deleteCommSentence(CommSentence req) {
        log.info("删除知识 : {}", JSON.toJSONString(req));

        String sentenceId = req.getSentenceId();
        KnowledgeContentPo record = knowledgeContentMapper.selectByPrimaryKey(sentenceId);

        if (record == null) {
            return BaseRspUtils.createErrorRsp("知识数据已失效，请勿多次操作");
        }
        if(InnerCommsentenceConstant.TENANTCODE.equals(record.getTenantCode())){
            return BaseRspUtils.createErrorRsp("该知识不支持操作");
        }
        if(req.getCreateUserId() == null || !req.getCreateUserId().equals(record.getCreateUserId())){
            return BaseRspUtils.createErrorRsp("无法删除他人内容");
        }

        int i = knowledgeContentMapper.deleteByPrimaryKey(sentenceId);
        if (i > 0) {
            return BaseRspUtils.createSuccessRsp(null, "删除成功");
        }
        return BaseRspUtils.createErrorRsp("删除失败");
    }

    @Override
    public RspList getCommSentencePageList(QueryCommSentencePageListReqBO req) {
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotEmpty(req.getTypeId())) {
            params.put("typeId", req.getTypeId());
        }
        if (StringUtils.isNotEmpty(req.getContentTitle())) {
            params.put("contentTitle", req.getContentTitle());
        }
        params.put("userId",req.get_userId());
        params.put("classes",req.getClasses());
        params.put("inner", InnerCommsentenceConstant.TENANTCODE);
        Page<CommSentenceRowPO> res = PageHelper.startPage(req.getPage(), req.getLimit());
        knowledgeMapper.getCommSentencePageList(params);
        List<CommSentenceRow> rows = new ArrayList<>();
        NiccCommonUtil.copyList(res.getResult(), rows, CommSentenceRow.class);
        return BaseRspUtils.createSuccessRspList(rows, res.getTotal());
    }

    @Override
    public Rsp getKnowledgeContentById(QueryKnowledgeConReq req) {
        String sentenceId = req.getSentenceId();
        KnowledgeContentPo record = knowledgeContentMapper.selectByPrimaryKey(sentenceId);

        if(record == null){
            return BaseRspUtils.createErrorRsp("知识数据已失效，请勿多次操作");
        }
        if(StringUtils.isBlank(req.get_userId()) || !req.get_userId().equals(record.getCreateUserId())){
            return BaseRspUtils.createErrorRsp("无法查询他人内容");
        }
        QueryKnowledgeConRsp knowledgeConRsp = new QueryKnowledgeConRsp();
        BeanUtils.copyProperties(record,knowledgeConRsp);
        return BaseRspUtils.createSuccessRsp(knowledgeConRsp,"查询成功");
    }

    @Override
    public Rsp getShareUrl(ShareKnowledgeReqBO req) {
        if(req.isNeedKey() && StringUtils.isEmpty(req.getShareKey())){
            req.setShareKey(RandomStringGenerator.generateRandomString(knowledgeConfig.getShareKeyLength()));
        }

        KnowledgeSharePo sharePo = new KnowledgeSharePo();
        BeanUtils.copyProperties(req,sharePo);
        sharePo.setSentenceShareId(NiccCommonUtil.createImUserId(true));
        sharePo.setShareUrl(knowledgeConfig.getShareUrlPrefix()+sharePo.getSentenceShareId());
        if(req.getExpiredDay() != -1){
            sharePo.setExpiredDate(DateTimeUtil.DateAddDayOfYear(req.getExpiredDay()));
        }else {
            sharePo.setExpiredDate(DateTimeUtil.convertAsDate("9999-01-01 00:00:00"));
        }
        sharePo.setCreatedAt(new Date());
        int i = knowledgeShareMapper.insertSelective(sharePo);
        if(i > 0){
            log.info("保存分享知识记录成功");
        }
        KnowledgeSharePo ret = knowledgeShareMapper.selectByPrimaryKey(sharePo.getSentenceShareId());
        log.info("分享记录：{},分享id:{}",ret,sharePo.getSentenceShareId());
        return BaseRspUtils.createSuccessRsp(ret);
    }

    @Override
    public Rsp queryShare(ShareKnowledgeReqBO reqBO) {
        log.info("查看分享的知识,分享知识:{}",reqBO);
        KnowledgeSharePo knowledgeSharePo = new KnowledgeSharePo();
        knowledgeSharePo.setSentenceShareId(reqBO.getSentenceShareId());
        KnowledgeSharePo sharePo = knowledgeShareMapper.queryByCondition(knowledgeSharePo);
        if(Objects.isNull(sharePo)){
            log.info("该条分享不存在或已过期:{}",reqBO.getSentenceShareId());
            return BaseRspUtils.createErrorRsp("该条分享不存在或已过期");
        }

        return BaseRspUtils.createSuccessRsp(sharePo);
    }

    @Override
    public Rsp checkShareKey(ShareKnowledgeReqBO reqBO) {
        KnowledgeSharePo knowledgeSharePo = new KnowledgeSharePo();
        knowledgeSharePo.setSentenceShareId(reqBO.getSentenceShareId());
        knowledgeSharePo.setShareKey(reqBO.getShareKey());
        KnowledgeSharePo selected = knowledgeShareMapper.queryByCondition(knowledgeSharePo);
        if(!Objects.isNull(selected) ){
            KnowledgeContentPo contentPo = knowledgeContentMapper.selectByPrimaryKey(selected.getSentenceId());
            if(!Objects.isNull(contentPo)){
                return BaseRspUtils.createSuccessRsp(contentPo);
            }
        }
        return BaseRspUtils.createErrorRsp("分享码错误或知识已过期");
    }

    private Short parseShort(String value) {
        return StringUtils.isNotEmpty(value) ? Short.parseShort(value) : null;
    }

    private Integer parseInt(String value) {
        return StringUtils.isNotEmpty(value) ? Integer.parseInt(value) : null;
    }

    private Long parseLong(String value) {
        return StringUtils.isNotEmpty(value) ? Long.parseLong(value) : null;
    }
}
