package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysPostApi;
import com.tydic.nbchat.admin.api.bo.post.SysPostBO;
import com.tydic.nbchat.admin.mapper.SysPostMapper;
import com.tydic.nbchat.admin.mapper.po.SysPost;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SysPostServiceImpl implements SysPostApi {

    @Resource
    SysPostMapper sysPostMapper;

    @Override
    public RspList query(SysPostBO request) {
        log.info("岗位查询: {}", request);
        SysPost cond = new SysPost();
        BeanUtils.copyProperties(request,cond);
        if (StringUtils.isNotEmpty(request.getTargetTenant())) {
            cond.setTenantCode(request.getTargetTenant());
        }
        Page<SysPost> page = PageHelper.startPage(request.getPage(), request.getLimit());
        sysPostMapper.selectAll(cond);
        List<SysPostBO> res = new ArrayList<>();
        NiccCommonUtil.copyList(page,res,SysPostBO.class);
        return BaseRspUtils.createSuccessRspList(res,page.getTotal());
    }

    @Override
    public Rsp save(SysPostBO request) {
        log.info("保存岗位信息: {}", request);
        SysPost sysPost = new SysPost();
        BeanUtils.copyProperties(request,sysPost);
        if (request.getPostId() == null){
            sysPost.setCreateTime(new Date());
            sysPost.setIsValid("1");
            sysPostMapper.insertSelective(sysPost);
        }else{
            sysPostMapper.update(sysPost);
        }
        return BaseRspUtils.createSuccessRsp(sysPost.getPostId());
    }
    /**
     * 删除岗位
     */
    @Override
    public Rsp delete(SysPostBO request) {
        log.info("删除岗位: {}", request);
        SysPost sysPost = new SysPost();
        sysPost.setPostId(request.getPostId());
        sysPost.setIsValid("0");
        sysPostMapper.update(sysPost);
        return BaseRspUtils.createSuccessRsp(request.getPostId(),"删除成功");
    }
}
