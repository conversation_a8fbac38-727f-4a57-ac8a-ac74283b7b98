package com.tydic.nbchat.admin.core.busi;

import com.tydic.nbchat.admin.api.bo.eum.PermissionRoleType;
import com.tydic.nbchat.admin.api.bo.permission.PermissionObject;
import com.tydic.nbchat.admin.api.bo.permission.PermissionObjectUpdateBO;
import com.tydic.nbchat.admin.api.bo.permission.PermissionObjects;
import com.tydic.nbchat.admin.mapper.SysDataPermissionMapper;
import com.tydic.nbchat.admin.mapper.po.PermissionObj;
import com.tydic.nbchat.admin.mapper.po.PermissionObjectsCondition;
import com.tydic.nbchat.admin.mapper.po.SysDataPermission;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class SysDataPermissionBusiService {

    @Resource
    private SysDataPermissionMapper sysDataPermissionMapper;
    private final RedisHelper redisHelper;

    public SysDataPermissionBusiService(RedisHelper redisHelper) {
        this.redisHelper = redisHelper;
    }


    /**
     * 删除权限
     * @param busiType
     * @param busiId
     */
    @Transactional
    public Integer deletePermission(String busiType,String busiId){
        if (StringUtils.isAnyBlank(busiType,busiId)){
            throw new IllegalArgumentException("busiType or busiId is blank");
        }
        return sysDataPermissionMapper.deleteByBusiIdAndType(busiType,busiId);
    }

    /**
     * 获取权限
     * @param busiType
     * @param busiId
     * @param permissionObjects
     * @return
     */
    public Set<String> getUserPermission(String busiType, String busiId, PermissionObjects permissionObjects) {
        PermissionObjectsCondition condition = new PermissionObjectsCondition();
        if (permissionObjects != null) {
            if (permissionObjects.getUserIds() != null && !permissionObjects.getUserIds().isEmpty()){
                condition.setUserId(permissionObjects.getUserIds().get(0));
            }
            if (permissionObjects.getDeptIds() != null && !permissionObjects.getDeptIds().isEmpty()){
                condition.setDeptId(permissionObjects.getDeptIds().get(0));
            }
            condition.setRoleIds(permissionObjects.getRoleIds());
            condition.setPostIds(permissionObjects.getPostIds());
            return sysDataPermissionMapper.selectPermissionByBusiIdAndType(busiId,busiType,condition);
        }
        return Set.of();
    }

    @Transactional
    public Integer updatePermission(PermissionObjectUpdateBO updateBO){
        List<SysDataPermission> list = new ArrayList<>();
        if (updateBO.getPermissionObjects() != null) {
            sysDataPermissionMapper.deleteByBusiIdAndType(updateBO.getBusiType(),updateBO.getBusiId());
            for (PermissionObject permissionObject : updateBO.getPermissionObjects()) {
                SysDataPermission permission = new SysDataPermission();
                permission.setPermission(permissionObject.getPermission());
                permission.setBusiId(updateBO.getBusiId());
                permission.setBusiType(updateBO.getBusiType());
                permission.setTenantCode(updateBO.getTenantCode());
                permission.setCreateBy(updateBO.getCreateBy());
                permission.setCreateTime(new Date());
                if (PermissionRoleType.role.name().equals(permissionObject.getType())) {
                    permission.setRoleId(permissionObject.getObjId());
                } else if (PermissionRoleType.dept.name().equals(permissionObject.getType())) {
                    permission.setDeptId(permissionObject.getObjId());
                } else if (PermissionRoleType.post.name().equals(permissionObject.getType())) {
                    permission.setPostId(permissionObject.getObjId());
                } else if (PermissionRoleType.user.name().equals(permissionObject.getType())) {
                    permission.setUserId(permissionObject.getObjId());
                }
                list.add(permission);
            }
            if (!list.isEmpty()) {
                sysDataPermissionMapper.batchInsert(list);
            }
            reloadPermissionCache(updateBO.getBusiType(),updateBO.getBusiId(),updateBO.getPermissionObjects());
        }
        return list.size();
    }

    private void reloadPermissionCache(String busiType, String busiId,List<PermissionObject> permissionObjs) {
        String key = "nbchat-admin:data_perm:" + busiType + ":" + busiId;
        redisHelper.set(key,permissionObjs, 60 * 60);
    }

    public List<PermissionObject> queryDataPermission(String tenantCode, String busiType, String busiId) {
        String key = "nbchat-admin:data_perm:" + busiType + ":" + busiId;
        List<PermissionObject> list = null;
        try {
            list = (List<PermissionObject>) redisHelper.get(key);
        } catch (Exception e) {
            log.warn("Redis queryDataPermission error: ",e);
        }
        if (list == null) {
            List<PermissionObj> permissionObjs = sysDataPermissionMapper.queryDataPermission(tenantCode, busiType, busiId);
            list = new ArrayList<>();
            NiccCommonUtil.copyList(permissionObjs,list,PermissionObject.class);
            redisHelper.set(key,list);
        }
        return list;
    }

}
