package com.tydic.nbchat.admin.core.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Validator;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysUserMaterialApi;
import com.tydic.nbchat.admin.api.bo.SysUserMaterialReqBO;
import com.tydic.nbchat.admin.core.utils.StringUtil;
import com.tydic.nbchat.admin.mapper.SysUserMaterialMapper;
import com.tydic.nbchat.admin.mapper.po.SysUserMaterial;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SysUserMaterialServiceImpl implements SysUserMaterialApi {
    private final SysUserMaterialMapper sysUserMaterialMapper;

    public SysUserMaterialServiceImpl(SysUserMaterialMapper sysUserMaterialMapper) {
        this.sysUserMaterialMapper = sysUserMaterialMapper;
    }

    @Override
    @MethodParamVerifyEnable
    public Rsp save(SysUserMaterialReqBO reqBO) {
        if (!Validator.isUrl(reqBO.getUrl())) {
            return BaseRspUtils.createErrorRsp("素材地址不合法");
        }
        SysUserMaterial sysUserMaterial = new SysUserMaterial();
        BeanUtils.copyProperties(reqBO, sysUserMaterial);
        sysUserMaterial.setCreateTime(new Date());
        if (StringUtils.isNotBlank(sysUserMaterial.getContent())) {
            sysUserMaterial.setWordCount(StringUtil.getWordCount(sysUserMaterial.getContent()));
        }
        sysUserMaterial.setName(FileUtil.getName(reqBO.getUrl()));
        sysUserMaterial.setSuffix(FileUtil.extName(reqBO.getUrl()));
        sysUserMaterialMapper.insertSelective(sysUserMaterial);
        return BaseRspUtils.createSuccessRsp("保存成功");
    }

    @Override
    public RspList list(SysUserMaterialReqBO reqBO) {
        log.info("查询用户素材:{}", reqBO);
        SysUserMaterial sysUserMaterial = new SysUserMaterial();
        BeanUtils.copyProperties(reqBO, sysUserMaterial);
        Page<SysUserMaterial> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        List<SysUserMaterial> categorys = sysUserMaterialMapper.selectAll(sysUserMaterial);
        List<SysUserMaterial> rspBOList = new ArrayList<>();
        if(CollectionUtils.isEmpty(page.getResult())){
            return BaseRspUtils.createSuccessRspList(rspBOList,0L);
        }
        NiccCommonUtil.copyList(categorys,rspBOList, SysUserMaterial.class);
        return  BaseRspUtils.createSuccessRspList(rspBOList,page.getTotal());
    }

    @Override
    public Rsp delete(SysUserMaterialReqBO reqBO) {
        log.info("删除用户素材:{}", reqBO);
        if (reqBO.getId() == null || reqBO.getId() <= 0) {
            return BaseRspUtils.createErrorRsp("素材ID不得为空");
        }
        int result = sysUserMaterialMapper.deleteByPrimaryKey(reqBO.getId());
        if (result > 0) {
            return BaseRspUtils.createSuccessRsp(result, "删除成功");
        } else {
            return BaseRspUtils.createErrorRsp("删除失败，未找到指定素材");
        }
    }
}