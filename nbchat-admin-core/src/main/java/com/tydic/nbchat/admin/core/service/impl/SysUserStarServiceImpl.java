package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.SysUserStarApi;
import com.tydic.nbchat.admin.api.bo.constants.RedisConstants;
import com.tydic.nbchat.admin.api.star.UserStarRequest;
import com.tydic.nbchat.admin.api.star.UserStarSaveRequest;
import com.tydic.nbchat.admin.mapper.SysUserStarMapper;
import com.tydic.nbchat.admin.mapper.po.SysUserStar;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 3000)
public class SysUserStarServiceImpl implements SysUserStarApi {

    private final SysUserStarMapper starMapper;

    private final RedisHelper redisHelper;

    public SysUserStarServiceImpl(SysUserStarMapper starMapper, RedisHelper redisHelper) {
        this.starMapper = starMapper;
        this.redisHelper = redisHelper;
    }

    /**
     * 新增收藏
     */
    @Override
    @MethodParamVerifyEnable
    public Rsp<String> saveStar(UserStarSaveRequest request) {
        SysUserStar sysUserStar = new SysUserStar();
        BeanUtils.copyProperties(request, sysUserStar);
        String key = buildStarKey(sysUserStar.getTenantCode(), sysUserStar.getUserId(), sysUserStar.getBusiId());

        // 1. 缓存判断
        if (redisHelper.hasKey(key)) {
            return BaseRspUtils.createSuccessRsp("已收藏，无需重复操作");
        }

        // 2. 数据库判断
        SysUserStar existedStar = selectStarByCondition(sysUserStar);
        if (existedStar != null) {
            // 缓存未更新，更新缓存
            redisHelper.set(key, existedStar);
            return BaseRspUtils.createSuccessRsp("已收藏，无需重复操作");
        }

        // 3. 插入数据库
        sysUserStar.setCreateTime(new Date());
        int insertCount = starMapper.insertSelective(sysUserStar);
        if (insertCount > 0) {
            // 插入成功更新缓存
            redisHelper.set(key, sysUserStar);
            return BaseRspUtils.createSuccessRsp("收藏成功");
        }
        return BaseRspUtils.createErrorRsp("收藏失败");
    }

    /**
     * 删除收藏
     */
    @Override
    @MethodParamVerifyEnable
    public Rsp<String> deleteStar(UserStarRequest request) {
        log.info("删除收藏，请求参数：{}", request);
        SysUserStar sysUserStar = new SysUserStar();
        BeanUtils.copyProperties(request, sysUserStar);
        String key = buildStarKey(sysUserStar.getTenantCode(), sysUserStar.getUserId(), sysUserStar.getBusiId());

        // 1. 缓存判断
        boolean isExistInCache = redisHelper.hasKey(key);

        // 2. 数据库判断
        SysUserStar existedStar = selectStarByCondition(sysUserStar);

        // 缓存和数据库都不存在，说明记录已被删除或根本不存在
        if (!isExistInCache && existedStar == null) {
            log.info("记录已删除或不存在，无需重复操作");
            return BaseRspUtils.createSuccessRsp("取消收藏成功");
        }

        // 3. 删除数据库
        int deleteCount = 0;
        if (existedStar != null) {
            deleteCount = starMapper.deleteById(existedStar.getId());
        }

        // 4. 更新缓存
        if (deleteCount > 0 || existedStar != null) {
            redisHelper.del(key);
            return BaseRspUtils.createSuccessRsp("取消收藏成功");
        }
        return BaseRspUtils.createErrorRsp("取消收藏失败");
    }

    /**
     * 查询收藏
     */
    @Override
    @MethodParamVerifyEnable
    public Rsp<String> getStar(UserStarRequest request) {
        SysUserStar sysUserStar = new SysUserStar();
        BeanUtils.copyProperties(request, sysUserStar);
        String key = buildStarKey(request.getTenantCode(), request.getUserId(), request.getBusiId());

        // 1. 查询缓存
        Object cacheData = redisHelper.get(key);
        if (cacheData != null) {
            return BaseRspUtils.createSuccessRsp("查询成功", cacheData.toString());
        }

        // 2. 查询数据库
        SysUserStar existedStar = selectStarByCondition(sysUserStar);
        if (existedStar != null) {
            // 更新缓存
            redisHelper.set(key, existedStar);
            return BaseRspUtils.createSuccessRsp("查询成功", existedStar.toString());
        }
        return BaseRspUtils.createErrorRsp("记录不存在");
    }

    /**
     * 公共方法：构造收藏对应的 Redis Key
     */
    private String buildStarKey(String tenantCode, String userId, String busiId) {
        return RedisConstants.STAR
                .replace("{tenantCode}", tenantCode)
                .replace("{userId}", userId)
                .replace("{busiId}", busiId);
    }

    /**
     * 公共方法：根据参数查询数据库中的收藏记录
     */
    private SysUserStar selectStarByCondition(SysUserStar star) {
        SysUserStar condition = new SysUserStar();
        condition.setTenantCode(star.getTenantCode());
        condition.setUserId(star.getUserId());
        condition.setBusiId(star.getBusiId());
        return starMapper.selectOne(condition);
    }
}
