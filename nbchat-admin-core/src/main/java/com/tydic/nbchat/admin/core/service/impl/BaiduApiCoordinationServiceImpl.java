package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.baidu.BaiduApiCoordinationService;
import com.tydic.nbchat.admin.api.bo.baidu.BaiduApiCoordinationReqBO;
import com.tydic.nbchat.admin.api.bo.baidu.ConversionTypeBO;
import com.tydic.nbchat.admin.core.config.properties.BaiDuConfigProperties;
import com.tydic.nbchat.admin.core.utils.BaiDuSendConvertDataUtil;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import java.util.List;
import com.tydic.nicc.dc.base.bo.Rsp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class BaiduApiCoordinationServiceImpl implements BaiduApiCoordinationService {

    private BaiDuConfigProperties baiDuConfigProperties;

    @Override
    public Rsp dataConversionCallback(BaiduApiCoordinationReqBO reqBo) {
        String token = baiDuConfigProperties.getOcpcToken();
        List<ConversionTypeBO> ConversionTypeBOs = reqBo.getConversionTypes();
        Boolean isSend = BaiDuSendConvertDataUtil.sendConvertData(token, ConversionTypeBOs);
        if (isSend) {
            return BaseRspUtils.createSuccessRsp("回传数据成功");
        }
        return BaseRspUtils.createErrorRsp("回传数据失败");
    }


}
