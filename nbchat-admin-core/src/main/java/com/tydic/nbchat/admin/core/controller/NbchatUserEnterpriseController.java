package com.tydic.nbchat.admin.core.controller;


import com.tydic.nbchat.admin.api.NbchatUserEnterpriseService;
import com.tydic.nbchat.admin.api.bo.NbchatUserEnterpriseReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/user/enterprise")
public class NbchatUserEnterpriseController {
    private final NbchatUserEnterpriseService nbchatUserEnterpriseService;
    @PostMapping("/query")
    public Rsp queryById(@RequestBody NbchatUserEnterpriseReqBO reqBO) {
        return nbchatUserEnterpriseService.queryById(reqBO);
    }

    @PostMapping("/save")
        public Rsp save(@RequestBody NbchatUserEnterpriseReqBO reqBO){
        return nbchatUserEnterpriseService.insert(reqBO);
    }

    @PostMapping("/query/enterprise")
    public RspList<String> queryEnterpriseList(@RequestBody NbchatUserEnterpriseReqBO reqBO){
        return nbchatUserEnterpriseService.queryEnterpriseList(reqBO);
    }
}
