package com.tydic.nbchat.admin.core.service.impl;


import com.alibaba.spring.util.ObjectUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.bo.NbchatUserEnterpriseReqBO;
import com.tydic.nbchat.admin.api.bo.NbchatUserEnterpriseRspBO;
import com.tydic.nbchat.admin.mapper.po.NbchatUserEnterprise;
import com.tydic.nbchat.admin.mapper.NbchatUserEnterpriseMapper;
import com.tydic.nbchat.admin.api.NbchatUserEnterpriseService;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * (NbchatUserEnterprise)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-01 11:26:31
 */
@Service("nbchatUserEnterpriseService")
public class NbchatUserEnterpriseServiceImpl implements NbchatUserEnterpriseService {
    @Resource
    private NbchatUserEnterpriseMapper nbchatUserEnterpriseDao;


    /**
     * 通过ID查询单条数据
     * @param reqBO
     * @return
     */
    @Override
    public Rsp queryById(NbchatUserEnterpriseReqBO reqBO) {
        NbchatUserEnterprise nbchatUserEnterprise = nbchatUserEnterpriseDao.queryById(reqBO.getUserId());
        if (nbchatUserEnterprise == null) {
            return BaseRspUtils.createErrorRsp("查询失败");
        }
        NbchatUserEnterpriseRspBO rspBO = new NbchatUserEnterpriseRspBO();
        BeanUtils.copyProperties(nbchatUserEnterprise, rspBO);
        return BaseRspUtils.createSuccessRsp(rspBO, "查询成功");
    }

    /**
     * 分页查询
     * @param reqBO
     * @return
     */
    @Override
    public RspList list(NbchatUserEnterpriseReqBO reqBO) {
        List<NbchatUserEnterpriseRspBO> list = new ArrayList();
        NbchatUserEnterprise nbchatUserEnterprise = new NbchatUserEnterprise();
        BeanUtils.copyProperties(reqBO, nbchatUserEnterprise);
        Page<NbchatUserEnterpriseReqBO> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        nbchatUserEnterpriseDao.selectAll(nbchatUserEnterprise);
        if (page.getResult() != null && page.getResult().size() > 0) {
            NiccCommonUtil.copyList(page.getResult(), list, NbchatUserEnterpriseRspBO.class);
            return BaseRspUtils.createSuccessRspList(page.getResult(), page.getTotal());
        }
        return BaseRspUtils.createSuccessRspList(list, 0);
    }


    /**
     * 新增数据
     *
     * @param reqBO
     */
    @MethodParamVerifyEnable
    @Override
    public Rsp insert(NbchatUserEnterpriseReqBO reqBO) {
        NbchatUserEnterprise userEnterprise = nbchatUserEnterpriseDao.queryById(reqBO.getUserId());
        if (userEnterprise != null) {
            return BaseRspUtils.createSuccessRsp("企业信息已存在");
        }
        NbchatUserEnterprise nbchatUserEnterprise = new NbchatUserEnterprise();
        BeanUtils.copyProperties(reqBO, nbchatUserEnterprise);
        nbchatUserEnterprise.setCreateTime(new Date());
        nbchatUserEnterpriseDao.insertSelective(nbchatUserEnterprise);
        return BaseRspUtils.createSuccessRsp("保存成功");
    }

    /**
     * 修改数据
     *
     * @param reqBO
     * @return
     */
    @Override
    public Rsp update(NbchatUserEnterpriseReqBO reqBO) {
        NbchatUserEnterprise nbchatUserEnterprise = new NbchatUserEnterprise();
        BeanUtils.copyProperties(reqBO, nbchatUserEnterprise);
        nbchatUserEnterpriseDao.update(nbchatUserEnterprise);
        return queryById(reqBO);
    }

    /**
     * 通过主键删除数据
     *
     * @param reqBO
     * @return
     */
    @Override
    public Rsp deleteById(NbchatUserEnterpriseReqBO reqBO) {
        nbchatUserEnterpriseDao.deleteById(reqBO.getUserId());
        return BaseRspUtils.createSuccessRsp("删除成功");
    }

    @Override
    public RspList<String> queryEnterpriseList(NbchatUserEnterpriseReqBO reqBO) {
        List<String> list = new ArrayList();
        NbchatUserEnterprise nbchatUserEnterprise = new NbchatUserEnterprise();
        BeanUtils.copyProperties(reqBO, nbchatUserEnterprise);
        Page<NbchatUserEnterpriseReqBO> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        nbchatUserEnterpriseDao.queryEnterpriseList(reqBO.getCompanyName());
        if (page.getResult() != null && page.getResult().size() > 0) {
            NiccCommonUtil.copyList(page.getResult(), list, String.class);
            return BaseRspUtils.createSuccessRspList(page.getResult(), page.getTotal());
        }
        return BaseRspUtils.createSuccessRspList(list, 0);
    }
}
