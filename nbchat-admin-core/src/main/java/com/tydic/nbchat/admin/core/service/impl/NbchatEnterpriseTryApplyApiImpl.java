package com.tydic.nbchat.admin.core.service.impl;

import com.tydic.nbchat.admin.api.NbchatEnterpriseTryApplyApi;
import com.tydic.nbchat.admin.api.bo.EnterpriseTryApplyReqBO;
import com.tydic.nbchat.admin.api.bo.sentence.constants.EnterpriseTryApplyConstants;
import com.tydic.nbchat.admin.core.config.utils.TenantCodeUtils;
import com.tydic.nbchat.admin.mapper.NbchatEnterpriseTryApplyMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatEnterpriseTryApply;
import com.tydic.nbchat.user.api.bo.utils.PhoneNumberUtils;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Random;

@Slf4j
@Service
@AllArgsConstructor
public class NbchatEnterpriseTryApplyApiImpl implements NbchatEnterpriseTryApplyApi {

    private final NbchatEnterpriseTryApplyMapper enterpriseTryApplyMapper;

    /**
     * 企业体验试用申请接口
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp enterpriseTrialApplication(EnterpriseTryApplyReqBO reqBO) {
        log.info("企业体验试用申请接口入参：{}",reqBO);
        if (StringUtils.isNotBlank(reqBO.getPhone()) && !PhoneNumberUtils.validPhoneNumber(reqBO.getPhone())){
            return BaseRspUtils.createErrorRsp("手机号格式不正确");
        }
        NbchatEnterpriseTryApply nbchatEnterpriseTryApply = new NbchatEnterpriseTryApply();
        BeanUtils.copyProperties(reqBO,nbchatEnterpriseTryApply);
        nbchatEnterpriseTryApply.setCreatedTime(new Date());
        nbchatEnterpriseTryApply.setUpdatedTime(new Date());
        nbchatEnterpriseTryApply.setTenantCode(TenantCodeUtils.generateMixedString(reqBO.getCompanyName()));
        nbchatEnterpriseTryApply.setStatus(EnterpriseTryApplyConstants.DEFAULT_STATUS);
        enterpriseTryApplyMapper.insertSelective(nbchatEnterpriseTryApply);
        log.info("企业体验试用申请接口出参：{}",nbchatEnterpriseTryApply);
        return BaseRspUtils.createSuccessRsp("企业体验试用申请提交成功");
    }



}
