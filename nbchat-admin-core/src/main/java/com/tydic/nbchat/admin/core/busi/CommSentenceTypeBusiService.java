package com.tydic.nbchat.admin.core.busi;


import com.tydic.nbchat.admin.api.bo.sentence.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface CommSentenceTypeBusiService {
    /**
     * <AUTHOR>
     * @description 保存知识类型
     **/
    Rsp saveCommSentenceType(SaveCommSentenceTypeReqBO reqBo);
    /**
     * <AUTHOR>
     * @description 更新知识类型
     **/
    Rsp updateCommSentenceType(UpdateCommSentenceTypeReqBO reqBo);
    /**
     * <AUTHOR>
     * @description 删除知识类型
     **/
    Rsp deleteCommSentenceType(DelCommSentenceTypeReqBO reqBo);

    /**
     * <AUTHOR>
     * @description 检查知识
     **/
    Rsp validTypeName(ValidTypeNameReqBO validTypeNameReqBO);

    /**
     * <AUTHOR>
     * @description 查询知识类型树状列表
     **/
    RspList queryCommSentenceTreeData(QueryCommSentenceTreeReqBO req);

}
