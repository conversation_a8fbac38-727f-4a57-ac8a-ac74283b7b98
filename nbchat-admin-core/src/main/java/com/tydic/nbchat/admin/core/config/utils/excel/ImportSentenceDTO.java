package com.tydic.nbchat.admin.core.config.utils.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


@Data
public class ImportSentenceDTO {

    @ExcelProperty("所属分类")
    private String typeId;

    @ExcelProperty("内容名称")
    private String contentTitle;

    @ExcelProperty("排序标识")
    private String commSortId;

    @ExcelProperty("内容")
    private String content;

//    @ExcelProperty("类型")
//    private String contentType;//类型 1 文本| 2 文件 | 6 富文本
}
