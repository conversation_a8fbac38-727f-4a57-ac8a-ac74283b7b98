package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysOuterUserSyncApi;
import com.tydic.nbchat.admin.api.bo.outer_user.OuterUserAccessTokenReqBO;
import com.tydic.nbchat.admin.api.bo.outer_user.OuterUserSyncReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/admin/outer_sys")
public class SysOuterUserController {


    private final SysOuterUserSyncApi sysOuterUserSyncApi;

    public SysOuterUserController(SysOuterUserSyncApi sysOuterUserSyncApi) {
        this.sysOuterUserSyncApi = sysOuterUserSyncApi;
    }

    @PostMapping("/user/sync")
    public Rsp syncUser(@Validated @RequestBody OuterUserSyncReqBO reqBO) {
        try {
            return sysOuterUserSyncApi.syncUser(reqBO);
        } catch (Exception e) {
            log.error("用户同步失败:", e);
            return BaseRspUtils.createErrorRsp("用户同步失败:" + e.getMessage());
        }
    }

    @PostMapping("/user/access_token")
    public Rsp accessToken(@RequestBody OuterUserAccessTokenReqBO reqBO) {
        try {
            return sysOuterUserSyncApi.accessToken(reqBO);
        } catch (Exception e) {
            log.error("获取用户token失败:", e);
            return BaseRspUtils.createErrorRsp("获取用户token失败:" + e.getMessage());
        }
    }


}
