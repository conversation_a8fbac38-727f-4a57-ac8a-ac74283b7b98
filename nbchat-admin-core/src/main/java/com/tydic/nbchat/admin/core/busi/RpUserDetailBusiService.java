package com.tydic.nbchat.admin.core.busi;

import com.tydic.nbchat.admin.mapper.NbchatSysTenantMapper;
import com.tydic.nbchat.admin.mapper.NbchatWxUserMapper;
import com.tydic.nbchat.admin.mapper.OpRpUserDetailMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatSysTenant;
import com.tydic.nbchat.admin.mapper.po.NbchatWxUser;
import com.tydic.nbchat.admin.mapper.po.OpRpUserDetail;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nbchat.user.api.bo.eums.UserVipStatusType;
import com.tydic.nbchat.user.api.bo.mq.UserRegistContext;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
@AllArgsConstructor
public class RpUserDetailBusiService {

    private final NbchatWxUserMapper nbchatWxUserMapper;
    private final OpRpUserDetailMapper opRpUserDetailMapper;
    private final NbchatSysTenantMapper nbchatSysTenantMapper;

    public void saveRpUserDetail(UserRegistContext context) {
        log.info("用户注册通知-保存用户详情: {}", context);
        OpRpUserDetail userDetail = opRpUserDetailMapper.selectByUserId(context.getTenantCode(), context.getUserId());
        if (StringUtils.isBlank(context.getPhone())) {
            log.info("用户注册通知-手机号为空: {}", context);
            return;
        }
        // 查询用户是否绑定了微信
        NbchatWxUser wxUser = nbchatWxUserMapper.findOneByTenantCodeAndUserId(context.getTenantCode(), context.getUserId());
        // 创建用户详情
        OpRpUserDetail detail = new OpRpUserDetail();
        detail.setUserId(context.getUserId());
        detail.setTenantCode(context.getTenantCode());
        detail.setUserName(context.getUserRealityName());
        detail.setRegTime(context.getCreatedTime());
        detail.setPhone(context.getPhone());
        detail.setPromKey(context.getPromKey());
        detail.setPromId(context.getPromId());
        detail.setPromChannel(context.getPromChannel());
        detail.setRegChannel(context.getJoinType());
        if (UserAttributeConstants.DEFAULT_TENANT_CODE.equals(context.getTenantCode())) {
            detail.setUserType("0");
        } else {
            detail.setUserType("1");
        }
        if (wxUser == null) {
            detail.setIsBindWx("0");
        } else {
            detail.setIsBindWx("1");
        }
        NbchatSysTenant tenant = nbchatSysTenantMapper.selectByPrimaryKey(context.getTenantCode());
        if (tenant != null) {
            detail.setCompanyName(tenant.getTenantName());
        }
        if (userDetail == null) {
            detail.setVipBuyCount(0);
            detail.setLoginTime(new Date());
            detail.setLoginCount(1);
            detail.setRegSource(context.getRegSource());
            detail.setVipStatus(UserVipStatusType.FREE.getCode());
            opRpUserDetailMapper.insertSelective(detail);
            log.info("用户详情创建成功:{}|{}", detail.getUserName(), detail.getPhone());
        } else {
            detail.setId(userDetail.getId());
            detail.setUpdateTime(new Date());
            opRpUserDetailMapper.updateByPrimaryKeySelective(detail);
        }
    }

}
