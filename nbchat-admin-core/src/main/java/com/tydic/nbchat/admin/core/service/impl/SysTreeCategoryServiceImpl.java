package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysTreeCategoryApi;
import com.tydic.nbchat.admin.api.bo.category.SysTreeCategoryReqBO;
import com.tydic.nbchat.admin.api.bo.category.SysTreeCategoryRsqBO;
import com.tydic.nbchat.admin.mapper.SysTreeCategoryMapper;
import com.tydic.nbchat.admin.mapper.po.SysTreeCategory;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.dc.boot.starter.util.IdWorker;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @datetime：2024/10/16 16:49
 * @description:
 */
@Slf4j
@Service
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 5000)
public class SysTreeCategoryServiceImpl implements SysTreeCategoryApi {
    @Resource
    private SysTreeCategoryMapper sysTreeCategoryMapper;

    @Override
    public RspList getCategoryTree(SysTreeCategoryReqBO reqBO) {
        log.info("查询类目树-开始:{}", reqBO);
        SysTreeCategory stc = new SysTreeCategory();
        BeanUtils.copyProperties(reqBO, stc);
        stc.setIsValid(EntityValidType.NORMAL.getCode());
        List<SysTreeCategory> sysTreeCategories = sysTreeCategoryMapper.selectAll(stc);
        List<SysTreeCategoryRsqBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(sysTreeCategories, list, SysTreeCategoryRsqBO.class);

        Map<String, List<SysTreeCategoryRsqBO>> map = list.stream().collect(Collectors.groupingBy(SysTreeCategoryRsqBO::getParentId));
        //往子类添加数据
        list.forEach(item -> item.setChildren(map.get(item.getCateId())));
        //通过过滤将父子菜单分离
        List<SysTreeCategoryRsqBO> collect = list.stream().filter(item -> item.getParentId().matches(ROOT)).collect(Collectors.toList());

        return BaseRspUtils.createSuccessRspList(collect);
    }

    @Override
    public RspList getCategoryList(SysTreeCategoryReqBO reqBO) {
        log.info("查询列表-开始:{}", reqBO);
        SysTreeCategory stc = new SysTreeCategory();
        BeanUtils.copyProperties(reqBO, stc);
        Page<SysTreeCategory> page = PageHelper.startPage(reqBO.getPage(), reqBO.getLimit());
        sysTreeCategoryMapper.selectAll(stc);
        List<SysTreeCategoryRsqBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), list, SysTreeCategoryRsqBO.class);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp addTreeCategory(SysTreeCategoryReqBO reqBO) {
        log.info("保存系统分类-开始:{}", reqBO);
        SysTreeCategory po = new SysTreeCategory();
        BeanUtils.copyProperties(reqBO, po);
        if (StringUtils.isBlank(po.getParentId())) {
            po.setParentId(ROOT);
        } else {
            SysTreeCategory sysTreeCategory = sysTreeCategoryMapper.queryById(po.getParentId());
            if (sysTreeCategory == null) {
                return BaseRspUtils.createErrorRsp("父级部门不存在");
            } else {
                po.setAncestors(sysTreeCategory.getAncestors());
            }
        }
        String cateId = IdWorker.nextAutoIdStr();
        po.setCateId(cateId);
        po.setCreateTime(new Date());
        po.setCreateBy(po.getUserId());
        if (StringUtils.isEmpty(po.getAncestors())) {
            po.setAncestors(po.getCateId());
        } else {
            po.setAncestors(po.getAncestors() + "," + po.getCateId());
        }
        po.setIsValid(EntityValidType.NORMAL.getCode());
        sysTreeCategoryMapper.insertSelective(po);
        return BaseRspUtils.createSuccessRsp(cateId, "操作成功");
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp updateTreeCategory(SysTreeCategoryReqBO reqBO) {
        log.info("修改系统分类-开始:{}", reqBO);
        SysTreeCategory po = new SysTreeCategory();
        BeanUtils.copyProperties(reqBO, po);
        if (!ROOT.equals(po.getParentId())) {
            SysTreeCategory sysTreeCategory = sysTreeCategoryMapper.queryById(po.getParentId());
            if (sysTreeCategory == null) {
                return BaseRspUtils.createErrorRsp("父级部门不存在");
            } else {
                po.setAncestors(sysTreeCategory.getAncestors());
            }
        }
        po.setUpdateTime(new Date());
        po.setUpdateBy(po.getUserId());
        if (StringUtils.isEmpty(po.getAncestors())) {
            po.setAncestors(po.getCateId());
        } else {
            po.setAncestors(po.getAncestors() + "," + po.getCateId());
        }
        sysTreeCategoryMapper.update(po);
        return BaseRspUtils.createSuccessRsp(po.getCateId(), "操作成功");
    }

    @Override
    public Rsp deleteTreeCategory(String cateId) {
        sysTreeCategoryMapper.logicDeleteById(cateId);
        return BaseRspUtils.createSuccessRsp(cateId, "操作成功");
    }

    @Override
    public Rsp info(String cateId) {
        SysTreeCategory category = sysTreeCategoryMapper.queryById(cateId);
        SysTreeCategoryRsqBO bo = new SysTreeCategoryRsqBO();
        if (ObjectUtils.isNotEmpty(category)) {
            BeanUtils.copyProperties(category, bo);
        }
        return BaseRspUtils.createSuccessRsp(bo);
    }


}
