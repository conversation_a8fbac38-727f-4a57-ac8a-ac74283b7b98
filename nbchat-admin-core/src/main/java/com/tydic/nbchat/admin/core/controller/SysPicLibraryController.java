package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysPicLibraryApi;
import com.tydic.nbchat.admin.api.bo.SysPicLibraryReqBO;
import com.tydic.nicc.dc.base.bo.RspList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @datetime：2024/9/9 15:21
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/admin/sys")
public class SysPicLibraryController {
    @Resource
    private SysPicLibraryApi sysPicLibraryApi;

    @PostMapping("/pic_library/search")
    public RspList getSysPicLibrary(@RequestBody SysPicLibraryReqBO reqBO) {
        return sysPicLibraryApi.getSysPicLibrary(reqBO);
    }

}
