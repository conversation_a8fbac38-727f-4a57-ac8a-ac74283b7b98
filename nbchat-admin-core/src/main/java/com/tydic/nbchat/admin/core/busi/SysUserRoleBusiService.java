package com.tydic.nbchat.admin.core.busi;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nbchat.admin.mapper.SysUserRoleMapper;
import com.tydic.nbchat.admin.mapper.po.SysUserRole;
import com.tydic.nbchat.admin.mapper.po.SysUserRoleRel;
import com.tydic.nbchat.admin.mapper.po.UserSettingPO;
import com.tydic.nbchat.user.api.bo.constants.UserAttributeConstants;
import com.tydic.nicc.common.bo.user.SysRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.common.nbchat.emus.UserRoleType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class SysUserRoleBusiService {
    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    public static <T> boolean areCollectionsEqual(Collection<T> collection1, Collection<T> collection2) {
        if (collection1.size() != collection2.size()) {
            return false;
        }
        Set<T> set1 = new HashSet<>(collection1);
        Set<T> set2 = new HashSet<>(collection2);
        return set1.equals(set2);
    }

    public int fixUserRoleFromUserSetting(){
        /**
         * {
         * 	"trainAdminPermission":"00000000,00010010,000TYDIC,0000ZYSY,0000ZGLT,0000ZMJT,0000CSZH",
         * 	"robotType":"doubao",
         * 	"userSettingPermission":"1",
         * 	"platformAdminPermission":"1"
         * }
         */
        List<UserSettingPO> userSettingPOS = sysUserRoleMapper.selectAllUserSetting();
        int count = 0;
        for (UserSettingPO userSettingPO : userSettingPOS) {
            JSONObject setting = JSONObject.parseObject(userSettingPO.getCurrentSetting());
            if (setting.containsKey("platformAdminPermission")
                    && "1".equals(setting.getString("platformAdminPermission"))) {
                Set<String> roles = new HashSet<>();
                roles.add(UserRoleConstants.sysAdmin);
                updateRoles(userSettingPO.getTenantCode(), userSettingPO.getUserId(), roles);
            }
            if (setting.containsKey("trainAdminPermission") &&
                    !setting.getString("trainAdminPermission").isEmpty()) {
                String[] tenantCodes = setting.getString("trainAdminPermission").split(",");
                //获取所有租户
                for (String tenantCode : tenantCodes) {
                    Set<String> update = new HashSet<>();
                    update.add(UserRoleConstants.tenantAdmin);
                    updateRoles(tenantCode, userSettingPO.getUserId(), update);
                }
            } else {

            }
            count ++;
        }
        return count;
    }


    public void addRole(String tenantCode, String userId, String role) {
        log.info("添加用户角色: {}|{}|{}",tenantCode,userId, role);
        SysUserRoleRel roleRel = sysUserRoleMapper.selectUserRole(tenantCode,userId,role);
        if (roleRel != null) {
            return;
        }
        SysUserRoleRel userRole = new SysUserRoleRel();
        userRole.setSubsystem(tenantCode);
        userRole.setRole(role);
        userRole.setUserId(userId);
        userRole.setCreateTime(new Date());
        if (UserRoleConstants.sysAdmin.equals(role)) {
            userRole.setSubsystem("");
        }
        sysUserRoleMapper.addUserRoles(Collections.singletonList(userRole));
    }

    /**
     * 移除角色
     * @param tenantCode
     * @param userId
     * @param role
     */
    public void removeRole(String tenantCode, String userId, String role) {
        log.info("删除用户角色: {}|{}|{}",tenantCode,userId, role);
        sysUserRoleMapper.deleteUserRoles(tenantCode, userId, role);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoles(String tenantCode, String userId, Set<String> roles) {
        log.info("更新用户角色: {}|{}|{}",tenantCode,userId, roles);
        if (roles == null) {
            return false;
        }
        List<SysUserRoleRel> list = new ArrayList<>();
        for (String role : roles) {
            SysUserRoleRel userRole = new SysUserRoleRel();
            userRole.setSubsystem(tenantCode);
            userRole.setRole(role);
            userRole.setUserId(userId);
            userRole.setCreateTime(new Date());
            if (UserRoleConstants.sysAdmin.equals(role)) {
                //跳过更新系统管理员
               continue;
            }
            list.add(userRole);
        }
        List<String> oldRoles = getRoleList(tenantCode, userId);
        if (!areCollectionsEqual(oldRoles, roles)) {
            sysUserRoleMapper.deleteUserRoles(tenantCode, userId,null);
            if (!list.isEmpty()) {
                sysUserRoleMapper.addUserRoles(list);
            }
            return true;
        }
        return false;
    }

    public List<String> getRoleList(String tenantCode, String userId) {
        List<String> roleList = new ArrayList<>();
        roleList.add(UserRoleType.user.getCode());
        List<SysUserRoleRel> userRoles = sysUserRoleMapper.selectUserRoles(tenantCode, userId);
        for (SysUserRoleRel userRole : userRoles) {
            roleList.add(userRole.getRole());
        }
        return roleList;
    }

    public Set<SysRole> getRoles(String tenantCode, String userId) {
        Set<SysRole> set = new LinkedHashSet<>();
        List<SysUserRoleRel> userRoles = sysUserRoleMapper.selectUserRoles(tenantCode, userId);
        for (SysUserRoleRel userRole : userRoles) {
            SysRole role = new SysRole();
            role.setRoleCode(userRole.getRole());
            role.setRoleName(userRole.getName());
            set.add(role);
        }
        if (UserAttributeConstants.TMO_TENANT.equals(tenantCode)) {
            //移除系统角色
            set.removeIf(sysRole -> UserRoleConstants.sysAdmin.equals(sysRole.getRoleCode()));
        } else {
            set.add(new SysRole(UserRoleType.user.getCode(), UserRoleType.user.getName()));
        }
        //添加默认角色
        log.info("查询用户角色: {}|{}", userId, set);
        return set;
    }


    public List<SysRole> getConfigRoles(String subSystem) {
        List<SysUserRole> roles = sysUserRoleMapper.selectBySubsystem(subSystem);
        List<SysRole> sysRoles = new ArrayList<>();
        for (SysUserRole role : roles) {
            SysRole sysRole = new SysRole();
            sysRole.setRoleCode(role.getRole());
            sysRole.setRoleName(role.getName());
            sysRoles.add(sysRole);
        }
        return sysRoles;
    }

}
