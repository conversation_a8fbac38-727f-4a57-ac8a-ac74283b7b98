package com.tydic.nbchat.admin.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.tydic.nbchat.admin.api.SysTokenConfigApi;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenConfigBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenCreateReqBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenRefreshReqBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenRefreshRspBO;
import com.tydic.nbchat.admin.api.bo.permission.ApiConfigPermission;
import com.tydic.nbchat.admin.mapper.SysApiTokenConfigMapper;
import com.tydic.nbchat.admin.mapper.po.SysApiTokenConfig;
import com.tydic.nicc.common.eums.EntityValidType;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
@DubboService(version = "${nicc-dc-config.dubbo-provider.version}",
        group = "${nicc-dc-config.dubbo-provider.group}", timeout = 2000)
public class SysTokenConfigServiceImpl implements SysTokenConfigApi {

    @Resource
    private SysApiTokenConfigMapper sysApiTokenConfigMapper;
    private final RedisHelper redisHelper;

    public SysTokenConfigServiceImpl(RedisHelper redisHelper) {
        this.redisHelper = redisHelper;
    }

    @Override
    public Rsp<SysApiTokenConfigBO> getApiTokenConfig(Integer appId) {
        log.info("获取token配置:{}", appId);
        SysApiTokenConfig config = sysApiTokenConfigMapper.selectById(appId);
        if (config.getExpireTime() > 0 && config.getRefreshTime() != null) {
            //判断 key的刷新时间是否超过了过期时间
            long time = System.currentTimeMillis() - config.getRefreshTime().getTime();
            if (time > config.getExpireTime() * 1000) {
                log.info("获取token配置-token已过期: {}",config);
                return BaseRspUtils.createErrorRsp("token已过期");
            }
        }
        return getSysApiTokenConfigRsp(config);
    }

    @Override
    public Rsp<SysApiTokenConfigBO> getApiTokenConfig(String accessKey) {
        SysApiTokenConfig config = sysApiTokenConfigMapper.selectByAk(accessKey);
        return getSysApiTokenConfigRsp(config);
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp<SysApiTokenRefreshRspBO> refreshToken(SysApiTokenRefreshReqBO refreshReqBO) {
        SysApiTokenConfig config;
        if (refreshReqBO.getAppId() == 0) {
            config = sysApiTokenConfigMapper.selectByNameAndSk(refreshReqBO.getAppName(),
                    refreshReqBO.getSecretKey());
        } else {
            config = sysApiTokenConfigMapper.selectById(refreshReqBO.getAppId());
        }
        if (config == null) {
            return BaseRspUtils.createErrorRsp("应用不存在");
        }
        if (config.getIsValid().equals(EntityValidType.DELETE.getCode())) {
            return BaseRspUtils.createErrorRsp("应用已删除");
        }
        if (!refreshReqBO.getSecretKey().equals(config.getSecretKey())) {
            return BaseRspUtils.createErrorRsp("非法操作");
        }
        //升成新的 Key
        Date flashTime = new Date();
        String newKey = createAk();
        config.setAccessKey(newKey);
        config.setRefreshTime(flashTime);
        SysApiTokenRefreshRspBO rspBO = new SysApiTokenRefreshRspBO();
        int i = sysApiTokenConfigMapper.updateKeyById(config);
        if (i > 0) {
            rspBO.setAppId(config.getId());
            rspBO.setAccessKey(newKey);
            rspBO.setExpireTime(config.getExpireTime());
            rspBO.setSubsystem(JSONObject.parseArray(config.getSubsystem()).toJavaList(String.class));
            rspBO.setPermission(JSONObject.parseObject(config.getApiPermission(), ApiConfigPermission.class));
            rspBO.setAppName(config.getAppName());
            rspBO.setRefreshTime(flashTime);
            rspBO.setAppTenantCode(config.getTenantCode());
            rspBO.setAppName(config.getAppName());
            //清空缓存
            redisHelper.del(TOKEN_CONFIG_KEY + config.getAccessKey());
            return BaseRspUtils.createSuccessRsp(rspBO,"刷新成功");
        }
        return BaseRspUtils.createErrorRsp("刷新失败");
    }

    @MethodParamVerifyEnable
    @Override
    public Rsp createTokenConfig(SysApiTokenCreateReqBO createReqBO) {
        SysApiTokenConfig config = new SysApiTokenConfig();
        config.setAppName(createReqBO.getAppName());
        config.setAccessKey(createAk());
        if (StringUtils.isBlank(createReqBO.getSecretKey())) {
            config.setSecretKey(createSk());
        } else {
            config.setSecretKey(createReqBO.getSecretKey());
        }
        Date date = new Date();
        config.setExpireTime(createReqBO.getExpireTime());
        config.setSubsystem(JSONObject.toJSONString(createReqBO.getSubsystem()));
        config.setApiPermission(JSONObject.toJSONString(createReqBO.getPermission()));
        config.setIsValid(EntityValidType.NORMAL.getCode());
        config.setCreateTime(date);
        config.setRefreshTime(date);
        config.setUserId(createReqBO.getAppUserId());
        config.setTenantCode(createReqBO.getAppTenantCode());
        int i = sysApiTokenConfigMapper.insert(config);
        if (i > 0) {
            SysApiTokenRefreshRspBO rspBO = new SysApiTokenRefreshRspBO();
            rspBO.setAppId(config.getId());
            rspBO.setSecretKey(config.getSecretKey());
            rspBO.setAccessKey(config.getAccessKey());
            rspBO.setExpireTime(config.getExpireTime());
            rspBO.setSubsystem(createReqBO.getSubsystem());
            rspBO.setPermission(createReqBO.getPermission());
            rspBO.setAppUserId(config.getUserId());
            rspBO.setAppTenantCode(config.getTenantCode());
            rspBO.setAppName(config.getAppName());
            rspBO.setRefreshTime(date);
            return BaseRspUtils.createSuccessRsp(rspBO,"创建成功");
        }
        return BaseRspUtils.createErrorRsp("创建失败");
    }


    public static String createAk(){
        return "ak-"+ NiccCommonUtil.createImUserId(true);
    }

    public static String createSk(){
        return "sk-"+ NiccCommonUtil.createImUserId(true);
    }

    @NotNull
    private Rsp<SysApiTokenConfigBO> getSysApiTokenConfigRsp(SysApiTokenConfig config) {
        if (config != null) {
            try {
                SysApiTokenConfigBO configRspBO = parseConfig(config);
                return BaseRspUtils.createSuccessRsp(configRspBO);
            } catch (Exception e) {
                log.error("获取api token配置异常", e);
                return BaseRspUtils.createErrorRsp("获取api token配置异常");
            }
        }
        return BaseRspUtils.createErrorRsp("未找到对应的配置信息");
    }

    private SysApiTokenConfigBO parseConfig(SysApiTokenConfig config) {
        SysApiTokenConfigBO configRspBO = new SysApiTokenConfigBO();
        configRspBO.setId(config.getId());
        configRspBO.setTenantCode(config.getTenantCode());
        configRspBO.setAppName(config.getAppName());
        configRspBO.setSecretKey(config.getSecretKey());
        configRspBO.setAccessKey(config.getAccessKey());
        configRspBO.setTenantCode(config.getTenantCode());
        configRspBO.setUserId(config.getUserId());
        configRspBO.setExpireTime(config.getExpireTime());
        configRspBO.setRefreshTime(config.getRefreshTime());
        List<String> subsystem = JSONObject.parseArray(config.getSubsystem()).toJavaList(String.class);
        configRspBO.setSubsystem(subsystem);
        ApiConfigPermission permission = new ApiConfigPermission();
        if (config.getApiPermission() != null && JSON.isValid(config.getApiPermission())) {
            permission = JSONObject.parseObject(config.getApiPermission(), ApiConfigPermission.class);
        }
        configRspBO.setPermission(permission);
        return configRspBO;
    }
}
