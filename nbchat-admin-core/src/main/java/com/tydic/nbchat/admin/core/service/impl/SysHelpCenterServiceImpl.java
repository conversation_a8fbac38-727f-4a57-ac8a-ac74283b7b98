package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.SysHelpCenterApi;
import com.tydic.nbchat.admin.api.bo.SysHelpCenterQueryReqBo;
import com.tydic.nbchat.admin.api.bo.SysHelpCenterRspBO;
import com.tydic.nbchat.admin.api.bo.eum.TenantUserStatusType;
import com.tydic.nbchat.admin.mapper.SysHelpCenterMapper;
import com.tydic.nbchat.admin.mapper.po.SysHelpCenter;
import com.tydic.nbchat.admin.mapper.po.SysHelpCenterCondition;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.IntStream;

@Slf4j
@Service
public class SysHelpCenterServiceImpl implements SysHelpCenterApi {
    @Resource
    private SysHelpCenterMapper sysHelpCenterMapper;

    /**
     * 根据条件查询帮助中心列表
     * @param reqBo
     * @return
     */
    @Override
    public RspList<SysHelpCenterRspBO> getHelpCenterList(SysHelpCenterQueryReqBo reqBo) {
        log.info("根据条件查询帮助中心列表,reqBo:{}", reqBo);
        SysHelpCenterCondition condition = new SysHelpCenterCondition();
        BeanUtils.copyProperties(reqBo, condition);
        condition.setIsValid(TenantUserStatusType.NORMAL.getCode());
        Page<SysHelpCenterRspBO> page = PageHelper.startPage(reqBo.getPage(), reqBo.getLimit());
        List<SysHelpCenter> selectResults = sysHelpCenterMapper.selectByCategoryAndTitle(condition);
        List<SysHelpCenterRspBO> rspBoList = new ArrayList<>();
        NiccCommonUtil.copyList(selectResults, rspBoList, SysHelpCenterRspBO.class);
        return BaseRspUtils.createSuccessRspList(rspBoList, page.getTotal());
    }

    /**
     * 保存更新帮助中心
     * @param reqBo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Rsp<SysHelpCenterRspBO> save(SysHelpCenterQueryReqBo reqBo) {
        log.info("保存帮助中心,reqBo:{}", reqBo);
        SysHelpCenterRspBO rspBo = new SysHelpCenterRspBO();
        BeanUtils.copyProperties(reqBo, rspBo);
        // 更新操作
        if (reqBo.getId() != null) {
            // 先查询原有数据
            SysHelpCenter existHelpCenter = sysHelpCenterMapper.selectByPrimaryKey(reqBo.getId());
            if(existHelpCenter == null) {
                return BaseRspUtils.createErrorRsp("数据不存在");
            }
            // 设置需要更新的字段
            SysHelpCenter helpCenter = new SysHelpCenter();
            helpCenter.setId(reqBo.getId());
            helpCenter.setTitle(reqBo.getTitle());
            helpCenter.setThumbUrl(reqBo.getThumbUrl());
            helpCenter.setContent(reqBo.getContent());
            helpCenter.setCategory(reqBo.getCategory());
            helpCenter.setBusiType(reqBo.getBusiType());
            helpCenter.setStatus(reqBo.getStatus());
            helpCenter.setTag(reqBo.getTag());
            helpCenter.setOrderIndex(reqBo.getOrderIndex());
            helpCenter.setDuration(reqBo.getDuration());
            helpCenter.setUpdateTime(new Date());

            helpCenter.setTenantCode(existHelpCenter.getTenantCode());
            helpCenter.setCreateTime(existHelpCenter.getCreateTime());
            helpCenter.setCreateUser(existHelpCenter.getCreateUser());
            helpCenter.setIsValid(existHelpCenter.getIsValid());

            sysHelpCenterMapper.updateByPrimaryKeySelective(helpCenter);
            BeanUtils.copyProperties(helpCenter, rspBo);
        } else {
            //新增操作
            rspBo.setIsValid(TenantUserStatusType.NORMAL.getCode());
            rspBo.setStatus("0");
            rspBo.setCreateTime(new Date());
            SysHelpCenter helpCenter = new SysHelpCenter();
            BeanUtils.copyProperties(rspBo, helpCenter);
            sysHelpCenterMapper.insertSelective(helpCenter);
            BeanUtils.copyProperties(helpCenter, rspBo);
        }
        return BaseRspUtils.createSuccessRsp(rspBo);
    }

    /**
     * 删除帮助中心
     * @param reqBo
     * @return
     */
    @Override
    public Rsp delete(SysHelpCenterQueryReqBo reqBo) {
        log.info("删除帮助中心,reqBo:{}", reqBo);
        if (reqBo.getId() == null) {
            return BaseRspUtils.createErrorRsp("参数错误");
        }
        SysHelpCenter helpCenter = new SysHelpCenter();
        helpCenter.setId(reqBo.getId());
        helpCenter.setTenantCode(reqBo.getTenantCode());
        helpCenter.setIsValid(TenantUserStatusType.DELETE.getCode());
        helpCenter.setUpdateTime(new Date());
        sysHelpCenterMapper.updateByPrimaryKeySelective(helpCenter);
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 帮助中心配置上下架
     * @param reqBo
     * @return
     */

    @Override
    public Rsp updateStatus(SysHelpCenterQueryReqBo reqBo) {
        log.info("帮助中心配置上下架,reqBo:{}", reqBo);
        if (reqBo.getId() == null) {
            return BaseRspUtils.createErrorRsp("参数错误");
        }
        SysHelpCenter helpCenter = new SysHelpCenter();
        helpCenter.setId(reqBo.getId());
        helpCenter.setStatus(reqBo.getStatus());
        helpCenter.setUpdateTime(new Date());
        sysHelpCenterMapper.updateByPrimaryKeySelective(helpCenter);
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    /**
     * 用户查询帮助中心
     * @param reqBo
     * @return
     */
    @Override
    public RspList<SysHelpCenterRspBO> getUserHelpCenterList(SysHelpCenterQueryReqBo reqBo) {
       log.info("用户查询帮助中心,reqBo:{}", reqBo);
        SysHelpCenterCondition condition = new SysHelpCenterCondition();
        BeanUtils.copyProperties(reqBo, condition);
        condition.setIsValid(TenantUserStatusType.NORMAL.getCode());
        Page<SysHelpCenterRspBO> page = PageHelper.startPage(reqBo.getPage(), reqBo.getLimit());
        sysHelpCenterMapper.selectByCategory(condition);
        List<SysHelpCenterRspBO> list = new ArrayList<>();
        NiccCommonUtil.copyList(page.getResult(), list, SysHelpCenterRspBO.class);
        return BaseRspUtils.createSuccessRspList(list, page.getTotal());
    }

    /**
     * 根据id列表排序
     * @param reqBo
     * @return
     */
    @Override
    public Rsp updateSort(SysHelpCenterQueryReqBo reqBo) {
        log.info("根据id列表排序,reqBo:{}", reqBo);
        List<SysHelpCenter> list = new ArrayList<>();
        IntStream.range(0, reqBo.getSortIds().size()).forEach(i -> {
            SysHelpCenter helpCenter = new SysHelpCenter();
            helpCenter.setId(reqBo.getSortIds().get(i));
            helpCenter.setCategory(reqBo.getCategory());
            helpCenter.setOrderIndex(i);
            helpCenter.setUpdateTime(new Date());
            list.add(helpCenter);
        });
        sysHelpCenterMapper.updateBatchSelective(list);
        return BaseRspUtils.createSuccessRsp("操作成功");
    }

    @Override
    public Rsp<SysHelpCenterRspBO> getHelpCenterById(SysHelpCenterQueryReqBo reqBo) {
        log.info("根据id查询帮助中心,reqBo:{}", reqBo);
        if (reqBo.getId() == null) {
            return BaseRspUtils.createErrorRsp("参数错误");
        }
        SysHelpCenter helpCenter = sysHelpCenterMapper.selectByPrimaryKey(reqBo.getId());
        SysHelpCenterRspBO rspBo = new SysHelpCenterRspBO();
        BeanUtils.copyProperties(helpCenter, rspBo);
        return BaseRspUtils.createSuccessRsp(rspBo);
    }
}
