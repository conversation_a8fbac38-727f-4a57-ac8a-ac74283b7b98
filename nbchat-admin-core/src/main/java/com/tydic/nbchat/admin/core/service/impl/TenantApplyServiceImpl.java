package com.tydic.nbchat.admin.core.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.tydic.nbchat.admin.api.TenantApplyApi;
import com.tydic.nbchat.admin.api.TenantApplyListener;
import com.tydic.nbchat.admin.api.bo.TenantApplyBO;
import com.tydic.nbchat.admin.core.config.utils.RandomStringGenerator;
import com.tydic.nbchat.admin.mapper.NbchatEnterpriseTryApplyMapper;
import com.tydic.nbchat.admin.mapper.po.NbchatEnterpriseTryApply;
import com.tydic.nbchat.user.api.UserSettingsApi;
import com.tydic.nbchat.user.api.bo.eums.UserSettingKey;
import com.tydic.nbchat.user.api.bo.setting.UserSettingContext;
import com.tydic.nbchat.user.api.bo.utils.PhoneNumberUtils;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.redis.RedisHelper;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import com.tydic.nicc.framework.utils.NiccCommonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Slf4j
@Service
public class TenantApplyServiceImpl implements TenantApplyApi {

    NbchatEnterpriseTryApplyMapper tryApplyMapper;
    TenantApplyListener tenantApplyListener;
    private final RedisHelper redisHelper;

    @DubboReference(version = "${nicc-dc-config.dubbo-provider.version}",
            group = "${nicc-dc-config.dubbo-provider.group}", timeout = 1000)
    private UserSettingsApi userSettingsApi;

    TenantApplyServiceImpl(NbchatEnterpriseTryApplyMapper tryApplyMapper,
                           @Autowired(required = false) TenantApplyListener tenantApplyListener, RedisHelper redisHelper){
        this.tryApplyMapper = tryApplyMapper;
        this.tenantApplyListener = tenantApplyListener;
        this.redisHelper = redisHelper;
    }

    @Override
    public Rsp save(TenantApplyBO request) {
        log.info("处理申请：{}",request);
        NbchatEnterpriseTryApply rec = new NbchatEnterpriseTryApply();
        BeanUtils.copyProperties(request,rec);
        if (StringUtils.isEmpty(request.getStatus())) {
            List<NbchatEnterpriseTryApply> applyRecord = tryApplyMapper.selectAll(rec);
            if (ObjectUtils.isEmpty(request.getId()) && CollectionUtils.isNotEmpty(applyRecord)) {
                log.info("已存在申请记录:{}", applyRecord);
                return BaseRspUtils.createSuccessRsp(applyRecord.get(0));
            }
        }

        if (ObjectUtils.isEmpty(request.getId())) {
            Rsp rsp = checkParams(request);
            if (!rsp.isSuccess()) {
                return rsp;
            }
            String tenantCode = RandomStringGenerator.generateRandomString(8);
            rec.setTenantCode(tenantCode);
            rec.setCreatedTime(new Date());
            rec.setUpdatedTime(new Date());
            log.info("租户管理-申请保存：{}",rec);
            tryApplyMapper.insertSelective(rec);
            return BaseRspUtils.createSuccessRsp(rec);
        } else {
            log.info("租户管理-申请更新：{}",request);
            NbchatEnterpriseTryApply tryApply = tryApplyMapper.queryById(request.getId());
            rec.setUpdatedTime(new Date());
            rec.setTenantCode(tryApply.getTenantCode());
            rec.setUserId(tryApply.getUserId());
            request.setTenantCode(tryApply.getTenantCode());
            request.setUserId(tryApply.getUserId());
            request.setName(tryApply.getName());
            int i = tryApplyMapper.update(rec);
            if (i > 0 && StringUtils.isNotEmpty(request.getStatus())) {
                if (request.getStatus().equals(tenantApplyListener.PRE_PASS)) {
                    Optional.ofNullable(tenantApplyListener).ifPresent(listener -> listener.prePass(request));
                }
                if (request.getStatus().equals(tenantApplyListener.PASS)) {
                    Optional.ofNullable(tenantApplyListener).ifPresent(listener -> listener.pass(request));
                }
                if (request.getStatus().equals(tenantApplyListener.NO_PASS)) {
                    Optional.ofNullable(tenantApplyListener).ifPresent(TenantApplyListener -> tenantApplyListener.noPass(request));
                    tryApply.setStatus(tenantApplyListener.NO_PASS);
                    tryApplyMapper.update(tryApply);
                }
            }
            return BaseRspUtils.createSuccessRsp(rec);
        }
    }

    @Override
    public RspList<TenantApplyBO> query(TenantApplyBO request) {
        log.info("租户管理-申请查询：{}",request);
        NbchatEnterpriseTryApply cond = new NbchatEnterpriseTryApply();
        BeanUtils.copyProperties(request,cond);
        cond.setTenantCode(null);

        //校验是否是管理员
        if (request.getType().equals(ALL)) {
            Rsp<UserSettingContext> contextRsp = userSettingsApi.loadSettings(request.getUserId());
            if (contextRsp.isSuccess() &&
                    contextRsp.getData().getSettings() != null &&
                    contextRsp.getData().getSettings().containsKey(UserSettingKey.platformAdminPermission.getKey())) {
                cond.setUserId(null);
            }
        }
        Page<NbchatEnterpriseTryApply> res = PageHelper.startPage(request.getPage(), request.getLimit());
        tryApplyMapper.selectAll(cond);
        ArrayList<TenantApplyBO> bos = new ArrayList<>();
        NiccCommonUtil.copyList(res.getResult(),bos,TenantApplyBO.class);
        return BaseRspUtils.createSuccessRspList(bos,res.getTotal());
    }

    public Rsp checkParams(TenantApplyBO bo){
        if (!PhoneNumberUtils.validPhoneNumber(bo.getPhone())) {
            return BaseRspUtils.createErrorRsp("手机号不合法");
        }
        String e_mail_reg = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$";
        if (!bo.getEmail().matches(e_mail_reg)) {
            return BaseRspUtils.createErrorRsp("邮箱不合法");
        }
        //校验申请链接
        String key = NbchatSysTenantInviteServiceImpl.NBCHAT_ADMIN_TENANT_INVITE_KEY + bo.getTryInviteKey();
        Long expire = redisHelper.getExpire(key);
        log.info("缓存查询key ：{},缓存剩余过期时间结:{}",key,expire);
        if (expire < 0) {
            return BaseRspUtils.createErrorRsp("二维码已过期");
        }

        return BaseRspUtils.createSuccessRsp(true);
    }


}
