package com.tydic.nbchat.admin.core.controller;

import com.tydic.nbchat.admin.api.SysUserRoleApi;
import com.tydic.nbchat.admin.core.busi.SysUserRoleBusiService;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.common.bo.premission.RequiresRole;
import com.tydic.nicc.common.nbchat.constants.UserRoleConstants;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;
import com.tydic.nicc.dc.boot.starter.util.BaseRspUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/sys/role")
public class SysUserRoleController {
    private final SysUserRoleApi sysUserRoleApi;
    private final SysUserRoleBusiService sysUserRoleBusiService;

    @GetMapping("/fix")
    public Rsp fix() {
        int count = sysUserRoleBusiService.fixUserRoleFromUserSetting();
        return BaseRspUtils.createSuccessRsp(count);
    }

    /**
     * 根据不用角色查询可设置的角色列表
     * @param reqBO
     * @return
     */
    @PostMapping("/select_list")
    public RspList list(@RequestBody BaseInfo reqBO) {
        return sysUserRoleApi.getSelectRoleList(reqBO);
    }

    /**
     * 查询用户归属租户
     * @param reqBO
     * @return
     */
    @PostMapping("/tenants")
    public RspList tenants(@RequestBody BaseInfo reqBO) {
        return sysUserRoleApi.getRoleTenants(reqBO);
    }
}
