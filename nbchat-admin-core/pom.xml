<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

	<parent>
		<groupId>com.tydic.nbchat</groupId>
		<artifactId>nbchat-admin</artifactId>
		<version>${revision}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<modelVersion>4.0.0</modelVersion>
	<artifactId>nbchat-admin-core</artifactId>
	<description>核心服务</description>

	<dependencies>
		<!--项目依赖开始 -->
		<dependency>
			<groupId>com.tydic.nbchat</groupId>
			<artifactId>nbchat-admin-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.tydic.nbchat</groupId>
			<artifactId>nbchat-admin-mapper</artifactId>
		</dependency>
		<dependency>
			<groupId>com.tydic.nbchat</groupId>
			<artifactId>nbchat-user-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.belerweb</groupId>
			<artifactId>pinyin4j</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>alibaba-dingtalk-service-sdk</artifactId>
		</dependency>
		<dependency>
		<groupId>cn.hutool</groupId>
		<artifactId>hutool-all</artifactId>
		</dependency>
	</dependencies>

</project>
