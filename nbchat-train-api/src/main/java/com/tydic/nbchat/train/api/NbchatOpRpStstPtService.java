package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.report.op.TrainOpStstPtRequestBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 运营统计表_次数
 * (OpRpStstPt)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-27 17:48:53
 */
public interface NbchatOpRpStstPtService {


    /**
     * 统计视频制作
     * 统计ppt制作
     * 统计出题次数
     * @param requestBO
     * @return
     */
    Rsp save(TrainOpStstPtRequestBO requestBO);

    /**
     * 查询报表
     * @param requestBO
     * @return
     */
    Rsp queryReport(TrainOpStstPtRequestBO requestBO);

    /**
     * 查询租户数据列表
     * @param requestBO
     * @return
     */
    RspList queryTenantDataList(TrainOpStstPtRequestBO requestBO);
//
//    /**
//     * 查询报表
//     * @param requestBO
//     * @return
//     */
//    Rsp queryReportTenantList(TrainOpStstPtRequestBO requestBO);
}
