package com.tydic.nbchat.train.api.bo.eums;

/**
 * 使用主播服务
 * ali 阿里云 asr tts
 * modelscope 自建模型 asr tts
 */
public enum AnchorType {

    COSYVOICE("cosyVoice","cosyVoice定制音频", ""),
    ALI("ali","阿里云", "aixia"),
    MOBVOI("mobovi","出门问问", "cissy_meet"),
    VOLCENGINE("volcengine","火山引擎", "BV407_V2_streaming"),
    PRIVATE("private","私有化模型", "sambert_zh_multisp_girl"),
    MODELSCOPE("modelscope","自建模型", "sambert_zh_multisp_girl");

    private String code;
    private String name;
    private String defaultVoice;

    AnchorType(String code, String name,String defaultVoice){
        this.code = code;
        this.name = name;
        this.defaultVoice = defaultVoice;
    }
    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }

    public String getDefaultVoice() {
        return defaultVoice;
    }

    public static String getDefaultVoiceByCode(String code ) {
        for (AnchorType value : AnchorType.values()) {
            if (value.code.equals(code)) {
                return value.defaultVoice;
            }
        }
        return "";
    }

    public static String getNameByCode(String code ) {
        for (AnchorType value : AnchorType.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return "";
    }
}
