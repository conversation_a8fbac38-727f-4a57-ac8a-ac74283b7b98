package com.tydic.nbchat.train.api.bo;

import java.util.Map;

public class ScoreRangeCount {
    private String range;
    private Integer count;

    public ScoreRangeCount(Map.Entry<String, Integer> entry) {
        this.range = entry.getKey();
        this.count = entry.getValue();
    }

    public String getRange() {
        return range;
    }

    public void setRange(String range) {
        this.range = range;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
