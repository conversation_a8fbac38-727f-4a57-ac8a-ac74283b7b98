package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数字人-虚拟人形象(TdhVirtualHuman)实体类
 *
 * <AUTHOR>
 * @since 2023-08-22 15:47:54
 */
@Data
public class TdhVirtualHumanReqBO implements Serializable {
    private static final long serialVersionUID = -84139554054157398L;

    /**
     * 定制状态 0: 草稿（上传中） 1: 订单创建（待支付） 2: 支付完成（审核中） 3: 审核完成（定制中） 4: 定制完成  5: 取消
     */
    private String customizeStatus;

    private String orderNo;
    /**
     * 虚拟人id
     */
    private String tdhId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 男/女
     */
    private String gender;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 虚拟人图片地址
     */
    private String tdhImg;
    private String tdhImgThu;
    private String tdhType;
    /**
     * 名称
     */
    private String tdhName;
    /**
     * 标签 [{name:’’,color:’’}]
     */
    private String tdhTags;
    /**
     * 1 全身 2 半身 3 大半身 4 坐姿
     */
    private String poseType;
    /**
     * 0 系统内置  1 自定义
     */
    private String tdhSource;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 排序
     */
    private Integer orderIndex;
    /**
     * 1 正常 0 已删除
     */
    private String isValid;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;


}

