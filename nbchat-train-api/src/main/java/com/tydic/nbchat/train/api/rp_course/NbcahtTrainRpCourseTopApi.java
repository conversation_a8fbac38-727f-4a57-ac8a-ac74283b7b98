package com.tydic.nbchat.train.api.rp_course;

import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseRankingQueryReqBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseRankingQueryRspBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseTop5QueryReqBO;
import com.tydic.nbchat.train.api.bo.report.course.TrainRpCourseTop5QueryRspBO;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 课程报表top排行
 */
public interface NbcahtTrainRpCourseTopApi {

    /**
     * 热门课程查询
     * @param top5QueryReqBO
     * @return
     */
    RspList<TrainRpCourseTop5QueryRspBO> getCourseHotTopList(TrainRpCourseTop5QueryReqBO top5QueryReqBO);

    /**
     * 课程排行统计查询
     * @param queryReqBO
     * @return
     */
    RspList<TrainRpCourseRankingQueryRspBO> getCourseRankingList(TrainRpCourseRankingQueryReqBO queryReqBO);

}
