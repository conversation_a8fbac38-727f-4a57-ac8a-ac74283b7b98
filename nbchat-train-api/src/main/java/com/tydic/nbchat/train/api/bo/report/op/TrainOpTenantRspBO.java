package com.tydic.nbchat.train.api.bo.report.op;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TrainOpTenantRspBO implements Serializable {
    /**
     * 入驻日期
     */
    private Date createTime;
    /**
     * 上次登录时间
     */
    private Date lastLoginTime;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 分组租户编码
     */
    private String groupTenantCode;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 租户下用户数
     */
    private int tenantUserNum;
    /**
     * 视频制作次数
     */
    private int videoNum;
    /**
     * 视频制作成功次数
     */
    private int videoSuccessNum;
    /**
     * 视频制作失败率
     */
    private double videoFailProbability;
    /**
     * ppt制作次数
     */
    private int pptNum;
    /**
     * 出题次数
     */
    private int questionNum;
    /**
     * 是否为公共租户
     */
    private boolean isCommonTenant=false;
    /**
     * 租户数量
     */
    private int tenantNum;


    public void addMetrics(TrainOpTenantRspBO other) {
        this.tenantUserNum += other.tenantUserNum;
        this.videoNum += other.videoNum;
        this.videoSuccessNum += other.videoSuccessNum;
        this.pptNum += other.pptNum;
        this.questionNum += other.questionNum;
        this.tenantNum += other.tenantNum;
    }

    public TrainOpTenantRspBO deepCopy() {
        TrainOpTenantRspBO copy = new TrainOpTenantRspBO();

        // 对于Date类型的字段，需要创建新的Date实例
        copy.createTime = (this.createTime != null) ? new Date(this.createTime.getTime()) : null;
        copy.lastLoginTime = (this.lastLoginTime != null) ? new Date(this.lastLoginTime.getTime()) : null;

        // 对于基本类型和不可变对象，可以直接赋值
        copy.tenantCode = this.tenantCode;
        copy.groupTenantCode = this.groupTenantCode;
        copy.tenantName = this.tenantName;
        copy.tenantUserNum = this.tenantUserNum;
        copy.videoNum = this.videoNum;
        copy.videoSuccessNum = this.videoSuccessNum;
        copy.videoFailProbability = this.videoFailProbability;
        copy.pptNum = this.pptNum;
        copy.questionNum = this.questionNum;
        copy.isCommonTenant = this.isCommonTenant;
        copy.tenantNum = this.tenantNum;

        return copy;
    }
}
