package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.task.NbchatTrainTaskWhiteListReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatTrainTaskWhiteListApi {
    /**
     * 添加白名单列表
     * @param reqBO
     * @return
     */
    Rsp addWhiteList(NbchatTrainTaskWhiteListReqBO reqBO);

    /**
     * 查询白名单列表
     * @param reqBO
     * @return
     */
    RspList queryWhiteList(NbchatTrainTaskWhiteListReqBO reqBO);

    /**
     * 移除白名单列表
     * @param reqBO
     * @return
     */
    Rsp deleteWhiteList(NbchatTrainTaskWhiteListReqBO reqBO);
}
