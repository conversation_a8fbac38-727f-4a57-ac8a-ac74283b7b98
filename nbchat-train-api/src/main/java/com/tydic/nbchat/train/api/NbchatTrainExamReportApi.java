package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.report.QueryReportRequest;
import com.tydic.nbchat.train.api.bo.report.exam.TrainRpExamStudentsQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatTrainExamReportApi {
    /**
     * 获取测评人次/获取测评人数
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    Rsp getEvaluationCount(TrainRpExamStudentsQueryReqBO reqBO);

    /**
     * 获取综合评分
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    Rsp getCompositeScore(TrainRpExamStudentsQueryReqBO reqBO);

    /**
     * 获取数据趋势
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    RspList getDataTrend(QueryReportRequest request);

    /**
     * 获取测评排行
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    RspList getEvaluationRanking(TrainRpExamStudentsQueryReqBO reqBO);
}
