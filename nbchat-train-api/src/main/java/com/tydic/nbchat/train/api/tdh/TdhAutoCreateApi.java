package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.*;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface TdhAutoCreateApi {
    String default_content = "未定义";
    String content_ = "content";
    String title_ = "title";
    String name_ = "name";
    String parts_ = "parts";
    String config_ = "config";

    String creationId = "creationId";
    String tenantCode = "tenantCode";
    String userId = "userId";
    String creationName = "creationName";
    String courseId = "courseId";
    String sectionId = "sectionId";
    Integer video_intro = 1; //课程简介视频生成流程
    Integer video_section = 2; //章节内容视频生成流程
    Integer preview_request = 1; //查看预览
    Integer course_intro_temp_code = 1; //课程介绍页模板
    Integer course_cata_temp_code = 2; //课程目录页模板
    Integer section_intro_temp_code = 3; //章节介绍页模板
    Integer section_content_temp_code = 4; //章节内容页模板
    Integer parts_content_horizontal_temp_code = 5; //片段内容页模板（横版）
    Integer parts_content_vertical_temp_code = 6; //片段内容页模板（竖版）

    default String createName(String var1, String var2){
        return var1 + "-" + var2;
    }
    /**
     * 自动生成视频
     * @param request
     * @return
     */
    Rsp autoCreate(TdhAutoCreateBO request);

    /**
     * 获取模板
     * @param request
     * @return
     */
    Rsp getTemplate(TdhAutoCreateBO request);

    /**
     * 替换模板
     * @param request
     * @return
     */
    Rsp replaceTemp(ReplaceTempBO request);

    /**
     * 自动创建片段
     * @param request
     * @return
     */
    Rsp autoParts(TdhPartsAutoBO request);

    Rsp partGenerator(TdhPartGeneratorReqBO request);

}
