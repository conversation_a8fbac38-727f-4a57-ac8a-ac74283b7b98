package com.tydic.nbchat.train.api.bo.train.scene;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 学习任务表：打包配置多个课程(NbchatTrainTask)实体类
 *
 * <AUTHOR>
 * @since 2024-07-10 17:50:52
 */
@Data
public class NbchatTrainTaskBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = -97638669505902323L;
    private List<String> targetUserIds;
    private List<JSONObject> targetUserInfoList;

    private String supportSubDept; //支持本级及下级部门 1支持 0不支持
    private String tenantCode;
    private String courseType; //课程类型 1-普通课程 2-训战课程 3-考试课程
    private String userId;
    //机构名称
    private String deptName;
    private String deptScope; //部门数据范围 0 本部门 1 本部门及下级部门
    //关卡数量
    private Integer taskCount;
    private String updateUserName;
    private String createUserName;

/**
     * 主键 任务id
     */
    private Integer id;
/**
     * 任务名称
     */
    private String taskName;
/**
     * 任务描述
     */
    private String taskDesc;
/**
     * 任务机构
     */
    private String deptId;
    private List<String> deptIds;
    private List<String> targetIds;
    private List<String> atDeptIds;

/**
     * 任务岗位
     */
    private String postId; //=岗位名称
    private List<String> postIds;

    private String postName; //=岗位名称
/**
     * 任务开始时间
     */
    private Date startTime;
/**
     * 任务结束时间
     */
    private Date endTime;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 创建人
     */
    private String createUser;
/**
     * 更新时间
     */
    private Date updateTime;
/**
     * 更新人
     */
    private String updateUser;


/**
     * 任务状态: 0 编辑中 1 上架 2 下架
     */
    private String startStatus;
/**
     * 是否发放考试证书
     */
    private String isDegree;
/**
     * 证书名称
     */
    private String degreeName;
/**
     * 证书介绍
     */
    private String degreeDesc;
/**
     * 证书模板地址
     */
    private String degreeTemplate;
/**
     * 证书有效期: 默认12个月
     */
    private Integer degreeValidity;
/**
     * 是否删除
     */
    private String isValid;


}

