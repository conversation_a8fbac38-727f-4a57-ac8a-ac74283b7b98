package com.tydic.nbchat.train.api.bo.course;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Data
public class TranUserCourseQueryReqBO extends BasePageInfo implements Serializable {
    private String userId;
    private String courseId;
    private String category;
    //0 学习中 1 已完成
    private String trainState;
    private String courseName;
    private String testState;
    //显示全部课程
    private Boolean showAll;

    //调用来源
    private String origin = "0"; //参考 OriginEnum
    private String star;
    private List<String> courseTypes = new ArrayList<>(Arrays.asList("1")); //1-普通课程 2-训战课程 3-考试课程
}
