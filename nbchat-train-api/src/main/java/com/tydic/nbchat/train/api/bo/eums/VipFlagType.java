package com.tydic.nbchat.train.api.bo.eums;

public enum VipFlagType {

    COMMON_USER("0","普通用户"),
    VIP("1","会员"),
    PROFESSIONAL_VIP("2","专业会员"),
    ENTERPRISE_VIP("9","企业会员"),
    ;

    private String code;
    private String name;
    VipFlagType(String code,String name){
        this.code = code;
        this.name = name;
    }
    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
    public static String getNameByCode(String code ) {
        for (VipFlagType value : VipFlagType.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return "";
    }
}
