package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BaseInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class ReplaceTempBO extends BaseInfo implements Serializable {
    private String userId;
    private String tpId;//替换模板id
    private List<String> textList;//替换文本列表
    private String title;//标题
    private String content;//内容
    private String jsonObject;//当前配置


}
