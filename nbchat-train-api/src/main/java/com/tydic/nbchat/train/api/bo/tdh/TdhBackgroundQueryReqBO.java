package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TdhBackgroundQueryReqBO extends BasePageInfo implements Serializable {
    private String sourceType = "0"; //0数字人 1ppt
    private String objectId;
    private String tenantCode;
    private String category;
    private String name;
    private String userId;
    private String objectUrl;
    private String objectSize;
    private String objectType;
    private Date createTime;
    private Short orderIndex;
    private String objectSource;
    private String isValid;
    private List<TdhBackgroundUrlBO> urlList;
}
