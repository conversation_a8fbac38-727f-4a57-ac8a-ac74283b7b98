package com.tydic.nbchat.train.api.bo.train.catalog;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class TrainCatalogBatchSaveBO implements Serializable {
    private String tenantCode;
    private String userId;
    private String courseId;
    private List<Node> catalogs;

    @Data
    public static class Node implements Serializable{
        private String catalogName;
        private String catalogId;
        private List<Node> catalog2;

        private String sectionId;
        private String sectionContent;
        private String videoUrl;
    }
}
