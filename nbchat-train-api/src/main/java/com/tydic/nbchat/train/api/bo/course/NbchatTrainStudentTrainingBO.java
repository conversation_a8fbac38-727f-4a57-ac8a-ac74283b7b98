package com.tydic.nbchat.train.api.bo.course;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime：2024/8/27 14:07
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NbchatTrainStudentTrainingBO implements Serializable {
    /**
     * 任务id
     */
    private Integer taskId;
    private String taskName;


    /**
     * 课程列表
     */
    private List<NbchatTrainStudentCourseBO> courseList;
}
