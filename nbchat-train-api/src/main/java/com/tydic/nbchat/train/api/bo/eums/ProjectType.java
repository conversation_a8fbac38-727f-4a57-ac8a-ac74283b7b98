package com.tydic.nbchat.train.api.bo.eums;

import lombok.Getter;

@Getter
public enum ProjectType {
    <PERSON><PERSON><PERSON>("tuotu<PERSON>", "妥妥E行"),
    <PERSON><PERSON><PERSON><PERSON>("zhong<PERSON><PERSON>", "中交养护")
    ;
    private String code;
    private String name;

    ProjectType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static ProjectType getByCode(String code) {
        for (ProjectType projectType : ProjectType.values()) {
            if (projectType.getCode().equals(code)) {
                return projectType;
            }
        }
        return null;
    }

}
