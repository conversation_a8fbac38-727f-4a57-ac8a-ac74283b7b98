package com.tydic.nbchat.train.api.bo.eums;

public enum TaskStateType {
    GENERATING("0","生成中"),
    MISSION_COMPLETED("1","任务完成"),
    TASK_EXCEPTION("2","任务异常"),
    TO_BE_EDITED("3","待编辑"),
    EDIT("4","编辑中"),
    IN_THE_LINE("q","排队中");
    private String code;
    private String name;
    TaskStateType(String code,String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
