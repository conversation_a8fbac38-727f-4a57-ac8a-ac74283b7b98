package com.tydic.nbchat.train.api.bo.eums;

public enum RoleType {

    DEFAULT_USER("user","默认角色"),
    DEFAULT_ASSISTANT("assistant","默认角色");

    private String code;
    private String name;
    RoleType(String code,String name){
        this.code = code;
        this.name = name;
    }
    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
    public static String getNameByCode(String code ) {
        for (RoleType value : RoleType.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return "";
    }
}
