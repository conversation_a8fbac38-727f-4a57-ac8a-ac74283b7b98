package com.tydic.nbchat.train.api.bo.asr_tts.modelscope;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class TtsResponse extends BaseResponse implements Serializable {
    private TtsResponseData data;

    public boolean success(){
        return "0".equals(this.getCode());
    }
}
