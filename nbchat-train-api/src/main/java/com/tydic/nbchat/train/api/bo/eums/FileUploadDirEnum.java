package com.tydic.nbchat.train.api.bo.eums;

public enum FileUploadDirEnum {

    TTS_TEMP("tdh/tmp_tts", "临时文件目录"),
    TTS_DIR("tts", "语音文件保存目录"),
    VIDEOS("videos", "视频文件保存目录");

    private String code;
    private String name;

    FileUploadDirEnum(String code, String name){
        this.code = code;
        this.name = name;
    }
    public String getCode() {
        return code;
    }
    public String getName() {
        return name;
    }
    public static String getNameByCode(String code ) {
        for (FileUploadDirEnum value : FileUploadDirEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return "";
    }

}
