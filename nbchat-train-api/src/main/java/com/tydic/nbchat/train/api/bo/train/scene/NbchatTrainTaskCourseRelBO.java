package com.tydic.nbchat.train.api.bo.train.scene;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 学习任务课程关联表(NbchatTrainTaskCourseRel)实体类
 *
 * <AUTHOR>
 * @since 2024-07-10 17:52:09
 */
@Data
public class NbchatTrainTaskCourseRelBO implements Serializable {
    private static final long serialVersionUID = -75186888758245534L;
/**
     * 主键
     */
    private Integer id;
/**
     * 任务ID
     */
    private String taskId;
/**
     * 课程ID
     */
    private String courseId;
    private String courseName;
/**
     * 排序
     */
    private Integer orderIndex;
/**
     * 是否学习视频
     */
    private String isStudyVideo;
/**
     * 是否课后评测
     */
    private String isAfterTest;
/**
     * 是否人机对练
     */
    private String isHumanMachine;
/**
     * 创建时间
     */
    private Date createTime;

    private int videoCount;
    private String courseState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String testPaperState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String dialogueState; //0 编辑中 1 预览中 2 上架中 3 已下架

}

