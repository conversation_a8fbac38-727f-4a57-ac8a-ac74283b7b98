package com.tydic.nbchat.train.api.bo.course;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class TrainCourseHotHubBO implements Serializable {
    private String userId;
    private String courseId;
    private String tenantCode;
    private Integer limit;

    private List<String> courseTypes = new ArrayList<>(Arrays.asList("1")); //1-普通课程 2-训战课程 3-考试课程

}
