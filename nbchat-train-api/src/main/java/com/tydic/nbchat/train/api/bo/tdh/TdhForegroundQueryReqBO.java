package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TdhForegroundQueryReqBO extends BasePageInfo implements Serializable {
    private String sourceType = "0"; //0数字人 1ppt

    private String name;
    private String category;
    private String userId;
    private String objectId;
    private String objectUrl;
    private List<TdhForegroundUrlBO> urlList;
    private String objectSize;
    private String objectType;
    private String objectSource;
    private String isValid;
    private String tag;
    private Integer sloop;
    private String objectConfig;
    private String thumbnail;
    private String previewUrl;
    private Integer duration;
    /**
     * 全局 "public"
     * 私有 填写下拉框里的tenantCode
     */
    private String targetTenant;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;
    /**
     * 0 下架 1 上架
     */
    private String tpState;
    /**
     * 音频地址
     */
    private String audioUrl;
    /**
     * 音频名称
     */
    private String audioName;

    /**
     * 应用场景
     */
    private String scene;

    /**
     * 风格
     */
    private String style;

    /**
     * 配色
     */
    private String color;


}
