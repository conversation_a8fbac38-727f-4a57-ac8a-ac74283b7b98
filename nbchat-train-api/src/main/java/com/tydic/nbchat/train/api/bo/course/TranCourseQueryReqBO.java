package com.tydic.nbchat.train.api.bo.course;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
public class TranCourseQueryReqBO extends BasePageInfo implements Serializable {

    private List<String> courseTypes = Collections.singletonList("1"); //1-普通课程 2-训战课程 3-考试课程

    private String majorId; //专属机器人id

    private boolean order = true; //是否排序
    private boolean searchAll = false;

    private String courseState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String testPaperState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String sceneState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String stepState; //1.基础信息  2.课程文档已解析  3.课程简介生成  4.目录生成  5.学习内容生成
    private String taskState; //0 任务未执行 1 任务执行中 2 任务异常
    private String deptId; //归属部门
    private String deptScope; //0 本部门 1 本部门及下级部门
    private String courseName;
    private String userId;
    private String courseId;
    private String category;
    private String category2;
    private String keyword;
}
