package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhBackgroundQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhBackgroundQueryRspBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 数字人：背景管理相关接口
 */
public interface TdhBackgroundApi {

    /**
     * 查询背景列表
     * @param reqBO
     * @return
     */
    RspList<TdhBackgroundQueryRspBO> getBackgroundList(TdhBackgroundQueryReqBO reqBO);
    RspList<TdhBackgroundQueryRspBO> save(TdhBackgroundQueryReqBO reqBO);

}
