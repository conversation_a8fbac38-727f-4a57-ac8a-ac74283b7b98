package com.tydic.nbchat.train.api.bo.dialogue;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class DialogueSessionBO implements Serializable {
    private String tenantCode;
    private String userId;

    private String sessionId;
    private String courseId;
    private List<Node> dialogues;

    @Data
    public static class Node implements Serializable{
        private String question;
        private String answer;
        private Float score;
        private String suggesstion;
    }
}
