package com.tydic.nbchat.train.api.bo.video;

import com.tydic.nbchat.user.api.bo.NbchatUserInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class NbchatVideoQueryRspBO implements Serializable {

    private NbchatUserInfo userInfo;

    private Date queueTime; //进入排队时间
    private String isValid;

    private String userId;
    private String tenantCode;
    private String courseId;
    private String sectionId;
    private Long duration = 0L;//任务时长
    private String videoDuration;
    private Integer queueNum; //排队序号
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 创作id
     */
    private String creationId;

    private String creationName;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    private Date createTime;
    /**
     * 总视频段
     */
    private Integer partCountTotal;
    /**
     * 完成视频段
     */
    private Integer partCountDone;
    /**
     * 视频下载地址
     */
    private String videoUrl;
    private String playUrl;
    /**
     * 0 生成中  1 任务完成 2 任务异常
     */
    private String taskState;
    /**
     * 0 待审核 1 审核通过 2 审核不通过
     */
    private String verifyState;
    /**
     * 错误码
     */
    private String errorCode;
    /**
     * 错误描述
     */
    private String errorDesc;

    /**
     * 0个人 1已共享
     */
    private String isShare;
    /**
     * 视频类型;1-普通创作/2-ppt创作
     */
    private String creationSource;
    /**
     * 内容分类;1上传PPT制作视频 2平台PPT制作视频
     */
    private String creationType;
    /**
     * 付费状态
     */
    private String vipStatus;
    /**
     * 用户属性
     */
    private String userType;
    /**
     * 创作者名称
     */
    private String creatorName;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 缩略图
     */
    private String previewUrl;
    /**
     * 原始文件地址
     */
    private String fileUrl;
    /**
     * 水印;0-无/1-有
     */
    private String waterMark;
    // 闲时队列传入 1
    private String idleQueue;
}
