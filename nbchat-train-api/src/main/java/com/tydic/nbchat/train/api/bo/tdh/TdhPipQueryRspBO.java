package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TdhPipQueryRspBO implements Serializable {
    private String pipId;
    /**
     * 租户
     */
    private String tenantCode;
    /**
     * 类别
     */
    private String category;
    /**
     * 名称
     */
    private String name;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 类型 png/jpg/mp4
     */
    private String pipType;
    /**
     * 描述
     */
    private String pipDesc;
    /**
     * 资源链接
     */
    private String pipUrl;
    private String firstFrame;

    /**
     * 0 已删除 1 正常
     */
    private String isValid;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 来源；0 系统内置 1 自定义
     */
    private String pipSource;
}
