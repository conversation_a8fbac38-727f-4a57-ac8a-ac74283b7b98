package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhPipQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface TdhPipApi {
    /**
     * 新增画中画记录
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    RspList AddPip(TdhPipQueryReqBO reqBO);

    /**
     * 修改画中画
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    RspList update(TdhPipQueryReqBO reqBO);

    /**
     * 查询最近20条上传记录
     * @param @param reqBO
     * @return @return {@link RspList }
     */
    RspList queryRecentHistory(TdhPipQueryReqBO reqBO);

    /**
     * 查询画中画
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    Rsp query(TdhPipQueryReqBO reqBO);

    /**
     * 删除画中画
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    Rsp delete(TdhPipQueryReqBO reqBO);
}
