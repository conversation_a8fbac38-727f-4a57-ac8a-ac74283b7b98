package com.tydic.nbchat.train.api.bo.dialogue.manage;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class DialogueManageBO implements Serializable {

    private String courseType; //课程类型 1-普通课程 2-训战课程 3-考试课程

    private String tenantCode;
    private String userId;

    private Integer id;

    private String courseId;
    private String courseName;
    private String category;
    private String category2;
    private String categoryName;
    private String category2Name;

    private String sceneState;
    private String courseState;
    /**
     * 会话数量
     */
    private Integer dialogueNum;
    private Integer score;
    private String trainRole; //实战角色
    private List<String> role; //[] 下标0: 提问角色 下标1:回答角色
    private List<Node> dialogue = new ArrayList<>();
    //对战描述
    private String dialogueDesc;
    //配置对战多个维度，每个维度有不同得分组成-json字段
    private String dialogueConfig;

    @Data
    public static class Node implements Serializable{
        private String keyPoints;
        private String question;
        private String questionAudio;
        private String answer;
        private String answerAudio;
    }
}
