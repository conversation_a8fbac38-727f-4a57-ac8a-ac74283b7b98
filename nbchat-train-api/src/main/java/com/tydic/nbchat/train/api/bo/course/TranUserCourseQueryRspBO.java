package com.tydic.nbchat.train.api.bo.course;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TranUserCourseQueryRspBO  implements Serializable {

    private String courseType;

    @JsonIgnore
    private String sceneState; //场景实践状态
    @JsonIgnore
    private String testPaperState; //考试配置状态
    private String dialogueState; //按钮状态 1可进行 0不可进行
    private String courseTestState; //按钮状态 1可进行 0不可进行
    private String qaState; //按钮状态 1可进行 0不可进行

    private String userId;
    private String courseId;
    private String courseName;
    private String imgAvatar;
    private String courseDesc;
    //0 学习中 1 已完成
    private String trainState;
    //0 未评测 1 已评测
    private String testState; //1可进行 0不可进行
    private Float classHourTotal;
    private Float classHourLearned;
    private Date lastTime;
    private String trainSections;
    private int trainCount;
}
