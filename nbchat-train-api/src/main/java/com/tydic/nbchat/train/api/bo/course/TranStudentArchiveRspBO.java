package com.tydic.nbchat.train.api.bo.course;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime：2024/8/26 15:04
 * @description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TranStudentArchiveRspBO implements Serializable {
    private Integer id;
    /**
     * 用户ID
     */
    private String userId;

    @ParamNotEmpty
    private String targetTenantCode;
    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 部门ID
     */
    private String deptId;
    private String deptScope;
    /**
     * 头像
     * private String avatar;
     * /**
     * 电话
     */
    private String phone;
    /**
     * 用户真实姓名
     */
    private String userRealityName;
    /**
     * 创建类型
     */
    private String joinType;
    /**
     * 租户代码
     */

    /**
     * 创建日期
     */
    private Date createTime;
    /**
     * 入职日期
     */
    private String entryTime;
    /**
     * 部门ID
     */

    /**
     * 排序
     */
    private Integer balanceScore;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 项目名称
     */
    private String projectName;
    /**
     * 性别
     */
    private String gender;
    /**
     * 培训类型
     */
    private List<NbchatTrainStudentTrainingBO> trainingList;
}
