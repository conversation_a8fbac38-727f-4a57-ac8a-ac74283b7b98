package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanSortReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhVirtualHumanUpdateReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 数字人：虚拟人形象管理相关接口
 */
public interface TdhVirtualHumanApi {

    /**
     * 查询虚拟人形象列表
     * @param reqBO
     * @return
     */
    RspList<TdhVirtualHumanQueryRspBO> getVirtualHumanList(TdhVirtualHumanQueryReqBO reqBO);

    /**
     * 更新虚拟人形象
     * @param request
     * @return
     */
    Rsp saveOrUpdateVirtualHuman(TdhVirtualHumanUpdateReqBO request);

    /**
     * 虚拟人形象排序
     * @param request
     * @returne
     */
    Rsp sort(TdhVirtualHumanSortReqBO request);

    /**
     * 数字人模版上下架
     */
    Rsp updateStatus(TdhVirtualHumanUpdateReqBO request);

    /**
     * 数字人模版删除
     */
    Rsp delete(TdhVirtualHumanUpdateReqBO request);

    /**
     * 运营平台数字人列表查询
     */
    RspList<TdhVirtualHumanQueryRspBO> getQueryVirtualHumanList(TdhVirtualHumanQueryReqBO request);
}
