package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.dialogue.*;
import com.tydic.nbchat.train.api.bo.dialogue.manage.DialogueManageBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatTrainDialogueApi {

    /**
     * 查询场景对话列表
     * @param reqBO
     * @return
     */
    RspList getCourseDialogues(DialogueReqBO reqBO);

    /**
     * 查询会话记录
     * @param request
     * @return
     */
    Rsp getSessionInfo(DialogueSessionQueryReqBO request);


    @Deprecated
    Rsp save(DialogueBO request);

    /**
     * 保存场景实践记录
     * @param request
     * @return
     */
    Rsp saveSession(DialogueSessionSaveReqBO request);



}
