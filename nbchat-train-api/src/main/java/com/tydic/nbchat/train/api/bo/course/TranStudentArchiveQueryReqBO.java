package com.tydic.nbchat.train.api.bo.course;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime：2024/8/26 15:04
 * @description:
 */
@Data
public class TranStudentArchiveQueryReqBO  implements Serializable {
    private String id;
    /**
     * 一组学员ID
     */
    private List<String> ids;
    /**
     * 用户ID
     */
    private String userId;

    @ParamNotEmpty
    private String targetTenantCode;
    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 部门ID
     */
    private String deptId;
    private String deptScope;

    /**
     * 是否包含下级部门 1：包含 0：不包含
     */
    private String supportSubDept;


}
