package com.tydic.nbchat.train.api.bo.report;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class QueryReportRequest extends BasePageInfo implements Serializable {
    private String tenantCode;
    private String userId;

    private Date startDate;
    private Date endDate;
    private String courseId;

    private String orderCode; //user,avg,甲乙丙丁
    private String orderType = "desc"; //asc desc
    private List<String> courseTypes = new ArrayList<>(Arrays.asList("1")); //1-普通课程 2-训战课程 3-考试课程

}
