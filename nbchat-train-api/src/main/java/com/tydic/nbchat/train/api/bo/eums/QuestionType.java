package com.tydic.nbchat.train.api.bo.eums;

/**
 * 2001 单选
 * 2002 多选
 * 2003 填空
 * 2004 判断
 * 2005 简答
 * 2006 论述
 */
public enum QuestionType {

    CHOICE_S("2001", "单选题"),
    CHOICE_M("2002", "多选题"),
    FILL_BLANK("2003", "填空题"),
    TRUE_FALSE("2004", "判断题"),
    QA_MIN("2005", "简答题"),
    QA_MAX("2006", "论述题");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private QuestionType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (QuestionType field : QuestionType.values()) {
            if (field.code.equals(code)) {
                return field.name;
            }
        }
        return "";
    }

    public static String getCodeByName(String name) {
        for (QuestionType field : QuestionType.values()) {
            if (field.name.equals(name)) {
                return field.code;
            }
        }
        return "";
    }

    public static boolean isFillBlank(String questionType) {
        return FILL_BLANK.getCode().equals(questionType);
    }

    public static boolean isChoice(String questionType) {
        return CHOICE_S.getCode().equals(questionType) ||
                CHOICE_M.getCode().equals(questionType);
    }

    public static boolean isJudgment(String questionType) {
        return TRUE_FALSE.getCode().equals(questionType);
    }

    public static boolean isQA(String questionType) {
        return TRUE_FALSE.getCode().equals(questionType) ||
                QA_MIN.getCode().equals(questionType) ||
                QA_MAX.getCode().equals(questionType);
    }

}
