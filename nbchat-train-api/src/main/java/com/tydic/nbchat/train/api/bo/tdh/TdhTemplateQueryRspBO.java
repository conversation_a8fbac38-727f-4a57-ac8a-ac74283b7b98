package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TdhTemplateQueryRspBO implements Serializable {
    /**
     * 模板id
     */
    private String tpId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     *分类
     */
    private String category;
    /**
     * 模板名称
     */
    private String tpName;
    private String remark;
    private String objectSize;
    //内容尺寸 16:9/9:16
    private String contentSize;
    /**
     * 模板内容
     */
    private String tpContent;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 排序字段
     */
    private Integer orderIndex;
    /**
     * 来源：0 系统内定 1 我的模板
     */
    private String tpSource;
    /**
     * 0 已删除 1 正常
     */
    private String isValid;
    /**
     * 是否热门
     */
    private String isHot;
    private String previewUrl;
    private String replaceTag;//0不可替换 1可替换
    private String videoUrl;
    private Integer duration;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;

    /**
     * 场景
     */
    private String scene;
    /**
     * 风格
     */
    private String style;
    /**
     * 配色
     */
    private String color;
    //1:视频类 2:播报类
    private String tdhType;
    //是否有数字人 0:无 1:有
    private String isTdh;
    //0 下架 1 上架
    private String tpState;
    private String isStar;
    /**
     * 0 静态 1动态
     */
    private String tplType;
}
