package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.util.Date;

@Data
public class TdhVirtualAnchorQueryReqBO extends BasePageInfo implements java.io.Serializable {

    private String demoUrl;
    private String volcId;
    private String customizeStatus;
    private String isSystem = "0";
    private String orderNo;

    private String emotion;//情感
    private String anchorId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 租户id
     */
    private String tenantCode;
    private String category;
    /**
     * 名称
     */
    private String name;
    /**
     * 性别
     */
    private String gender;
    /**
     * 类型： 新闻、小说、纪录片、教育…
     */
    private String type;
    /**
     * 年龄段：老年、中年、青年、少年
     */
    private String ageGroup;
    /**
     * 声音 标示
     */
    private String voice;
    /**
     * 风格
     */
    private String style;
    /**
     * 语言类型
     */
    private String language;
    /**
     * 价格
     */
    private String price;
    /**
     * 标签
     */
    private String label;
    /**
     * 0 系统默认 1 用户自定义
     */
    private String anchorSource;
    private String anchorConfig;
    /**
     * 头像地址
     */
    private String imgAvatar;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 排序
     */
    private Integer orderIndex;
    //业务分类: 0 通用/1 AI陪练
    private String busiType;
    /**
     * 是否有效
     */
    private String isValid;
}
