package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数字人-前景(TdhAudio)实体类
 *
 * <AUTHOR>
 * @since 2023-10-16 14:18:12
 */
@Data
public class TdhAudioBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = -98486538242454624L;
    
    private String objectId;
    
    private String tenantCode;
    
    private String userId;
    /**
     * 分类
     */
    private String category;
    /**
     * 名称
     */
    private String objectName;
    
    private String objectUrl;
    /**
     * wav/mp3
     */
    private String objectType;
    
    private Date createTime;
    /**
     * 时长
     */
    private Integer duration;
    
    private Integer orderIndex;
    /**
     * 0 系统内置 1 自定义
     */
    private String objectSource;
    /**
     * 0 已删除 1 正常
     */
    private String isValid;

}

