package com.tydic.nbchat.train.api.bo.task;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class NbchatUserScoreTaskRequest implements Serializable {

    private static final long serialVersionUID = 259028932646104797L;
    /**
     * 主键
     */
    private Long taskId;
    /**
     * 租户id
     */
    private String tenantCode;
    /**
     * 充值给用户id
     */
    private String userId;
    /**
     * 任务类型 1充值 2退款
     */
    private String taskType;
    /**
     * 充值/退款数量
     */
    private Integer score;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 任务执行时间
     */
    private Date execTime;
    /**
     * 算力点过期时间
     */
    private Date expireTime;
    /**
     * 任务状态 0未执行 1已执行 2执行异常
     */
    private String taskStatus;
    /**
     * 任务描述
     */
    private String taskDesc;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否有效 0无效 1有效
     */
    private String isValid;


}
