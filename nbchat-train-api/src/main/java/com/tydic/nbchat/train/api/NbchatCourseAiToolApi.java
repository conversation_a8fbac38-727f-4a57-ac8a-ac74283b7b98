package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.generate.TranCourseGenerateRequest;
import com.tydic.nicc.dc.base.bo.Rsp;

/**
 * 课程ai统计
 */
public interface NbchatCourseAiToolApi {

    /**
     * 生成课程简介
     * @param courseId
     * @return
     */
    Rsp generateCourseDesc(String courseId);

    /**
     * 生成目录
     * @param courseId
     * @return
     */
    Rsp generateCourseCatalog(String courseId);

    /**
     * 生成课程章节
     * @param courseId
     * @return
     */
    Rsp generateCourseSections(String courseId,String catalog);

    /**
     * 生成工具
     * @param request
     * @return
     */
    Rsp generate(TranCourseGenerateRequest request);

}
