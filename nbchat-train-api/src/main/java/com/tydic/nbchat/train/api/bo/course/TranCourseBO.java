package com.tydic.nbchat.train.api.bo.course;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TranCourseBO implements Serializable {

    private int videoCount;
    private String courseType; //课程类型 1-普通课程 2-训战课程 3-考试课程

    private String creationId;
    private String taskId; //任务id
    private String courseFileUrl; //课程文件地址
    private String majorId; //专属机器人id

    private String categoryName;
    private String courseState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String sceneState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String testPaperState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String stepState; //1.基础信息  2.课程文档已解析  3.课程简介生成  4.目录生成  5.学习内容生成
    private String taskState; //0 任务未执行 1 任务执行中 2 任务异常
    private String fastStudyVideo;    //是否允许快进视频
    private String deptId;

    private String videoUrl;
    private String videoImg;
    private String voiceUrl;
    private String courseId;
    private String courseName;
    private String courseDesc;
    private Short courseSource;
    private String labels;
    private String category;
    private String category2;
    private String category2Name;

    private String imgAvatar;

    private String tenantCode;
    private String userId;
    private String createUserName;
    private String isValid;
    private Integer orderIndex;
    private String extInfo;
    private Float classHour;
    private Date createTime;

    private Date updateTime;
    //学习人数
    private int trainCount;
    private int videoSource;

}
