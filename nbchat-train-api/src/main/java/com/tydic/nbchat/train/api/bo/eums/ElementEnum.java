package com.tydic.nbchat.train.api.bo.eums;

public enum ElementEnum {

    //课程介绍页
    course_name("#course_name#","课程名称"),
    course_desc("#course_desc#","课程描述"),
    course_catalog("#course_catalog_","目录,后面跟目录序号"),

    //章节介绍页
    catalog_level_1_name("#catalog_level_1_name#","一级目录标题"),
    section_num("#section_num#","序号"),
    //章节内容页
    catalog_level_2_content("#catalog_level_2_content#","二级目录内容"),
    catalog_level_2_name("#catalog_level_2_name#","二级目录标题"),
    key("#key_","关键点，后面跟序号"),
    key_content("#key_content_","关键点内容，后面跟对应关键点的序号"),

    //片段内容
    parts_content("#parts_content#","片段内容"),
    bg_url("#bg_url#","背景图片"),
    image_h("#image_h#","图片高度"),
    image_w("#image_w#","图片宽度"),
    ;
    private String code;
    private String name;

    ElementEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String concatNum(Integer num){
        return this.code + num + "#";
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
