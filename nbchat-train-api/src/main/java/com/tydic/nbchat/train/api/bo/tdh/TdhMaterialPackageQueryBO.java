package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class TdhMaterialPackageQueryBO extends BasePageInfo implements Serializable {
    /**
     * 主键
     */
    private String pkgId;
    /**
     * 主分类
     */
    private String category1;
    /**
     * 子分类
     */
    private String category2;
    /**
     * 色调
     */
    private String color;
    /**
     * 颜色
     */
    private String colorSet;
    /**
     * 素材包名称
     */
    @Size(max = 30, message = "素材包名称长度不能超过30个字符")
    private String pkgName;
    /**
     * 核心关键词
     */
    @Size(max = 60, message = "核心关键词长度不能超过60个字符")
    private String keywords;
    /**
     * 素材包封面
     */
    private String pkgCover;
    /**
     * 素材包描述
     */
    @Size(max = 200, message = "素材包描述长度不能超过200个字符")
    private String pkgDesc;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 素材包类型：1-图片，2-视频
     */
    private String pkgType;
    /**
     * 素材包状态：0-删除，1-正常
     */
    private String isValid;
    /**
     * 上下架
     */
    private String status;
}
