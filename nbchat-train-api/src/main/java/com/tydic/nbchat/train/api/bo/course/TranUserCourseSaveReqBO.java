package com.tydic.nbchat.train.api.bo.course;

import com.alibaba.fastjson.JSONObject;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Data
public class TranUserCourseSaveReqBO implements Serializable {
    private String tenantCode;
    @ParamNotEmpty
    private String userId;
    @ParamNotEmpty
    private String courseId;
    //0 学习中 1 已完成
    private String trainState;
    private Float classHourLearned;
    private Float classHourTotal;
    //0 未评测 1 已评测
    private String testState;
    private Set<String> trainSections;
    private String star;
}
