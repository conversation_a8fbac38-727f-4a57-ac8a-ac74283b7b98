package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TdhVirtualHumanQueryRspBO implements Serializable {

    private String isExpire = "0"; //1过期 0未过期
    private String orderStatus;
    private String avatar;
    /**
     * 定制状态 0: 草稿（上传中） 1: 订单创建（待支付） 2: 支付完成（审核中） 3: 审核完成（定制中） 4: 定制完成  5: 取消
     */
    private String customizeStatus;
    private String orderNo;

    private String tdhId;
    private String gender;


    private String tenantCode;

    private String userId;

    private String tdhImg;
    private String tdhImgThu;
    private String tdhType;

    private String tdhName;

    private String tdhTags;

    private String poseType;

    private String tdhSource;

    private Date createTime;

    private Integer orderIndex;

    private String isValid;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;
    /**
     * 绑定音色
     */
    private String targetVoice;

    //前端分类展示字段
    private String tdhGroup;
}
