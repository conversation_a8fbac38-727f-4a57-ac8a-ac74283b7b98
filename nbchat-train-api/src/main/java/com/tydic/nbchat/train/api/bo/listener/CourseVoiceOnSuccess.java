package com.tydic.nbchat.train.api.bo.listener;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CourseVoiceOnSuccess implements Serializable {
    private String sectionId;
    private String courseId;
    //阿里云地址-需要下载存储到minio
    private String address;
    private String taskId;
    //流式返回的音频数据
    private byte[] file;
}
