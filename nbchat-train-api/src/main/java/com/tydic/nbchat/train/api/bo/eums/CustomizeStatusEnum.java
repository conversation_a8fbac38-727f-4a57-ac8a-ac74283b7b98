package com.tydic.nbchat.train.api.bo.eums;

import lombok.Getter;

@Getter
public enum CustomizeStatusEnum {

    //定制状态 0: 草稿（上传中） 1: 订单创建（待支付） 2: 支付完成（审核中） 3: 审核完成（定制中） 4: 定制完成  5: 取消
    DRAFT("0", "草稿"),
    ORDER_CREATE("1", "订单创建"),
    PAY_COMPLETED("2", "支付完成"),
    REVIEW_COMPLETED("3", "审核完成"),
    CUSTOMIZE_COMPLETED("4", "定制完成"),
    CANCEL("5", "取消");

    private String code;
    private String desc;

    CustomizeStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static Boolean isExistCode(String code){
        for (CustomizeStatusEnum field : CustomizeStatusEnum.values()){
            if(field.code.equals(code)){
                return true;
            }
        }
        return false;
    }

}
