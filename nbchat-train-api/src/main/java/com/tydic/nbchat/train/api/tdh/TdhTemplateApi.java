package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhTemplateQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhTemplateQueryRspBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 数字人：模板管理接口
 */
public interface TdhTemplateApi {

    /**
     * 查询模板列表
     * @param reqBO
     * @return
     */
    RspList<TdhTemplateQueryRspBO> getTemplateList(TdhTemplateQueryReqBO reqBO);

    /**
     * 查询模板列表-运营管理
     * @param reqBO
     * @return
     */
    RspList<TdhTemplateQueryRspBO> getTemplateAdminList(TdhTemplateQueryReqBO reqBO);

    /**
     * 保存
     * @param reqBO
     * @return
     */
    Rsp<TdhTemplateQueryRspBO> save(TdhTemplateQueryReqBO reqBO);

    /**
     * 获取模板
     * @param request
     * @return
     */
    Rsp getTemplate(TdhTemplateQueryReqBO request);

    /**
     * 获取热门模板列表
     * @param request
     * @return
     */
    RspList getHotTemplateList(TdhTemplateQueryReqBO request);

    /**
     * 获取收藏模板列表
     * @param request
     * @return
     */
    RspList getStarTemplateList(TdhTemplateQueryReqBO request);
}
