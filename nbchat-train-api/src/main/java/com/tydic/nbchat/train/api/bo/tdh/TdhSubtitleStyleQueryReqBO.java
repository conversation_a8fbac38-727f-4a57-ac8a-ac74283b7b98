package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 字幕样式查询请求BO
 */
@Data
public class TdhSubtitleStyleQueryReqBO extends BasePageInfo implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 样式名称
     */
    private String styleName;

    /**
     * 样式值
     */
    private String styleValue;

    /**
     * 描述
     */
    private String styleDesc;

    /**
     * 开始时间
     */
    private Date createTime;

    /**
     * 缩略图
     */
    private String thumbUrl;

    /**
     * 排序
     */
    private Integer orderIndex;

    /**
     * 是否有效
     */
    private String isValid;

    /**
     * 行级样式
     */
    private String styleLine;
}
