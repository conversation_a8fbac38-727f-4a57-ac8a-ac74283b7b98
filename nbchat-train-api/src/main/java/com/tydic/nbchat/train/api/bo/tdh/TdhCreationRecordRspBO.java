package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数字人-用户创作记录(TdhCreationRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-08-22 15:47:53
 */
@Data
public class TdhCreationRecordRspBO implements Serializable {
    private static final long serialVersionUID = 453296879570050790L;
    private String creationId;

    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名称
     */
    private String name;
    /**
     * 创作名称
     */
    private String creationName;
    /**
     * 创作配置
     */
    private String creationConfig;
    /**
     * 创作内容:参考具体格式
     */
    private String creationContent;
    /**
     * 片段数
     */
    private Integer partCount;
    /**
     * 创建时间
     */
    private Date createTime;
    private Date updateTime;

    /**
     * 排序字段
     */
    private Integer orderIndex;
    /**
     * 创作状态
     */
    private String creationState;
    /**
     * 0 已删除 1 正常
     */
    private String isValid;
    /**
     * 0个人 1已共享
     */
    private String isShare;
    /**
     * 预览图
     */
    private String previewUrl;
    /**
     * 1-普通创作/2-ppt创作
     */
    private String creationSource;
    /**
     * 原始文件地址
     */
    private String fileUrl;

}

