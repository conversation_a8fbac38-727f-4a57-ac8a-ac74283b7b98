package com.tydic.nbchat.train.api.bo.course;

import com.tydic.nicc.common.bo.file.FileManageSaveBO;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/07/20
 * @email <EMAIL>
 * @description 培训课程分数查询响应BO
 */
@Data
public class TrainCourseScoreQueryRspBO implements Serializable {
    /**
     * 课程名称
     */
    private String courseName;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 名字
     */
    private String name;
    /**
     * 电话
     */
    private String phone;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后一次
     */
    private Date lastTime;
    /**
     * 学习结束
     */
    private String learnEnd;
    /**
     * 学习进度
     */
    private String learnSchedule;
    /**
     * 考试状态
     */
    private String testState;
    /**
     * 分数
     */
    private String score;
    /**
     * 测试次数
     */
    private String testCount;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 文件管理保存BO
     */
    private List<FileManageSaveBO> fileManageSaveBO;
}
