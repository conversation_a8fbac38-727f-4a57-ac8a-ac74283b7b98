package com.tydic.nbchat.train.api.bo.generate;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TranCourseGenerateRequest implements Serializable {
    @ParamNotEmpty
    private String userId;
    private String tenantCode;
    @ParamNotEmpty
    private String courseId;
    @ParamNotEmpty
    private String presetId;
    private String catalog;
    //生成条数
    private String count = "5";

    private String dialogueDesc;

    private String userRole;
    private String robotRole;
}
