package com.tydic.nbchat.train.api.bo.asr_tts;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;


/**
 {
 "status":200,
 "error_code":20000000,
 "error_message":"SUCCESS",
 "request_id":"c541eae489af48d69dae2d2e203a****",
 "data":{
 "sentences":[
 {
 "text":"长文本语音合成接口",
 "begin_time":"0",
 "end_time":"2239"
 },
 {
 "text":"一次返回所有文本对应的音频.现在需要增加句级别的时间戳信息",
 "begin_time":"2239",
 "end_time":"8499"
 },
 {
 "text":"客户可利用该信息，实现播放控制功能",
 "begin_time":"8499",
 "end_time":"12058"
 }
 ],
 "task_id":"f4e9bf53cb1611eab327b15f61b4****",
 "audio_address":"此处为生成的URL地址",
 "notify_custom":""
 }
 }
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TtsVoiceTaskContext implements Serializable {
    private String courseId;
    private String sectionId;
    @JsonAlias("audio_url")
    private String audio_address;
    private Integer task_status;
    private String task_id;
    //持续时长 ms
    private int duration;
    private Long notify_custom;
    private List<TtsVoiceResult> sentences;
    //优化字幕用
    private List<TtsVoiceResult> subtitles;
    //字符
    private List<TtsWordResult> words;

    public int getDuration() {
        if (sentences != null && !sentences.isEmpty()) {
            duration = sentences.get(sentences.size() - 1).getEnd_time();
        }
        return duration;
    }

    public boolean isSuccess(){
        return StringUtils.isNotBlank(audio_address);
    }
}
