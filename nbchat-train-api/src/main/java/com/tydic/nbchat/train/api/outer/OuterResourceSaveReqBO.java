package com.tydic.nbchat.train.api.outer;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OuterResourceSaveReqBO extends BaseInfo implements Serializable {
    private String requestId;
    @ParamNotEmpty
    private String tenantCode;
    private String userId;
    private String bizId;
    private String bizCode;
    private String sourceUrl;
    private String status;
    private Date createTime;
    private Date updateTime;
    private String resultUrl;
    private String sourceContent;
    private String resultContent;
}
