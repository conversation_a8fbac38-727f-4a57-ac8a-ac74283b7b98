package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhAudioBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface TdhAudioApi {

    /**
     * 保存背景音乐
     * @param request
     * @return
     */
    Rsp save(TdhAudioBO request);

    /**
     * 查询背景音乐
     * @param request
     * @return
     */
    RspList query(TdhAudioBO request);


    /**
     * 删除背景音乐
     * @param request
     * @return
     */
    Rsp delete(TdhAudioBO request);

}
