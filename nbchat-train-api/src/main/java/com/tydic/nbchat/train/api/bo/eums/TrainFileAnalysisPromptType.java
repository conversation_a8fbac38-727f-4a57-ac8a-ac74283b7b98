package com.tydic.nbchat.train.api.bo.eums;

public enum TrainFileAnalysisPromptType {

    TRAIN_PART_SUB("train_part_sub", "段落摘要"),
    TRAIN_DOC_SUB("train_doc_sub", "课程摘要"),
    TRAIN_COURSE_CONTENT("train_course_content", "课程内容"),
    TRAIN_COURSE_CATALOG("train_course_catalog", "课程目录"),
    TRAIN_COURSE_INTRO("train_course_intro", "课程介绍");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private TrainFileAnalysisPromptType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (TrainFileAnalysisPromptType field : TrainFileAnalysisPromptType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }


}
