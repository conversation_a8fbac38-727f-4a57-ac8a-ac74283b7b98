package com.tydic.nbchat.train.api.bo.asr_tts;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 "sentences":[
 {
 "text":"长文本语音合成接口",
 "begin_time":"0",
 "end_time":"2239"
 },
 {
 "text":"一次返回所有文本对应的音频.现在需要增加句级别的时间戳信息",
 "begin_time":"2239",
 "end_time":"8499"
 }
 ],
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TtsVoiceResult implements Serializable {
    @JsonAlias({"start","begin_time"})
    private Integer begin_Time;
    @JsonAlias({"end","end_time"})
    private Integer end_time;
    private String text;
    private String origin_text;
    //双语，另外一个语言的文本
    private String bilingualText;
    //text内容的主语言是否中文
    private Boolean isCh;

    public TtsVoiceResult(Integer begin, Integer end, String text, String originText) {
        this.begin_Time = begin;
        this.end_time = end;
        this.text = text;
        this.origin_text = originText;
    }

    public TtsVoiceResult(Integer begin, Integer end, String text, String originText, String bilingualText) {
        this.begin_Time = begin;
        this.end_time = end;
        this.text = text;
        this.origin_text = originText;
        this.bilingualText = bilingualText;
    }
}
