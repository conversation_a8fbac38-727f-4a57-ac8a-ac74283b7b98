package com.tydic.nbchat.train.api.bo.ppt;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (PptTheme)实体类
 *
 * <AUTHOR>
 * @since 2024-01-02 17:27:05
 */
@Data
public class PPTThemeBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = -57989793888105620L;

    private String keyword;

    /**
     * 主题id
     */
    private String themeId;
    /**
     * 排序
     */
    private int orderIndex;
    /**
     * 主题名称
     */
    private String themeName;
    /**
     * 描述
     */
    private String themeDesc;
    /**
     * 主题分类：
     * 0000：通用主题;9221: 环保, 动物;9220: 酒店, 餐饮;9219: 水彩, 乐器, 介绍;
     */
    private String themeType;
    /**
     * 0 系统内置  1 自定义
     */
    private String themeSource;
    /**
     * ppt配置-主题配置
     */
    private String config;
    /**
     * 组合类型：0主题组合 1布局组合
     */
    private String composeType;
    /**
     * 场景
     */
    private String scene;
    /**
     * 风格
     */
    private String style;
    /**
     * 配色
     */
    private String color;
    /**
     * 分类
     */
    private String category;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 预览图
     */
    private String previewUrl;

    private String tenantCode;

    private String userId;

    private Date createTime;

    private Date updateTime;
    /**
     * 是否有效
     */
    private String isValid;

    /**
     * 上架状态 0下架 1上架
     */
    private String themeState;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;

    private String targetTenant;

    private String isStar;
    /**
     * 标星
     */
    private String star;
    /**
     * 热门
     */
    private String hot;
    /**
     * 标签
     */
    private String tag;

}

