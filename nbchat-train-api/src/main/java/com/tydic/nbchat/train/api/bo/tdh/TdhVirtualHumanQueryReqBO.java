package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;

@Data
public class TdhVirtualHumanQueryReqBO extends BasePageInfo implements Serializable {
    /**
     * 虚拟形象id
     */
    private String tdhId;

    private String userId;
    /**
     * 1 全身 2 半身 3 大半身 4 坐姿
     */
    private String poseType;
    /**
     * 0 系统内置  1 自定义
     */
    private String tdhSource;
    /**
     * 男/女
     */
    private String gender;
    /**
     * 标签
     */
    private String tdhTags;
    /**
     * 租户
     */
    private String tenantCode;
    /**
     * 形象
     */
    private String tdhType;
    /**
     * 会员
     */
    private String vipFlag;
    /**
     * 数字人名称
     */
    private String tdhName;
    /**
     * 指定租户
     */
    private String specifyTenantCode;
    /**
     * 上下架
     */
    private String isValid;
}
