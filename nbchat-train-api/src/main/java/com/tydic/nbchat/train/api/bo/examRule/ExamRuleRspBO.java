package com.tydic.nbchat.train.api.bo.examRule;

import com.tydic.nbchat.train.api.bo.exam.ExamQuestion;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/08/01
 * @email <EMAIL>
 * @description 试题生成规则响应BO
 */
@Data
public class ExamRuleRspBO implements Serializable {

    private String courseType; //课程类型 1-普通课程 2-训战课程 3-考试课程

    /**
     * 试题ID
     */
    private int paperId;
    /**
     * 课程ID
     */
    private String courseId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 试卷配置状态
     */
    private String examState;

    /**
     * 课程状态
     */
    private String courseState;

    /**
     * 合格分数
     */
    private String passingScore;

    /**
     * 分类名称
     */
    private String catalogName;

    /**
     * 试题数量
     */
    private Integer testNum;
    /**
     * 单选题
     */
    private Integer single;

    /**
     * 多选题
     */
    private Integer multiple;

    /**
     * 判断题
     */
    private Integer trueOrFalse;

    /**
     * 出题类型: 1 随机 2 顺序
     */
    private String testType;

    /**
     * 是否需要学完课程
     * 0 否 1 是
     */
    private String trainState;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建用户
     */
    private String createUser;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 试题
     */
    private List<ExamQuestion> questions;

}
