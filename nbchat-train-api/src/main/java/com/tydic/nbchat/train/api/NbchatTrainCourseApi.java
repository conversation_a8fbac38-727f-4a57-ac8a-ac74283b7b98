package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.course.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 课程管理接口
 */
public interface NbchatTrainCourseApi {

    /**
     * 删除
     * @param queryReqBO
     * @return
     */
    Rsp deleteCourse(TranCourseQueryReqBO queryReqBO);
    /**
     * 查询详情
     * @param queryReqBO
     * @return
     */
    Rsp<TranCourseBO> getCourse(TranCourseQueryReqBO queryReqBO);

    /**
     * 查询课程列表
     * @param queryReqBO
     * @return
     */
    RspList<TranCourseBO> getCourses(TranCourseQueryReqBO queryReqBO) ;

    /**
     * 创建/修改课程
     * @param saveReqBO
     * @return
     */
    Rsp saveCourse(TranCourseSaveReqBO saveReqBO);

    /**
     * 新增考试、对战类型课程
     * @param saveReqBO
     * @return
     */
    Rsp newCourse(TranCourseSaveReqBO saveReqBO);

    /**
     * 查询热点课程
     * @param saveReqBO
     * @return
     */
    RspList getTopHubCourse(TrainCourseHotHubBO saveReqBO);

    /**
     * 查询课程内容
     * @param reqBO
     * @return
     */
    Rsp getCourseContent(NbchatTrainCourseTextBO reqBO);

    /**
     * 保存课程内容
     * @param reqBO
     * @return
     */
    Rsp saveCourseContent(NbchatTrainCourseTextBO reqBO);

    /**
     * 保存课程章节内容
     * @param @param request
     * @return @return {@link Rsp }
     */
    Rsp saveCourseSections(TrainCourseSectionsQueryReqBO request);

    /**
     * 查询课程章节内容
     * @param @param request
     * @return @return {@link Rsp }
     */
    Rsp queryCourseSections(TrainCourseSectionsQueryReqBO request);
}
