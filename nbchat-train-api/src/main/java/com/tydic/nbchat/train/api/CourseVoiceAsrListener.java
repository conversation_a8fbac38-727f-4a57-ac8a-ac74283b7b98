package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.listener.CourseVoiceAsrOnSuccess;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnError;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnSuccess;

/**
 * 培训音频生成监听
 */
public interface CourseVoiceAsrListener {

    /**
     * 成功
     * @param onSuccess
     */
    void onSuccess(CourseVoiceAsrOnSuccess onSuccess);

    /**
     * 异常
     * @param onError
     */
    void onError(CourseVoiceOnError onError);

}
