package com.tydic.nbchat.train.api.bo.tdh;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务完成通知(TdhNoticeTask)实体类
 *
 * <AUTHOR>
 * @since 2023-12-11 14:21:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TdhNoticeTaskBO implements Serializable {
    private static final long serialVersionUID = -16352502544761067L;
/**
     * 主键自增
     */
    private Long id;
/**
     * 用户id
     */
    private String userId;
/**
     * 任务id
     */
    private String taskId;
/**
     * 租户
     */
    private String tenantCode;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 更新时间
     */
    private Date updateTime;
/**
     * 事件类型：1 视频制作 2 课程制作 ..其他未定义
     */
    private String eventType;
/**
     * 通知结果
     */
    private String noteResult;
/**
     * 通知状态 0 待通知 1 已通知
     */
    private String noteState;
/**
     * 手机号 / 邮箱
     */
    private String noteType;

    //通知模版code
    private String smsTempCode;

}

