package com.tydic.nbchat.train.api.bo.asr_tts.modelscope;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tydic.nbchat.train.api.bo.asr_tts.AsrVoiceResult;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@JsonIgnoreProperties
@Data
public class AsrResponseData implements Serializable {
    @JsonAlias("asrResult")
    private String text;
    private List<AsrVoiceResult> sentences;
}
