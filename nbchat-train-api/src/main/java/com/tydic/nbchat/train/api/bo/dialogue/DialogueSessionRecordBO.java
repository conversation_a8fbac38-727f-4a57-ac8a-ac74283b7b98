package com.tydic.nbchat.train.api.bo.dialogue;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class DialogueSessionRecordBO implements Serializable {
    private String question;
    private String answer;
    private Float score;
    private String suggestion;
    private Date createTime;
    private String keyPoints;
    private String analysis;
    private String standardAnswer;

    private String customAnalysis; //自定义解析
    private String questionNo; //问题序号
}
