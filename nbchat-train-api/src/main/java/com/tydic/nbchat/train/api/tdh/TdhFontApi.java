package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhFontQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhFontQueryRspBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhSubtitleStyleQueryReqBO;
import com.tydic.nicc.dc.base.bo.RspList;

public interface TdhFontApi {
    /**
     * 查询字体列表
     * @param queryReqBO
     * @return
     */
    RspList<TdhFontQueryRspBO> getFontList(TdhFontQueryReqBO queryReqBO);

    /**
     * 查询字幕样式列表
     * @return
     */
    RspList getSubtitleStyleList(TdhSubtitleStyleQueryReqBO queryReqBO);
}
