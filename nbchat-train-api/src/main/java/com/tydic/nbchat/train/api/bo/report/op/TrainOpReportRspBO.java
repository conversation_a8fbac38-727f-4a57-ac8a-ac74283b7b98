package com.tydic.nbchat.train.api.bo.report.op;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class TrainOpReportRspBO implements Serializable {
    /**
     * 专业租户总数
     */
    private int tenantTotal;
    /**
     * 专业租户用户总量
     */
    private int tenantUserTotal;
    /**
     * 公共租户下公司总数
     */
    private int enterpriseTotal;
    /**
     * 公共租户下用户总量
     */
    private int enterpriseUserTotal;
    /**
     * 累计视频制作
     */
    private int videoTotal;
    /**
     * 累计视频制作成功数量
     */
    private int videoSuccessTotal;
    /**
     * 累计ppt制作
     */
    private int pptTotal;
    /**
     * 累计出题次数
     */
    private int questionTotal;
    /**
     * 最近访问时间
     */
    private Date lastLoginTime;
    /**
     * 专业租户列表
     */
    private List<TrainOpTenantRspBO> opTenantRspBOList;
    /**
     * 专业租户数量
     */
    private int tenantNum;
    /**
     * 专业租户用户数量
     */
    private int tenantUserNum;
    /**
     * 公共租户下公司数量
     */
    private int enterpriseNum;
    /**
     * 公共租户下用户数量
     */
    private int enterpriseUserNum;
    /**
     * 视频制作数量
     */
    private int videoNum;
    /**
     * 视频制作成功数量
     */
    private int videoSuccessNum;
    /**
     * ppt制作数量
     */
    private int pptNum;
    /**
     * 出题次数
     */
    private int questionNum;

}
