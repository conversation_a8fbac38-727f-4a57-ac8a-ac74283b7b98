package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialLibraryQueryBo;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialLibraryRspBo;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialPackageQueryBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhMaterialPackageRspBo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * PPT素材库接口
 */
public interface TdhMaterialPackageApi {
    /**
     * 根据条件查询素材包列表
     */
    RspList<TdhMaterialPackageRspBo> getMaterialPackageList(TdhMaterialPackageQueryBO request);
    /**
     * 更新、新增素材包
     */
    Rsp<TdhMaterialPackageRspBo> saveMaterialPackage(TdhMaterialPackageQueryBO request);
    /**
     * 根据素材包id查询素材详情
     */
    RspList<TdhMaterialLibraryRspBo> getMaterialLibraryId(TdhMaterialLibraryQueryBo queryBo);
    /**
     * 根据素材包pkgId批量新增图片素材
     */
    Rsp<TdhMaterialLibraryRspBo> saveMaterialLibrary(TdhMaterialLibraryQueryBo queryBo);
    /**
     * 删除素材包并且批量删除图片素材
     */
    Rsp deleteMaterialPackage(String pkgId, String isValid);
    /**
     * 用户获取素材包列表和素材详情
     */
    RspList<TdhMaterialLibraryRspBo> getMaterialPackageAndLibrary(TdhMaterialLibraryQueryBo queryBo);
}
