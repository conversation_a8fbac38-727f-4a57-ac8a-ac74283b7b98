package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.examRule.ExamRuleBO;
import com.tydic.nbchat.train.api.bo.examRule.ExamRuleReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

import java.io.InputStream;

public interface NbchatExamRuleTestApi {

    /**
     * 保存考试配置
     * @param request
     * @return
     */
    Rsp save(ExamRuleBO request);

    /**
     * 查询考试配置列表
     * @param request
     * @return
     */
    Rsp query(ExamRuleReqBO request);

    /**
     * 题库配置查询(分页)
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    RspList getCourseExamList(ExamRuleReqBO request);

    /**
     * 上架
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    Rsp putOnShelves(ExamRuleReqBO request);

    /**
     * 试题导出
     * @param request
     * @return
     */
    Rsp export(ExamRuleReqBO request);

    Rsp importFile(InputStream inputStream,String courseId,String tenantCode);

    /**
     * 直接导出问题
     * @param request
     * @return
     */
    Rsp exportQuestion(ExamRuleReqBO request);
}
