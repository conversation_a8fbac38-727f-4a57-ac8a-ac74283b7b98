package com.tydic.nbchat.train.api.bo.course;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @datetime：2024/9/2 15:33
 * @description:
 */
@Data
public class TrainVideosRspBO implements Serializable {
    /***
     * 课程id
     */
    private String courseId;
    /**
     * 课程名称
     */
    private String courseName;
    /**
     * 标签
     */
    private String CatalogTitle;
    /**
     * 分类
     */
    private String category;
    /**
     * 二级目录
     */
    private String category2;
    /**
     * 缩略图
     */
    private String imgAvatar;
    /**
     * 音频源
     */
    private String videoUrl;

    private String videoImg;

    /**
     * 播放源
     */
    private String playUrl;
    /**
     * 章节序号
     */
    private String sectionsIndex;

    private String sectionId;
}
