package com.tydic.nbchat.train.api.bo.report.exam;

import lombok.Data;

import java.io.Serializable;

@Data
public class TrainRpExamStudentsQueryRspBO implements Serializable {

    /**
     * 课程ID
     */
    private String courseId;

    /**
     * 课程名称
     */
    private String courseName;
    /**
     * 人数
     */
    private String students;
    /**
     * 人均分数
     */
    private String avgScore="0.0";
    /**
     * 不及格
     */
    private String failed="0.0";
    /**
     * 及格
     */
    private String pass="0.0";
    /**
     * 良好
     */
    private String good="0.0";
    /**
     * 优秀
     */
    private String excellent="0.0";
    /**
     * 不及格人数
     */
    private String failedStudents="0";
    /**
     * 及格人数
     */
    private String passStudents="0";
    /**
     * 良好人数
     */
    private String goodStudents="0";
    /**
     * 优秀人数
     */
    private String excellentStudents="0";
}
