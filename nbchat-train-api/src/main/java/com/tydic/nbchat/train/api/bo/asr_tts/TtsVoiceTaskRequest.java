package com.tydic.nbchat.train.api.bo.asr_tts;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TtsVoiceTaskRequest implements Serializable {
    private String anchorType;
    private String userId;
    private String tenantCode;
    // 更改为语音存放目录： tdh/voice，默认为 tdh/tmp_tts
    private String sectionId;
    @ParamNotEmpty
    private String text;
    @ParamNotEmpty
    private String courseId;
    private String voice;
    //语调，范围是-500~500，可选，默认是0
    private Integer pitchRate;
    //语速，范围是-500~500，可选，默认是0
    private Integer speechRate;
    //音量，范围是0~100，可选，默认50
    private Integer volume;
    private boolean async = false;
    //是否需要字符级别的时间戳信息
    private boolean wordSplit = false;
    //语言类型
    private String language;
    //开启双语模式
    private boolean bilingual = false;
    /**
     * 获取缓存hash值
     * @return
     */
    public String getCacheHashCode() {
        return anchorType + text + voice + pitchRate + speechRate + volume + wordSplit + language + bilingual;
    }
}
