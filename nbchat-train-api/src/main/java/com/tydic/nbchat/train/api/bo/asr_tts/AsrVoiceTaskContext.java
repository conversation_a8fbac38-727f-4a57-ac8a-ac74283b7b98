package com.tydic.nbchat.train.api.bo.asr_tts;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;


/**
 * {"TaskId":"53b432abd5944acfa7f3fe1a12dfc459","RequestId":"37299948-4CB6-5B20-BEFF-60E24C7CCAB7",
 * "StatusText":"SUCCESS","BizDuration":3101,"SolveTime":1688023363204,"RequestTime":1688023360767,"StatusCode":21050000,
 * "Result":{"Sentences":[{"EndTime":2510,"SilenceDuration":0,"BeginTime":880,"Text":"北京的天气。",
 * "ChannelId":0,"SpeechRate":184,"EmotionValue":6.7}]}}
 */
@Data
@JsonIgnoreProperties
public class AsrVoiceTaskContext implements Serializable {
    private String courseId;
    private String TaskId;
    private String RequestId;
    private Integer BizDuration;
    private Long SolveTime;
    private Long RequestTime;
    private Integer StatusCode;
    private List<AsrVoiceResult> Sentences;

    public boolean isSuccess(){
        return StringUtils.isNotBlank(TaskId);
    }
}
