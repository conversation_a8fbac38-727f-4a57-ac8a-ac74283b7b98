package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.course.TranStudentArchiveQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * <AUTHOR>
 * @datetime：2024/8/26
 * @description:
 */
public interface NbchatTrainStudentsArchiveReportApi {

    Rsp getStudentArchive(TranStudentArchiveQueryReqBO reqBO);

    RspList getStudentArchiveList(TranStudentArchiveQueryReqBO reqBO);
}
