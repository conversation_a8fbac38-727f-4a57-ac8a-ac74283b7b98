package com.tydic.nbchat.train.api.bo.course;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (NbchatTrainCourseText)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 16:43:08
 */
@Data
public class NbchatTrainCourseTextBO implements Serializable {
    private static final long serialVersionUID = 851919659532049890L;
    /**
     * 课程id
     */
    private String courseId;
    /**
     * 内容-长文本
     */
    private String text;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 文件名
     */
    private String fileName;
    /**
     * 文件链接
     */
    private String fileUrl;

}

