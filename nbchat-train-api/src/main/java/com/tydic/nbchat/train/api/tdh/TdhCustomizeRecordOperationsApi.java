package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordPriceChangeReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordQueryReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhCustomizeRecordModifyStatusReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface TdhCustomizeRecordOperationsApi {
    /**
     * 定制数据列表查询 sysAdmin
     * @param reqBO
     * @return
     */
    RspList list(TdhCustomizeRecordQueryReqBO reqBO);

    /**
     * 明细查询接口 sysAdmin
     * @param reqBO
     * @return
     */
    Rsp info(TdhCustomizeRecordQueryReqBO reqBO);

    /**
     * 订单改价 sysAdmin
     * @param request
     * @return
     */
    Rsp priceChange(TdhCustomizeRecordPriceChangeReqBO request);

    /**
     * @param request
     * @return
     */
    Rsp modifyStatus(TdhCustomizeRecordModifyStatusReqBO request);
}
