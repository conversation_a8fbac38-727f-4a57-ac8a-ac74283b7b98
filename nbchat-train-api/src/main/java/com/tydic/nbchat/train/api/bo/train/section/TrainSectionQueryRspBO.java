package com.tydic.nbchat.train.api.bo.train.section;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 章节内容查询
 */
@Data
public class TrainSectionQueryRspBO implements Serializable {
    private String catalogTitle;
    private String pdfUrl;
    private String videoUrl;
    private String videoImg;
    private Integer pdfNum;
    private Integer trainCount; //学习人数
    private List<SectionInfo> SectionInfos;
}
