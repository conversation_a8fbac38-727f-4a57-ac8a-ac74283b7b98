package com.tydic.nbchat.train.api.bo.exam;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ExamQuestion implements Serializable {
    //试题id
    private String questionId;
    //试题
    private String questionName;
    //答案解析
    private String explan;
    //难度
    private String difficulty;
    //试题类型
    private String questionType;
    //选项
    private List<ExamQuestionItem> items;
    //答案
    private List<String> answers;
    //用户答案(错误答案)
    private List<ExamQuestionItem> userItems;
    //知识要点
    private String knowledges;
}
