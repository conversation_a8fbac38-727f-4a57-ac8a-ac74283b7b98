package com.tydic.nbchat.train.api.tdh;

import com.tydic.nbchat.train.api.bo.tdh.TdhCreationRecordReqBO;
import com.tydic.nbchat.train.api.bo.tdh.TdhCreationRecordRspBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 创作记录接口(我的草稿)
 */
public interface TdhCreationRecordApi {

    /**
     * 查询列表
     * @param request
     * @return
     */
    RspList<TdhCreationRecordRspBO> queryList(TdhCreationRecordReqBO request);

    /**
     * 查询单条
     * @param request
     * @return
     */
    Rsp<TdhCreationRecordRspBO> queryRecord(TdhCreationRecordReqBO request);

    Rsp save(TdhCreationRecordReqBO request);
}
