package com.tydic.nbchat.train.api.bo.asr_tts;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.io.Serializable;

/**
 * EndTime: 2510,
 * SilenceDuration: 0,
 * BeginTime: 880,
 * Text: 北京的天气。,
 * ChannelId: 0,
 * SpeechRate: 184,
 * EmotionValue: 6.7
 **/
@Data
public class AsrVoiceResult implements Serializable {
    @JsonAlias("end")
    private Integer EndTime;
    private Integer SilenceDuration;
    @JsonAlias("start")
    private Integer BeginTime;
    @JsonAlias("text")
    private String Text;
    private Integer ChannelId;
    private Integer SpeechRate;
    private Double EmotionValue;
}
