package com.tydic.nbchat.train.api.trainTask;

import com.tydic.nbchat.train.api.bo.train.scene.NbchatTrainTaskDegreeBO;
import com.tydic.nicc.dc.base.bo.RspList;

public interface QueryDegreeApi {

    /**
     * 查询证书发放列表
     * @param request
     * @return
     */
    RspList list(NbchatTrainTaskDegreeBO request);

    /**
     * 统计证书发放详情
     * @param request
     * @return
     */
    RspList analysis(NbchatTrainTaskDegreeBO request);

    /**
     * 新的统计证书发放详情
     * @param request
     * @return
     */
    RspList newAnalysis(NbchatTrainTaskDegreeBO request);

}
