package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TdhCustomizeRecordPriceChangeReqBO  extends BaseInfo implements Serializable {
    /**
     * 订单id
     */
    @ParamNotEmpty(message = "订单号不能为空")
    private String orderNo;

    /**
     * 订单价格
     */
    @ParamNotEmpty(message = "订单价格不能为空")
    private Integer orderPrice;
}
