package com.tydic.nbchat.train.api.bo.eums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public enum RpCourseItemType {
    new_registrations("new_registrations","新注册用户数量"),
    question_creators("question_creators","出题次数"),
    ppt_creators ("ppt_creators","ppt创作数量"),
    video_creators ("video_creators","制作视频数量"),
    video_success("video_success","视频制作成功数量"),

    video_success_probability("video_success_probability","视频制作成功率"),
    video_fail_probability("video_fail_probability","视频制作失败率"),
    dialogue_pt_count("dialogue_pt_count","实践人次"),
    dialogue_pn_count("dialogue_pn_count","实践人数"),
    dialogue_avg_score_count("dialogue_avg_score_count","场景实践人均分"),
    dialogue_level_count("dialogue_level_count","场景实践级别统计"),
    dialogue_rank_count("dialogue_rank_count","场景实践排行统计"),
    exam_pt_count("exam_pt_count","测评人次"),
    exam_pn_count("exam_pn_count","测评人数"),
    exam_avg_score_count("exam_avg_score_count","测评人均分"),
    exam_comprehensive_score_count("exam_comprehensive_score_count","测评综合得分"),
    user_new_count("user_new_count","新增用户数"),
    user_total_count("user_total_count","累计用户数"),

    course_pn_count("course_pn_count", "学习人数"),
    course_pn_end_count("course_pn_end_count", "学习人数(已完成)"),
    course_pt_count("course_pt_count", "学习人次");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private RpCourseItemType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (RpCourseItemType field : RpCourseItemType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }

    public static List<RpCourseItemType> courseItems(){
        return new ArrayList<>(Arrays.asList(course_pn_count,course_pt_count,course_pn_end_count));
    }
    public static List<RpCourseItemType> dialogueItems(){
        return new ArrayList<>(Arrays.asList(dialogue_pt_count,dialogue_pn_count));
    }

    public static List<RpCourseItemType> examItems(){
        return new ArrayList<>(Arrays.asList(exam_pt_count,exam_pn_count,exam_avg_score_count));
    }

    public static List<RpCourseItemType> studentsItems(){
        return new ArrayList<>(Arrays.asList(user_new_count,user_total_count));
    }
}
