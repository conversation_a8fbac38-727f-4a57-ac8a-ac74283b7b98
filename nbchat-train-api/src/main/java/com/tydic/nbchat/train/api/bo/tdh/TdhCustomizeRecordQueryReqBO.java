package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TdhCustomizeRecordQueryReqBO extends BasePageInfo implements Serializable {

    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;
    private String isCutout; //1抠图 0不扣
    private String skuId;
    private String originImage;

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 租户id
     */
    private String tenantCode;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 订单id
     */
    @ParamNotEmpty(message = "订单号不能为空")
    private String orderNo;

    /**
     * 订单状态 0 待支付 1 已支付 2 已发货 3 已关闭 4支付异常
     */
    private String orderStatus;

    /**
     * 订单价格-原价
     */
    private Integer originPrice;

    /**
     * 订单价格
     */
    private Integer orderPrice;

    /**
     * 实际支付金额
     */
    private Integer payPrice;

    /**
     * 定制类型 2d/2.5d/audio/2d_gif
     */
    private String customizeType;

    /**
     * 定制状态 0: 草稿（上传中） 1: 订单创建（待支付） 2: 支付完成（审核中） 3: 审核完成（定制中） 4: 定制完成  5: 取消
     */
    private String customizeStatus;

    /**
     * 身份审核视频地址
     */
    private String identityUrl;

    /**
     * 音频定制地址
     */
    private String voiceUrl;

    /**
     * 发音人名称
     */
    private String voiceName;

    /**
     * 音频演示地址
     */
    private String voiceDemo;

    /**
     * 音频id: 火山音频id
     */
    private String volcId;

    /**
     * 2d数字人形象照片地址
     */
    private String tdhImg;

    /**
     * 数字人id: 2.5d的唯一模型名称
     */
    private String tdhId;

    /**
     * 数字人名称
     */
    private String tdhName;

    /**
     * 数字人演示地址
     */
    private String tdhDemo;

    /**
     * 性别: 男/女
     */
    private String gender;

    /**
     * 姿势: 1/2
     */
    private String poseType;

    /**
     * 定制视频训练的视频地址
     */
    private String videoUrl;

    /**
     * 沟通记录
     */
    private String communication;

    /**
     * 有效期起始时间
     */
    private Date startTime;

    /**
     * 有效期结束时间
     */
    private Date endTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 是否有效 0:删除 1:有效
     */
    private String isValid;
}
