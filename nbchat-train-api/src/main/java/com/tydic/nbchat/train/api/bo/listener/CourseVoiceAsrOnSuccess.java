package com.tydic.nbchat.train.api.bo.listener;

import com.tydic.nbchat.train.api.bo.asr_tts.AsrVoiceTaskContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CourseVoiceAsrOnSuccess implements Serializable {
    private String courseId;
    private String taskId;
    private String fileId;
    private AsrVoiceTaskContext taskContext;
}
