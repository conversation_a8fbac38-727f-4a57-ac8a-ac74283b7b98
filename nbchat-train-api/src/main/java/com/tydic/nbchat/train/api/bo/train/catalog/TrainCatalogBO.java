package com.tydic.nbchat.train.api.bo.train.catalog;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TrainCatalogBO implements Serializable {

    private String sectionId;
    private String sectionContent;
    private String sectionVideoUrl;

    private String isValid;
    private Integer studied = 0; //0未学习 1已学习
    private Integer trainCount; //学习人数
    private String userId;
    private String courseId;
    private String catalogId;
    private String fileId;
    private String tenantCode;
    private String catalogTitle;
    private Short catalogLevel;
    private Short catalogIndex;
    private String parentId;
    private String sectionIds;
    private List<TrainCatalogBO> children;
}
