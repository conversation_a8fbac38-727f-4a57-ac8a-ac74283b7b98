package com.tydic.nbchat.train.api.bo.ppt;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PPTThemeMatchReqBO implements Serializable {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 场景
     */
    private String scene;
    /**
     * 风格
     */
    private List<String> style;
    /**
     * 配色
     */
    private List<String> color;
    /**
     * 预设模板
     */
    private String presetId;
    /**
     * 匹配图片提示词ID
     */
    private String imagePromptId;

    private boolean trim;
    /**
     * 预设参数
     */
    private List<String> presetPrompts;
    /**
     * 模型类型
     */
    private String robotType;
}
