package com.tydic.nbchat.train.api.bo.train.scene;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 证书发放表(NbchatTrainTaskDegree)实体类
 *
 * <AUTHOR>
 * @since 2024-07-29 16:20:14
 */
@Data

public class NbchatTrainTaskDegreeBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = 616054352764547522L;

    private String degreeDesc;
    /**
     * 证书编号
     */
    private String id;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务状态: 0 编辑中 1 上架 2 下架
     */
    private String startStatus;

    /**
     * 是否发放考试证书
     */
    private String isDegree;
    /**
     * 学员id
     */
    private String userId;
    /**
     * 学员名称
     */
    private String userName;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 归属机构
     */
    private String deptId;
    private String deptName;
    private String topDeptId;

    /**
     *目标机构
     */
    private String targetId;
    private List<String> targetIds;
    private String targetName;
    /**
     * 是否支持子部门
     */
    private String supportSubDept;
    /**
     * 部门范围
     */
    private String deptScope;
    /**
     * 所属岗位
     */
    private String postId;
    private List<String> postIds;
    private String postName;

    /**
     * 证书名称
     */
    private String degreeName;
    private Integer degreeCount;//证书数量

    /**
     * 证书发放日期
     */
    private Date issueDate;
    /**
     * 证书有效期/月
     */
    private Integer validityPeriod;
    /**
     * 证书状态 0到期失效 1生效中 2已吊销
     */
    private String status;
    /**
     * 证书下载地址
     */
    private String degreeUrl;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private String isValid;


    private Integer targetUserCount;
    private Integer finishUserCount;
    private String finishRate;
}

