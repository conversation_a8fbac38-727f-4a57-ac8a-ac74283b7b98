package com.tydic.nbchat.train.api.bo.train.scene;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class NbchatTaskInfoBO implements Serializable {

    private Integer targetUserCount;
    private Integer finishUserCount;
    private String finishRate;


    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务机构
     */
    private String deptId;
    private String deptName;
    /**
     * 任务岗位
     */
    private String postId;
    private String postName;
    /**
     * 是否发放考试证书
     */
    private String isDegree;
    /**
     * 任务开始时间
     */
    private Date startTime;
    /**
     * 任务结束时间
     */
    private Date endTime;

}
