package com.tydic.nbchat.train.api.bo.eums;

public enum StateEnum {

    ;
    public enum SOURCE {
        SYSTEM("0","系统内置"),
        SELF("1","自定义"),
        OTHER_1("2","课程培训知识库"),
        PUBLIC("public","公共");

        private String code;
        private String name;
        SOURCE(String code,String name){
            this.code = code;
            this.name = name;
        }
        public String getCode() {
            return code;
        }
        public String getName() {
            return name;
        }

    }
    public enum BUTTON {
        GRAY("0","置灰"),
        INVALID("2","不显示"),
        VALID("1","亮");
        private String code;
        private String name;
        BUTTON(String code,String name){
            this.code = code;
            this.name = name;
        }
        public String getCode() {
            return code;
        }
        public String getName() {
            return name;
        }

    }
    public enum TASK {
        NOT_RUN("0","任务未执行"),
        RUNNING("1","任务执行中"),
        ERROR("2","任务异常"),
        FINISH("3","任务结束");
        private String code;
        private String name;
        TASK(String code,String name){
            this.code = code;
            this.name = name;
        }
        public String getCode() {
            return code;
        }
        public String getName() {
            return name;
        }
        public static String getNameByCode(String code) {
            for (TASK value : TASK.values()) {
                if (value.code.equals(code)) {
                    return value.name;
                }
            }
            return "";
        }
    }

    public enum COURSE {
        EDIT("0","编辑中"),
        PREVIEW("1","预览中"),
        ON("2","上架中"),
        OFF("3","已下架"),
        PRE_EDIT("4","待编辑")
        ;
        private String code;
        private String name;
        COURSE(String code,String name){
            this.code = code;
            this.name = name;
        }
        public String getCode() {
            return code;
        }
        public String getName() {
            return name;
        }
        public static String getNameByCode(String code ) {
            for (COURSE value : COURSE.values()) {
                if (value.code.equals(code)) {
                    return value.name;
                }
            }
            return "";
        }
    }

    public enum STEP {
        BASE_INFO("1","基础信息"),
        ANALYSIS("2","课程文档已解析"),
        PROFILE("3","课程简介生成"),
        CATEGORY("4","目录生成"),
        CONTENT("5","学习内容生成");
        private String code;
        private String name;
        STEP(String code,String name){
            this.code = code;
            this.name = name;
        }
        public String getCode() {
            return code;
        }
        public String getName() {
            return name;
        }
        public static String getNameByCode(String code ) {
            for (STEP value : STEP.values()) {
                if (value.code.equals(code)) {
                    return value.name;
                }
            }
            return "";
        }
    }
    public enum  STATE{
        AVAILABLE("1","上架"),
        UNAVAILABLE("0","下架");
        private String code;
        private String name;
        STATE(String code,String name){
            this.code = code;
            this.name = name;
        }
        public String getCode() {
            return code;
        }
        public String getName() {
            return name;
        }
        public static String getNameByCode(String code ) {
            for (STATE value : STATE.values()) {
                if (value.code.equals(code)) {
                    return value.name;
                }
            }
            return "";
        }
    }

}
