package com.tydic.nbchat.train.api.bo.task;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class NbchatTaskRecordBO implements Serializable {

    private Integer taskId;
    private String taskName;
    private String status = "0"; //2已完成、1学习中、0未开始、3已过期
    private String isExpire = "1"; //0已过期、1未过期
    private String finishStatus = "0"; //0未完成 1已完成
    private String startStatus;  //任务状态: 0 编辑中 1 上架 2 下架
    private String isDegree = "0";//1证书任务、0普通任务
    private String hasDegree = "0";//1已领取证书、0未领取证书
    private String degreeName;
    private String degreeDesc;

    private List<CourseFinishInfo> infoList = new ArrayList<>();

    private Date updateTime;
}
