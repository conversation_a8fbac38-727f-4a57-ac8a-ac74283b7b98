package com.tydic.nbchat.train.api.bo.examRule;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class ExamRuleReqBO extends BasePageInfo implements Serializable {
    private String tenantCode;
    private String userId;
    /**
     * 课程ID
     */
    private String courseId;
    /**
     * 课程名称
     */
    private String courseName;
    private String testState; //课程状态
    private String catalog; //课程目录
    /**
     * 试题配置状态
     */
    private String testPaperState;
    /**
     * 二级分类ID
     */
    private String category2;
    /**
     * 排序
     */
    private String sort;
    /**
     * 是否查询试题
     */
    private String queryQuestion;
    /**
     * 试题内容
     */
    private String content;
    /**
     * 试题ID集合
     */
    private List<String> ids;
    /**
     * 知识要点
     */
    private String knowledges;
    private String deptId; //归属部门
    private String deptScope; //0 本部门 1 本部门及下级部门
    private List<String> courseTypes = new ArrayList<>(Arrays.asList("1")); //1-普通课程 2-训战课程 3-考试课程

}
