package com.tydic.nbchat.train.api.bo.train.section;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
public class TrainSectionsBO implements Serializable {
    private String creationId;
    private String taskId;
    private String sectionId;
    private String courseId;
    private String catalogTitle;
    private String fileId;
    private String tenantCode;
    private String userId;
    private String voiceUrl;
    private String videoUrl;
    private String content;

    private Short sectionsIndex;
    private Date createTime;
    private int videoSource;
    private String taskState;
    private String catalogId;
}