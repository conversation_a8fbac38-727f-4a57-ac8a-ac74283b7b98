package com.tydic.nbchat.train.api.bo.task;

import lombok.Data;

import java.io.Serializable;

@Data
public class CourseFinishInfo implements Serializable {
    private String courseId;
    private String courseName;
    private String imgAvatar;
    private String courseType;
    /**
     * 是否学习视频
     */
    private String isStudyVideo = "0";
    /**
     * 是否课后评测
     */
    private String isAfterTest = "0";
    /**
     * 是否人机对练
     */
    private String isHumanMachine = "0";

    private String trainState = "0";
    private String testState = "0";
    private Integer testScore;
    private String sceneState = "0";

    private int videoCount;
    private String courseState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String testPaperState; //0 编辑中 1 预览中 2 上架中 3 已下架
    private String dialogueState; //0 编辑中 1 预览中 2 上架中 3 已下架
}
