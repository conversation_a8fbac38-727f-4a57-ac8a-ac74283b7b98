package com.tydic.nbchat.train.api.bo.video;

import lombok.Data;

import java.io.Serializable;

@Data
public class NbchatVideoCountQueryRspBO implements Serializable {
    private int today;        // 今日的视频制作数量
    private int lastWeek;     // 最近7天的视频制作数量
    private int lastMonth;    // 最近30天的视频制作数量
    // 企业用户
    private int enterpriseToday;     // 企业用户今日的视频制作数量
    private int enterpriseLastWeek;  // 企业用户最近7天的视频制作数量
    private int enterpriseLastMonth;  // 企业用户最近30天的视频制作数量

    // 个人用户
    private int personalToday;       // 个人用户今日的视频制作数量
    private int personalLastWeek;    // 个人用户最近7天的视频制作数量
    private int personalLastMonth;   // 个人用户最近30天的视频制作数量
}
