package com.tydic.nbchat.train.api.bo.eums;



import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 语言类型枚举
 */
public enum LanguageType {
    // 主要语言 (speakers > 50M)
    CHINESE("中文", "Chinese", "cmn"),
    SPANISH("西班牙语", "Spanish", "spa"),
    ENGLISH("英语", "English", "eng"),
    RUSSIAN("俄语", "Russian", "rus"),
    ARABIC("阿拉伯语", "Arabic", "arb"),
    BENGALI("孟加拉语", "Bengali", "ben"),
    HINDI("印地语", "Hindi", "hin"),
    PORTUGUESE("葡萄牙语", "Portuguese", "por"),
    INDONESIAN("印尼语", "Indonesian", "ind"),
    JAPANESE("日语", "Japanese", "jpn"),
    FRENCH("法语", "French", "fra"),
    GERMAN("德语", "German", "deu"),
    JAVANESE("爪哇语", "Javanese", "jav"),
    KOREAN("韩语", "Korean", "kor"),
    TELUGU("泰卢固语", "Telugu", "tel"),
    VIETNAMESE("越南语", "Vietnamese", "vie"),
    MARATHI("马拉地语", "Marathi", "mar"),
    ITALIAN("意大利语", "Italian", "ita"),
    TAMIL("泰米尔语", "Tamil", "tam"),
    TURKISH("土耳其语", "Turkish", "tur"),
    URDU("乌尔都语", "Urdu", "urd"),

    // 次要语言 (speakers > 20M)
    GUJARATI("古吉拉特语", "Gujarati", "guj"),
    POLISH("波兰语", "Polish", "pol"),
    UKRAINIAN("乌克兰语", "Ukrainian", "ukr"),
    KANNADA("卡纳达语", "Kannada", "kan"),
    MAITHILI("迈蒂利语", "Maithili", "mai"),
    MALAYALAM("马拉雅拉姆语", "Malayalam", "mal"),
    PERSIAN("波斯语", "Persian", "pes"),
    BURMESE("缅甸语", "Burmese", "mya"),
    SWAHILI("斯瓦希里语", "Swahili", "swh"),
    SUNDANESE("巽他语", "Sundanese", "sun"),
    ROMANIAN("罗马尼亚语", "Romanian", "ron"),
    PANJABI("旁遮普语", "Panjabi", "pan"),
    BHOJPURI("博杰普尔语", "Bhojpuri", "bho"),
    AMHARIC("阿姆哈拉语", "Amharic", "amh"),
    HAUSA("豪萨语", "Hausa", "hau"),
    FULFULDE("富拉尼语", "Fulfulde", "fuv"),
    BOSNIAN("波斯尼亚语", "Bosnian", "bos"),
    CROATIAN("克罗地亚语", "Croatian", "hrv"),
    DUTCH("荷兰语", "Dutch", "nld"),
    SERBIAN("塞尔维亚语", "Serbian", "srp"),
    THAI("泰语", "Thai", "tha"),
    KURDISH("库尔德语", "Kurdish", "ckb"),
    YORUBA("约鲁巴语", "Yoruba", "yor"),
    UZBEK("乌兹别克语", "Uzbek", "uzn"),
    MALAY("马来语", "Malay", "zlm"),
    IGBO("伊博语", "Igbo", "ibo");

    private final String chineseName;
    private final String englishName;
    private final String languageCode;

    LanguageType(String chineseName, String englishName, String languageCode) {
        this.chineseName = chineseName;
        this.englishName = englishName;
        this.languageCode = languageCode;
    }

    public String getChineseName() {
        return chineseName;
    }

    public String getEnglishName() {
        return englishName;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    /**
     * 获取所有语言映射（中文名到语言代码）
     */
    public static Map<String, String> getLanguageMap() {
        return Arrays.stream(values())
                .collect(Collectors.toMap(
                        LanguageType::getChineseName,
                        LanguageType::getLanguageCode,
                        (oldValue, newValue) -> oldValue // 如果有重复的key，保留第一个
                ));
    }

    /**
     * 获取所有语言显示映射（中文名到英文名）
     */
    public static Map<String, String> getLanguageDisplayMap() {
        return Arrays.stream(values())
                .collect(Collectors.toMap(
                        LanguageType::getChineseName,
                        LanguageType::getEnglishName,
                        (oldValue, newValue) -> oldValue // 如果有重复的key，保留第一个
                ));
    }

    /**
     * 根据语言代码获取枚举对象
     */
    public static LanguageType getByCode(String code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(language -> code.equals(language.getLanguageCode()))
                .findFirst()
                .orElse(null);
    }
}