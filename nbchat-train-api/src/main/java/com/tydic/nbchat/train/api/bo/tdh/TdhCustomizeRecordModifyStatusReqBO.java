package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TdhCustomizeRecordModifyStatusReqBO extends BaseInfo implements Serializable {

    private String vipFlag;
    /**
     * 订单id
     */
    @ParamNotEmpty(message = "订单号不能为空")
    private String orderNo;

    /**
     * 定制类型 2d/2.5d/audio
     */
    @ParamNotEmpty(message = "定制类型不能为空")
    private String customizeType;

    /**
     * 定制状态 0: 草稿（上传中） 1: 订单创建（待支付） 2: 支付完成（审核中） 3: 审核完成（定制中） 4: 定制完成  5: 取消
     */
    @ParamNotEmpty(message = "定制状态不能为空")
    private String customizeStatus;

    /**
     * 音频定制地址
     */
    private String voiceUrl;

    /**
     * 音频演示地址
     */
    private String voiceDemo;

    /**
     * 音频id: 火山音频id
     */
    private String volcId;

    /**
     * 虚拟人图片地址
     */
    private String tdhImg;

    /**
     * 原图
     */
    private String originImage;
}
