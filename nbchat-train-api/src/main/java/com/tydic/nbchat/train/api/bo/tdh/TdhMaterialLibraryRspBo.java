package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TdhMaterialLibraryRspBo implements Serializable {
    /**
     * id
     */
    private String id;
    /**
     * 素材包pkgId
     */
    private String pkgId;
    /**
     * 素材名称
     */
    private String name;

    /**
     * 素材地址
     */
    private String url;

    /**
     * 素材描述
     */
    private String desc;

    /**
     * 高度
     */
    private Short height;

    /**
     * 宽度
     */
    private Short width;

    /**
     * 图片比例
     */
    private String ratio;

    /**
     * 资源大小:字节
     */
    private Integer size;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否有效：0-无效，1-有效
     */
    private String isValid;

}
