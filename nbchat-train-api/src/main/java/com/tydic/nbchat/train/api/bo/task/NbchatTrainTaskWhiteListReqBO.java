package com.tydic.nbchat.train.api.bo.task;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.MethodParamVerifyEnable;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class NbchatTrainTaskWhiteListReqBO extends BasePageInfo implements Serializable {
    /**
     * 主键
     */
    private Integer id;
    /**
     * 可选:指定任务的白名单
     */
    private String taskId;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 目标租户编码
     */
    private String targetTenantCode;
    /**
     * 用户名称
     * @return
     */
    private String name;

    /**
     * 岗位id
     * @return
     */
    private String deptId;

    @ParamNotEmpty(message = "白名单列表不能为空")
    private List nbchatTrainTaskWhiteList;
}
