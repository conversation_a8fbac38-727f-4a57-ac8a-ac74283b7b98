package com.tydic.nbchat.train.api.bo.report.op;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TrainOpStstPtRequestBO extends BasePageInfo implements Serializable {
    /**
     * 用户id
     */
    private String userId;
    /**
     * 租户
     */
    private String tenantCode;
    /**
     * 指标编码
     */
    private String itemCode;
    /**
     * 请求路径
     */
    private String requestUrl;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 目标租户
     */
    private String targetTenant;
    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 是否启用日期
     */
    private Boolean isDate=false;
}
