package com.tydic.nbchat.train.api.bo.train.task;

import lombok.NonNull;

public class TextBuilder {

    public static TextInfo year(@NonNull String text) {
        return new TextInfo(160, 985, text);
    }

    public static TextInfo month(@NonNull String text) {
        return new TextInfo(285, 985, text);
    }

    public static TextInfo day(@NonNull String text) {
        return new TextInfo(385, 985, text);
    }

    public static TextInfo score(@NonNull String text) {
        return new TextInfo(390, 1251, text);
    }

    public static TextInfo level(@NonNull String text) {
        return new TextInfo(390, 1340, text);
    }
}
