package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;

@Data
public class TdhTemplateQueryReqBO extends BasePageInfo implements Serializable {
    private String keyword;
    private String userId;
    private String tenantCode;
    private String tpName;
    /**
     * 模板内容
     */
    private String tpContent;
    private String remark;
    private String previewUrl;
    private String tpId;
    private String isValid;
    private String isHot;
    private String objectSize;
    //内容尺寸 16:9/9:16
    private String contentSize;
    private String targetTenant;
    /**
     * 场景
     */
    private String scene;
    /**
     * 风格
     */
    private String style;
    /**
     * 配色
     */
    private String color;
    /**
     * 分类
     */
    private String category;
    /**
     * 来源：0 系统内定 1 我的模板
     */
    private String tpSource;
    /**
     * 0 普通会员, 其他值对应vip类型: 1/2
     */
    private String vipFlag;
    //1:视频类 2:播报类
    private String tdhType;
    //是否有数字人 0:无 1:有
    private String isTdh;
    //0 下架 1 上架
    private String tpState;
    //0 公共 1个人
    private String common = "0";
    /**
     * 0 静态 1动态
     */
    private String tplType;


    public boolean isCommon(){
        return common.equals("0");
    }
}
