package com.tydic.nbchat.train.api.bo.dialogue;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class DialogueSessionRspBO implements Serializable {
    private String dialogueType; //1跟读 2练习 3考试

    private String tenantCode;
    private String userId;
    private String sessionId;
    private String courseId;
    /**
     * 通过分
     */
    private Integer passScore;
    /**
     * 用户得分
     */
    private Integer userScore;
    /**
     * 分析结果
     */
    private String analysis;
    /**
     * 创建时间
     */
    private Date startTime;
    /**
     * 完成时间
     */
    private Date endTime;
    /**
     * 会话状态: 0 未完成 1 完成
     */
    private String sessionState;
    /**
     * 是否通过: 0 未通过 1 通过
     */
    private String sceneState;
    /**
     * 会话记录
     */
    private List<DialogueSessionRecordBO> dialogues;
}
