package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数字人-用户创作记录(TdhCreationRecord)实体类
 *
 * <AUTHOR>
 * @since 2023-08-22 15:47:53
 */
@Data
public class TdhCreationRecordReqBO  extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = 453296879570050790L;
    /**
     * 创作id
     */
    private String creationId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 课程ID
     */
    private String courseId;
    /**
     * 章节ID
     */
    private String sectionId;
    /**
     * 创作名称
     */
    private String creationName;
    /**
     * 创作配置
     */
    private String creationConfig;
    /**
     * 创作内容:参考具体格式
     */
    private String creationContent;
    /**
     * 片段数
     */
    private Integer partCount;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 排序字段
     */
    private Integer orderIndex;
    /**
     * 创作状态
     */
    private String creationState;
    /**
     * 0 已删除 1 正常
     */
    private String isValid;
    /**
     * 0个人 1已共享
     */
    private String isShare;
    /**
     * 预览图
     */
    private String previewUrl;
    /**
     * 1-普通创作/2-ppt创作
     */
    private String creationSource;
    /**
     * 内容分类：1上传PPT制作视频 2平台PPT制作视频
     */
    private String creationType;
    /**
     * 原始文件地址
     */
    private String fileUrl;
}

