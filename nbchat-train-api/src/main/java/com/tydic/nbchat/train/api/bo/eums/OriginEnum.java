package com.tydic.nbchat.train.api.bo.eums;

public enum OriginEnum {

    COURSE_DETAIL("0", "课程学习页面，学习历史页面，我的课程页面"), //过滤下架课程
    COURSE_TRAIN("1", "场景实战页面"), // 上架课程，过滤考试配置和实践配置都失效的记录
    HIST_TEST("2", "历史评测页面"); //上架课程，过滤考试配置失效的记录
    private String code;
    private String name;

    OriginEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (OriginEnum value : OriginEnum.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return "";
    }
}