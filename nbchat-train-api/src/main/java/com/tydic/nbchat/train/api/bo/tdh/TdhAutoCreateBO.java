package com.tydic.nbchat.train.api.bo.tdh;

import com.tydic.nicc.common.bo.BaseInfo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class TdhAutoCreateBO extends BaseInfo implements Serializable {
    private String quality = "1";
    private String resolution = "1080p";
    private String size = "16:9";

    private String userId;
    private Integer preview = 0;//1 页面生成预览
    private Integer autoCreateType;//自动生成视频类型：1课程简介视频  2章节内容视频
    private Integer tmpType;//模板类型
    private Integer cataNum;//目录数
    private Integer keyNum;//关键点数量
    private String courseId;
    private String sectionId = null; //章节id
    private String catalogId = ""; //目录id
    private String tmpId;//模板id

}
