package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.course.TranUserCourseQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TranUserCourseQueryRspBO;
import com.tydic.nbchat.train.api.bo.course.TranUserCourseSaveReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * 用户课程管理
 */
public interface NbchatUserTrainRecordApi {

    /**
     * 查询课程列表
     * @param queryReqBO
     * @return
     */
    RspList<TranUserCourseQueryRspBO> getUserTrainCourses(TranUserCourseQueryReqBO queryReqBO);

    /**
     * 保存用户学习记录
     * @param queryReqBO
     * @return
     */
    Rsp saveUserTrainRecord(TranUserCourseSaveReqBO queryReqBO);

    /**
     * 收藏课程
     * @param @param request 请求
     * @return @return {@link Rsp }
     */
    Rsp collectCourse(TranUserCourseSaveReqBO request);
}
