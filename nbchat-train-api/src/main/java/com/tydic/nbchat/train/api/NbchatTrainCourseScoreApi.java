package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.course.TrainCourseScoreQueryReqBO;
import com.tydic.nbchat.train.api.bo.course.TrainUserQueryReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatTrainCourseScoreApi {
    /**
     * 导出成绩
     * @param
     * @return @return {@link Rsp }
     */
    RspList getStudyRecordsByPage(TrainCourseScoreQueryReqBO reqBO);

    /**
     * 导出学习记录
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    Rsp export(TrainCourseScoreQueryReqBO reqBO);

    /**
     * 获取用户姓名-手机号
     * @param @param reqBO 要求博
     * @return @return {@link Rsp }
     */
    RspList getUserInformation(TrainUserQueryReqBO reqBO);
}
