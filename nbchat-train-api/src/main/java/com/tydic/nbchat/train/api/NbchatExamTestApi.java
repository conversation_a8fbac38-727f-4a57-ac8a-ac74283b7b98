package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.exam.ExamTestCreateReqBO;
import com.tydic.nbchat.train.api.bo.exam.ExamTestResultReqBO;
import com.tydic.nbchat.train.api.bo.exam.ExamTestResultRspBO;
import com.tydic.nbchat.train.api.bo.exam.ExamTestSubmitReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface NbchatExamTestApi {

    /**
     * 创建考试，如已存在则直接返回数据
     * @param reqBO
     * @return
     */
    Rsp createExamTest(ExamTestCreateReqBO reqBO);

    /**
     * 提交试卷
     * @param reqBO
     * @return
     */
    Rsp submitExamTestPaper(ExamTestSubmitReqBO reqBO);

    /**
     * 查询考试结果 - 最近一次结果
     * @param reqBO
     * @return
     */
    Rsp<ExamTestResultRspBO> getExamTestResult(ExamTestResultReqBO reqBO);
}
