package com.tydic.nbchat.train.api.bo.eums;

public enum PPTSortType {
    PPT_THEME("1", "主题模版"),
    PPT_LAYOUT("2", "布局模版"),
    PPT_VIDEO("3", "视频模版"),
    PPT_ORAL("4", "口播模版");
    private String code;
    private String name;

    PPTSortType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for (PPTSortType value : PPTSortType.values()) {
            if (value.code.equals(code)) {
                return value.name;
            }
        }
        return "";
    }

    public static Boolean isExist(String code) {
        for (PPTSortType value : PPTSortType.values()) {
            if (value.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
