package com.tydic.nbchat.train.api.bo.dialogue;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 类描述
 */
@Data
public class DialogueSessionSaveReqBO implements Serializable {
    @ParamNotEmpty
    private String tenantCode;
    @ParamNotEmpty
    private String userId;
    private String sessionId;
    @ParamNotEmpty
    private String courseId;
    private String dialogueType; //1跟读 2练习 3考试

    /**
     * 通过分
     */
    private Integer passScore;
    /**
     * 用户得分
     */
    private Integer userScore;
    /**
     * 分析结果
     */
    private String analysis;
    /**
     * 会话状态: 0 未完成 1 完成
     */
    private String sessionState;
    /**
     * 是否通过: 0 未通过 1 通过
     */
    private String sceneState;
    /**
     * 会话记录
     */
    private List<DialogueSessionRecordBO> dialogues;
}
