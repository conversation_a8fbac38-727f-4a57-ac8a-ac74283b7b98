package com.tydic.nbchat.train.api.bo.tdh;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (TdhObjectShare)实体类
 *
 * <AUTHOR>
 * @since 2023-12-25 18:01:15
 */
@Data
public class TdhObjectShareBO implements Serializable {
    private static final long serialVersionUID = -91942607085781882L;

    private String tenantCode;
    private String userId;

/**
     * 自增主键
     */
    private Long id;
/**
     * 对象类型：human: 数字人 anchor: 音频 template: 模板
     */
    private String objectType;
/**
     * 对象id
     */
    private String objectId;
/**
     * 共享给指定租户id、个人id
     */
    private String shareTo;
/**
     * 0 : 租户 1 个人
     */
    private String shareType;

    private Date createTime;

}

