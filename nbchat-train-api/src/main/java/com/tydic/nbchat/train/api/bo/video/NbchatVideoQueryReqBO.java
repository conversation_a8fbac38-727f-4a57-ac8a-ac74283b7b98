package com.tydic.nbchat.train.api.bo.video;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class NbchatVideoQueryReqBO extends BasePageInfo implements Serializable {

    private Date createTimeBegin;
    private Date createTimeEnd;
    /**
     * 租户ID
     */
    private String targetTenantCode;
    /**
     * 视频类型;1-普通创作/2-ppt创作
     */
    private String creationSource;
    /**
     * 视频时长
     */
    private Integer minVideoDuration;
    /**
     * 视频时长
     */
    private Integer maxVideoDuration;
    /**
     * 制作时间
     */
    private Date startTime;
    /**
     * 制作时间
     */
    private Date endTime;
    /**
     * 内容分类;1上传PPT制作视频 2平台PPT制作视频
     */
    private String creationType;
    /**
     * 付费状态
     */
    private String isPay;
    /**
     * 用户属性
     */
    private String userType;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 视频状态
     */
    private List<String> taskStateList;
    // 闲时队列传入 1
    private String idleQueue;
    // 是否删除
    private String isValid;
    /**
     * 是否进行分页;默认进行分页
     */
    private Boolean isPaged = true;

}
