package com.tydic.nbchat.train.api;

import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnError;
import com.tydic.nbchat.train.api.bo.listener.CourseVoiceOnSuccess;

/**
 * 培训音频生成监听
 */
public interface CourseVoiceListener {

    /**
     * 成功
     * @param onSuccess
     */
    String onSuccess(CourseVoiceOnSuccess onSuccess);

    /**
     * 异常
     * @param onError
     */
    String onError(CourseVoiceOnError onError);

    /**
     * 保存语音
     * @param address
     * @param targetDir
     * @return
     */
    String saveRemoteVoice(String address,String targetDir);
}
