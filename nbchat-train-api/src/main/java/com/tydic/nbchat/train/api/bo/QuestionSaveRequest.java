package com.tydic.nbchat.train.api.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QuestionSaveRequest implements Serializable {
    private String questionType;
    private String tenantCode;
    private String userId;
    private String courseId;
    private String partId;
    //结构化数据 - gpt提取的试题内容
    private String content;

    private String knowledges;
}
