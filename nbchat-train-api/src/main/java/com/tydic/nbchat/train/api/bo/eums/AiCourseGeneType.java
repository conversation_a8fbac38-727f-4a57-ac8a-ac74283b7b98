package com.tydic.nbchat.train.api.bo.eums;

public enum AiCourseGeneType {
    tdh_pdf_parts_content("tdh_pdf_parts_content","整理pdf片段内容"),

    train_ppt_extract("train_ppt_extract", "ppt提取关键点"),
    train_course_desc("train_course_desc", "培训课程描述"),
    train_course_exam_s_choice("train_course_exam_s_choice", "培训课程单选试题"),
    train_course_catalog("train_course_catalog", "培训课程目录"),
    train_course_section("train_course_section", "培训课程内容"),
    train_course_section_all("train_course_section_all", "全部课程章节"),
    train_course_scene_dialogue("train_course_scene_dialogue", "培训场景实战"),
    prac_course_scene_dialogue("prac_course_scene_dialogue", "实战场景模拟");

    private String code;
    private String name;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private AiCourseGeneType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code){
        for (AiCourseGeneType field : AiCourseGeneType.values()){
            if(field.code.equals(code)){
                return field.name;
            }
        }
        return "";
    }
}
