package com.tydic.nbchat.train.api.bo.eums;

/**
 * <AUTHOR>
 * @date 2023/08/01
 * @email <EMAIL>
 * @description 试题顺序枚举类
 */
public enum ExamTestType {
    ORDER("1", "顺序"),
    RANDOM("2", "随机");
    private String code;
    private String name;

    private ExamTestType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
