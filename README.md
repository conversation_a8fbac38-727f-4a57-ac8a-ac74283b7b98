## 工程说明
### 1.1. dc-base-bo  
数据中心基础模块，定义公共bo参数：
```
<dependency>
  <groupId>com.tydic.nicc.dc</groupId>
  <artifactId>dc-base-bo</artifactId>
  <version>1.0.RELEASE</version>
</dependency>
```
### 1.2. dc-boot-starter
提供redisHelper、dubbo等集成，公共组件定义，出入参辅助工具等。其他模块依赖：
```
<dependency>
  <groupId>com.tydic.nicc.dc</groupId>
  <artifactId>dc-boot-starter</artifactId>
  <version>1.0.RELEASE</version>
</dependency>
```
#### 1.2.1 配置说明

```$xslt
# 数据中心相关配置
nicc-dc-config:
  redis:
    enable: true #是否开启redisHelper，开启后必须配置spring.redis，同时直接用dc-base中的 RedisHelper
  oss:
    enable: true #是否启用oss组件
    endpoint: http://oss-cn-qingdao.aliyuncs.com
    accesskey: accesskey
    accessKeySecret: accessKeySecret
    accessUrl: http://oss-cn-qingdao.aliyuncs.com
    bucketName: temp
  ftp:
    enable: true #是否启用ftp组件
    type: ftp #支持 ftp/sftp  分别对应 FTPHelper/SFTPHelper
    host: ecs.92kk.top
    port: 21
    username: app_n2
    password: app_n2
    homeDir: /  
  datasource: #多数据源定义-都写在这下边
    nicc-datacenter:  #数据中心数据源，以模块名称命名
      # 使用HikariDataSource数据源
      type: com.zaxxer.hikari.HikariDataSource
      jdbc-url: ************************************************************************************************
      username: root
      password: 199432
      driver-class-name: com.mysql.jdbc.Driver
```

#### 1.2.2 引用其他模块

