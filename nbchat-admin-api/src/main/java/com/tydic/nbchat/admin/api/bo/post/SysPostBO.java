package com.tydic.nbchat.admin.api.bo.post;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;


import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;

/**
 * 岗位配置表(SysPost)实体类
 *
 * <AUTHOR>
 * @since 2024-07-15 15:54:16
 */
@Data
public class SysPostBO  extends BasePageInfo implements Serializable{
    private static final long serialVersionUID = 884444762747405082L;

    private String targetTenant;

    /**
     * 主键
     */
    private Integer postId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 岗位名称
     */
    @NotEmpty(message = "岗位名称不能为空")
    private String postName;
    /**
     * 岗位描述
     */
    private String postDesc;
    /**
     * 排序
     */
    private Integer orderIndex;
    /**
     * 是否启用/0否/1是
     */
    private String postStatus;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 是否删除
     */
    private String isValid;

}

