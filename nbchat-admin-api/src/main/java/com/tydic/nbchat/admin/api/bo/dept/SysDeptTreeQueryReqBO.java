package com.tydic.nbchat.admin.api.bo.dept;

import com.tydic.nicc.common.bo.BaseInfo;
import lombok.Data;

import java.io.Serializable;

@Data
public class SysDeptTreeQueryReqBO extends BaseInfo implements Serializable {
    /**
     * 层级
     */
    private Integer level;
    /**
     * 租户编码
     */
    private String tenantCode;
    //指定租户查询
    private String targetTenant;
    //返回最大层级
    private Integer maxLevel;
    private String status;
    //查询指定部门及下级部门
    private String deptId;
    //匹配部门id
    private String filterId;
}
