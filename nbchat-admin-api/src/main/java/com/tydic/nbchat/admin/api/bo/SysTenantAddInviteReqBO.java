package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class SysTenantAddInviteReqBO extends BaseInfo {
    @ParamNotEmpty
    private String userId;
    private String tenantCode;
    @ParamNotEmpty
    private String inviteKey;
    private String userRealityName;

    // 以下是新增字段  用户头像、性别、身份证号、岗位、出生日期
    private String avatar;
    private String idCard;
    private List<Integer> postIds;
    private Date birthday;
    private String gender;
    private String deptId;

    //确定切换部门 1 0
    private String isChangeDept = "0"; //1切换

}
