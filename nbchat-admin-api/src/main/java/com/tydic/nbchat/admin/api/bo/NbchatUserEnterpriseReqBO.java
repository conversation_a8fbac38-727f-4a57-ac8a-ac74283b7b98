package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;
import lombok.NonNull;

@Data
public class NbchatUserEnterpriseReqBO extends BasePageInfo implements java.io.Serializable{
    private String userId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 企业名称
     */
    @ParamNotEmpty(message = "企业名称不能为空")
    private String companyName;
    /**
     * 所属行业
     */
    @ParamNotEmpty(message = "所属行业不能为空")
    private String businessSector;
    /**
     * 需求
     */
    private String requirement;
}
