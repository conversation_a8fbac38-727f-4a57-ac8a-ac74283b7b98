package com.tydic.nbchat.admin.api.bo;

import com.tydic.nbchat.admin.api.bo.menu.SysMenuBO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class SysTenantBO implements Serializable {

    private String payType; //0企业支付 1个人支付

    private String tenantName;
    private String tenantDesc;
    private String imgAvatar;
    private String extInfo;
    private Date createTime;
    private String tenantConfig;
    private List<SysMenuBO> menus;
    private List<SysTenantSubsystemRspBo> subsystems;
}
