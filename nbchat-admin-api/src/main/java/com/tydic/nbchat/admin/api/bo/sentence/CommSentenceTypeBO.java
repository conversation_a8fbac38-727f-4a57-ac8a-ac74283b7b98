package com.tydic.nbchat.admin.api.bo.sentence;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class CommSentenceTypeBO implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 1205889144167810026L;

    private Long typeId;

    private String tenantCode;

    private Short typeGroup;

    private String custCode;

    private String typeName;

    private String typeDesc;

    private Integer sortId;

    private Date createTime;

    private String createUserId;

    private String createUserName;

    private Date updateTime;

    private String updateUserId;

    private String updateUserName;

    private Integer level;

    private Long parentId;

    private List<CommSentenceTypeBO> childrenCommSentenceTypeList;

    public List<CommSentenceTypeBO> getChildrenCommSentenceTypeList() {
        return childrenCommSentenceTypeList;
    }

    public void setChildrenCommSentenceTypeList(List<CommSentenceTypeBO> childrenCommSentenceTypeList) {
        this.childrenCommSentenceTypeList = childrenCommSentenceTypeList;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Long getTypeId() {
        return typeId;
    }

    public void setTypeId(Long typeId) {
        this.typeId = typeId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Short getTypeGroup() {
        return typeGroup;
    }

    public void setTypeGroup(Short typeGroup) {
        this.typeGroup = typeGroup;
    }

    public String getCustCode() {
        return custCode;
    }

    public void setCustCode(String custCode) {
        this.custCode = custCode;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getTypeDesc() {
        return typeDesc;
    }

    public void setTypeDesc(String typeDesc) {
        this.typeDesc = typeDesc;
    }

    public Integer getSortId() {
        return sortId;
    }

    public void setSortId(Integer sortId) {
        this.sortId = sortId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUserName() {
        return createUserName;
    }

    public void setCreateUserName(String createUserName) {
        this.createUserName = createUserName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    @Override
    public String toString() {
        return "commSentenceTypeBo{" +
                "typeId=" + typeId +
                ", tenantCode='" + tenantCode + '\'' +
                ", typeGroup=" + typeGroup +
                ", custCode='" + custCode + '\'' +
                ", typeName='" + typeName + '\'' +
                ", typeDesc='" + typeDesc + '\'' +
                ", sortId=" + sortId +
                ", createTime=" + createTime +
                ", createUserId=" + createUserId +
                ", createUserName='" + createUserName + '\'' +
                ", updateTime=" + updateTime +
                ", updateUserId=" + updateUserId +
                ", updateUserName='" + updateUserName + '\'' +
                '}';
    }
}
