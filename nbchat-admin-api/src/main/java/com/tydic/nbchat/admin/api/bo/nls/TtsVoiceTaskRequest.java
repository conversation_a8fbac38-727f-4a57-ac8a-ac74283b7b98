package com.tydic.nbchat.admin.api.bo.nls;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

@Data
public class TtsVoiceTaskRequest implements Serializable {
    private String anchorType;
    private String userId;
    private String tenantCode;
    private String sectionId;
    @ParamNotEmpty
    private String text;
    @ParamNotEmpty
    private String courseId;
    private String voice;
    //语调，范围是-500~500，可选，默认是0
    private Integer pitchRate;
    //语速，范围是-500~500，可选，默认是0
    private Integer speechRate;
    //音量，范围是0~100，可选，默认50
    private Integer volume;
    private boolean async = false;
}
