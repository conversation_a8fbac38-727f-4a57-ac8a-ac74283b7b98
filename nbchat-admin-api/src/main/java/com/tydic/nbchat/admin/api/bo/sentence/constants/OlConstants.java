package com.tydic.nbchat.admin.api.bo.sentence.constants;

import java.util.LinkedList;
import java.util.Queue;

public class OlConstants {

   /* *//**
     * 会话默认有效时长，单位：分钟
     *//*
    public static final int DEFAULT_EFFECTIVE_TIME = 30;

    *//**
     * 客服默认最大接待上限
     *//*
    public static final int CUST_SERVICE_DEFAULT_MAX = 10;

    *//**
     * redis客户/客服缓存信息刷新间隔
     *//*
    public static final int CACHE_REFRESH_INTERVAL = 20;

    *//**
     * 客户排队提醒时间间隔
     *//*
    public static final int LINE_UP_REMIND_INTERVAL = 3;

    *//**
     * Cache-key Token
     *//*
    public static final String TOKEN = "token";
    public static final String CUST_TOKEN = "nicc_cust:";
    public static final String CUST_SERVICE_TOKEN = "cust_service:";
    *//**
     * Cache-key userId:csId
     *//*
    public static final String USERID_CUST_SERVICE_ID = "user2onlineCs:";

    *//**
     * undefined
     *//*
    public static final String UNDEFINED = "undefined";

    *//**
     * 缓存时间：一天
     *//*
    public static final int EXPIRE = 60 * 60 * 24;
    *//**
     * 三十分钟
     *//*
    public static final int CUST_EXPIRE = 60 * 30;
    *//**
     * 八小时
     *//*
    public static final int CUST_SERVICE_EXPIRE = 60 * 60 * 8;

    *//**
     * 接受消息对象类型：cust-客户
     *//*
    public static final String RECEIVER_TYPE_CUST = "cust";
    *//**
     * 接受消息对象类型：custService-客服
     *//*
    public static final String RECEIVER_TYPE_SERVICE = "custService";

    *//**
     * 发送消息路由：T1001-客户发送的消息
     *//*
    public static final String USER_TYPE_CUSTOMER = "T1001";
    *//**
     * 发送消息路由：TCS01-客服发送消息
     *//*
    public static final String USER_TYPE_CUSTOMER_SERVICE = "TCS01";

    *//**
     * 坐席登录后信息存入到SESSION中Key
     *//*
    public static final String SESSION_CUST_SERVICE_ID = "ONLINE_SESSION_CUST_SERVICE_ID";

    *//**
     * 历史消息默认查询数量
     *//*
    public static final int HISTORY_PAGE_SIZE = 10;

    *//**
     * 消息类型： 0-text，默认为text纯文本
     *//*
    public static final String MAG_TYPE_0 = "0";
    *//**
     * 消息类型：1-image,content传递图片地址
     *//*
    public static final String MAG_TYPE_1 = "1";
    *//**
     * 消息类型：2-voice，content传递音频地址,支持mp3,wav格式
     *//*
    public static final String MAG_TYPE_2 = "2";*/

    /**
     * 对话返回状态及相关提示语设置
     */
    public static final String RESULT_SUCCESS_CODE = "0000";
    public static final String RESULT_SUCCESS_MSG = "SUCCESS";
    public static final String RESULT_ERROR_CODE = "9999";
    public static final String IN_QUERY_NOT_QUIT = "有正在咨询用户";
    public static final String NOT_MATCH_CUST_SERVICE = "网络繁忙，请稍后重试！";
    public static final String MATCH_CUST_SERVICE_SUCCESS = "竭诚为您服务！";
    public static final String LINE_UP_REMIND_CONTENT = "请稍等，您前面还有#人数#人在排队...";

    /**
     * websocket链接失败原因，链接被终端
     */
    public static String LINK_VIOLAATION = "link violation";
    /**
     * websocket链接失败原因，租户编码为空
     */
    public static String TENANT_CODE_IS_NOT_NULL = "tenant code is not null";

    /**
     * 分配客服结果类型，-1：无客服在线
     */
    public static final String LINK_TYPE_NOTCS = "-1";
    /**
     * 分配客服结果类型，0：直连
     */
    public static final String LINK_TYPE_CONN = "0";
    /**
     * 分配客服结果类型，1：排队
     */
    public static final String LINK_TYPE_AWAIT = "1";
    /**
     * 分配客服结果类型，1：客户不在线
     */
    public static final String LINK_TYPE_NONE = "2";

    /**
     * 快捷回复设置类型：fastType:0-表示个人快捷回复语，表中应记录客服ID
     */
    public static String FAST_TYPE_0 = "0";

    /**
     * 系统消息通知类型-上线通知
     */
    public static final String NOTIFY_TYPE_OPEN = "open";
    /**
     * 系统消息通知类型-心跳机制
     */
    public static final String NOTIFY_TYPE_HEARTBEAT = "heartbeat";
    /**
     * 系统消息通知类型-推送通告
     */
    public static final String NOTIFY_TYPE_NOTIFY = "notify";
    /**
     * 系统系统通知类型-下线通知
     */
    public static final String NOTIFY_TYPE_CLOSE = "close";
    /**
     * 回执消息类型
     */
    public static final String NOTIFY_TYPE_RECEIPT = "receipt";
    /**
     * 系统消息通知类型-邀请评价
     */
    public static final String NOTIFY_TYPE_INVITE_EVALUATE = "inviteEvaluate";

    /**
     * 消息类型：心跳消息
     */
    public static final String MSG_TYPE_HEARTBEAT = "heartbeat";
    /**
     * 消息类型：系统提示消息
     */
    public static final String MSG_TYPE_SYS = "Tips";
    /**
     * 消息类型：对话消息
     */
    public static final String MSG_TYPE_DIALOGUE = "dialogue";

    /**
     * 小蜜返回类型
     */
    public static final String RECOMMEND = "Recommend";
    /**
     * 小蜜返回Recommend拼接首句话入库
     */
    public static final String RECOMMEND_DESC = "您是不是想问这个？";
    /**
     * 小蜜默认欢迎语
     */
    public static final String CHAR_CONTENT_DEFAULT_WELCOME = "很高兴为您服务，请问有什么可以帮您？";
    /**
     * 小蜜返回空入库内容
     */
    public static final String CHAR_CONTENT_ISNULL_DESC = "很抱歉我没理解您的意思，换个问法再问一遍吧~";

    /**
     * 路由策略类型：visitSource-咨询渠道
     */
    public static final String CUST_ROUTING_TYPE_VISIT_SOURCE = "visitSource";
    /**
     * 路由策略类型：custVip-客户级别
     */
    public static final String CUST_ROUTING_TYPE_CUST_LEVEL = "custVip";

    /**
     * 路由策略SIGN：0-是
     */
    public static final String CUST_ROUTING_SIGN_YES = "0";
    /**
     * 路由策略SIGN：1-否
     */
    public static final String CUST_ROUTING_SIGN_NO = "1";

    /**
     * 路由策略之咨询渠道：0-桌面网站
     */
    public static final String VISIT_SOURCE_DESKTOP = "0";
    /**
     * 路由策略之咨询渠道：1-移动网站
     */
    public static final String VISIT_SOURCE_MOBILE = "1";
    /**
     * 路由策略之咨询渠道：2-APP
     */
    public static final String VISIT_SOURCE_APP = "2";
    /**
     * 路由策略之咨询渠道：3-微信
     */
    public static final String VISIT_SOURCE_WECHAT = "3";
    /**
     * 路由策略之咨询渠道：4-微博
     */
    public static final String VISIT_SOURCE_WEIBO = "4";
    /**
     * 路由策略之咨询渠道：5-企业微信
     */
    public static final String VISIT_SOURCE_WORK_WECHAT = "5";
    /**
     * 路由策略之咨询渠道：6-小程序
     */
    public static final String VISIT_SOURCE_MINI_PROGRAM = "6";

    /**
     * 路由策略之客户级别：0-普通
     */
    public static final String CUST_LEVEL_COMMON = "0";
    /**
     * 路由策略之客户级别：1-VIP
     */
    public static final String CUST_LEVEL_VIP = "1";

    /**
     * 执行类型：group-技能组
     */
    public static final String EXECTE_TYPE_SKILLS_GROUP = "group";
    /**
     * 执行类型：cust-客服
     */
    public static final String EXECTE_TYPE_CUST_SERVICE = "cust";

    /**
     * 技能组状态：0-在线
     */
    public static final String GROUP_STATUS_ONLINE = "0";
    /**
     * 技能组状态：1-忙碌
     */
    public static final String GROUP_STATUS_BUSY = "1";
    /**
     * 技能组状态：2-离线
     */
    public static final String GROUP_STATUS_OFFLINE = "2";
    /**
     * 技能组状态： 3-接待上限
     */
    public static final String GROUP_STATUS_RECEIVE_LIMIT = "3";
    /**
     * 技能组状态：7 - 可接待
     */
    public static final String GROUP_STATUS_CANRECEIVE = "4";
    /**
     * 技能状态：8-不可接待
     */
    public static final String GROUP_STATUS_NOCANRECEIVE = "5";

    /**
     * 客服状态：1-在线
     */
    public static final String CUST_SERVICE_STATUS_ONLINE = "1";
    /**
     * 客服状态：2-小休
     */
    public static final String CUST_SERVICE_STATUS_STEALTH = "2";
    /**
     * 客服状态：3-空闲
     */
    public static final String CUST_SERVICE_STATUS_FREE = "3";
    /**
     * 客服状态：4-离线
     */
    public static final String CUST_SERVICE_STATUS_RESPITE = "4";
    /**
     * 客服状态：5-忙碌
     */
    public static final String CUST_SERVICE_STATUS_BUSY = "5";
    /**
     * 客服状态：6-离线
     */
    public static final String CUST_SERVICE_STATUS_OFFLINE = "6";


    /**
     * 队列类型Key前缀：VIP队列
     */
    public static final String QUEUE_TYPE_VIP_PREFIX = "queue_VIP_";
    /**
     * 队列类型Key前缀：普通队列
     */
    public static final String QUEUE_TYPE_COMMON_PREFIX = "queue_";

    /**
     * 排队规则：vip-vip排队
     */
    public static final String QUEUE_TYPE_VIP = "vip";
    /**
     * 排队规则：common-普通排队
     */
    public static final String QUEUE_TYPE_COMMON = "common";

    /**
     * 客服路由策略状态： 0-启用
     */
    public static final short ROUTING_STATUS_0 = 0;
    /**
     * 客服路由策略状态：1-停用
     */
    public static final short ROUTING_STATUS_1 = 1;

    /**
     * 客服分配策略分配方式： saturation-按客服饱和度分配
     */
    public static final String POLICY_TYPE_SATURATION = "saturation";
    /**
     * 客服分配策略分配方式：poll-按客服轮询分配
     */
    public static final String POLICY_TYPE_POLL = "poll";
    /**
     * 客服分配策略分配方式：familiar-熟客指定分配
     */
    public static final String POLICY_TYPE_FAMILIAR = "familiar";

    /**
     * 客户人工会话状态：0-离线
     */
    public static final String CUST_OFF_LINE = "0";
    /**
     * 客户人工会话状态：1-在线
     */
    public static final String CUST_ON_LINE = "1";
    /**
     * 客户人工会话状态：2-会话中
     */
    public static final String CUST_ON_CHAT = "2";

    /**
     * 在线客服生效状态
     */
    public static final short CUST_SERVICE_STATUS_VALID = 0;

    /**
     * 客户状态，0：失效;
     */
    public static final String CUST_STATUS_INVALID = "0";

    /**
     * 客户状态，1：生效;
     */
    public static final String CUST_STATUS_VALID = "1";

    /**
     * 客户级别，1：vip;
     */
    public static final String CUST_VIP_INVALID = "0";

    public static final String CUST_VIP_LEVEL = "1";

    /**
     * 客户级别，0：普通;
     */
    public static final String CUST_VIP_COMMON = "0";

    /**
     * 自动应答项开启
     */
    public static final String REPLY_CONFIG_IS_OPEN = "1";

    /**
     * 自动应答项关闭
     */
    public static final String REPLY_CONFIG_IS_CLOSE = "2";

    /**
     * 会话自动应答配置分类，0：通告消息
     */
    public static final String REPLY_CONFIG_TYPE_0 = "0";
    /**
     * 会话自动应答配置分类，1：机器人欢迎语
     */
    public static final String REPLY_CONFIG_TYPE_1 = "1";
    /**
     * 会话自动应答配置分类，2：人工客服欢迎语
     */
    public static final String REPLY_CONFIG_TYPE_2 = "2";
    /**
     * 会话自动应答配置分类，3：未知问题说辞
     */
    public static final String REPLY_CONFIG_TYPE_3 = "3";
    /**
     * 会话自动应答配置分类，4：客服不在线说辞
     */
    public static final String REPLY_CONFIG_TYPE_4 = "4";
    /**
     * 会话自动应答配置分类，5：客户排队说辞
     */
    public static final String REPLY_CONFIG_TYPE_5 = "5";
    /**
     * 会话自动应答配置分类，6：排队超时接入说辞
     */
    public static final String REPLY_CONFIG_TYPE_6 = "6";
    /**
     * 会话自动应答配置分类，7：客服结束会话说辞
     */
    public static final String REPLY_CONFIG_TYPE_7 = "7";
    /**
     * 会话自动应答配置分类，8：客户超时提示
     */
    public static final String REPLY_CONFIG_TYPE_8 = "8";
    /**
     * 会话自动应答配置分类，9：客服超时提示
     */
    public static final String REPLY_CONFIG_TYPE_9 = "9";
    /**
     * 会话自动应答配置分类，10：客户超时下线
     */
    public static final String REPLY_CONFIG_TYPE_10 = "10";
    /**
     * 会话自动应答配置分类，11：客户排队人数超限设置
     */
    public static final String REPLY_CONFIG_TYPE_11 = "11";
    /**
     * 会话自动应答配置分类，11：转接客服失败
     */
    public static final String REPLY_CONFIG_TYPE_12 = "12";

    /**
     * 兜底配置：系统异常兜底
     */
    public static final String REPLY_CONFIG_TYPE_13 = "13";
    /**
     * 兜底配置：未获取到内容兜底
     */
    public static final String REPLY_CONFIG_TYPE_14 = "14";

    /**
     * websocket 心跳间隔时长
     */
    public static final Long HEART_BEAT_INTERVAL = 5L;

    /**
     * 发送者类型：0-用户对机器人
     */
    public static final short SENDER_TYPE_CUST2ROBOT = 0;
    /**
     * 发送者类型：1-机器人
     */
    public static final short SENDER_TYPE_REBOT = 1;
    /**
     * 发送者类型：2-客服
     */
    public static final short SENDER_TYPE_SERVICE = 2;
    /**
     * 发送者类型：3-系统提示
     */
    public static final short SENDER_TYPE_SYSTEM = 3;
    /**
     * 发送者类型：4-机器人自动应答
     */
    public static final short SENDER_TYPE_ROBOT_AUTO_ANSWER = 4;
    /**
     * 发送者类型：5-客服自动应答
     */
    public static final short SENDER_TYPE_CS_AUTO_ANSWER = 5;
    /**
     * 发送者类型：6-用户对人工客服
     */
    public static final short SENDER_TYPE_CUST2CS = 6;

    /**
     * 消息类型：0-text，默认为text纯文本
     */
    public static final String MESSAGE_TYPE_TEXT = "0";
    /**
     * 消息类型：1-image
     */
    public static final String MESSAGE_TYPE_IMAGE = "1";
    /**
     * 消息类型：2-voice
     */
    public static final String MESSAGE_TYPE_VOICE = "2";
    /**
     * 消息类型：3-file
     */
    public static final String MESSAGE_TYPE_FILE = "3";

    /**
     * websocket消息类型：image
     */
    public static final String TEXT_MESSAGE = "text";
    /**
     * websocket消息类型：image
     */
    public static final String IMAGE_MESSAGE = "image";
    /**
     * websocket消息类型：voice
     */
    public static final String VOICE_MESSAGE = "voice";
    /**
     * websocket消息类型：file
     */
    public static final String FILE_MESSAGE = "file";

    /**
     * 技能组类型：0：在线技能组
     */
    public static final int GROUP_TYPE_ONLINE = 0;
    /**
     * 技能组类型：1：热线技能组
     */
    public static final int GROUP_TYPE_HTLINE = 1;
    /**
     * 技能组类型：2、工单技能组
     */
    public static final int GROUP_TYPE_WORKORDER = 2;


    /**
     * “转人工” 机器人返回的标识，前端自主触发转人工
     */
    public static final String ROBOT2CS_FLAG = "robot_$#Turn_&%Man";

    /*************************私有云*****************************/

    /**
     * 智能推荐-接口唯一标识
     **/
    public static final String PRIVATE_ALIYUN_ACTION_RECOMMENDKNOWLEDGES = "RecommendKnowledges";

    /**
     * 转人工 key 前缀
     */
    public static final String LOCK_TO_MAN_PREFIX = "lock_to_man_cust_";
    /**
     * 转人工 key 前缀
     */
    public static final String LOCK_TO_ADD_PREFIX = "lock_to_add_cust_";
    /**
     * 转人工 key 过期时间（单位秒）
     */
    public static final int LOCK_TO_MAN_EXPIRE_TIME = 5;
    /**
     * 客服类型 0:热线 1:在线 2:工单 3::机器人
     */
    public static final short CUST_SERVICE_TYPE_HTLINE = 0;
    public static final short CUST_SERVICE_TYPE_ONLINE = 1;
    public static final short CUST_SERVICE_TYPE_WORKER = 2;
    public static final short CUST_SERVICE_TYPE_ROBOT = 3;


    /**
     * 留言状态 0：未分配
     */
    public static final String LEAVING_MSG_STATUS_0 = "0";
    /**
     * 留言状态 1：已分配未处理
     */
    public static final String LEAVING_MSG_STATUS_1 = "1";
    /**
     * 留言状态 2：已处理
     */
    public static final String LEAVING_MSG_STATUS_2 = "2";
    /**
     * 留言状态 3：已删除
     */
    public static final String LEAVING_MSG_STATUS_3 = "3";
    /**
     * 客服状态变更记录状态 0:失效 1:有效
     */
    public static final short CS_SERVICE_RECODE_STATUS_OFF = 0;
    public static final short CS_SERVICE_RECODE_STATUS_ON = 1;

    /**
     * 客服知识分组 1:公共 2:个人
     */

    public static final short CS_COMM_SENTENCE_TYPE_PUBLIC = 1;
    public static final short CS_COMM_SENTENCE_TYPE_PRIVATE = 2;

    /**
     * 辅助查询用户类型 1:公共 2:个人
     */
    public static final String AUXILIARY_COMM_SENTENCE_TYPE_TENANT = "0";
    public static final String AUXILIARY_COMM_SENTENCE_TYPE_CUST = "1";
    public static final String AUXILIARY_COMM_SENTENCE_TYPE_ARTIFICIAL = "2";
    public static final String AUXILIARY_COMM_SENTENCE_TYPE_SKILL = "3";

    /**
     * 客服角色：0：普通坐席
     */
    public static final int CUST_SERVICE_STATION_NORMAL = 0;
    /**
     * 客服角色： 1：班长坐席
     */
    public static final int CUST_SERVICE_STATION_MONITOR = 1;

    /**
     * 字典 TAG：技能组类型
     */
    public static final String DICT_TAG_SKILL_GROUP_TYPE = "skill_group_type";


    public static final String FAILUR_BATCH_IMPORT_MSG = "请检查模板字段是否合法";

    /**
     * 智能辅助会话关闭redis队列
     */
    public static Queue<String> sessionClosequeue = new LinkedList<String>();



    /** 常有语分类名称分割符*/
    public static final String COMM_SENTENCE_NAME_DELIMITER = "->";
}
