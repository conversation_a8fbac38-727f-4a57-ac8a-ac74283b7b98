package com.tydic.nbchat.admin.api.bo.category;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @datetime：2024/10/16 16:31
 * @description:
 */
@Data
public class SysTreeCategoryRsqBO implements Serializable {
    /**
     * 树ID
     */
    private String cateId;
    /**
     * 父ID
     */
    private String parentId;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 租户ID
     */
    private String tenantCode;
    /**
     * 分类名称
     */
    private String cateName;
    /**
     * 级别
     */
    private Integer cateLevel;
    /**
     * 路径
     */
    private String ancestors;
    /**
     * 0 系统默认 1 用户自定义
     */
    private String cateSource;
    /**
     * 业务类型,区分不同业务用
     */
    private String busiType;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 是否有效 1 有效 0 无效
     */
    private String isValid;
    /**
     * 排序
     */
    private Integer orderIndex;
    private List<SysTreeCategoryRsqBO> children;
}
