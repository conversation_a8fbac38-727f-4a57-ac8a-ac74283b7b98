package com.tydic.nbchat.admin.api.bo.sentence;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;


@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CommSentence implements Serializable {
    private String tenantCode;//租户编码
    private Long typeId;//分类ID
    private String content;//知识内容
    private Integer sortId;//排序标识
    private String createUserId;//创建人ID
    private String createUserName;//创建人名称
    private boolean admin;//是否管理员

    private String sentenceId;//知识ID
    //类型 1 文本| 2 文件 | 6 富文本
    private String contentType;
    private String contentTitle;
    private String classes;
    private Date createTime;
}
