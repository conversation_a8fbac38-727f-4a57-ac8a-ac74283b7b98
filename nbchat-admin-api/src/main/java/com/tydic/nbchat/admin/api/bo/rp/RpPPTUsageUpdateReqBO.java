package com.tydic.nbchat.admin.api.bo.rp;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

@Data
public class RpPPTUsageUpdateReqBO extends BasePageInfo implements Serializable {
    /**
     * PPT ID（必填）
     */
    @NotBlank(message = "pptId不能为空")
    private String pptId;

    /**
     * 总页数
     */
    private Integer totalPage;

    /**
     * 实际使用的主题 ID（可选）
     */
    private String useThemeId;

    /**
     * 每页布局信息（可选 - 不传仅更新主题部分）
     */
    @Valid
    private List<RpPPTLayoutUsageBO> layouts;
}
