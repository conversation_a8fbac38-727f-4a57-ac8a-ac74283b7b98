package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;

@Data
public class EnterpriseTryApplyReqBO extends BasePageInfo implements Serializable {
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 所属行业
     */
    private String businessSector;
    /**
     * 职位
     */
    private String position;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 具体需求
     */
    private String specificRequirements;
    /**
     * 状态：0 待审批；1 预审批通过；2 通过；3拒绝
     */
    private String status;
}
