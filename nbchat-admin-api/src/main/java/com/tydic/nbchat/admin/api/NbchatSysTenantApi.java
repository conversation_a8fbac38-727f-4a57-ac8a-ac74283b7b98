package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.*;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserQueryReqBO;
import com.tydic.nbchat.admin.api.bo.dept.SysDeptUserSaveReqBO;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface NbchatSysTenantApi {

    /**
     * 租户信息
     * @param reqBO
     * @return
     */
    Rsp<SysTenantBO> getTenant(BaseInfo reqBO);
    /**
     * 将用户加入到租户下
     * @param bindUserReqBO
     * @return
     */
    Rsp addUsers(SysTenantOptUserReqBO bindUserReqBO);

    /**
     * 用户充值
     * @param rechargeReqBO
     * @return
     */
    Rsp userRecharge(SysTenantUserRechargeReqBO rechargeReqBO);

    /**
     * 用户余额查询
     * @param balanceReqBO
     * @return
     */
    Rsp userBalance(SysTenantUserBalanceReqBO balanceReqBO);

    /**
     * 账单列表
     * @param balanceReqBO
     * @return
     */
    RspList userBillList(SysTenantUserBillReqBO balanceReqBO);


    /**
     * 将用户从租户移除
     * @param removeUserReqBO
     * @return
     */
    Rsp removeUsers(SysTenantOptUserReqBO removeUserReqBO);

    /**
     * 查询租户列表-分页
     * @param reqBO
     * @return
     */
    RspList<SysTenantQueryRspBO> getTenants(SysTenantQueryReqBO reqBO);

    /**
     * 查询租户下的所有用户
     * @param reqBO
     * @return
     */
    RspList<SysTenantUserQueryRspBO> getTenantUsers(SysDeptUserQueryReqBO reqBO);

    /**
     * 查询用户租户列表-分页
     * @param reqBO
     * @return
     */
    RspList<SysTenantQueryRspBO> getUserTenants(SysTenantQueryReqBO reqBO);

    /**
     * 查询所有的租户(专业租户和公共租户)
     * @param reqBO
     * @return
     */
    RspList<SysTenantQueryRspBO> selectTenantList(SysTenantQueryReqBO reqBO);

    /**
     * 查询用户信息
     * @param reqBo
     * @return
     */
    Rsp getUserInfo(SysTenantOptUserReqBO reqBo);

    /**
     * 更新用户信息
     * @param reqBo
     * @return
     */
    Rsp updateUser(SysTenantUserInfoBO reqBo);

    /**
     * 禁用用户
     * @param reqBo
     * @return
     */
    Rsp disableUser(SysTenantUserInfoBO reqBo);

    /**
     * 检查租户用户数量是否达到上限
     * @param reqBo
     * @return
     */
    Rsp checkTenantUserLimitReached(SysDeptUserQueryReqBO reqBo);
}
