package com.tydic.nbchat.admin.api.bo.sentence;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShareKnowledgeReqBO  implements Serializable {
    private static final long serialVersionUID = -80305677970869384L;
    private String sentenceShareId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 知识id
     */
    private String sentenceId;
    /**
     * 密码
     */
    private String shareKey;
    /**
     * 是否需要密码 1需要 0不需要
     */
    private int needKey = 1;

    private Integer expiredDay;//有效期 1天 7天 30天 -1永久有效

    public boolean isNeedKey(){
        return needKey == 1;
    }
}
