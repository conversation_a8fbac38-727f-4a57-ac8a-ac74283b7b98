package com.tydic.nbchat.admin.api.bo.robot;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SysRobotConfigAdminSetReqBO implements Serializable {
    // 0 租户 1 个人： 前端传入
    @ParamNotEmpty
    private String configType;
    // 租户id/个人id
    @ParamNotEmpty
    private String configId;
    //当前设定值-回显
    private String configValue;
    //机器人配置列表
    @ParamNotNull
    private List<String> configList;
}
