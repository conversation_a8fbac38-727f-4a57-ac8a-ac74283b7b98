package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * 绑定用户
 */
@Data
public class SysTenantUserBillReqBO extends BasePageInfo implements Serializable {
    private String userId;
    private String tenantCode;
    @ParamNotEmpty(message = "租户ID不能为空")
    private String targetTenant;
    private String targetUid;
    //支付类型 0 个人 1 企业
    private String payType;

}
