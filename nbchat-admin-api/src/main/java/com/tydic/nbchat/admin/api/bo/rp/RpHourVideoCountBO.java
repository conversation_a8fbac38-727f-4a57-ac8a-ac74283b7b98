package com.tydic.nbchat.admin.api.bo.rp;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/4/17 21:02
 * @description ppt|视频小时统计报表
 */
@Data
public class RpHourVideoCountBO {
    private Integer id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 统计日期
     */
    private Date dayData;

    /**
     * 统计小时数
     */
    private String hourData;

    /**
     * PPT制作数量
     */
    private Integer pptMakeTimes;

    /**
     * 【AI生成PPT】数量
     */
    private Integer pptAiCount;

    /**
     * 【文档生成PPT】数量
     */
    private Integer pptDocCount;

    /**
     * 【文本生成PPT】数量
     */
    private Integer pptTxtCount;

    /**
     * 视频制作数量
     */
    private Integer videoMakeTimes;

    /**
     * 视频生成数量
     */
    private Integer videoBuildTimes;

    /**
     * 【PPT播讲】视频数量
     */
    private Integer videoPptCount;

    /**
     * 【PPT播讲】【上传PPT】视频数量
     */
    private Integer videoPptUploadCount;

    /**
     * 【PPT播讲】【平台PPT】视频数量
     */
    private Integer videoPptPlatformCount;

    /**
     * 【Word播讲】视频数量
     */
    private Integer videoWordSpeechCount;

    /**
     * 【AI智能生成】视频数量
     */
    private Integer videoAiCount;

    /**
     * 【Word生成】视频数量
     */
    private Integer videoWordGenerateCount;

    /**
     * 【模版创作】视频数量
     */
    private Integer videoTemplateCount;

    /**
     * 【自定义创作】视频数量,
     */
    private Integer videoCustomCount;

    /**
     * 卡通数字人-视频总量,
     */
    private Integer videoCartoonCount;

    /**
     * 2.5d数字人-视频总量,
     */
    private Integer video25dCount;

    /**
     * 2d数字人-视频总量,
     */
    private Integer video2dCount;

    /**
     * 无数字人-视频总量,
     */
    private Integer videoNoneCount;

    /**
     * 定制2.5d数字人-视频总量,
     */
    private Integer videoCustom25dCount;

    /**
     * 定制2d数字人-视频总量,
     */
    private Integer videoCustom2dCount;

    /**
     * 定制卡通数字人-视频总量,
     */
    private Integer videoCustomCartoonCount;

    /**
     * 定制音频-视频总量
     */
    private Integer videoAudioCount;

    /**
     * 创建时间
     */
    private Date createTime;
}
