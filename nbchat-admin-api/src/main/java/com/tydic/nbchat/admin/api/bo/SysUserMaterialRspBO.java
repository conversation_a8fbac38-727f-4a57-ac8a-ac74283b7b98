package com.tydic.nbchat.admin.api.bo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SysUserMaterialRspBO implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 素材类型: 1-文档/2-音频/3-图片/4-视频
     */
    private String type;

    /**
     * 素材地址
     */
    private String url;

    /**
     * 素材名称
     */
    private String name;

    /**
     * 素材内容
     */
    private String content;

    /**
     * 字数
     */
    private Integer wordCount;

    /**
     * 文档后缀
     */
    private String suffix;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
