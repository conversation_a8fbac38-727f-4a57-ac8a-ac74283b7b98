package com.tydic.nbchat.admin.api.bo.api_token;

import com.tydic.nbchat.admin.api.bo.permission.ApiConfigPermission;
import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SysApiTokenCreateReqBO extends BaseInfo implements Serializable {
    @ParamNotEmpty
    private String appName;
    private String appTenantCode;
    private String appUserId;
    private String secretKey;
    @ParamNotNull
    private Integer expireTime;
    @ParamNotEmpty
    private List<String> subsystem;
    @ParamNotEmpty
    private ApiConfigPermission permission;
}
