package com.tydic.nbchat.admin.api.bo.dept;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class SysDeptUserSaveReqBO extends BasePageInfo implements Serializable {
    /**
     * id
     */
    private Integer id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 目标用户ID
     */
    private String targetUserId;
    /**
     * 目标租户
     */
    private String targetTenantCode;

    /**
     * 电话
     */
    @ParamNotEmpty(message = "手机号不能为空")
    private String phone;
    /**
     * 用户真实姓名
     */
    @ParamNotEmpty(message = "用户真实姓名不能为空")
    private String userRealityName;
    /**
     * 初始密码
     */
    private String password;
    /**
     * 新手机号
     */
    private String newPhone;

    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 加入类型:
     * 1. 用户注册
     * 2. 批量导入
     * 3. 人工创建
     * 4. 邀请码
     */
    private String joinType;
    /**
     * 关键字
     */
    private String keyWords;
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 排序
     */
    private String sort;
    /**
     * 设置角色
     */
    private Set<String> role;

    // 以下是新增字段  用户头像、性别、身份证号、岗位、出生日期
    private String avatar;
    private String idCard;
    private List<Integer> postIds;
    private Date birthday;
    private String gender;

    @Override
    public String toString() {
        return "SysDeptUserSaveReqBO{" +
                "id=" + id +
                ", userId='" + userId + '\'' +
                ", targetUserId='" + targetUserId + '\'' +
                ", targetTenantCode='" + targetTenantCode + '\'' +
                ", phone='" + phone + '\'' +
                ", userRealityName='" + userRealityName + '\'' +
                ", newPhone='" + newPhone + '\'' +
                ", tenantCode='" + tenantCode + '\'' +
                ", joinType='" + joinType + '\'' +
                ", keyWords='" + keyWords + '\'' +
                ", deptId='" + deptId + '\'' +
                ", sort='" + sort + '\'' +
                ", role=" + role +
                ", avatar='" + avatar + '\'' +
                ", idCard='" + idCard + '\'' +
                ", postIds='" + postIds + '\'' +
                ", birthday=" + birthday +
                ", gender='" + gender + '\'' +
                '}';
    }
}
