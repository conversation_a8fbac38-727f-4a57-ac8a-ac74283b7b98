package com.tydic.nbchat.admin.api.bo.dept;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
public class SysDeptUserQueryReqBO extends BasePageInfo implements Serializable {

    private Integer id;
    /**
     * 用户ID
     */
    private String userId;

    @NotBlank(message = "指定租户不能为空")
    private String targetTenantCode;
    //指定用户id
    private String targetUid;
    /**
     * 租户代码
     */
    private String tenantCode;

    /**
     * 加入类型:
     * 1. 用户注册
     * 2. 批量导入
     * 3. 人工创建
     * 4. 邀请码
     */
    private String joinType;
    /**
     * 关键字
     */
    private String keyWords;
    /**
     * 部门ID
     */
    private String deptId;
    private String deptScope;
    /**
     * 排序
     */
    private String sort;
}
