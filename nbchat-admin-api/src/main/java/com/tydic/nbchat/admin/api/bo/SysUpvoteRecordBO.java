package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @datetime：2024/9/18 10:46
 * @description:
 */
@Data
public class SysUpvoteRecordBO extends BasePageInfo implements Serializable {
    private Long id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 租户ID
     */
    private String tenantCode;
    /**
     * 业务编码
     */
    private String busiCode;
    /**
     * 业务ID
     */
    private String busiId;
    /**
     * 点赞状态 0踩 1赞
     */
    private String likeStatus;
    /**
     * 点赞数
     */
    private String likesCount;
    /**
     * 点菜数
     */
    private String dislikesCount;
    /**
     * 不喜欢原因选择
     */
    private String unlikeReason;
    /**
     * 不喜欢原因用户输入
     */
    private String unlikeInput;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 评分
     */
    private Integer rating;
    /**
     * 产品模块 1：视频制作 2：ppt制作 3：形象/声音定制 4：智能出题 5：其他;
     */
    private String productModule;
}
