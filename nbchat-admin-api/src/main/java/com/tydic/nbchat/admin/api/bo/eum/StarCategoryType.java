package com.tydic.nbchat.admin.api.bo.eum;

public enum StarCategoryType {
    /**
     * 全部
     */
    ALL("0",""),
    /**
     * 视频制作
     */
    VIDEO("1","视频制作"),
    /**
     * PPT生成
     */
    PPT("2","PPT生成"),
    /**
     * 形象/声音定制
     */
    IMAGE_VOICE("3","形象/声音定制"),
    /**
     * 出题
     */
    QUESTION("4","智能出题");

    private String code;
    private String name;
    public String getCode() {
        return code;
    }
    public void setCode(String code) {
        this.code = code;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    StarCategoryType(String code, String name) {
        this.code = code;
        this.name = name;
    }
    public static String getByCode(String code) {
        for (StarCategoryType starCategoryType : StarCategoryType.values()) {
            if (starCategoryType.getCode().equals(code)) {
                return starCategoryType.getName();
            }
        }
        return null;
    }
    public static String getNameByCode(String code) {
        for (StarCategoryType starCategoryType : StarCategoryType.values()) {
            if (starCategoryType.getCode().equals(code)) {
                return starCategoryType.getName();
            }
        }
        return null;
    }

}
