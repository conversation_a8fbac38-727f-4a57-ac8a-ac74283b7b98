package com.tydic.nbchat.admin.api.sentence;


import com.tydic.nbchat.admin.api.bo.sentence.*;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;


public interface CommSentenceApi {
    /**
     * 知识增加
     *
     * @param req
     * @return
     */
    Rsp saveCommSentence(SaveCommSentenceReq req);
    /**
     * 知识修改
     *
     * @param req
     * @return
     */
    Rsp updateCommSentence(UpdateCommSentenceReq req);

    /**
     * 知识删除
     *
     * @param req
     * @return
     */
    RspList deleteCommSentence(DelCommSentenceReqBO req);

    /**
     * 知识列表分页查询
     *
     * @param req
     * @return
     */
    RspList getCommSentencePageList(QueryCommSentencePageListReqBO req);

    Rsp getKnowledgeContentById(QueryKnowledgeConReq req);

    Rsp getShareUrl(ShareKnowledgeReqBO req);

    Rsp queryShare(ShareKnowledgeReqBO reqBO);

    Rsp checkShareKey(ShareKnowledgeReqBO reqBO);

    /**
     * 获取知识分类下拉列表
     *
     * @param req
     * @return
     */
//    GetCommSentenceTypeListRsp getCommSentenceTypeList(GetCommSentenceTypeListWebReq req);

    /**
     * 知识详情查询
     *
     * @param req
     * @return
     */
//    GetCommSentenceByIdRsp getCommSentenceById(GetCommSentenceByIdWebReq req);

    /**
     * 知识交换排序标识
     *
     * @param req
     * @return
     */
//    SwapCommSentenceSortIdRsp swapCommSentenceSortId(SwapCommSentenceSortIdWebReq req);
}
