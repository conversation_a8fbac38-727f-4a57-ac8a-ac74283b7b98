package com.tydic.nbchat.admin.api.bo.outer_user;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class OuterUserSyncReqBO extends BaseInfo implements Serializable {
    @ParamNotEmpty
    @Size(max = 50)
    private String appTenantCode;
    @Size(max = 32)
    private String username;
    @Size(max = 32)
    private String password;
    @Size(max = 32)
    private String name;
    @Size(max = 32)
    private String userRealityName;
    @Size(max = 11)
    private String phone;
}
