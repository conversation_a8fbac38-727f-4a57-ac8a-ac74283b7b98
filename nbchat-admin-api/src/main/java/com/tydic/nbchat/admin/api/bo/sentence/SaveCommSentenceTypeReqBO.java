package com.tydic.nbchat.admin.api.bo.sentence;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.Data;

import java.io.Serializable;
@Data
public class SaveCommSentenceTypeReqBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID= 957410836141071117L;

    private String tenantCode;

    private String userId;

    private Short typeGroup;//1工共 2个人

    @ParamNotEmpty(message = "目录名称不能为空")
    private String typeName;

    @ParamNotEmpty(message = "目录类别不能为空")
    private String classes;

    private Integer sortId;

    private String typeDesc;

    private String admin; //1是 0否

    private Long parentId;


}
