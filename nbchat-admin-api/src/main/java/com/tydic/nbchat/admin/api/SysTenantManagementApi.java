package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.SysTenantManagementReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface SysTenantManagementApi {
    /**
     * 创建租户
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    Rsp createTenant(SysTenantManagementReqBO reqBO);

    /**
     * 查询租户信息
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    RspList getTenantList(SysTenantManagementReqBO reqBO);

    /**
     * 更新租户信息
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    Rsp updateTenant(SysTenantManagementReqBO reqBO);


    /**
     * 查询租户价格配置
     *
     * @param @param reqBO
     * @return @return {@link Rsp }
     */
    Rsp queryCustomConfig(SysTenantManagementReqBO reqBO);

}
