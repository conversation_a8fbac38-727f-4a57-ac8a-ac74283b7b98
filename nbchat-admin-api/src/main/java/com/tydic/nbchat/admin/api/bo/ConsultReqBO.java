package com.tydic.nbchat.admin.api.bo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ConsultReqBO implements Serializable {
    //联系电话、企业名称、企业规模、联系人、行业、产品、需求详情
    private String phone="";
    private String enterpriseName="";
    private String employeeRange="";
    private String contactName="";
    private String sector="";
    private String product="";
    private String detail="";


    @Override
    public String toString() {
        return "\n联系电话: " + phone +
                "\n企业名称: " + enterpriseName +
                "\n企业规模: " + employeeRange +
                "\n联系人: " + contactName +
                "\n行业: " + sector +
                "\n产品: " + product +
                "\n需求详情: " + detail;
    }
}
