package com.tydic.nbchat.admin.api.bo.eum;

public enum PermissionRangeEnum {
    NONE("0", "无"),
    ALL("1", "所有人"),
    PART("2", "部分人");

    private String code;
    private String name;



    public boolean equals(String code) {
        return this.code.equals(code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    private PermissionRangeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getNameByCode(String code) {
        for (PermissionRangeEnum field : PermissionRangeEnum.values()) {
            if (field.code.equals(code)) {
                return field.name;
            }
        }
        return "";
    }
}
