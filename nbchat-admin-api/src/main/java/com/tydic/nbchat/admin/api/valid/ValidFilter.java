package com.tydic.nbchat.admin.api.valid;


import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = FilterValidator.class) // 绑定校验逻辑
@Target({ElementType.FIELD, ElementType.PARAMETER}) // 适用于字段和方法参数
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidFilter {
    String message() default "Invalid filter format. Allowed formats: =N, >N, <N, >=N, <=N";

    Class<?>[] groups() default {};  // 分组校验

    Class<? extends Payload>[] payload() default {};  // 负载信息
}