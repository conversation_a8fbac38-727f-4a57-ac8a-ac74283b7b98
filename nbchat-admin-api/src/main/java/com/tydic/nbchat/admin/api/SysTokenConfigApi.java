package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenConfigBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenCreateReqBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenRefreshReqBO;
import com.tydic.nbchat.admin.api.bo.api_token.SysApiTokenRefreshRspBO;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface SysTokenConfigApi {

    public final String TOKEN_CONFIG_KEY = "nbchat-admin:token:";

    /**
     * 获取api token配置
     * @param appId
     * @return
     */
    Rsp<SysApiTokenConfigBO> getApiTokenConfig(Integer appId);

    /**
     * 获取api token配置
     * @param accessKey
     * @return
     */
    Rsp<SysApiTokenConfigBO> getApiTokenConfig(String accessKey);


    /**
     * 重新刷新 token
     * @param refreshReqBO
     * @return
     */
    Rsp<SysApiTokenRefreshRspBO> refreshToken(SysApiTokenRefreshReqBO refreshReqBO);


    Rsp<SysApiTokenRefreshRspBO> createTokenConfig(SysApiTokenCreateReqBO refreshReqBO);

}
