package com.tydic.nbchat.admin.api.bo.file;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
public class PreDownloadUrlRequest implements Serializable {
    @NotEmpty
    private String tenantCode;
    @NotEmpty
    private String userId;
    //文件id - 可选
    @Size(max = 40)
    private String fileNo;
    //传入文件链接
    private String fileUrl;
    //下载文件名 - 可选
    @Size(max = 100)
    private String fileName;
}
