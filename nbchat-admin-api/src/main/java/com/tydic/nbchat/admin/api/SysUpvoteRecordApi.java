package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.SysUpvoteRecordBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * <AUTHOR>
 * @datetime：2024/9/19 10:11
 * @description:
 */
public interface SysUpvoteRecordApi {
    Rsp addNewUpvoteRecord(SysUpvoteRecordBO recordBO);

    Rsp getById(SysUpvoteRecordBO recordBO);

    RspList getUpvoteRecordList(SysUpvoteRecordBO recordBO);
}
