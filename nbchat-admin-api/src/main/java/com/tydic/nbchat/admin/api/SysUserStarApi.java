package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.star.UserStarRequest;
import com.tydic.nbchat.admin.api.star.UserStarSaveRequest;
import com.tydic.nicc.dc.base.bo.Rsp;

public interface SysUserStarApi {

    /**
     * 保存收藏
     * @param request
     * @return
     */
    Rsp<String> saveStar(UserStarSaveRequest request);

    /**
     * 删除收藏
     * @param request
     * @return
     */
    Rsp<String> deleteStar(UserStarRequest request);

    /**
     * 获取收藏
     * @param request
     * @return
     */
    Rsp<String> getStar(UserStarRequest request);
}
