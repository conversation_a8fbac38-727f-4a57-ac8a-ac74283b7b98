package com.tydic.nbchat.admin.api.bo.api_token;

import com.tydic.nbchat.admin.api.bo.permission.ApiConfigPermission;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class SysApiTokenConfigBO implements Serializable {
    private Integer id;
    private String appName;
    private String tenantCode;
    private String userId;
    private String accessKey;
    private String secretKey;
    private Integer expireTime;
    private Date refreshTime;
    private List<String> subsystem;
    private ApiConfigPermission permission;

    public boolean isAllow(String api) {
        if (permission == null) {
            return false;
        }
        if (permission.getDeny().contains(api)) {
            return false;
        }
        if (this.matchDeny(api)) {
            return false;
        }
        if (checkSubSystem(api) && permission.getPermit().contains("/**")) {
            return true;
        }
        if (checkSubSystem(api) && this.matchAllow(api)) {
            return true;
        }
        return checkSubSystem(api) && permission.getPermit().contains(api);
    }


    public boolean matchDeny(String api) {
        if (permission == null) {
            return false;
        }
        for (String s : permission.getDeny()) {
            if (matchUrl(api, s)) return true;
        }
        return false;
    }

    public boolean matchAllow(String api) {
        if (permission == null) {
            return false;
        }
        for (String s : permission.getPermit()) {
            if (matchUrl(api, s)) return true;
        }
        return false;
    }

    private boolean matchUrl(String api, String s) {
        if (s.endsWith("/**")) {
            s = s.substring(0, s.length() - 3);
        } else if (s.endsWith("/*")) {
            s = s.substring(0, s.length() - 2);
        }
        return api.startsWith(s);
    }

    public boolean checkSubSystem(String api) {
        /**
         * 是否子系统的 subSystem
         * /api/session/ 为子系统  接口为 /api/session/query ，判断接口以子系统开头即可
         */
        if (subsystem == null || subsystem.isEmpty()) {
            return false;
        }
        for (String sub : subsystem) {
            if (api.startsWith(sub)) {
                return true;
            }
        }
        return false;
    }

    public static void main(String[] args) {
        SysApiTokenConfigBO sysApiTokenConfigBO = new SysApiTokenConfigBO();
        sysApiTokenConfigBO.setAppName("appName");
        sysApiTokenConfigBO.setTenantCode("tenantCode");
        sysApiTokenConfigBO.setUserId("userId");
        sysApiTokenConfigBO.setAccessKey("accessKey");
        sysApiTokenConfigBO.setSecretKey("secretKey");
        sysApiTokenConfigBO.setExpireTime(1);
        List<String> subsystem = new ArrayList<>();
        subsystem.add("/api/session");
        System.out.println(subsystem.contains("/api/session/get"));
    }

    public boolean isDeny(String api) {
        if (permission == null) {
            return false;
        }
        return this.matchDeny(api);
    }
}
