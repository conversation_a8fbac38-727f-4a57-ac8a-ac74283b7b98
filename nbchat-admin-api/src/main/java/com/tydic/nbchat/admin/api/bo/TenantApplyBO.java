package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 租户申请表(NbchatEnterpriseTryApply)实体类
 *
 * <AUTHOR>
 * @since 2023-09-11 14:31:34
 */
@Data
public class TenantApplyBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = 339221815388653026L;

    private String type = "0"; //0 所有 1个人
    private String tryInviteKey; //申请key，校验有效性
    private String fileUrl;
    private String fileName;
    /**
     * ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private String userId;
    /**
     * 企业名称
     */
    private String companyName;
    /**
     * 所属行业
     */
    private String businessSector;
    /**
     * 租户
     */
    private String tenantCode;
    /**
     * 职位
     */
    private String position;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 具体需求
     */
    private String specificRequirements;
    /**
     * 状态：状态：0 待审批；1 预审批通过；2 通过；3拒绝 4预审批
     */
    private String status;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新时间
     */
    private Date updatedTime;

}

