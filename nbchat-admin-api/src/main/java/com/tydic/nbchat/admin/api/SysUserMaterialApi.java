package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.SysUserMaterialReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface SysUserMaterialApi {
    /**
     * 保存用户素材
     * @param reqBO
     * @return
     */
    Rsp save(SysUserMaterialReqBO reqBO);

    /**
     * 查询用户素材
     * @param reqBO
     * @return
     */
    RspList list(SysUserMaterialReqBO reqBO);

    /**
     * 删除用户素材
     * @param reqBO
     * @return
     */
    Rsp delete(SysUserMaterialReqBO reqBO);
}
