package com.tydic.nbchat.admin.api.bo.file;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR> <br>
 * @Description: FileUploadReqBO  <br>
 * @date 2021/5/11 7:48 下午  <br>
 * @Copyright tydic.com
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class FileUploadRequest implements Serializable {
    private String tenantCode;
    private String busiCode;
    @ParamNotEmpty
    private String uploadUser;
    private String clientIp;
    private String serverIp;
    @ParamNotEmpty
    private String fileName;
    private boolean useOriginalName = false;
    //是否自动生成目录
    private boolean autoDir = true;
    @ParamNotNull
    private byte[] file;
    //文件目录
    private String targetDir;
}
