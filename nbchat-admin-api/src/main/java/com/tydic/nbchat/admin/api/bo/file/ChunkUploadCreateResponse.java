package com.tydic.nbchat.admin.api.bo.file;

import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import com.tydic.nicc.dc.base.bo.mino.MultipartUploadCompleteRequest;
import lombok.Data;

import java.io.Serializable;

@Data
public class ChunkUploadCreateResponse implements Serializable {
    private String tenantCode;
    @ParamNotEmpty
    private String userId;
    @ParamNotNull
    private Integer chunkCount;
    @ParamNotEmpty
    private String fileName;

}
