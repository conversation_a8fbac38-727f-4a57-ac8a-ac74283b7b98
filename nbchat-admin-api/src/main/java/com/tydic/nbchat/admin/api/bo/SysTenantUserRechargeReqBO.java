package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 绑定用户
 */
@Data
public class SysTenantUserRechargeReqBO extends BaseInfo implements Serializable {
    private String userId;
    private String tenantCode;
    /**
     * 租户ID
     */
    @NotEmpty(message = "租户ID不能为空")
    private String targetTenant;
    @NotEmpty(message = "用户ID不能为空")
    private String targetUid;
    @NotNull(message = "请填写充值数额")
    private Integer score;
    //有效期
    private Date expireTime;
    //备注
    private String remark;
    //指定时间天数
    private Integer days;
}
