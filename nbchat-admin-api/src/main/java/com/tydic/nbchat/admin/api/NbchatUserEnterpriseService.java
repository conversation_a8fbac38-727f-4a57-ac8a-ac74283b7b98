package com.tydic.nbchat.admin.api;


import com.tydic.nbchat.admin.api.bo.NbchatUserEnterpriseReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

/**
 * (NbchatUserEnterprise)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-01 11:27:28
 */
public interface NbchatUserEnterpriseService {

    /**
     * 通过ID查询单条数据
     * @param reqBO
     * @return
     */
    Rsp queryById(NbchatUserEnterpriseReqBO reqBO);

    /**
     * 分页查询
     * @param reqBO
     */
    RspList list(NbchatUserEnterpriseReqBO reqBO);

    /**
     * 新增数据
     * @param reqBO
     * @return
     */
    Rsp insert(NbchatUserEnterpriseReqBO reqBO);

    /**
     * 修改数据
     * @param reqBO
     * @return
     */
    Rsp update(NbchatUserEnterpriseReqBO reqBO);

    /**
     * 通过主键删除数据
     * @param reqBO
     * @return
     */
    Rsp deleteById(NbchatUserEnterpriseReqBO reqBO);

    RspList<String> queryEnterpriseList(NbchatUserEnterpriseReqBO reqBO);
}
