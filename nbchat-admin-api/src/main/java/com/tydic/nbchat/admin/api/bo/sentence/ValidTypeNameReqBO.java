package com.tydic.nbchat.admin.api.bo.sentence;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;
@Data
public class ValidTypeNameReqBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID= -1624511032603306823L;
    private Short typeGroup;
    private String typeName;
    private String typeId;

    private String oldTypeName;
    private Long parentId;
    private String tenantCode;
    private String userId;

    private String classes;

    private boolean isUpdate;
}
