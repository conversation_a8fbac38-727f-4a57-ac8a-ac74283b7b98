package com.tydic.nbchat.admin.api;

import com.tydic.nbchat.admin.api.bo.robot.SysRobotConfigAdminSetReqBO;
import com.tydic.nbchat.admin.api.bo.robot.SysRobotConfigQueryReqBO;
import com.tydic.nbchat.admin.api.bo.robot.SysRobotConfigUserSetReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;

/**
 * 课程生成机器人配置
 */
public interface SysRobotConfigApi {

    /**
     * 获取默认机器人配置
     * 1. 优先获取用户配置
     * 2. 用户配置不存在获取机器人配置
     * 3. 都不存在使用默认配置
     * @param tenantCode
     * @param userId
     * @return 机器人配置
     */
    String getRobotValue(String tenantCode,String userId);

    /**
     * 查询机器人列表
     * @param queryReqBO
     * @return
     */
    Rsp getRobotConfigList(SysRobotConfigQueryReqBO queryReqBO);

    /**
     * 管理员配置机器人
     * @param setReqBO
     * @return
     */
    Rsp setRobotConfigByAdmin(SysRobotConfigAdminSetReqBO setReqBO);

    /**
     * 切换 个人/租户 机器人
     * @param setReqBO
     * @return
     */
    Rsp setRobotConfig(SysRobotConfigUserSetReqBO setReqBO);

}
