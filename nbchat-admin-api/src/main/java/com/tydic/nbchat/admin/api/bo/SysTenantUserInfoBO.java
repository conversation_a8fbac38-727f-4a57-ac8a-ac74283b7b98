package com.tydic.nbchat.admin.api.bo;

import com.tydic.nicc.common.bo.BaseInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class SysTenantUserInfoBO extends BaseInfo implements Serializable {

    private Integer id;
    /**
     * 用户ID
     */
    @ParamNotNull(message = "用户ID不能为空")
    private String userId;
    /**
     * 用户真实姓名
     */
    @ParamNotNull(message = "用户真实姓名不能为空")
    private String userRealityName;

    private String tenantCode;

    // 以下是新增字段  用户头像、性别、身份证号、岗位、出生日期
    private String avatar;
    private String idCard;
    private List<Integer> postIds;
    private Date birthday;
    private String gender;
    private String userStatus;


}
