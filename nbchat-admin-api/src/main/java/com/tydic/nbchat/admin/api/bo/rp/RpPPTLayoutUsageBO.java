package com.tydic.nbchat.admin.api.bo.rp;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class RpPPTLayoutUsageBO implements Serializable {
    /**
     * 系统推荐布局ID
     */
    private String layoutId;

    /**
     * 当前页
     */
    private Integer pageNum;

    /**
     * 当前页布局ID
     */
    @NotBlank(message = "布局ID不能为空")
    private String layId;

    /**
     * 系统推荐布局类型;0 删除 /1 新增 /2 修改
     */
    @NotBlank(message = "布局类型不能为空")
    private String type;

    /**
     * 实际使用布局ID
     */
    private String useLayoutId;
}
