package com.tydic.nbchat.admin.api.bo.rp;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/3/24 15:43
 * @description 付费基本信息
 */
@Data
public class RpUserPaymentInfoBO {
    private Integer id;

    /**
     * 租户ID
     */
    private String tenantCode;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 是否是付费用户 0-否/1-是
     */
    private String isSubscriber;

    /**
     * 是否进行过退款 0-否/1-是
     */
    private String isRefund;

    /**
     * 累计退款金额，单位：分
     */
    private Integer totalRefundAmount;

    /**
     * 累计退款次数
     */
    private Integer refundNum;

    /**
     * 累计支付金额，单位：分
     */
    private Integer totalPayAmount;

    /**
     * 累计⽀付次数
     */
    private Integer payNum;

    /**
     * ⾸次⽀付时间
     */
    private Date firstPayTime;

    /**
     * 最近⽀付时间
     */
    private Date lastPayTime;

    /**
     * 首次开通会员日期
     */
    private Date firstVipTime;

    /**
     * 最终会员失效日期
     */
    private Date lastVipTime;

    /**
     * 会员累计付费金额，单位：分
     */
    private Integer totalVipAmount;

    /**
     * 累计充值算力点数量
     */
    private Integer scoreRechargeTotal;

    /**
     * 累计消耗算力点数量
     */
    private Integer scoreConsumeTotal;

    /**
     * 累计消耗算力点数量-个人
     */
    private Integer scoreConsumeTotalPerson;

    /**
     * 累计失效算力点数量
     */
    private Integer scoreLoseTotal;

    /**
     * 当前可用算力点数量
     */
    private Integer scoreBalance;

    /**
     * 算力点购买次数
     */
    private Integer scoreTotalCount;

    /**
     * 算力点累计支付金额，单位：分
     */
    private Integer scoreTotalAmount;

    /**
     * 算力点累计购买数量
     */
    private Integer scoreTotalNum;

    /**
     * 500算力点购买次数
     */
    private Integer score500Count;

    /**
     * 500算力点累计支付金额，单位：分
     */
    private Integer score500Amount;

    /**
     * 2000算力点购买次数
     */
    private Integer score2000Count;

    /**
     * 2000算力点累计支付金额，单位：分
     */
    private Integer score2000Amount;

    /**
     * 5000算力点购买次数
     */
    private Integer score5000Count;

    /**
     * 5000算力点累计支付金额，单位：分
     */
    private Integer score5000Amount;

    /**
     * 更新时间
     */
    private Date updateTime;
}
