package com.tydic.nbchat.session.api.bo;

import com.tydic.nicc.dc.base.annotions.ParamNotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (NbchatSessionShare)实体类
 *
 * <AUTHOR>
 * @since 2023-03-29 15:25:38
 */
@Data
public class NbchatSessionShareReqBO implements Serializable {
    private static final long serialVersionUID = -80305677970869374L;
    /**
     * 共享id
     */
    private String sessionShareId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 会话id
     */
    @ParamNotNull(message = "会话id不得为空")
    private String sessionId;
    /**
     * 密码
     */
    private String shareKey;
    /**
     * 是否需要密码 1需要 0不需要
     */
    private int needKey = 1;
    /**
     * 过期时间
     */
    private Date expiryDate;

    @ParamNotNull(message = "过期时间不得为空")
    private Integer expiredDay;//有效期 1天 7天 30天 -1永久有效
    /**
     * 创建时间
     */
    private Date createdAt;
    /**
     * 共享链接
     */
    private String shareUrl;
    /**
     * 是否删除
     */
    private String isValid;

    public boolean isNeedKey(){
        return needKey == 1;
    }


}

