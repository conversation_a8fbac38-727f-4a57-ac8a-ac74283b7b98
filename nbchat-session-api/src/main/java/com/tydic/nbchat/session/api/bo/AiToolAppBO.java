package com.tydic.nbchat.session.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ai应用配置表(AiToolApp)实体类
 *
 * <AUTHOR>
 * @since 2024-04-11 11:30:02
 */
@Data
public class AiToolAppBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = 649952083978726242L;

    private String status;
    private String appId;
/**
     * 应用名称
     */
    private String appName;
/**
     * 描述
     */
    private String appDesc;
/**
     * 头像
     */
    private String avatar;
/**
     * 应用类型
     */
    private String appType;
/**
     * 应用配置
     */
    private String appConfig;
/**
     * 租户
     */
    private String tenantCode;
/**
     * 用户ID
     */
    private String userId;
/**
     * 机器人
     */
    private String robotType;
/**
     * 创建时间
     */
    private Date createTime;
/**
     * 更新时间
     */
    private Date updateTime;
/**
     * 分享链接
     */
    private String shareLink;
/**
     * 1 分享
     */
    private String shareState;
/**
     * 0 删除 1 有效
     */
    private String isValid;


}

