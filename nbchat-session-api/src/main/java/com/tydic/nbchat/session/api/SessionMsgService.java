package com.tydic.nbchat.session.api;

import com.tydic.nbchat.session.api.bo.AiToolAppUserBO;
import com.tydic.nbchat.session.api.bo.NbchatSessionMsgReqBO;
import com.tydic.nbchat.session.api.bo.NbchatSessionReqBO;
import com.tydic.nbchat.session.api.bo.NbchatSessionShareReqBO;
import com.tydic.nicc.dc.base.bo.Rsp;
import com.tydic.nicc.dc.base.bo.RspList;

public interface SessionMsgService {

    /**
     * 查询历史会话
     * @param reqBO
     * @return
     */
    RspList queryHistory(NbchatSessionMsgReqBO reqBO);

    /**
     * 分页查询会话消息
     * @param reqBO
     * @return
     */
    RspList getMsgList(NbchatSessionMsgReqBO reqBO);

    /**
     * 删除消息
     * @param reqBO
     * @return
     */
    Rsp deleteMsg(NbchatSessionMsgReqBO reqBO);

    /**
     * 清除会话记录
     * @param reqBO
     * @return
     */
    Rsp clearMsg(NbchatSessionMsgReqBO reqBO);


    /**
     * 查询会话消息
     * @param request
     * @return
     */
    RspList getAllSession(NbchatSessionMsgReqBO request);


}
