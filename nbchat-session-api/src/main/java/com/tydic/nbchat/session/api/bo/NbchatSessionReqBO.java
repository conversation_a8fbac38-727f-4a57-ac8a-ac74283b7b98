package com.tydic.nbchat.session.api.bo;

import com.tydic.nicc.common.bo.BasePageInfo;
import com.tydic.nicc.dc.base.annotions.ParamNotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * (NbchatSession)实体类
 *
 * <AUTHOR>
 * @since 2023-03-29 15:25:08
 */
@Data
public class NbchatSessionReqBO extends BasePageInfo implements Serializable {
    private static final long serialVersionUID = -71621972286533333L;
    private String sessionType = "0"; //0迪问会话 1生成试题会话

    /**
     * 会话分享id
     */
    private String sessionShareId;
    /**
     * 会话id
     */
    private String sessionId;
    /**
     * 租户编码
     */
    private String tenantCode;
    /**
     * 会话名称
     */
    private String sessionName;
    /**
     * 所属分类
     */
    private String categoryId;
    /**
     * 用户id
     */
    @ParamNotEmpty(message = "用户id不得为空")
    private String userId;
    /**
     * 1:自有 2:共享
     */
    private String source;
    /**
     * 机器人id
     */
    private String appId;
    /**
     * 机器人类型
     */
    private String robotType;
    /**
     * 是否收藏 1 
     */
    private String isStar;
    /**
     * 0 已删除 1 正常
     */
    private String isValid;
    /**
     * 是否多轮对话
     */
    private Integer chatTurn;

}

