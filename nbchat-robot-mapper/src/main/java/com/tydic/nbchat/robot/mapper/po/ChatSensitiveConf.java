package com.tydic.nbchat.robot.mapper.po;

import java.io.Serializable;
import java.util.Date;


public class ChatSensitiveConf implements Serializable {

    private Integer confId;
    private String tenantCode;
    private Integer filterState;
    private String filterType;
    private Date crtTime;


    public Date getCrtTime() {
        return crtTime;
    }

    public void setCrtTime(Date crtTime) {
        this.crtTime = crtTime;
    }

    public Integer getConfId() {
        return confId;
    }

    public void setConfId(Integer confId) {
        this.confId = confId;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public Integer getFilterState() {
        return filterState;
    }

    public void setFilterState(Integer filterState) {
        this.filterState = filterState;
    }

    public String getFilterType() {
        return filterType;
    }

    public void setFilterType(String filterType) {
        this.filterType = filterType;
    }

    @Override
    public String toString() {
        return "ChatSensitiveConf{" +
                "confId=" + confId +
                ", tenantCode='" + tenantCode + '\'' +
                ", filterState=" + filterState +
                ", filterType='" + filterType + '\'' +
                ", crtTime=" + crtTime +
                '}';
    }
}
